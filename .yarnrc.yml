nodeLinker: node-modules

plugins:
  - path: .yarn/plugins/@yarnpkg/plugin-interactive-tools.cjs
    spec: "@yarnpkg/plugin-interactive-tools"

yarnPath: .yarn/releases/yarn-3.6.4.cjs

npmScopes: {
  bryntum: {
    npmRegistryServer: https://npm-us.bryntum.com,
    npmAlwaysAuth: true,
    npmAuthIdent: alec..newgennow.com,
    npmAuthToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWFsX2dyb3VwcyI6WyJ0cmlhbCIsInNjaGVkdWxlcnBybyIsImNhbGVuZGFyIiwiJGFsbCIsIiRhdXRoZW50aWNhdGVkIiwiQGFsbCIsIkBhdXRoZW50aWNhdGVkIiwiYWxsIl0sIm5hbWUiOiJhbGVjLi5uZXdnZW5ub3cuY29tIiwiZ3JvdXBzIjpbInRyaWFsIiwic2NoZWR1bGVycHJvIiwiY2FsZW5kYXIiLCIkYWxsIiwiJGF1dGhlbnRpY2F0ZWQiLCJAYWxsIiwiQGF1dGhlbnRpY2F0ZWQiLCJhbGwiXSwiaWF0IjoxNzE4MDMzODQ0LCJuYmYiOjE3MTgwMzM4NDQsImV4cCI6MjU4MTk0NzQ0NH0.fKztKiVHC_uMhaWcCAPb-ba4YqwJWCcLYzz7Zr-3q8g
  }
}
