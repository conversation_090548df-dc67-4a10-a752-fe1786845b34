"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { CalendarIcon, RefreshCw } from "lucide-react"

interface FallbackUIProps {
  onRetry?: () => void
  error?: string
}

export function FallbackUI({ onRetry, error }: FallbackUIProps) {
  return (
    <div className="flex flex-col items-center justify-center h-screen bg-background p-4">
      <div className="text-primary mb-4">
        <CalendarIcon size={64} />
      </div>
      <h1 className="text-2xl font-bold text-foreground mb-2">Calendar Application</h1>
      <p className="text-muted-foreground mb-6 text-center max-w-md">
        {error || "The calendar application is currently unavailable. Please try again."}
      </p>
      {onRetry && (
        <Button onClick={onRetry} className="flex items-center gap-2">
          <RefreshCw size={16} />
          Retry
        </Button>
      )}
    </div>
  )
}

