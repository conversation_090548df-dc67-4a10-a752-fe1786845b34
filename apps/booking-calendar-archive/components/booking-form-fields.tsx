"use client"

import type React from "react"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import type { Venue, Room } from "@/types/calendar"

interface BookingFormFieldsProps {
  title: string
  setTitle: (title: string) => void
  description: string
  setDescription: (description: string) => void
  venueId: string
  handleVenueChange: (venueId: string) => void
  roomId: string
  setRoomId: (roomId: string) => void
  start: Date
  end: Date
  handleDateChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleTimeChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  isAllDay: boolean
  setIsAllDay: (isAllDay: boolean) => void
  venues: Venue[]
  venueRooms: Room[]
  isCompact?: boolean
}

export function BookingFormFields({
  title,
  setTitle,
  description,
  setDescription,
  venueId,
  handleVenueChange,
  roomId,
  setRoomId,
  start,
  end,
  handleDateChange,
  handleTimeChange,
  isAllDay,
  setIsAllDay,
  venues,
  venueRooms,
  isCompact = false,
}: BookingFormFieldsProps) {
  return (
    <div className="space-y-5 py-2">
      <div className="space-y-2">
        <Label htmlFor="title" className="text-sm font-medium">
          Title *
        </Label>
        <Input
          id="title"
          name="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter booking title"
          required
          autoFocus
          className="w-full"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="venue" className="text-sm font-medium">
          Venue *
        </Label>
        <Select value={venueId} onValueChange={handleVenueChange}>
          <SelectTrigger id="venue" className="w-full">
            <SelectValue placeholder="Select a venue" />
          </SelectTrigger>
          <SelectContent>
            {venues.map((venue) => (
              <SelectItem key={venue.id} value={venue.id}>
                {venue.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="room" className="text-sm font-medium">
          Room *
        </Label>
        <Select value={roomId} onValueChange={(value) => setRoomId(value)}>
          <SelectTrigger id="room" className="w-full">
            <SelectValue placeholder="Select a room" />
          </SelectTrigger>
          <SelectContent>
            {venueRooms.map((room) => (
              <SelectItem key={room.id} value={room.id}>
                {room.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {venueRooms.length === 0 && (
          <p className="text-xs text-destructive mt-1">
            No rooms available for this venue. Please select another venue or add rooms.
          </p>
        )}
      </div>

      <div className="flex items-center gap-2 py-1">
        <Checkbox id="isAllDay" checked={isAllDay} onCheckedChange={(checked) => setIsAllDay(!!checked)} />
        <Label htmlFor="isAllDay" className="text-sm font-medium cursor-pointer">
          All day event
        </Label>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="startDate" className="text-sm font-medium">
            Start Date
          </Label>
          <Input
            id="startDate"
            type="date"
            name="startDate"
            value={start.toISOString().split("T")[0]}
            onChange={handleDateChange}
            className="w-full"
          />
        </div>

        {!isAllDay && (
          <div className="space-y-2">
            <Label htmlFor="startTime" className="text-sm font-medium">
              Start Time
            </Label>
            <Input
              id="startTime"
              type="time"
              name="startTime"
              value={`${String(start.getHours()).padStart(2, "0")}:${String(start.getMinutes()).padStart(2, "0")}`}
              onChange={handleTimeChange}
              className="w-full"
            />
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="endDate" className="text-sm font-medium">
            End Date
          </Label>
          <Input
            id="endDate"
            type="date"
            name="endDate"
            value={end.toISOString().split("T")[0]}
            onChange={handleDateChange}
            className="w-full"
          />
        </div>

        {!isAllDay && (
          <div className="space-y-2">
            <Label htmlFor="endTime" className="text-sm font-medium">
              End Time
            </Label>
            <Input
              id="endTime"
              type="time"
              name="endTime"
              value={`${String(end.getHours()).padStart(2, "0")}:${String(end.getMinutes()).padStart(2, "0")}`}
              onChange={handleTimeChange}
              className="w-full"
            />
          </div>
        )}
      </div>

      {!isCompact && (
        <div className="space-y-2">
          <Label htmlFor="description" className="text-sm font-medium">
            Description
          </Label>
          <Textarea
            id="description"
            name="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter booking description"
            rows={3}
            className="w-full resize-none"
          />
        </div>
      )}


    </div>
  )
}

