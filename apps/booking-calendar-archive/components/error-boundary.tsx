"use client"

import { Component, type ErrorInfo, type ReactNode } from "react"

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error("Error caught by ErrorBoundary:", error, errorInfo)
  }

  render(): ReactNode {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="p-4 bg-destructive text-destructive-foreground rounded-md m-4">
            <h2 className="text-lg font-bold mb-2">Something went wrong</h2>
            <details className="whitespace-pre-wrap">
              <summary className="cursor-pointer">Show error details</summary>
              <p className="mt-2 font-mono text-sm">{this.state.error?.toString()}</p>
            </details>
          </div>
        )
      )
    }

    return this.props.children
  }
}

