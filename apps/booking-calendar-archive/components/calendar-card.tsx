"use client"

import { Card, CardContent } from "@/components/ui/card"
import { FullCalendarWrapper } from "./full-calendar"
import { useCalendar } from "@/context/calendar-context"
import { cn } from "../lib/utils"

interface CalendarCardProps {
  className?: string
}

export function CalendarCard({ className }: CalendarCardProps) {
  const { view } = useCalendar()

  return (
    <Card className={cn("flex flex-col h-full overflow-hidden border rounded-lg shadow-sm bg-card", className)}>
      <CardContent className="flex-1 p-0 overflow-hidden">
        <div
          className={cn(
            "h-full w-full transition-all duration-200 calendar-wrapper",
            view === "timeline" ? "pt-2" : "pt-3",
          )}
        >
          <FullCalendarWrapper />
        </div>
      </CardContent>
    </Card>
  )
}

