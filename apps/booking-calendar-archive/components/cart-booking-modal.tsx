"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useBookingForm } from "@/hooks/use-booking-form"
import { useBookingCart } from "@/store/booking-cart"
import { useCalendar } from "@/context/calendar-context"
import { BookingFormFields } from "./booking-form-fields"
import { ShoppingCart, Clock, AlertCircle, Trash2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import type { Booking, GhostBooking } from "@/context/calendar-context"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"

interface CartBookingModalProps {
  isOpen: boolean
  onClose: () => void
  booking: GhostBooking
}

export function CartBookingModal({ isOpen, onClose, booking }: CartBookingModalProps) {
  const { items, updateItem, removeItem } = useBookingCart()
  const { rooms } = useCalendar()

  // Extract cart item ID from ghost booking ID
  const [cartItemId, setCartItemId] = useState<string | null>(null)
  const [cartItemAddedAt, setCartItemAddedAt] = useState<Date | null>(null)

  // Initialize cart-specific state
  useEffect(() => {
    if (booking && booking.id.startsWith("ghost-cart-")) {
      const itemId = booking.id.replace("ghost-cart-", "")
      setCartItemId(itemId)

      // Find the cart item to get its addedAt date
      const cartItem = items.find((item) => item.id === itemId)
      if (cartItem) {
        setCartItemAddedAt(cartItem.addedAt)
      }
    }
  }, [booking, items])

  const {
    title,
    setTitle,
    description,
    setDescription,
    venueId,
    roomId,
    setRoomId,
    start,
    end,
    isAllDay,
    setIsAllDay,
    venueRooms,
    venues,
    handleVenueChange,
    handleDateChange,
    handleTimeChange,
    handleCancel,
    validateForm,
  } = useBookingForm({
    isOpen,
    onClose,
    booking,
    createGhostByDefault: true,
  })

  // Handle removing item from cart
  const handleRemoveFromCart = () => {
    if (cartItemId && window.confirm("Are you sure you want to remove this item from your cart?")) {
      removeItem(cartItemId)
      onClose()
    }
  }

  // Handle form submission for cart items
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm() || !cartItemId) return

    const bookingData: Booking = {
      id: cartItemId,
      title,
      description,
      venueId,
      roomId,
      start,
      end,
      isAllDay,
    }

    const room = rooms.find((r) => r.id === roomId)
    if (room) {
      const cartItem = items.find((item) => item.id === cartItemId)
      if (cartItem) {
        updateItem(cartItemId, {
          ...cartItem,
          booking: bookingData,
          room,
        })
      }
    }

    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleCancel()}>
      <DialogContent className="w-full max-w-md p-0 overflow-hidden">
        <DialogHeader className="px-6 pt-6 pb-4">
          <DialogTitle>Edit Cart Item</DialogTitle>
          <DialogDescription>
            <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
              <ShoppingCart className="h-4 w-4" />
              <span>Editing an item in your booking cart</span>
            </div>
          </DialogDescription>

          {cartItemAddedAt && (
            <div className="mt-2 pt-2">
              <Badge variant="outline" className="gap-1 text-xs font-normal">
                <Clock className="h-3 w-3" />
                Added to cart: {format(cartItemAddedAt, "MMM d, yyyy 'at' h:mm a")}
              </Badge>
            </div>
          )}
        </DialogHeader>

        <div className="bg-blue-50 dark:bg-blue-950 mx-6 p-4 rounded-md flex items-start gap-2">
          <AlertCircle className="h-5 w-5 text-blue-500 dark:text-blue-400 flex-shrink-0 mt-0.5" />
          <div className="text-sm text-blue-700 dark:text-blue-300">
            <p className="font-medium">Cart Item</p>
            <p>Changes will be reflected in your booking cart but won't be confirmed until you complete checkout.</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="px-6">
          <BookingFormFields
            title={title}
            setTitle={setTitle}
            description={description}
            setDescription={setDescription}
            venueId={venueId}
            handleVenueChange={handleVenueChange}
            roomId={roomId}
            setRoomId={setRoomId}
            start={start}
            end={end}
            handleDateChange={handleDateChange}
            handleTimeChange={handleTimeChange}
            isAllDay={isAllDay}
            setIsAllDay={setIsAllDay}
            createGhost={true}
            handleGhostToggle={() => {}}
            venues={venues}
            venueRooms={venueRooms}
            showGhostToggle={false}
          />

          <div className="flex justify-between mt-6 pb-3">
            <Button
              type="button"
              variant="destructive"
              onClick={handleRemoveFromCart}
              className="sm:w-auto"
            >
              <Trash2 className="h-4 w-4" />
              Remove
            </Button>
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={handleCancel} className="w-full sm:w-auto">
                Cancel
              </Button>

              <Button
                type="submit"
                disabled={!roomId || venueRooms.length === 0}
                className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
              >
                Update Item
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

