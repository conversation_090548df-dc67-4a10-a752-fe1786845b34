"use client"

import { useState, useEffect } from "react"
import { CalendarProvider } from "./context/calendar-context"
import { CalendarHeader } from "./components/calendar-header"
import { CalendarSidebar } from "./components/calendar-sidebar"
import { Calendar } from "./components/calendar"
import { BookingCart } from "./components/booking-cart"
import { CartGhostIndicator } from "./components/cart-ghost-indicator"
import { useTheme } from "next-themes"
import { hydrateBookingCart } from "./store/booking-cart"
import { useCartGhostSync } from "./hooks/use-cart-ghost-sync"

export default function BookingCalendar() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // useEffect only runs on the client, so now we can safely show the UI
  useEffect(() => {
    // Hydrate the cart from storage
    hydrateBookingCart()
    setMounted(true)
  }, [])

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark")
  }

  // Avoid rendering with incorrect theme
  if (!mounted) {
    return null
  }

  return (
    <CalendarProvider>
      <CalendarInitializer />
      <div className="flex flex-col h-screen">
        <CalendarHeader toggleTheme={toggleTheme} currentTheme={theme === "dark" ? "dark" : "light"} />
        <div className="flex flex-1 overflow-hidden">
          <CalendarSidebar />
          <Calendar />
          <BookingCart />
        </div>
        <CartGhostIndicator />
      </div>
    </CalendarProvider>
  )
}

// This component handles initialization of ghost events from cart items
function CalendarInitializer() {
  // Use our custom hook to sync cart items with ghost bookings
  useCartGhostSync()

  // This is a utility component that doesn't render anything
  return null
}

