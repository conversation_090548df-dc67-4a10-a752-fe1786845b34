"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect, useCallback } from "react"
import { mockBookings, venues, rooms, buildings } from "@/data/mock-data"
import { addDays, addMonths, addWeeks, subDays, subMonths, subWeeks } from "date-fns"
import { useViewPreferences } from "@/store/view-preferences"

// Define types
export interface Booking {
  id: string
  title: string
  start: Date
  end: Date
  venueId: string
  roomId: string
  description?: string
  isAllDay?: boolean
}

export interface GhostBooking extends Booking {
  status: "draft" | "pending" | "confirmed" | "rejected"
}

export interface Venue {
  id: string
  name: string
  color: string
  isSelected: boolean
  isExpanded: boolean
}

export interface Building {
  id: string
  name: string
  isExpanded: boolean
  isSelected: boolean
}

export interface Room {
  id: string
  name: string
  color: string
  isSelected: boolean
  buildingId: string
  venueId: string
}

export type AppView = "day" | "week" | "month" | "timeline"

// Define context type
interface CalendarContextType {
  bookings: Booking[]
  filteredBookings: Booking[]
  ghostBookings: GhostBooking[]
  venues: Venue[]
  buildings: Building[]
  rooms: Room[]
  currentDate: Date
  view: AppView
  setCurrentDate: (date: Date) => void
  setView: (view: AppView) => void
  addBooking: (booking: Booking) => void
  updateBooking: (booking: Booking) => void
  deleteBooking: (id: string) => void
  addGhostBooking: (booking: GhostBooking) => void
  updateGhostBooking: (booking: GhostBooking) => void
  deleteGhostBooking: (id: string) => void
  clearGhostBookings: () => void
  convertGhostBookingToReal: (ghostId: string) => void
  toggleVenueSelection: (id: string) => void
  toggleBuildingSelection: (id: string) => void
  toggleRoomSelection: (id: string) => void
  toggleVenueExpansion: (id: string) => void
  toggleBuildingExpansion: (id: string) => void
  nextPeriod: () => void
  prevPeriod: () => void
  todayPeriod: () => void
  goToToday: () => void
}

// Create context
const CalendarContext = createContext<CalendarContextType | undefined>(undefined)

// Provider component
export function CalendarProvider({ children }: { children: React.ReactNode }) {
  // Initialize state
  const [bookings, setBookings] = useState<Booking[]>([])
  const [ghostBookings, setGhostBookings] = useState<GhostBooking[]>([])
  const [venuesList, setVenuesList] = useState<Venue[]>([])
  const [buildingsList, setBuildingsList] = useState<Building[]>([])
  const [roomsList, setRoomsList] = useState<Room[]>([])
  const [currentDate, setCurrentDate] = useState(new Date())
  const { view: persistedView, setView: persistView } = useViewPreferences()
  const [view, setViewState] = useState<AppView>(persistedView)

  // Load initial data
  useEffect(() => {
    // Convert string dates to Date objects
    const formattedBookings = mockBookings.map((booking) => ({
      ...booking,
      start: new Date(booking.start),
      end: new Date(booking.end),
    }))

    setBookings(formattedBookings)
    setVenuesList(venues)
    setBuildingsList(buildings)
    setRoomsList(rooms)
  }, [])

  // Filter bookings based on selected venues and rooms
  const filteredBookings = bookings.filter((booking) => {
    const venue = venuesList.find((v) => v.id === booking.venueId)
    const room = roomsList.find((r) => r.id === booking.roomId)
    return venue?.isSelected && room?.isSelected
  })

  // Set view and persist it
  const setView = useCallback(
    (newView: AppView) => {
      setViewState(newView)
      persistView(newView)
    },
    [persistView],
  )

  // Add a new booking
  const addBooking = useCallback((booking: Booking) => {
    setBookings((prev) => [...prev, booking])
  }, [])

  // Update an existing booking
  const updateBooking = useCallback((booking: Booking) => {
    setBookings((prev) => prev.map((b) => (b.id === booking.id ? booking : b)))
  }, [])

  // Delete a booking
  const deleteBooking = useCallback((id: string) => {
    setBookings((prev) => prev.filter((b) => b.id !== id))
  }, [])

  // Add a ghost booking
  const addGhostBooking = useCallback((booking: GhostBooking) => {
    setGhostBookings((prev) => [...prev, booking])
  }, [])

  // Update a ghost booking
  const updateGhostBooking = useCallback((booking: GhostBooking) => {
    setGhostBookings((prev) => prev.map((b) => (b.id === booking.id ? booking : b)))
  }, [])

  // Delete a ghost booking
  const deleteGhostBooking = useCallback((id: string) => {
    setGhostBookings((prev) => prev.filter((b) => b.id !== id))
  }, [])

  // Clear all ghost bookings
  const clearGhostBookings = useCallback(() => {
    setGhostBookings([])
  }, [])

  // Convert a ghost booking to a real booking
  const convertGhostBookingToReal = useCallback(
    (ghostId: string) => {
      const ghostBooking = ghostBookings.find((b) => b.id === ghostId)

      if (ghostBooking) {
        // Create a new booking from the ghost booking
        const newBooking: Booking = {
          id: `booking-${Date.now()}`, // Generate a new ID
          title: ghostBooking.title,
          start: ghostBooking.start,
          end: ghostBooking.end,
          venueId: ghostBooking.venueId,
          roomId: ghostBooking.roomId,
          description: ghostBooking.description,
          isAllDay: ghostBooking.isAllDay,
        }

        // Add the new booking
        addBooking(newBooking)

        // Delete the ghost booking
        deleteGhostBooking(ghostId)
      }
    },
    [ghostBookings, addBooking, deleteGhostBooking],
  )

  // Toggle venue selection
  const toggleVenueSelection = useCallback((id: string) => {
    // First, update the venue's selection state
    setVenuesList((prev) => {
      const updatedVenues = prev.map((venue) => {
        if (venue.id === id) {
          return { ...venue, isSelected: !venue.isSelected }
        }
        return venue
      })

      // Get the new selection state of the venue
      const venue = updatedVenues.find((v) => v.id === id)
      const newIsSelected = venue?.isSelected || false

      // Then, update all rooms that belong to this venue
      setRoomsList((prevRooms) =>
        prevRooms.map((room) => {
          if (room.venueId === id) {
            return { ...room, isSelected: newIsSelected }
          }
          return room
        }),
      )

      return updatedVenues
    })
  }, [])

  // Toggle building selection
  const toggleBuildingSelection = useCallback(
    (id: string) => {
      setBuildingsList((prev) =>
        prev.map((building) => {
          if (building.id === id) {
            return { ...building, isSelected: !building.isSelected }
          }
          return building
        }),
      )

      // Also toggle all rooms in this building
      const building = buildingsList.find((b) => b.id === id)
      if (building) {
        const newIsSelected = !building.isSelected
        setRoomsList((prev) =>
          prev.map((room) => {
            if (room.buildingId === id) {
              return { ...room, isSelected: newIsSelected }
            }
            return room
          }),
        )
      }
    },
    [buildingsList],
  )

  // Toggle room selection
  const toggleRoomSelection = useCallback((id: string) => {
    setRoomsList((prev) =>
      prev.map((room) => {
        if (room.id === id) {
          return { ...room, isSelected: !room.isSelected }
        }
        return room
      }),
    )
  }, [])

  // Toggle venue expansion
  const toggleVenueExpansion = useCallback((id: string) => {
    setVenuesList((prev) =>
      prev.map((venue) => {
        if (venue.id === id) {
          return { ...venue, isExpanded: !venue.isExpanded }
        }
        return venue
      }),
    )
  }, [])

  // Toggle building expansion
  const toggleBuildingExpansion = useCallback((id: string) => {
    setBuildingsList((prev) =>
      prev.map((building) => {
        if (building.id === id) {
          return { ...building, isExpanded: !building.isExpanded }
        }
        return building
      }),
    )
  }, [])

  // Navigate to next period
  const nextPeriod = useCallback(() => {
    setCurrentDate((prev) => {
      switch (view) {
        case "day":
          return addDays(prev, 1)
        case "week":
          return addWeeks(prev, 1)
        case "month":
          return addMonths(prev, 1)
        case "timeline":
          return addDays(prev, 7) // Default to week for timeline
        default:
          return prev
      }
    })
  }, [view])

  // Navigate to previous period
  const prevPeriod = useCallback(() => {
    setCurrentDate((prev) => {
      switch (view) {
        case "day":
          return subDays(prev, 1)
        case "week":
          return subWeeks(prev, 1)
        case "month":
          return subMonths(prev, 1)
        case "timeline":
          return subDays(prev, 7) // Default to week for timeline
        default:
          return prev
      }
    })
  }, [view])

  // Navigate to today
  const todayPeriod = useCallback(() => {
    setCurrentDate(new Date())
  }, [])

  const goToToday = useCallback(() => {
    setCurrentDate(new Date())
  }, [])

  // Create context value
  const contextValue: CalendarContextType = {
    bookings,
    filteredBookings,
    ghostBookings,
    venues: venuesList,
    buildings: buildingsList,
    rooms: roomsList,
    currentDate,
    view,
    setCurrentDate,
    setView,
    addBooking,
    updateBooking,
    deleteBooking,
    addGhostBooking,
    updateGhostBooking,
    deleteGhostBooking,
    clearGhostBookings,
    convertGhostBookingToReal,
    toggleVenueSelection,
    toggleBuildingSelection,
    toggleRoomSelection,
    toggleVenueExpansion,
    toggleBuildingExpansion,
    nextPeriod,
    prevPeriod,
    todayPeriod,
    goToToday,
  }

  return <CalendarContext.Provider value={contextValue}>{children}</CalendarContext.Provider>
}

// Custom hook to use the calendar context
export function useCalendar() {
  const context = useContext(CalendarContext)
  if (context === undefined) {
    throw new Error("useCalendar must be used within a CalendarProvider")
  }
  return context
}

