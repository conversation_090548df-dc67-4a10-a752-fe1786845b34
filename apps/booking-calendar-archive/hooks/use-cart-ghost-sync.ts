"use client"

import { useEffect, useRef } from "react"
import { useBookingCart } from "@/store/booking-cart"
import { useCalendar } from "@/context/calendar-context"
import type { GhostBooking } from "@/context/calendar-context"

// Declare the global flag
declare global {
  interface Window {
    disableGhostUpdates?: boolean
  }
}

/**
 * Hook to synchronize cart items with ghost bookings
 * This ensures cart items are visually represented on the calendar
 * even after page refresh
 */
export function useCartGhostSync() {
  const { items } = useBookingCart()
  const { ghostBookings, addGhostBooking, deleteGhostBooking } = useCalendar()
  const syncedRef = useRef(false)
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Skip if ghost updates are disabled
    if (window.disableGhostUpdates) return

    // Skip if we've already synced or if there are no items
    if (syncedRef.current || items.length === 0) return

    // Mark as synced to prevent multiple syncs
    syncedRef.current = true

    // Clear any existing timeout
    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current)
    }

    // Use a debounced timeout to batch updates
    syncTimeoutRef.current = setTimeout(() => {
      // Get existing cart ghost IDs
      const existingCartGhostIds = ghostBookings
        .filter((ghost) => ghost.id.startsWith("ghost-cart-"))
        .map((ghost) => ghost.id)

      // Get current cart item IDs
      const currentCartItemIds = items.map((item) => `ghost-cart-${item.id}`)

      // Find ghost bookings to remove (exist in ghostBookings but not in items)
      const ghostsToRemove = existingCartGhostIds.filter((ghostId) => !currentCartItemIds.includes(ghostId))

      // Remove ghost bookings that don't have corresponding cart items
      ghostsToRemove.forEach((ghostId) => {
        deleteGhostBooking(ghostId)
      })

      // Add or update ghost bookings for each cart item
      items.forEach((item) => {
        const ghostId = `ghost-cart-${item.id}`
        const existingGhost = ghostBookings.find((ghost) => ghost.id === ghostId)

        // Ensure we have valid date objects
        const validStart =
          item.booking.start instanceof Date && !isNaN(item.booking.start.getTime()) ? item.booking.start : new Date()

        const validEnd =
          item.booking.end instanceof Date && !isNaN(item.booking.end.getTime())
            ? item.booking.end
            : new Date(validStart.getTime() + 3600000) // Default to 1 hour after start

        // Only add if the ghost doesn't exist
        if (!existingGhost) {
          const ghostBooking: GhostBooking = {
            id: ghostId,
            title: `Cart: ${item.booking.title || `New Booking for ${item.room.name}`}`,
            start: validStart,
            end: validEnd,
            venueId: item.booking.venueId,
            roomId: item.booking.roomId,
            description: item.booking.description || "",
            isAllDay: item.booking.isAllDay || false,
            status: "pending",
          }

          addGhostBooking(ghostBooking)
        }
      })
    }, 500) // Debounce for 500ms
  }, [items, ghostBookings, addGhostBooking, deleteGhostBooking])

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current)
      }
    }
  }, [])
}

