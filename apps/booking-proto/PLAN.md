# Implementation Plan – Booking Calendar (Booking-Proto)

> This document is mirrored in the NetSuite micro-service folder so both FE & BE teams share a single source of truth.

---

## 1. Overview

A Next-JS + FullCalendar booking application that integrates with NetSuite SuiteCloud. Supports:

* Drag-and-drop create / resize / delete
* Two distinct queues:
  * **Cart** – brand-new bookings (🛒)
  * **Cloud Sync** – edits to existing bookings (☁️)
* Bulk checkout / sync endpoints powered by NetSuite RESTlets
* 15-minute date-range caching with `N/cache`
* “Hold” status queue (max 10 per space)
* OAuth 2.0 (NextAuth) authentication

---

## 2. Data Model Mapping

| NetSuite Record | Field Map | FullCalendar Field |
|-----------------|-----------|--------------------|
| **CS Booking**  | id, startDate, endDate, status, space, event | id, start, end, status, resourceId |
| **CS Space**    | id, name, venue                                | resource.id, title |
| **CS Venue**    | id, name                                       | resource.group |
| **CS Event**    | id, name                                       | extendedProps.event |

Special: `status === "HOLD"` → `queuePosition` 1-10.

---

## 3. Queues & UX

| Queue | Purpose | Icon | Store Key |
|-------|---------|------|-----------|
| Cart | New bookings (not yet saved) | 🛒 | `bookingCart` |
| Cloud Sync | Deltas for committed bookings | ☁️ | `bookingSync` |

Both icons live in `CalendarHeader` next to each other, each displaying a counter badge.

Actions:

* **Select date range** → push ghost booking to **Cart**.
* **Drag / resize existing event** → add patch object to **Cloud Sync**.
* **Delete existing event** → add `{ id, action: "delete" }` to **Cloud Sync**.

---

## 4. API Endpoints (Next-JS `/app/api`)

| Route | RESTlet | Purpose |
|-------|---------|---------|
| `GET /api/bookings` | `booking-events` | List events & resources |
| `POST /api/bookings/sync` | `booking-sync` | Bulk create/update/delete |
| `GET /api/settings` | `booking-settings` | Misc settings |
| `GET /api/user` | `booking-user` | Current user |

### 4.1 Bulk Payload

```ts
interface BulkSyncPayload {
  creates?: FCEventCreateDTO[]   // From Cart
  updates?: FCEventPatchDTO[]    // From Cloud Sync (action="update")
  deletes?: string[]             // From Cloud Sync (action="delete")
}
```

---

## 5. Frontend Stores

* `store/booking-cart.ts` (exists)
* **NEW** `store/booking-sync.ts` – zustand store for cloud queue.

```ts
type BookingPatch = {
  id: string
  start?: string
  end?: string
  resourceId?: string
  action: "update" | "delete"
}
```

---

## 6. FullCalendar Integration

* `eventChange` – push/update patch in `bookingSync`.
* `eventRemove` – push delete action.
* Add class `fc-event-pending-sync` for unsynced edits (yellow border).

---

## 7. RESTlets (SuiteCloud)

Files live in `apps/backend/.../micro-services/booking-calendar/`:

* `ng_cses_rl_booking_events.js` – GET list (w/ `N/cache`).
* `ng_cses_rl_booking_sync.js`  – bulk mutator (handles creates / updates / deletes).
* `ng_cses_rl_booking_settings.js` – settings.

---

## 8. Caching Strategy

* Key = `${venueId}|${from}|${to}`
* TTL = 900 s (15 min)
* Invalidate on successful bulk sync for overlapping range.

---

## 9. Testing Matrix

| Type | Tool | Scenario |
|------|------|----------|
| Unit (FE) | RTL + Jest | Drag → Sync queue increments |
| Unit (BE) | Jest | Hold queue overflow returns 409 |
| E2E | Playwright | Resize → ☁️ count ↑ → click → changes persist |

---

## 10. Milestones (FE + BE)

1. RESTlet skeletons + cache wrappers
2. API route wrappers & auth wiring
3. Sync store + header icons
4. Drag/resizes feed sync queue; selects feed cart
5. Bulk checkout & hold logic
6. Tests & CI

---

## 11. Data Models

### 11.1 Booking Resources Data Model

#### Overview
This data model defines the structure for calendar resources (Venues and Spaces) used by the booking calendar RESTlet (`ng_cses_rl_booking_resources.js`). The structure is designed for use with FullCalendar's vertical resource view, where Venues are parent resources and Spaces are their children.

#### Endpoint
`GET https://tstdrv1516212.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_ng_cses_rl_booking_resource&deploy=customdeploy_ng_cses_rl_booking_resource`

An optional `venueId` query parameter can be passed to filter the results for a single venue.

#### Response Payload Structure
The top-level response is an object containing the success status and the data payload.

```json
{
  "ok": true,
  "data": {
    "resources": [
      {
        "id": "1",
        "title": "Main Convention Center",
        "children": [
          {
            "id": "101",
            "title": "Hall A",
            "extendedProps": {
              "occupancy": "500",
              "bookingLegendColor": "#ff0000",
              "subSpaces": "A1, A2, A3",
              "isGroupSpace": false
            }
          }
        ]
      }
    ]
  }
}
```

#### Data Dictionary

##### Venue Object
| Key | Type | Description |
|---|---|---|
| `id` | String | The internal ID of the Venue record. |
| `title` | String | The name of the Venue. |
| `children` | Array<Space> | An array of Space objects belonging to this Venue. |

##### Space Object
| Key | Type | Description |
|---|---|---|
| `id` | String | The internal ID of the Space record. |
| `title` | String | The name of the Space. |
| `extendedProps` | Object | An object containing additional custom properties for the space. |

##### extendedProps Object
| Key | Type | Description |
|---|---|---|
| `occupancy` | String | The maximum occupancy of the space. |
| `bookingLegendColor` | String | The hex color code for bookings in this space. |
| `subSpaces` | String | A comma-separated list of any sub-spaces. |
| `isGroupSpace` | Boolean | A flag indicating if this is a group space. |

---

> Update this doc with every significant architecture change. 