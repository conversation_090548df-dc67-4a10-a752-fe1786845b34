import type { Booking } from "src/types/calendar"
import { isSameDay } from "./date-utils"

/**
 * Checks if two bookings overlap in time
 */
export function doBookingsOverlap(booking1: Booking, booking2: Booking): boolean {
  // Different venues can have overlapping times
  if (booking1.venueId !== booking2.venueId) return false

  // Check if the bookings are on the same day
  if (!isSameDay(booking1.start, booking2.start)) return false

  // Check for time overlap
  return (
    (booking1.start < booking2.end && booking1.end > booking2.start) ||
    (booking2.start < booking1.end && booking2.end > booking1.start)
  )
}

/**
 * Detects conflicts between a booking and existing bookings
 */
export function detectBookingConflicts(booking: Booking, existingBookings: Booking[]): Booking[] {
  return existingBookings.filter(
    (existingBooking) => booking.id !== existingBooking.id && doBookingsOverlap(booking, existingBooking)
  )
}

/**
 * Snaps a time value to the nearest 15-minute interval
 */
export function snapToFifteenMinutes(date: Date): Date {
  const minutes = date.getMinutes()
  const remainder = minutes % 15

  const snappedDate = new Date(date)

  if (remainder < 7.5) {
    // Snap down
    snappedDate.setMinutes(minutes - remainder, 0, 0)
  } else {
    // Snap up
    snappedDate.setMinutes(minutes + (15 - remainder), 0, 0)
  }

  return snappedDate
}

/**
 * Calculates the position of a booking when moved to a new date/time
 */
export function calculateNewBookingPosition(
  booking: Booking,
  sourceDate: Date,
  destinationDate: Date,
  hourOffset = 0,
  sourceVenueId?: string,
  destinationVenueId?: string
): Booking {
  // Calculate time difference between source and destination dates
  const dateDiff = destinationDate.getTime() - sourceDate.getTime()

  // Calculate hour offset in milliseconds
  const hourOffsetMs = hourOffset * 60 * 60 * 1000

  // Create new dates for the booking
  const newStart = new Date(booking.start.getTime() + dateDiff + hourOffsetMs)
  const newEnd = new Date(booking.end.getTime() + dateDiff + hourOffsetMs)

  // Snap both start and end times to 15-minute intervals
  const snappedStart = snapToFifteenMinutes(newStart)

  // Preserve the original duration
  const duration = booking.end.getTime() - booking.start.getTime()
  const snappedEnd = new Date(snappedStart.getTime() + duration)

  return {
    ...booking,
    start: snappedStart,
    end: snappedEnd,
    venueId: destinationVenueId || booking.venueId,
  }
}

/**
 * Calculates a time from a vertical position in pixels
 */
export function calculateTimeFromPosition(pixelPosition: number, baseDate: Date): Date {
  // Convert pixels to minutes (1px = 1 minute)
  const totalMinutes = pixelPosition

  // Calculate hours and minutes
  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60

  // Create a new date with the calculated time
  const result = new Date(baseDate)
  result.setHours(hours, minutes, 0, 0)

  // Snap to nearest 15-minute interval
  return snapToFifteenMinutes(result)
}

/**
 * Calculates day column from x position
 */
export function calculateDayFromPosition(x: number, containerRect: DOMRect, dayWidth: number, baseDate: Date): Date {
  // Calculate relative position
  const relativeX = x - containerRect.left

  // Calculate day index (0-based)
  const dayIndex = Math.floor(relativeX / dayWidth)

  // Create new date with the calculated day
  const newDate = new Date(baseDate)
  newDate.setDate(newDate.getDate() + dayIndex)

  return newDate
}
