/**
 * Calculates the optimal position for a FullCalendar event popover
 */
export function calculatePopoverPosition(
  el: HTMLElement,
  popoverWidth: number,
  popoverHeight: number
): { left: number; top: number } {
  const rect = el.getBoundingClientRect();
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  let left = rect.right + 10;
  let top = rect.top;

  if (left + popoverWidth > viewportWidth - 20) {
    left = rect.left - popoverWidth - 10;
    if (left < 20) {
      left = Math.max(20, rect.left + rect.width / 2 - popoverWidth / 2);
    }
  }

  if (top + popoverHeight > viewportHeight - 20) {
    top = Math.max(20, viewportHeight - popoverHeight - 20);
  }

  if (top < 20) {
    top = 20;
  }

  const transform = window.getComputedStyle(el).transform;
  if (transform && transform !== 'none') {
    console.log('Event has transform:', transform);
  }

  return { left, top };
}
