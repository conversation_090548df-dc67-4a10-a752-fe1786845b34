const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || ''

interface EncryptedResponse {
	token: string
}

async function getKey(secret: string): Promise<CryptoKey> {
	try {
		const encoder = new TextEncoder()
		const keyData = encoder.encode(secret)

		// Create a hash of the key to ensure proper length
		const keyHash = await crypto.subtle.digest('SHA-256', keyData)

		return await crypto.subtle.importKey(
			'raw',
			keyHash,
			{ name: 'AES-GCM' },
			false,
			['encrypt', 'decrypt'],
		)
	} catch (error) {
		console.error('Key generation error:', error)
		throw new Error(
			`Key generation failed: ${error instanceof Error ? error.message : 'unknown error'}`,
		)
	}
}

export async function encrypt(text: string): Promise<string> {
	try {
		if (typeof text !== 'string') {
			throw new Error('Input must be a string')
		}

		const encoder = new TextEncoder()
		const key = await getKey(ENCRYPTION_KEY)
		const iv = crypto.getRandomValues(new Uint8Array(12))
		const encoded = encoder.encode(text)

		const ciphertext = await crypto.subtle.encrypt(
			{ name: 'AES-GCM', iv },
			key,
			encoded,
		)

		// Combine IV and ciphertext
		const combined = new Uint8Array(
			iv.length + new Uint8Array(ciphertext).length,
		)
		combined.set(iv)
		combined.set(new Uint8Array(ciphertext), iv.length)

		// Use URL-safe base64 encoding
		const base64 = Buffer.from(combined).toString('base64')
		return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '')
	} catch (error) {
		console.error('Encryption error:', {
			error,
			keyPresent: !!ENCRYPTION_KEY,
			inputType: typeof text,
		})
		throw new Error(
			`Encryption failed: ${error instanceof Error ? error.message : 'unknown error'}`,
		)
	}
}

export async function decrypt(
	encryptedData: string | EncryptedResponse,
): Promise<string> {
	try {
		// Handle both string and response object cases
		const encryptedText =
			typeof encryptedData === 'string' ? encryptedData : encryptedData?.token

		if (typeof encryptedText !== 'string') {
			throw new Error(
				`Invalid input: encryptedText must be a string, received ${typeof encryptedText}`,
			)
		}

		if (!encryptedText) {
			throw new Error('No encrypted text provided')
		}

		// Restore base64 padding
		let base64 = encryptedText.replace(/-/g, '+').replace(/_/g, '/')
		const pad = base64.length % 4
		if (pad) {
			base64 += '='.repeat(4 - pad)
		}

		const combined = new Uint8Array(Buffer.from(base64, 'base64'))

		if (combined.length < 13) {
			throw new Error('Invalid encrypted data length')
		}

		const iv = combined.slice(0, 12)
		const ciphertext = combined.slice(12)

		const key = await getKey(ENCRYPTION_KEY)
		const decrypted = await crypto.subtle.decrypt(
			{ name: 'AES-GCM', iv },
			key,
			ciphertext,
		)

		const decoder = new TextDecoder()
		return decoder.decode(decrypted)
	} catch (error) {
		console.error('Decryption error:', {
			error,
			keyPresent: !!ENCRYPTION_KEY,
			inputType: typeof encryptedData,
		})
		throw new Error(
			`Decryption failed: ${error instanceof Error ? error.message : 'unknown error'}`,
		)
	}
}
