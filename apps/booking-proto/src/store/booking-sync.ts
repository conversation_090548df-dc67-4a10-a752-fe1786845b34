import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { immer } from "zustand/middleware/immer"

export type BookingPatch = {
  id: string
  start?: string
  end?: string
  resourceId?: string
  action: "update" | "delete"
}

interface BookingSyncState {
  items: BookingPatch[]
  addOrUpdateItem: (item: BookingPatch) => void
  removeItem: (id: string) => void
  clear: () => void
}

export const useBookingSync = create<BookingSyncState>()(
  persist(
    immer((set) => ({
      items: [],
      addOrUpdateItem: (item) =>
        set((state) => {
          const existingIndex = state.items.findIndex((i) => i.id === item.id)
          if (existingIndex > -1) {
            // If item exists, merge new changes.
            // This is useful for when a user drags an event, then resizes it.
            // We only want one final "update" record.
            Object.assign(state.items[existingIndex] as any, item)
          } else {
            state.items.push(item)
          }
        }),
      removeItem: (id) =>
        set((state) => {
          state.items = state.items.filter((item) => item.id !== id)
        }),
      clear: () => set({ items: [] }),
    })),
    {
      name: "booking-sync-storage",
      storage: createJSONStorage(() => sessionStorage), // Use sessionStorage
    }
  )
)
