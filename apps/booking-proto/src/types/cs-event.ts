export interface CSEvent {
  id: string
  name: string
  date: Date
  description?: string
  location?: string
  organizer?: string
  contactEmail?: string
  contactPhone?: string
  status: CSEventStatus
  attendees?: number
  budget?: number
  notes?: string
  createdAt: Date
  updatedAt: Date
  bookingIds?: string[] // Reference to bookings associated with this event
}

export type CSEventStatus = 
  | "Planning" 
  | "Confirmed" 
  | "InProgress" 
  | "Completed" 
  | "Canceled"

export interface CSEventFormData {
  name: string
  date: Date
  description?: string
  location?: string
  organizer?: string
  contactEmail?: string
  contactPhone?: string
  status: CSEventStatus
  attendees?: number
  budget?: number
  notes?: string
}
