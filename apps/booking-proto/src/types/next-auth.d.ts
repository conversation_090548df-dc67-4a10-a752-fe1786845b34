import NextAuth, { DefaultSession } from "next-auth"

declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    accessToken?: string
    exhibitorUrl?: string
    error?: string
    user: {
      /** Extends the default session user */
      id?: string
      location?: string
      role?: string
      contact?: any
      isScanUser?: boolean
    } & DefaultSession["user"]
  }

  /**
   * The shape of the user object returned in the OAuth providers' `profile` callback,
   * or the second parameter of the `session` callback, when using a database.
   */
  interface User {
    id?: string
    location?: string
    role?: string
    contact?: any
    isScanUser?: boolean
  }
}
