export type ViewType = "day" | "week" | "month" | "timeline"

export interface Room {
  id: string
  name: string
  color: string
  isSelected: boolean
  buildingId: string
  venueId: string // Add venueId to associate rooms with venues
}

export interface Building {
  id: string
  name: string
  isExpanded: boolean
  isSelected: boolean
}

export interface Venue {
  id: string
  name: string
  color: string
  isSelected: boolean
  isExpanded?: boolean // Add isExpanded property for UI state
}

export type BookingStatus = 
  | "Inquiry" 
  | "Prospect" 
  | "Tentative" 
  | "Definite" 
  | "Lost" 
  | "Canceled" 
  | "Hold"

export interface Booking {
  id: string
  title: string
  start: Date
  end: Date
  venueId: string
  ghostBookingId?: string  // ID of the source ghost booking if this came from the cart
  roomId: string // Changed from optional to required
  description?: string
  color?: string
  isAllDay?: boolean
  status?: BookingStatus
  holdRank?: number // For Hold statuses
  lostReason?: string // For Lost status
  csEventId?: string // Reference to associated CS Event
  needsEventAssignment?: boolean // Flag to indicate if this booking needs to be assigned to an event
}

export interface CalendarDay {
  date: Date
  isToday: boolean
  isCurrentMonth: boolean
  bookings: Booking[]
}

export interface TimeSlot {
  id: string
  time: Date
  bookings: Booking[]
}

export interface DragItem {
  id: string
  type: "BOOKING"
  booking: Booking
  sourceIndex: number
  sourceDate: string
}

export interface DropResult {
  bookingId: string
  sourceDate: string
  destinationDate: string
  sourceIndex: number
  destinationIndex: number
}

// New interface for resource representation
export interface ResourceItem {
  id: string
  title: string
  parentId?: string // For parent-child relationships (spaces belong to venues)
  venueName?: string // For grouping by venue
  venueId?: string // For identifying the venue
  eventColor?: string
  businessHours?: {
    startTime: string
    endTime: string
    daysOfWeek: number[]
  }
  extendedProps?: {
    occupancy?: string
    bookingLegendColor?: string
    subSpaces?: string
    isGroupSpace?: boolean
    venueId?: string
    venueName?: string
  }
}

export type TimeSlotInterval = "15min" | "30min" | "1hour" | "2hours" | "4hours" | "8hours" | "1day"

