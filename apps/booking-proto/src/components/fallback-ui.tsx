"use client"

import { <PERSON><PERSON> } from "src/components/ui/button"
import { CalendarIcon, RefreshCw } from "lucide-react"

interface FallbackUIProps {
  onRetry?: () => void
  error?: string
}

export function FallbackUI({ onRetry, error }: FallbackUIProps) {
  return (
    <div className="flex h-screen flex-col items-center justify-center bg-background p-4">
      <div className="mb-4 text-primary">
        <CalendarIcon size={64} />
      </div>
      <h1 className="mb-2 text-2xl font-bold text-foreground">Calendar Application</h1>
      <p className="mb-6 max-w-md text-center text-muted-foreground">
        {error || "The calendar application is currently unavailable. Please try again."}
      </p>
      {onRetry && (
        <Button onClick={onRetry} className="flex items-center gap-2">
          <RefreshCw size={16} />
          Retry
        </Button>
      )}
    </div>
  )
}
