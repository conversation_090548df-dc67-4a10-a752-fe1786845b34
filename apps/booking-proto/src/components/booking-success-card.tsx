"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "src/components/ui/card"
import { But<PERSON> } from "src/components/ui/button"
import { ArrowRight, CalendarCheck, Clock, MapPin, X } from "lucide-react"
import { format } from "date-fns"
import { cn } from "src/lib/utils"
import { type CartItem } from "src/store/booking-cart"
import { Badge } from "src/components/ui/badge"
import { ScrollArea } from "src/components/ui/scroll-area"

interface BookingSuccessCardProps {
  bookings: CartItem[]
  eventName: string
  onClose: () => void
  onViewCalendar: () => void
}

export function BookingSuccessCard({ bookings, eventName, onClose, onViewCalendar }: BookingSuccessCardProps) {
  const [visible, setVisible] = useState(false)

  useEffect(() => {
    // Animate in when component mounts
    const timer = setTimeout(() => {
      setVisible(true)
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  const handleClose = () => {
    setVisible(false)
    // Wait for animation to complete before calling onClose
    setTimeout(onClose, 500)
  }

  const totalBookings = bookings.length
  const totalHours = bookings.reduce((acc, item) => {
    const duration = new Date(item.booking.end).getTime() - new Date(item.booking.start).getTime()
    return acc + duration / (1000 * 60 * 60)
  }, 0)

  return (
    <div
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center bg-black/40 transition-opacity duration-300",
        visible ? "opacity-100" : "pointer-events-none opacity-0"
      )}
      onClick={(e) => e.target === e.currentTarget && handleClose()}
    >
      <Card
        className={cn(
          "w-full max-w-lg transform border-indigo-200 shadow-xl transition-all duration-500",
          visible ? "scale-100 shadow-indigo-100/20" : "scale-95"
        )}
      >
        <CardHeader className="flex flex-row items-start justify-between rounded-t-lg border-b bg-gradient-to-r from-green-50 to-emerald-50">
          <div>
            <div className="flex items-center gap-2">
              <div className="rounded-full bg-green-100 p-1.5">
                <CalendarCheck className="h-5 w-5 text-green-600" />
              </div>
              <CardTitle className="text-green-800">Bookings Confirmed</CardTitle>
            </div>
            <CardDescription className="mt-1.5">
              Your bookings have been successfully confirmed and added to your calendar.
            </CardDescription>
          </div>
          <Button
            size="icon"
            variant="ghost"
            className="h-8 w-8 rounded-full transition-colors hover:bg-red-50 hover:text-red-500"
            onClick={handleClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-4 p-6">
          {eventName && (
            <div className="rounded-lg bg-muted/50 p-3">
              <div className="text-sm font-medium text-muted-foreground">Associated Event</div>
              <div className="mt-1 font-medium">{eventName}</div>
            </div>
          )}

          <div>
            <div className="mb-2 text-sm font-medium text-muted-foreground">Booking Summary</div>
            <ScrollArea className="h-[160px]">
              <div className="space-y-3">
                {bookings.map((booking) => {
                  const start = new Date(booking.booking.start)
                  const end = new Date(booking.booking.end)

                  return (
                    <div key={booking.id} className="rounded-lg border bg-card p-3 shadow-sm">
                      <div className="font-medium">{booking.booking.title}</div>
                      <div className="mt-1 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1.5">
                          <MapPin className="h-3.5 w-3.5" />
                          <span>{booking.room.name}</span>
                        </div>
                        <div className="mt-1 flex items-center gap-1.5">
                          <Clock className="h-3.5 w-3.5" />
                          <span>
                            {format(start, "MMM d, yyyy")} · {format(start, "h:mm a")} - {format(end, "h:mm a")}
                          </span>
                        </div>
                      </div>
                      <Badge variant="outline" className="mt-2 border-green-200 bg-green-50 text-green-700">
                        Confirmed
                      </Badge>
                    </div>
                  )
                })}
              </div>
            </ScrollArea>

            <div className="mt-4 flex items-center justify-between px-2 text-sm">
              <span>
                Total Bookings: <span className="font-medium">{totalBookings}</span>
              </span>
              <span>
                Total Hours: <span className="font-medium">{totalHours.toFixed(1)}h</span>
              </span>
            </div>
          </div>
        </CardContent>

        <CardFooter className="flex justify-end gap-3 rounded-b-lg border-t bg-muted/20 p-4">
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleClose} className="border-gray-200 hover:bg-gray-50">
              Close
            </Button>
            <Button
              className="gap-2 bg-gradient-to-r from-indigo-500 to-purple-600 transition-all hover:from-indigo-600 hover:to-purple-700"
              onClick={onViewCalendar}
            >
              View Calendar
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
