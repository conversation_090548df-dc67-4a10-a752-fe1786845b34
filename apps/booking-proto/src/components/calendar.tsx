"use client"

import dynamic from 'next/dynamic'
import type { EventClickArg, DateSelectArg } from '@fullcalendar/core'

// Dynamically import the FullCalendarWrapper to ensure it only loads on the client
const FullCalendarWrapper = dynamic(
  () => import('./calendar/FullCalendar'),
  { ssr: false }
)

export function Calendar({ onEventClick, onDateSelect }: {
  onEventClick?: (arg: EventClickArg) => void
  onDateSelect?: (arg: DateSelectArg) => void
}) {
  return <FullCalendarWrapper onEventClick={onEventClick} onDateSelect={onDateSelect} />
}
