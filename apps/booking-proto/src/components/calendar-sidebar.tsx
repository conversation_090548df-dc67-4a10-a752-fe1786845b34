"use client"

import { useCalendar } from "src/context/calendar-context"
import { useResourceManagement } from "src/components/calendar/hooks/useResourceManagement"
import { Checkbox } from "src/components/ui/checkbox"
import { Input } from "src/components/ui/input"
import { Skeleton } from "src/components/ui/skeleton"
import { Search, ChevronDown, ChevronRight, Building2, DoorClosed, ChevronLeft, Minus, AlertCircle } from "lucide-react"
import { useState, useMemo, useEffect, useRef } from "react"
import { addDays } from "src/utils/date-utils"
import { MiniCalendar } from "./mini-calendar"
import { Card, CardContent, CardHeader } from "src/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "src/components/ui/avatar"
import { cn } from "src/lib/utils"

export function CalendarSidebar() {
  const {
    currentDate,
    setCurrentDate,
    venues: contextVenues,
    rooms: contextRooms,
    toggleVenueSelection,
    toggleRoomSelection,
    toggleVenueExpansion,
    setVenues,
    setRooms,
  } = useCalendar()

  // Get NetSuite resource data
  const {
    venues: netsuiteVenues,
    rooms: netsuiteRooms,
    isLoading,
    isError,
  } = useResourceManagement()

  const [searchTerm, setSearchTerm] = useState("")
  
  // Use context data which will be populated from NetSuite
  const venues = contextVenues
  const rooms = contextRooms

  // Initialize venues and rooms in context when NetSuite data loads
  useEffect(() => {
    // Only initialize if context is empty and we have NetSuite data
    if (netsuiteVenues.length > 0 && contextVenues.length === 0) {
      // Transform NetSuite venues to context format with default state
      const venuesWithState = netsuiteVenues.map(venue => ({
        ...venue,
        isSelected: true,
        isExpanded: true,
      }))
      setVenues(venuesWithState)
    }
  }, [netsuiteVenues, contextVenues, setVenues])

  useEffect(() => {
    if (netsuiteRooms.length > 0 && contextRooms.length === 0) {
      // Transform NetSuite rooms to context format with default state
      const roomsWithState = netsuiteRooms.map(room => ({
        ...room,
        isSelected: true,
        buildingId: 'building1', // Default for backward compatibility
      }))
      setRooms(roomsWithState)
    }
  }, [netsuiteRooms, contextRooms, setRooms])

  // Filter venues and rooms based on search term
  const filteredData = useMemo(() => {
    if (!searchTerm.trim()) {
      return { venues, rooms }
    }

    const term = searchTerm.toLowerCase()

    // Filter rooms first
    const matchedRooms = rooms.filter((room) => room.name.toLowerCase().includes(term))

    // Get venue IDs that have matching rooms
    const venueIdsWithMatchingRooms = Array.from(new Set(matchedRooms.map((room) => room.venueId)))

    // Filter venues that match directly or have matching rooms
    const matchedVenues = venues.filter(
      (venue) => venue.name.toLowerCase().includes(term) || venueIdsWithMatchingRooms.includes(venue.id)
    )

    return {
      venues: matchedVenues,
      rooms: matchedRooms,
    }
  }, [venues, rooms, searchTerm])

  // Get rooms for a specific venue
  const getRoomsForVenue = (venueId: string) => {
    return filteredData.rooms.filter((room) => room.venueId === venueId)
  }

  // Check if all rooms in a venue are selected
  const areAllRoomsSelected = (venueId: string) => {
    const venueRooms = rooms.filter((room) => room.venueId === venueId)
    return venueRooms.length > 0 && venueRooms.every((room) => room.isSelected)
  }

  // Check if some (but not all) rooms in a venue are selected
  const areSomeRoomsSelected = (venueId: string) => {
    const venueRooms = rooms.filter((room) => room.venueId === venueId)
    const selectedCount = venueRooms.filter((room) => room.isSelected).length
    return selectedCount > 0 && selectedCount < venueRooms.length
  }



  // Function to handle date selection in the mini calendar
  const handleSelect = (date: Date | undefined) => {
    if (date) {
      setCurrentDate(date)
    }
  }

  // Add state for sidebar collapse
  const [isCollapsed, setIsCollapsed] = useState(false)

  // Auto-collapse on smaller screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsCollapsed(true)
      }
    }

    // Set initial state
    handleResize()

    // Add event listener
    window.addEventListener("resize", handleResize)

    // Cleanup
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  return (
    <div className="relative h-full">
      {/* Toggle button - visible when collapsed */}
      {isCollapsed && (
        <button
          onClick={() => setIsCollapsed(false)}
          className="fixed left-0 top-20 z-20 rounded-r-md border border-l-0 border-border bg-background p-2 text-foreground shadow-sm transition-all hover:bg-muted/80"
          aria-label="Expand sidebar"
        >
          <ChevronRight className="h-4 w-4" />
        </button>
      )}

      <aside
        className={`
          overflow-y-auto border-r bg-background transition-all duration-300 ease-in-out
          ${isCollapsed ? "w-0 p-0 opacity-0" : "w-75 p-4 opacity-100"}
          fixed left-0 top-0 z-10 h-[calc(100vh-4rem)] md:relative
        `}
      >
        <div className="mb-4 flex items-center justify-between px-1">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8 border border-border">
              <AvatarImage src="/placeholder.svg?height=32&width=32" alt="User avatar" />
              <AvatarFallback>JD</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="text-sm font-medium leading-none">John Doe</span>
              <span className="text-xs text-muted-foreground">Acme Corp</span>
            </div>
          </div>
          <button
            onClick={() => setIsCollapsed(true)}
            className="flex h-6 w-6 items-center justify-center rounded-md text-muted-foreground transition-colors hover:bg-muted/80 hover:text-foreground"
            aria-label="Collapse sidebar"
          >
            <ChevronLeft className="h-4 w-4" />
          </button>
        </div>

        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search venues & rooms"
              className="rounded-full border-0 bg-muted pl-8 focus-visible:ring-1 focus-visible:ring-primary"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Mini Calendar */}
        <div className="mb-6">
          <Card className="w-full overflow-hidden">
            <CardHeader className="bg-muted/50 px-3 py-2">
              <h3 className="text-sm font-medium text-foreground/70">Mini Calendar</h3>
            </CardHeader>
            <CardContent className="p-0">
              <MiniCalendar
                mode="single"
                selected={currentDate}
                onSelect={handleSelect}
                disabled={(date) => date < addDays(new Date(), -365) || date > addDays(new Date(), 365)}
              />
            </CardContent>
          </Card>
        </div>

        <div>
          <h3 className="mb-2 pl-4 text-sm font-medium text-foreground">My Venues</h3>

          {/* Loading State */}
          {isLoading && (
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  {/* Venue skeleton */}
                  <div className="flex items-center space-x-3 rounded-lg px-3 py-2">
                    <Skeleton className="h-4 w-4 rounded-sm" />
                    <Skeleton className="h-4 w-4 rounded-sm" />
                    <Skeleton className="h-4 w-4 rounded-sm" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                  {/* Room skeletons */}
                  <div className="ml-7 space-y-1 border-l border-border pl-4">
                    {Array.from({ length: 2 + Math.floor(Math.random() * 3) }).map((_, j) => (
                      <div key={j} className="flex items-center space-x-3 rounded-lg px-3 py-1">
                        <Skeleton className="h-3 w-3 rounded-sm" />
                        <Skeleton className="h-3 w-3 rounded-sm" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Error State */}
          {isError && !isLoading && (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <AlertCircle className="h-8 w-8 text-destructive mb-2" />
              <p className="text-sm font-medium text-foreground mb-1">Unable to load venues</p>
              <p className="text-xs text-muted-foreground">Please check your connection and try again</p>
            </div>
          )}

          {/* Content */}
          {!isLoading && !isError && (
            <div className="space-y-1">
              {filteredData.venues.map((venue) => {
                const venueRooms = getRoomsForVenue(venue.id)
                const hasRooms = venueRooms.length > 0
                                    const allSelected = areAllRoomsSelected(venue.id)
                    const someSelected = areSomeRoomsSelected(venue.id)
                    const isIndeterminate = someSelected && !allSelected
                    const isExpanded = venue.isExpanded

                return (
                  <div key={venue.id} className="space-y-1">
                    {/* Venue row */}
                    <div className="group flex items-center space-x-2 rounded-lg px-3 py-2.5 transition-all hover:bg-muted/50">
                      {/* Expand/collapse button */}
                      <button
                        onClick={() => toggleVenueExpansion(venue.id)}
                        className="flex h-5 w-5 items-center justify-center text-muted-foreground transition-colors hover:text-foreground"
                        aria-label={`${isExpanded ? 'Collapse' : 'Expand'} ${venue.name}`}
                      >
                        {hasRooms && (
                          isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )
                        )}
                      </button>

                      {/* Venue checkbox */}
                      <div className="relative">
                        <Checkbox
                          id={venue.id}
                          checked={allSelected}
                          onCheckedChange={() => toggleVenueSelection(venue.id)}
                          aria-label={`Select all rooms in ${venue.name}`}
                          style={{
                            borderColor: venue.color,
                            backgroundColor: allSelected ? venue.color : "transparent",
                          }}
                          className={cn(
                            "h-4 w-4 rounded-sm border-2 transition-all duration-200 hover:border-opacity-80",
                            isIndeterminate && "bg-opacity-50"
                          )}
                        />
                        {isIndeterminate && (
                          <div
                            className="pointer-events-none absolute inset-0 flex items-center justify-center"
                            style={{ color: venue.color }}
                          >
                            <Minus className="h-3 w-3 text-white drop-shadow-sm" />
                          </div>
                        )}
                      </div>

                      {/* Venue label */}
                      <div className="flex min-w-0 flex-1 items-center gap-2">
                        <div 
                          className="flex h-8 w-8 items-center justify-center rounded-lg"
                          style={{ backgroundColor: `${venue.color}15`, color: venue.color }}
                        >
                          <Building2 className="h-4 w-4" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <label
                            htmlFor={venue.id}
                            className="cursor-pointer text-sm font-medium leading-none text-foreground group-hover:text-foreground"
                          >
                            {venue.name}
                          </label>
                          <p className="text-xs text-muted-foreground mt-0.5">
                            {hasRooms ? `${venueRooms.length} spaces` : 'No spaces'}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Rooms list (collapsible) */}
                    {isExpanded && hasRooms && (
                      <div className="ml-7 space-y-1 border-l-2 border-border/30 pl-4">
                        {venueRooms.map((room) => {
                          return (
                            <div 
                              key={room.id} 
                              className="group flex items-center space-x-2 rounded-lg px-3 py-2 transition-all hover:bg-muted/30"
                            >
                              <Checkbox
                                id={room.id}
                                checked={room.isSelected}
                                onCheckedChange={() => toggleRoomSelection(room.id)}
                                style={{
                                  backgroundColor: room.isSelected ? room.color : "transparent",
                                  borderColor: room.color,
                                }}
                                className="h-3.5 w-3.5 rounded-sm border-2 transition-all duration-200 hover:border-opacity-80"
                              />
                              <div className="flex min-w-0 flex-1 items-center gap-2">
                                <div 
                                  className="flex h-6 w-6 items-center justify-center rounded"
                                  style={{ backgroundColor: `${room.color}10`, color: room.color }}
                                >
                                  <DoorClosed className="h-3 w-3" />
                                </div>
                                <label 
                                  htmlFor={room.id} 
                                  className="cursor-pointer text-sm leading-none text-foreground/90 group-hover:text-foreground"
                                >
                                  {room.name}
                                </label>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    )}
                  </div>
                )
              })}

              {/* No results message */}
              {filteredData.venues.length === 0 && !isLoading && (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <Search className="h-8 w-8 text-muted-foreground/50 mb-2" />
                  <p className="text-sm font-medium text-foreground mb-1">No matches found</p>
                  <p className="text-xs text-muted-foreground">Try adjusting your search terms</p>
                </div>
              )}
            </div>
          )}
        </div>
      </aside>

      {/* Overlay for mobile - closes sidebar when clicking outside */}
      {!isCollapsed && (
        <div
          className="fixed inset-0 z-0 bg-black/20 md:hidden"
          onClick={() => setIsCollapsed(true)}
          aria-hidden="true"
        />
      )}
    </div>
  )
}
