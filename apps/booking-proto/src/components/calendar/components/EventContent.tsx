"use client";

import React from "react";

/**
 * Component props for EventContent
 */
interface EventContentProps {
  event: any;
  timeText?: string;
}

/**
 * Simplified event content renderer for FullCalendar
 * 
 * Optimized to not interfere with drag operations:
 * - Minimal DOM structure
 * - No pointer-events interference
 * - Simple styling during drag
 */
export function EventContent({ event, timeText }: EventContentProps) {
  // Check if we're currently dragging
  const isDragging = typeof document !== 'undefined' && 
    document.body.classList.contains('fc-dragging');
  
  // Get event properties
  const { title, extendedProps } = event;
  const { status, isCartGhost } = extendedProps || {};

  // Show simplified content during drag to avoid interference
  if (isDragging) {
    return (
      <div 
        className="event-content-simple"
        style={{ 
          pointerEvents: 'none',
          userSelect: 'none',
          padding: '2px 4px',
          fontSize: '12px',
          lineHeight: 1.2
        }}
      >
        {title || 'Untitled'}
      </div>
    );
  }

  // Normal content when not dragging
  return (
    <div 
      className="event-content-wrapper"
      style={{
        userSelect: 'none',
        width: '100%',
        height: '100%',
        padding: '2px 4px',
        fontSize: '12px',
        lineHeight: 1.2,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center'
      }}
    >
      <div className="event-title" style={{ fontWeight: 500, pointerEvents: 'none' }}>
        {title || 'Untitled Event'}
      </div>
      
      {timeText && (
        <div className="event-time" style={{ 
          fontSize: '11px', 
          opacity: 0.8,
          marginTop: '1px',
          pointerEvents: 'none'
        }}>
          {timeText}
        </div>
      )}
      
      {status && status !== 'Confirmed' && (
        <div className="event-status" style={{ 
          fontSize: '10px', 
          fontWeight: 600,
          marginTop: '1px',
          color: status === 'Hold' ? '#f59e0b' : '#6b7280',
          pointerEvents: 'none'
        }}>
          {status}
          </div>
        )}
    </div>
  );
}

export default EventContent; 