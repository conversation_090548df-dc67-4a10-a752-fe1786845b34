"use client"

import { useState, useCallback, useEffect } from "react"
import { useViewPreferences } from "src/store/view-preferences"
import type { TimeSlotInterval } from "src/types/calendar"
import type { AppView } from "src/context/calendar-context"
import type { TimelineDuration } from "src/components/timeline-toolbar"
import {
  getViewSpecificSlotConfig,
  getFullCalendarViewType,
  enhanceGridLines,
  getTimelineDurationFromViewType,
} from "src/utils/calendar-utils"

/**
 * Hook for managing calendar view configuration
 *
 * Handles:
 * - View types (day, week, month, timeline)
 * - Timeline duration (day, 3 days, week, etc.)
 * - Time slot intervals (15min, 30min, 1hour)
 * - Grid rendering and enhancements
 *
 * @param calendarRef - Reference to the FullCalendar component
 * @param view - Current app view type
 * @param setView - Function to update the app view
 * @returns Configuration objects and handler functions
 */
export const useCalendarViewConfig = (
  calendarRef: React.RefObject<any>,
  view: AppView,
  setView: (view: AppView) => void
) => {
  // Get persisted view preferences from store
  const {
    timelineDuration: persistedTimelineDuration,
    setTimelineDuration: persistTimelineDuration,
    timeSlotInterval: persistedTimeSlotInterval,
    setTimeSlotInterval: persistTimelineSlotInterval,
  } = useViewPreferences()

  // Timeline duration state - initialize from persisted value
  const [timelineDuration, setTimelineDurationState] = useState<TimelineDuration>(persistedTimelineDuration)

  // Time slot interval state - initialize from persisted value
  const [timeSlotInterval, setTimeSlotIntervalState] = useState<TimeSlotInterval>(persistedTimeSlotInterval)

  // Flag to track internal updates
  const [isInternalUpdate, setIsInternalUpdate] = useState(false)

  /**
   * Handle timeline duration change
   */
  const handleTimelineDurationChange = useCallback(
    (duration: TimelineDuration) => {
      setTimelineDurationState(duration)

      // Persist the timeline duration preference
      persistTimelineDuration(duration)

      if (calendarRef.current) {
        const calendarApi = calendarRef.current.getApi()

        // Map our duration values to FullCalendar view names
        const viewName = getFullCalendarViewType("timeline", duration)

        // Change the view
        calendarApi.changeView(viewName)
      }
    },
    [calendarRef, persistTimelineDuration]
  )

  /**
   * Handle time slot interval change
   */
  const handleTimeSlotIntervalChange = useCallback(
    (interval: TimeSlotInterval) => {
      setTimeSlotIntervalState(interval)

      // Persist the time slot interval preference
      persistTimelineSlotInterval(interval)

      if (calendarRef.current) {
        const calendarApi = calendarRef.current.getApi()

        // Refresh the view to apply the new time slot interval
        const currentView = calendarApi.view.type
        calendarApi.changeView(currentView)

        // Apply grid lines after view change
        setTimeout(() => {
          if (calendarRef.current) {
            try {
              // The following is a workaround for accessing a private property
              // This might break with future FullCalendar updates
              // @ts-ignore - accessing private property
              const calendarElement = calendarRef.current.elRef?.current
              if (calendarElement) {
                enhanceGridLines(calendarElement, view, interval)
              }
            } catch (error) {
              console.warn("Could not enhance grid lines:", error)
            }
          }
        }, 100)
      }
    },
    [calendarRef, view, persistTimelineSlotInterval]
  )

  /**
   * Detect view changes from FullCalendar and sync with app state
   */
  const handleViewChange = useCallback(
    (info: any) => {
      // Extract view type from FullCalendar info
      const fcViewType = info.view.type

      // Already an internal update in progress, skip
      if (isInternalUpdate) {
        setIsInternalUpdate(false)
        return
      }

      // Determine app view from FullCalendar view
      let newAppView: AppView

      if (fcViewType.startsWith("timeGrid")) {
        newAppView = fcViewType === "timeGridDay" ? "day" : "week"
      } else if (fcViewType.startsWith("dayGrid")) {
        newAppView = "month"
      } else if (fcViewType.startsWith("resourceTimeline")) {
        newAppView = "timeline"

        // Also update timeline duration if needed
        const newDuration = getTimelineDurationFromViewType(fcViewType)
        if (newDuration !== timelineDuration) {
          setTimelineDurationState(newDuration)
          persistTimelineDuration(newDuration)
        }
      } else {
        // Fallback to current view if unknown
        newAppView = view
      }

      // Only update if the view has changed
      if (newAppView !== view) {
        setIsInternalUpdate(true)
        setView(newAppView)

        // Apply grid lines after view change
        setTimeout(() => {
          if (calendarRef.current) {
            try {
              // @ts-ignore - accessing private property
              const calendarElement = calendarRef.current.elRef?.current
              if (calendarElement) {
                enhanceGridLines(calendarElement, newAppView, timeSlotInterval)
              }
            } catch (error) {
              console.warn("Could not enhance grid lines:", error)
            }
          }
        }, 100)
      }
    },
    [view, timelineDuration, timeSlotInterval, setView, persistTimelineDuration, calendarRef, isInternalUpdate]
  )

  // Apply grid lines and setup listeners
  useEffect(() => {
    if (!calendarRef.current) return

    // Apply grid lines after initial render
    const applyGridLines = () => {
      if (calendarRef.current) {
        try {
          // @ts-ignore - accessing private property
          const calendarElement = calendarRef.current.elRef?.current
          if (calendarElement) {
            enhanceGridLines(calendarElement, view, timeSlotInterval)
          }
        } catch (error) {
          console.warn("Could not enhance grid lines:", error)
        }
      }
    }

    // Initial application
    setTimeout(applyGridLines, 100)

    // Apply grid lines whenever the window is resized
    window.addEventListener("resize", applyGridLines)

    // Apply grid lines periodically to ensure they remain visible
    const intervalId = setInterval(applyGridLines, 2000)

    return () => {
      window.removeEventListener("resize", applyGridLines)
      clearInterval(intervalId)
    }
  }, [calendarRef, view, timeSlotInterval])

  // Get the slot configuration based on current view
  const slotConfig = getViewSpecificSlotConfig(view, timeSlotInterval)

  return {
    timelineDuration,
    timeSlotInterval,
    slotConfig,
    handleTimelineDurationChange,
    handleTimeSlotIntervalChange,
    handleViewChange,
    getFullCalendarViewType: (v: AppView, duration?: TimelineDuration) =>
      getFullCalendarViewType(v, duration || timelineDuration),
  }
}
