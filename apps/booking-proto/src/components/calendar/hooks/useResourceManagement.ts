"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { useBookingResources } from "src/hooks/useBookingResources"
import { useCalendar } from "src/context/calendar-context"
import type { ResourceItem } from "src/types/calendar"

/**
 * Custom hook for managing calendar resources
 *
 * Handles:
 * - Fetching resources from NetSuite API
 * - Converting NetSuite resources to FullCalendar resource format
 * - Resource grouping by venue (parent-child structure)
 *
 * @returns Object containing resources and related state/handlers
 */
export const useResourceManagement = () => {
  console.group("🏢 useResourceManagement - Initialization")
  console.log("Hook initialized")
  console.groupEnd()

  const { resources: netsuiteResources, isLoading, isError } = useBookingResources()
  const { venues: contextVenues, rooms: contextRooms } = useCalendar()
  const [resources, setResources] = useState<ResourceItem[]>([])

  // Transform NetSuite resources to FullCalendar format
  useEffect(() => {
    console.group("🔄 useResourceManagement - Resource Transformation")
    console.log("NetSuite resources received:", {
      count: netsuiteResources.length,
      isLoading,
      isError,
      data: netsuiteResources
    })

    if (isLoading) {
      console.log("⏳ Still loading resources, skipping transformation")
      console.groupEnd()
      return
    }

    if (isError) {
      console.error("❌ Error loading resources, skipping transformation")
      console.groupEnd()
      return
    }

    const fullCalendarResources: ResourceItem[] = []

    // NetSuite resources already come in parent-child structure
    netsuiteResources.forEach((venue, venueIndex) => {
      console.group(`🏢 Processing Venue ${venueIndex + 1}: ${venue.title} (ID: ${venue.id})`)
      
      // Add the venue as a parent resource
      const venueResource = {
        id: venue.id,
        title: venue.title,
        // FullCalendar will treat this as a parent resource because it has no parentId
      }
      fullCalendarResources.push(venueResource)
      console.log("✅ Added venue resource:", venueResource)

      // Add each space as a child resource
      if (venue.children) {
        console.log(`📦 Processing ${venue.children.length} child spaces`)
        
        venue.children.forEach((space, spaceIndex) => {
          const spaceResource = {
            id: space.id,
            title: space.title,
            // This creates the parent-child relationship in FullCalendar
            parentId: venue.id,
            extendedProps: {
              ...space.extendedProps,
              venueId: venue.id,
              venueName: venue.title,
            },
            // Use the booking legend color if available
            eventColor: space.extendedProps?.bookingLegendColor || '#4285F4',
            businessHours: {
              startTime: "08:00",
              endTime: "20:00",
              daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
            },
          }
          fullCalendarResources.push(spaceResource)
          console.log(`  ✅ Space ${spaceIndex + 1}: ${space.title} (ID: ${space.id})`, {
            color: spaceResource.eventColor,
            occupancy: space.extendedProps?.occupancy,
            extendedProps: space.extendedProps
          })
        })
      } else {
        console.log("⚠️  No children found for venue")
      }
      
      console.groupEnd()
    })

    console.log("📊 Transformation Summary:", {
      totalVenues: netsuiteResources.length,
      totalResources: fullCalendarResources.length,
      venues: fullCalendarResources.filter(r => !r.parentId).length,
      spaces: fullCalendarResources.filter(r => r.parentId).length
    })

    setResources(fullCalendarResources)
    console.log("✅ Resources state updated")
    console.groupEnd()
  }, [netsuiteResources, isLoading, isError])

  /**
   * Finds a space by id
   */
  const findSpace = useCallback(
    (spaceId: string) => {
      console.group(`🔍 findSpace - Looking for space ID: ${spaceId}`)
      const space = resources.find((r) => r.id === spaceId && r.parentId)
      console.log(space ? "✅ Space found:" : "❌ Space not found", space)
      console.groupEnd()
      return space
    },
    [resources]
  )

  /**
   * Finds a venue by id
   */
  const findVenue = useCallback(
    (venueId: string) => {
      console.group(`🔍 findVenue - Looking for venue ID: ${venueId}`)
      const venue = resources.find((r) => r.id === venueId && !r.parentId)
      console.log(venue ? "✅ Venue found:" : "❌ Venue not found", venue)
      console.groupEnd()
      return venue
    },
    [resources]
  )

  /**
   * Gets venue color by venue id
   */
  const getVenueColor = useCallback(
    (venueId: string) => {
      // Find spaces belonging to this venue and get their color
      const venueSpace = resources.find((r) => r.parentId === venueId)
      return venueSpace?.eventColor || "#4285F4"
    },
    [resources]
  )

  /**
   * Gets space color by space id
   */
  const getSpaceColor = useCallback(
    (spaceId: string) => {
      const space = resources.find((r) => r.id === spaceId)
      return space?.eventColor || "#4285F4"
    },
    [resources]
  )

  /**
   * Gets spaces for a specific venue
   */
  const getSpacesForVenue = useCallback(
    (venueId: string) => {
      console.group(`📦 getSpacesForVenue - Getting spaces for venue ID: ${venueId}`)
      const spaces = resources.filter((r) => r.parentId === venueId)
      console.log(`Found ${spaces.length} spaces:`, spaces.map(s => ({ id: s.id, title: s.title })))
      console.groupEnd()
      return spaces
    },
    [resources]
  )

  // Create a rooms array for backward compatibility
  const rooms = useMemo(() => {
    console.group("🔄 useResourceManagement - Creating backward compatibility rooms array")
    const roomsArray = resources
      .filter((r) => r.parentId) // Only spaces (children)
      .map((space) => ({
        id: space.id,
        name: space.title,
        color: space.eventColor || '#4285F4',
        isSelected: true, // Default to selected
        buildingId: 'building1', // Legacy field, not used
        venueId: space.parentId || '',
      }))
    console.log(`Created ${roomsArray.length} room entries for backward compatibility:`, roomsArray)
    console.groupEnd()
    return roomsArray
  }, [resources])

  // Create a venues array for backward compatibility
  const venues = useMemo(() => {
    console.group("🔄 useResourceManagement - Creating backward compatibility venues array")
    const venuesArray = resources
      .filter((r) => !r.parentId) // Only venues (parents)
      .map((venue) => ({
        id: venue.id,
        name: venue.title,
        color: getVenueColor(venue.id),
        isSelected: true, // Default to selected
        isExpanded: true, // Default to expanded
      }))
    console.log(`Created ${venuesArray.length} venue entries for backward compatibility:`, venuesArray)
    console.groupEnd()
    return venuesArray
  }, [resources, getVenueColor])

  // Filter resources based on selection state from context
  const filteredResources = useMemo(() => {
    console.group("🎯 useResourceManagement - Filtering Resources by Selection")
    
    if (contextVenues.length === 0 || contextRooms.length === 0) {
      console.log("⚠️ Context not initialized yet, returning all resources")
      console.groupEnd()
      return resources
    }

    const selectedVenueIds = new Set(contextVenues.filter(v => v.isSelected).map(v => v.id))
    const selectedRoomIds = new Set(contextRooms.filter(r => r.isSelected).map(r => r.id))
    
    const filtered = resources.filter(resource => {
      if (!resource.parentId) {
        // This is a venue - include if selected
        return selectedVenueIds.has(resource.id)
      } else {
        // This is a room/space - include if both the room and its venue are selected
        return selectedRoomIds.has(resource.id) && selectedVenueIds.has(resource.parentId)
      }
    })
    
    console.log(`Filtered from ${resources.length} to ${filtered.length} resources based on selection`)
    console.log("Selected venues:", Array.from(selectedVenueIds))
    console.log("Selected rooms:", Array.from(selectedRoomIds))
    console.groupEnd()
    
    return filtered
  }, [resources, contextVenues, contextRooms])

  const returnValue = {
    resources: filteredResources, // Return filtered resources instead of all resources
    rooms, // For backward compatibility
    venues, // For backward compatibility
    findRoom: findSpace, // Alias for backward compatibility
    findSpace,
    findVenue,
    getVenueColor,
    getRoomColor: getSpaceColor, // Alias for backward compatibility
    getSpaceColor,
    getRoomsForVenue: getSpacesForVenue, // Alias for backward compatibility
    getSpacesForVenue,
    isLoading,
    isError,
  }

  console.group("📋 useResourceManagement - Final State Summary")
  console.log("Hook execution complete. Returning:", {
    totalResourcesCount: resources.length,
    filteredResourcesCount: filteredResources.length,
    roomsCount: rooms.length,
    venuesCount: venues.length,
    isLoading,
    isError,
    availableMethods: [
      'findSpace', 'findVenue', 'getVenueColor', 'getSpaceColor', 
      'getSpacesForVenue', 'findRoom (alias)', 'getRoomColor (alias)', 
      'getRoomsForVenue (alias)'
    ]
  })
  console.groupEnd()

  return returnValue
}
