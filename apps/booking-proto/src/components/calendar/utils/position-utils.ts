/**
 * Utility functions for calculating positions of calendar-related UI elements
 */

/**
 * Calculates the optimal position for a popover relative to an element
 * Ensures the popover stays within the viewport boundaries
 * 
 * @param el - The HTML element that the popover should be positioned relative to
 * @param popoverWidth - The width of the popover in pixels
 * @param popoverHeight - The height of the popover in pixels
 * @returns An object with left and top coordinates for the popover
 */
export const calculatePopoverPosition = (el: HTMLElement, popoverWidth: number, popoverHeight: number) => {
  // Get the element's position and dimensions
  const rect = el.getBoundingClientRect();
  
  // Get viewport dimensions
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  
  // Calculate the default position (to the right of the element)
  let left = rect.right + 10;
  let top = rect.top;
  
  // Check if the popover would go off the right edge of the viewport
  if (left + popoverWidth > viewportWidth - 20) {
    // If it would go off the right, position it to the left of the element
    left = rect.left - popoverWidth - 10;
    
    // If it would also go off the left edge, center it over the element
    if (left < 20) {
      left = Math.max(20, rect.left + rect.width / 2 - popoverWidth / 2);
    }
  }
  
  // Check if the popover would go off the bottom edge of the viewport
  if (top + popoverHeight > viewportHeight - 20) {
    // Adjust vertical position to stay within viewport
    top = Math.max(20, viewportHeight - popoverHeight - 20);
  }
  
  // Check if the popover would go off the top edge
  if (top < 20) {
    top = 20;
  }
  
  // Apply any CSS transform the element might have
  const transform = window.getComputedStyle(el).transform;
  if (transform && transform !== 'none') {
    // This helps maintain position when the calendar has transforms
    // due to animations or other effects
    console.log('Event has transform:', transform);
  }
  
  return { left, top };
};

/**
 * Adjusts coordinates when dragging to ensure proper positioning
 * relative to scrolled content
 * 
 * @param x - The x-coordinate
 * @param y - The y-coordinate
 * @param container - The container element
 * @returns Adjusted coordinates accounting for scrolling
 */
export const adjustForScroll = (x: number, y: number, container: HTMLElement) => {
  return {
    x: x + (container.scrollLeft || 0),
    y: y + (container.scrollTop || 0)
  };
};

/**
 * Calculates grid position based on pixel coordinates
 * Useful for snapping to grid in calendar views
 * 
 * @param x - The x pixel coordinate
 * @param y - The y pixel coordinate
 * @param cellWidth - The width of a grid cell
 * @param cellHeight - The height of a grid cell
 * @returns Grid cell coordinates as row and column
 */
export const calculateGridPosition = (
  x: number, 
  y: number, 
  cellWidth: number, 
  cellHeight: number
) => {
  const col = Math.floor(x / cellWidth);
  const row = Math.floor(y / cellHeight);
  
  return { row, col };
}; 