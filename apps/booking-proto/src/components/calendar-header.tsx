"use client"

import { useState, useEffect } from "react"
import { useCalendar } from "src/context/calendar-context"
import { MONTHS } from "src/utils/date-utils"
import { Button } from "src/components/ui/button"
import {
  CalendarIcon,
  ChevronLeft,
  ChevronRight,
  Moon,
  Plus,
  Settings,
  Sun,
  ShoppingCart,
  Maximize,
  Minimize,
  Cloud, // Import the Cloud icon
} from "lucide-react"
import { ViewSelector } from "./view-selector"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "src/components/ui/dropdown-menu"
import { AppearanceSettings } from "./appearance-settings"
import { ViewOptions } from "./view-options"
import { useBookingCart } from "src/store/booking-cart"
import { useBookingSync } from "src/store/booking-sync" // Import the new sync store hook
import { Badge } from "src/components/ui/badge"

interface CalendarHeaderProps {
  toggleTheme?: () => void
  currentTheme?: "light" | "dark"
}

export function CalendarHeader({ toggleTheme, currentTheme = "light" }: CalendarHeaderProps) {
  const { currentDate, nextPeriod, prevPeriod, goToToday, view } = useCalendar()
  const { items: cartItems, toggleCart } = useBookingCart()
  const { items: syncItems } = useBookingSync() // Use the new sync store
  const [appearanceOpen, setAppearanceOpen] = useState(false)
  const [viewOptionsOpen, setViewOptionsOpen] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)

  const formatHeaderDate = () => {
    // Ensure currentDate is a valid Date object
    if (!(currentDate instanceof Date) || isNaN(currentDate.getTime())) {
      console.error("Invalid date object in formatHeaderDate", currentDate)
      return "Invalid date"
    }

    const month = MONTHS[currentDate.getMonth()]
    const year = currentDate.getFullYear()

    if (view === "month") {
      return `${month} ${year}`
    } else if (view === "week" || view === "timeline") {
      // For week view, show the month and year of the first day of the week
      const firstDay = new Date(currentDate)
      firstDay.setDate(currentDate.getDate() - currentDate.getDay())
      const lastDay = new Date(firstDay)
      lastDay.setDate(firstDay.getDate() + 6)

      const firstMonth = MONTHS[firstDay.getMonth()]
      const lastMonth = MONTHS[lastDay.getMonth()]
      const firstYear = firstDay.getFullYear()
      const lastYear = lastDay.getFullYear()

      if (firstMonth === lastMonth && firstYear === lastYear) {
        return `${firstMonth} ${firstYear}`
      } else if (firstYear === lastYear) {
        return `${firstMonth} - ${lastMonth} ${firstYear}`
      } else {
        return `${firstMonth} ${firstYear} - ${lastMonth} ${lastYear}`
      }
    } else {
      // Day view
      return `${month} ${currentDate.getDate()}, ${year}`
    }
  }

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement
        .requestFullscreen()
        .then(() => {
          setIsFullscreen(true)
        })
        .catch((err) => {
          console.error(`Error attempting to enable fullscreen: ${err.message}`)
        })
    } else {
      if (document.exitFullscreen) {
        document
          .exitFullscreen()
          .then(() => {
            setIsFullscreen(false)
          })
          .catch((err) => {
            console.error(`Error attempting to exit fullscreen: ${err.message}`)
          })
      }
    }
  }

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener("fullscreenchange", handleFullscreenChange)
    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange)
    }
  }, [])

  // Placeholder function for handling the cloud sync action
  const handleCloudSync = () => {
    // This will eventually call the /api/bookings/sync endpoint
    // with the items from the useBookingSync store.
    console.log("Syncing pending changes:", syncItems)
    // alert(`Syncing ${syncItems.length} changes.`)
  }

  return (
    <header className="flex h-16 shrink-0 items-center border-b bg-background px-4 text-foreground md:px-6">
      <div className="mr-4 flex items-center gap-2 text-lg font-medium sm:text-base">
        <CalendarIcon className="h-6 w-6 text-primary" />
        <span>Calendar</span>
      </div>

      <div className="ml-auto flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={goToToday} className="rounded-full px-4 hover:bg-muted">
          Today
        </Button>

        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={prevPeriod} className="rounded-full">
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={nextPeriod} className="rounded-full">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="mx-4 min-w-[180px] text-center text-lg font-medium">{formatHeaderDate()}</div>

        <ViewSelector />

        <Button size="sm" className="ml-4 gap-1 rounded-full bg-primary text-primary-foreground hover:bg-primary/90">
          <Plus className="h-4 w-4" />
          Create
        </Button>

        {/* Settings Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="ml-2 rounded-full" aria-label="Calendar settings">
              <Settings className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>Calendar Settings</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem
                onSelect={(e) => {
                  e.preventDefault()
                  setAppearanceOpen(true)
                }}
              >
                Appearance
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={(e) => {
                  e.preventDefault()
                  setViewOptionsOpen(true)
                }}
              >
                View options
              </DropdownMenuItem>
              <DropdownMenuItem>Working hours</DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem>Print</DropdownMenuItem>
              <DropdownMenuItem>Export calendar</DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onSelect={(e) => {
                e.preventDefault()
                toggleFullscreen()
              }}
            >
              {isFullscreen ? (
                <>
                  <Minimize className="mr-2 h-4 w-4" />
                  Exit Fullscreen
                </>
              ) : (
                <>
                  <Maximize className="mr-2 h-4 w-4" />
                  Enter Fullscreen
                </>
              )}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Cloud Sync Button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={handleCloudSync}
          className="relative ml-2 rounded-full"
          aria-label="Sync pending changes"
        >
          <Cloud className="h-5 w-5" />
          {syncItems.length > 0 && (
            <Badge className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center bg-accent p-0 text-accent-foreground">
              {syncItems.length}
            </Badge>
          )}
        </Button>

        {/* Cart toggle button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleCart}
          className="relative ml-2 rounded-full"
          aria-label="Toggle booking cart"
        >
          <ShoppingCart className="h-5 w-5" />
          {cartItems.length > 0 && (
            <Badge className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center p-0">
              {cartItems.length}
            </Badge>
          )}
        </Button>

        <Button
          variant="ghost"
          size="icon"
          onClick={toggleTheme}
          className="ml-2 rounded-full"
          aria-label={currentTheme === "light" ? "Switch to dark mode" : "Switch to light mode"}
        >
          {currentTheme === "light" ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />}
        </Button>

        {/* Appearance Settings Dialog */}
        <AppearanceSettings open={appearanceOpen} onOpenChange={setAppearanceOpen} />

        {/* View Options Dialog */}
        <ViewOptions open={viewOptionsOpen} onOpenChange={setViewOptionsOpen} />
      </div>
    </header>
  )
}
