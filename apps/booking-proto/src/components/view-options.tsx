"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "src/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "src/components/ui/dialog"
import { Label } from "src/components/ui/label"
import { Switch } from "src/components/ui/switch"
import { Checkbox } from "src/components/ui/checkbox"
import { useCalendar } from "src/context/calendar-context"
import { useViewPreferences } from "src/store/view-preferences"

interface ViewOptionsProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ViewOptions({ open, onOpenChange }: ViewOptionsProps) {
  const { view } = useCalendar()
  const viewPreferences = useViewPreferences()

  // These would be connected to actual state in a real implementation
  const [showWeekends, setShowWeekends] = useState(true)
  const [showDeclinedEvents, setShowDeclinedEvents] = useState(false)
  const [showCompleted, setShowCompleted] = useState(true)
  const [showWeekNumbers, setShowWeekNumbers] = useState(false)

  const handleSave = () => {
    // Apply the current view preference to the calendar context
    // Other settings would be saved here too
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>View Options</DialogTitle>
          <DialogDescription>Customize what you see in your calendar.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="show-weekends" className="flex-1">
              Show weekends
            </Label>
            <Switch id="show-weekends" checked={showWeekends} onCheckedChange={setShowWeekends} />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="show-declined" className="flex-1">
              Show declined events
            </Label>
            <Switch id="show-declined" checked={showDeclinedEvents} onCheckedChange={setShowDeclinedEvents} />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="show-completed" className="flex-1">
              Show completed events
            </Label>
            <Switch id="show-completed" checked={showCompleted} onCheckedChange={setShowCompleted} />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="show-week-numbers" className="flex-1">
              Show week numbers
            </Label>
            <Switch id="show-week-numbers" checked={showWeekNumbers} onCheckedChange={setShowWeekNumbers} />
          </div>

          <div className="mt-2">
            <Label className="mb-2 block">Default view</Label>
            <div className="ml-2 space-y-2">
              {[
                { id: "view-day", label: "Day", value: "day" },
                { id: "view-week", label: "Week", value: "week" },
                { id: "view-month", label: "Month", value: "month" },
                { id: "view-timeline", label: "Timeline", value: "timeline" },
              ].map((viewOption) => (
                <div key={viewOption.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={viewOption.id}
                    checked={viewPreferences.view === viewOption.value}
                    onCheckedChange={() => viewPreferences.setView(viewOption.value as any)}
                  />
                  <Label htmlFor={viewOption.id}>{viewOption.label}</Label>
                </div>
              ))}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
