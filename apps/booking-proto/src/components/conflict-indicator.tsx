"use client"

import type { Booking } from "src/types/calendar"
import { AlertCircle } from "lucide-react"

interface ConflictIndicatorProps {
  isVisible: boolean
  conflicts: Booking[]
  position: { x: number; y: number }
}

export function ConflictIndicator({ isVisible, conflicts, position }: ConflictIndicatorProps) {
  if (!isVisible || conflicts.length === 0) return null

  return (
    <div
      className="fixed z-50 rounded-lg bg-destructive p-3 text-destructive-foreground shadow-lg"
      style={{
        left: `${position.x + 20}px`,
        top: `${position.y - 40}px`,
        pointerEvents: "none",
        maxWidth: "250px",
        fontSize: "13px",
        fontWeight: "500",
      }}
    >
      <div className="flex items-center gap-2">
        <AlertCircle className="h-5 w-5 flex-shrink-0" />
        <span>
          {conflicts.length === 1 ? "Conflicts with 1 booking" : `Conflicts with ${conflicts.length} bookings`}
        </span>
      </div>
    </div>
  )
}
