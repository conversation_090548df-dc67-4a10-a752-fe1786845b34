"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "src/components/ui/button"
import { useBookingForm } from "src/hooks/use-booking-form"
import { useBookingCart } from "src/store/booking-cart"
import { useCalendar } from "src/context/calendar-context"
import { BookingFormFields } from "./booking-form-fields"
import { ShoppingCart, Clock, AlertCircle, Trash2 } from "lucide-react"
import { Badge } from "src/components/ui/badge"
import { format } from "date-fns"
import type { Booking, GhostBooking } from "src/context/calendar-context"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "src/components/ui/dialog"
import type { BookingStatus } from "src/types/calendar"

interface CartBookingModalProps {
  isOpen: boolean
  onClose: () => void
  booking: GhostBooking
}

export function CartBookingModal({ isOpen, onClose, booking }: CartBookingModalProps) {
  const { items, updateItem, removeItem } = useBookingCart()
  const { rooms } = useCalendar()

  // Extract cart item ID from ghost booking ID
  const [cartItemId, setCartItemId] = useState<string | null>(null)
  const [cartItemAddedAt, setCartItemAddedAt] = useState<Date | null>(null)

  // Add state for status-related fields
  const [status, setStatus] = useState<BookingStatus>("Tentative")
  const [holdRank, setHoldRank] = useState<number>(1)
  const [lostReason, setLostReason] = useState<string>("")

  // Initialize cart-specific state
  useEffect(() => {
    if (booking && booking.id.startsWith("ghost-cart-")) {
      const itemId = booking.id.replace("ghost-cart-", "")
      setCartItemId(itemId)

      // Find the cart item to get its addedAt date and status data
      const cartItem = items.find((item) => item.id === itemId)
      if (cartItem) {
        setCartItemAddedAt(cartItem.addedAt)

        // Initialize status fields from the cart item
        if (cartItem.booking.status) {
          setStatus(cartItem.booking.status as BookingStatus)
        }

        if (cartItem.booking.holdRank) {
          setHoldRank(cartItem.booking.holdRank)
        }

        if (cartItem.booking.lostReason) {
          setLostReason(cartItem.booking.lostReason)
        }
      }
    }
  }, [booking, items])

  const {
    title,
    setTitle,
    description,
    setDescription,
    venueId,
    roomId,
    setRoomId,
    start,
    end,
    isAllDay,
    setIsAllDay,
    venueRooms,
    venues,
    handleVenueChange,
    handleDateChange,
    handleTimeChange,
    handleCancel,
    validateForm,
  } = useBookingForm({
    isOpen,
    onClose,
    booking,
    createGhostByDefault: true,
  })

  // Handle removing item from cart
  const handleRemoveFromCart = () => {
    if (cartItemId && window.confirm("Are you sure you want to remove this item from your cart?")) {
      removeItem(cartItemId)
      onClose()
    }
  }

  // Handle form submission for cart items
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm() || !cartItemId) return

    const bookingData: Booking = {
      id: cartItemId,
      title,
      description,
      venueId,
      roomId,
      start,
      end,
      isAllDay,
      // Add status fields
      status,
      holdRank: status === "Hold" ? holdRank : undefined,
      lostReason: status === "Lost" ? lostReason : undefined,
    }

    const room = rooms.find((r) => r.id === roomId)
    if (room) {
      const cartItem = items.find((item) => item.id === cartItemId)
      if (cartItem) {
        updateItem(cartItemId, bookingData)
      }
    }

    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleCancel()}>
      <DialogContent className="w-full max-w-md overflow-hidden p-0">
        <DialogHeader className="px-6 pb-4 pt-6">
          <DialogTitle>Edit Cart Item</DialogTitle>
          <div>
            <div className="mt-1 flex items-center gap-2 text-sm text-muted-foreground">
              <ShoppingCart className="h-4 w-4" />
              <span>Editing an item in your booking cart</span>
            </div>
          </div>

          {cartItemAddedAt && (
            <div className="mt-2 pt-2">
              <Badge variant="outline" className="gap-1 text-xs font-normal">
                <Clock className="h-3 w-3" />
                Added to cart: {format(cartItemAddedAt, "MMM d, yyyy 'at' h:mm a")}
              </Badge>
            </div>
          )}
        </DialogHeader>

        <div className="mx-6 flex items-start gap-2 rounded-md bg-blue-50 p-4 dark:bg-blue-950">
          <AlertCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-blue-500 dark:text-blue-400" />
          <div className="text-sm text-blue-700 dark:text-blue-300">
            <p className="font-medium">Cart Item</p>
            <p>Changes will be reflected in your booking cart but won't be confirmed until you complete checkout.</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="px-6">
          <BookingFormFields
            title={title}
            setTitle={setTitle}
            description={description}
            setDescription={setDescription}
            venueId={venueId}
            handleVenueChange={handleVenueChange}
            roomId={roomId}
            setRoomId={setRoomId}
            start={start}
            end={end}
            handleDateChange={handleDateChange}
            handleTimeChange={handleTimeChange}
            isAllDay={isAllDay}
            setIsAllDay={setIsAllDay}
            // @ts-ignore
            createGhost={true}
            handleGhostToggle={() => {}}
            venues={venues}
            venueRooms={venueRooms}
            showGhostToggle={false}
            // Add status-related props
            status={status}
            setStatus={setStatus}
            holdRank={holdRank}
            setHoldRank={setHoldRank}
            lostReason={lostReason}
            setLostReason={setLostReason}
            showStatusSelector={true}
          />

          <div className="mt-6 flex justify-between pb-3">
            <Button type="button" variant="destructive" onClick={handleRemoveFromCart} className="sm:w-auto">
              <Trash2 className="h-4 w-4" />
              Remove
            </Button>
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={handleCancel} className="w-full sm:w-auto">
                Cancel
              </Button>

              <Button
                type="submit"
                disabled={!roomId || venueRooms.length === 0}
                className="w-full bg-blue-600 hover:bg-blue-700 sm:w-auto"
              >
                Update Item
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
