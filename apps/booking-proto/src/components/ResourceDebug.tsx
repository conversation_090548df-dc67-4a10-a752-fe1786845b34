"use client"

import { useBookingResources } from "src/hooks/useBookingResources"

export function ResourceDebug() {
  const { resources, isLoading, isError, error } = useBookingResources()

  if (isLoading) {
    return <div className="p-4">Loading resources...</div>
  }

  if (isError) {
    return (
      <div className="p-4 text-red-500">
        Error loading resources: {error?.message || "Unknown error"}
      </div>
    )
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Resources Debug</h2>
      <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-96">
        {JSON.stringify(resources, null, 2)}
      </pre>
      <div className="mt-4">
        <h3 className="font-semibold">Summary:</h3>
        <p>Total Venues: {resources.length}</p>
        <p>
          Total Spaces:{" "}
          {resources.reduce((acc, venue) => acc + (venue.children?.length || 0), 0)}
        </p>
      </div>
    </div>
  )
} 