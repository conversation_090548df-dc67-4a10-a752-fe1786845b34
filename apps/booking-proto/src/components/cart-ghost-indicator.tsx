"use client"

import { useState, useEffect } from "react"
import { useBookingCart } from "src/store/booking-cart"
import { useCalendar } from "src/context/calendar-context"
import { Badge } from "src/components/ui/badge"
import { ShoppingCart } from "lucide-react"

export function CartGhostIndicator() {
  const { items } = useBookingCart()
  const { ghostBookings } = useCalendar()
  const [isRestoring, setIsRestoring] = useState(false)
  const [count, setCount] = useState(0)

  useEffect(() => {
    // Check if we're restoring ghost events from cart
    const cartGhostIds = ghostBookings.filter((ghost) => ghost.id.startsWith("ghost-cart-")).map((ghost) => ghost.id)

    const cartItemIds = items.map((item) => `ghost-cart-${item.id}`)

    // Count how many cart items have corresponding ghost events
    const matchCount = cartItemIds.filter((id) => cartGhostIds.includes(id)).length

    // If we have cart items but not all have ghost events yet, we're restoring
    if (items.length > 0 && matchCount < items.length) {
      setIsRestoring(true)
      setCount(matchCount)
    } else if (items.length > 0 && matchCount === items.length) {
      // All cart items have ghost events, show success briefly then hide
      setIsRestoring(false)
      setCount(matchCount)

      // Show success indicator briefly
      const timer = setTimeout(() => {
        setCount(0)
      }, 3000)

      return () => clearTimeout(timer)
    } else {
      setIsRestoring(false)
      setCount(0)
    }
  }, [items, ghostBookings])

  if (count === 0) return null

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <Badge variant={isRestoring ? "outline" : "secondary"} className="flex items-center gap-1 px-3 py-1">
        <ShoppingCart className="h-3.5 w-3.5" />
        {isRestoring ? (
          <span>
            Restoring cart items ({count}/{items.length})...
          </span>
        ) : (
          <span>Cart items restored ({count})</span>
        )}
      </Badge>
    </div>
  )
}
