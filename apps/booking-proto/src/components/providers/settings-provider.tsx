"use client"

import { useEffect } from "react"
import { useBookingSettings } from "src/hooks/use-booking-settings"

export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const { isLoading, error, data } = useBookingSettings()

  useEffect(() => {
    if (data) {
      console.log("Booking settings loaded:", {
        events: data.event?.list?.length || 0,
        statuses: data.status?.list?.length || 0,
        rates: data.pricing?.rates?.length || 0,
        rateTypes: data.rateTypes?.length || 0,
      })
    }
  }, [data])

  // You could show a loading state here if needed
  // For now, we'll just render children immediately
  return <>{children}</>
} 