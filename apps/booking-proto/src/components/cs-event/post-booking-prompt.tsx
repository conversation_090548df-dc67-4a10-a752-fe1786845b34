"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "src/components/ui/dialog"
import { Button } from "src/components/ui/button"
import { CalendarPlus, ArrowRight, Building, BookOpen } from "lucide-react"
import { useRouter } from "next/navigation"
import { useToast } from "src/components/ui/use-toast"
import { type CartItem } from "src/store/booking-cart"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "src/components/ui/card"
import { Badge } from "src/components/ui/badge"

interface PostBookingPromptProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  bookings: CartItem[]
  onClose: () => void
}

export function PostBookingPrompt({ open, onOpenChange, bookings, onClose }: PostBookingPromptProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState<string | null>(null)

  // This function would navigate to the CS Event creation page
  const handleCreateNewEvent = () => {
    setLoading("create")
    // Simulate navigation delay
    setTimeout(() => {
      toast({
        title: "Navigating to event creation",
        description: "Opening event creation with pre-filled booking details.",
      })
      // In a real implementation, this would navigate to the event creation page
      // router.push("/cs-events/new?bookingIds=" + bookings.map(b => b.id).join(","))
      onClose()
    }, 800)
  }

  // This function would navigate to the event assignment page
  const handleAssignToEvent = () => {
    setLoading("assign")
    // Simulate navigation delay
    setTimeout(() => {
      toast({
        title: "Navigating to event assignment",
        description: "Opening event selection for your bookings.",
      })
      // In a real implementation, this would navigate to the event assignment page
      // router.push("/cs-events/assign?bookingIds=" + bookings.map(b => b.id).join(","))
      onClose()
    }, 800)
  }

  // This function would mark bookings as not needing event assignment
  const handleSkip = () => {
    toast({
      title: "Bookings confirmed",
      description: "Your bookings have been confirmed without event assignment.",
    })
    onClose()
  }

  const totalBookings = bookings.length

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-xl">Complete Your Booking Process</DialogTitle>
          <DialogDescription>
            You've successfully booked {totalBookings} {totalBookings === 1 ? "item" : "items"}. Would you like to
            associate these bookings with a CS Event?
          </DialogDescription>
        </DialogHeader>

        <div className="my-4 grid grid-cols-1 gap-4 md:grid-cols-2">
          <Card className="border-indigo-100 bg-gradient-to-br from-indigo-50 to-blue-50 transition-shadow hover:shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-base text-indigo-700">
                <Building className="mr-2 h-4 w-4" />
                Assign to Existing Event
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-2 text-sm text-gray-600">
              Choose from your existing CS Events to assign these bookings to.
            </CardContent>
            <CardFooter className="pt-0">
              <Button
                variant="outline"
                className="w-full border-indigo-200 hover:bg-indigo-100 hover:text-indigo-700"
                onClick={handleAssignToEvent}
                disabled={loading !== null}
              >
                {loading === "assign" ? (
                  <span className="flex items-center">
                    <svg
                      className="-ml-1 mr-2 h-4 w-4 animate-spin text-indigo-600"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Loading...
                  </span>
                ) : (
                  <span className="flex items-center">
                    Select Event <ArrowRight className="ml-2 h-4 w-4" />
                  </span>
                )}
              </Button>
            </CardFooter>
          </Card>

          <Card className="border-purple-100 bg-gradient-to-br from-purple-50 to-fuchsia-50 transition-shadow hover:shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-base text-purple-700">
                <CalendarPlus className="mr-2 h-4 w-4" />
                Create New Event
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-2 text-sm text-gray-600">
              Create a new CS Event and automatically associate these bookings with it.
            </CardContent>
            <CardFooter className="pt-0">
              <Button
                className="w-full bg-gradient-to-r from-purple-500 to-fuchsia-500 text-white hover:from-purple-600 hover:to-fuchsia-600"
                onClick={handleCreateNewEvent}
                disabled={loading !== null}
              >
                {loading === "create" ? (
                  <span className="flex items-center">
                    <svg
                      className="-ml-1 mr-2 h-4 w-4 animate-spin text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Creating...
                  </span>
                ) : (
                  <span className="flex items-center">
                    Create Event <ArrowRight className="ml-2 h-4 w-4" />
                  </span>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>

        <div className="my-2 flex items-center justify-center">
          <div className="relative flex items-center">
            <div className="h-px w-16 bg-gray-200"></div>
            <Badge variant="outline" className="mx-2 border-gray-200 px-2 py-1 text-xs font-normal text-gray-500">
              OR
            </Badge>
            <div className="h-px w-16 bg-gray-200"></div>
          </div>
        </div>

        <DialogFooter className="mt-2 sm:justify-center">
          <Button variant="ghost" className="text-gray-500" onClick={handleSkip}>
            <BookOpen className="mr-2 h-4 w-4" />
            Skip for now
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
