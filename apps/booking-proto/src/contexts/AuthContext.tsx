"use client"

import React, { createContext, useCallback, useEffect, useMemo, useReducer } from "react"
import Cookies from "js-cookie"
import { signOut, useSession } from "next-auth/react"

// Define the session status type to match NextAuth.js
type SessionStatus = "authenticated" | "unauthenticated" | "loading"

// Auth provider
let ActionType
;(function (ActionType: any) {
  ActionType.AUTH_STATE_CHANGED = "AUTH_STATE_CHANGED"
})(ActionType || (ActionType = {}))

const initialState = {
  isAuthenticated: false,
  isInitialized: false,
  user: null,
  loading: true,
}

const reducer = (state: any, action: any) => {
  if (action.type === "AUTH_STATE_CHANGED") {
    const { isAuthenticated, user } = action.payload

    console.log("Reducer used auth changed", action, state)

    return {
      ...state,
      isAuthenticated,
      isInitialized: true,
      user,
    }
  }
  return state
}

export const AuthContext = createContext({
  ...initialState,
})

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const { data: session, status } = useSession()
  const [state, dispatch] = useReducer(reducer, initialState)

  const initAuth = useCallback(async () => {
    console.log("Init auth called", session?.user, status)
    if (status === "authenticated") {
      let userData = {
        ...initialState,
        user: session?.user,
        isAuthenticated: status === "authenticated",
        loading: false,
      }

      if (userData.user && status === "authenticated") {
        dispatch({
          type: ActionType.AUTH_STATE_CHANGED,
          payload: userData,
        })
      } else {
        dispatch({
          type: ActionType.AUTH_STATE_CHANGED,
          payload: userData,
        })
      }
    } else if (status === "unauthenticated") {
      dispatch({
        type: ActionType.AUTH_STATE_CHANGED,
        payload: {
          ...initialState,
          isAuthenticated: false,
          loading: false,
        },
      })
    }
  }, [session, status])

  useEffect(() => {
    initAuth()
  }, [initAuth])

  const logout = useCallback(async () => {
    try {
      await signOut({ callbackUrl: "/booking/auth/signin" })
    } catch (error) {
      console.error("Logout error:", error)
    }
  }, [])

  const authState = useMemo(() => {
    if (status !== "loading" && state.isAuthenticated) {
      // Store cookie for server side rendering
      const maxAge = 100 * 365 * 24 * 60 * 60 // 100 years, never expires
      Cookies.set("contact", session?.user?.contact, {
        path: "/booking",
        expires: maxAge,
        sameSite: "lax",
        secure: true,
      })

      Cookies.set("customer", session?.user?.id as string, {
        path: "/booking",
        expires: maxAge,
        sameSite: "lax",
        secure: true,
      })
    }

    return {
      ...state,
      loading: status === "loading",
      isAuthenticated: status === "authenticated",
      logout,
    }
  }, [status, state, session, logout])

  return <AuthContext.Provider value={authState}>{children}</AuthContext.Provider>
}

export const AuthConsumer = AuthContext.Consumer
export const useAuth = () => React.useContext(AuthContext)
