# Booking Calendar Application

This project is a comprehensive scheduling and resource management tool designed for venue operations. It provides a modern, interactive interface for managing bookings across multiple venues and rooms, inspired by the functionality of applications like Google Calendar.

## Core Technologies

The application is built with a modern, robust tech stack:

-   **Framework**: [Next.js](https://nextjs.org/) (using the App Router)
-   **Language**: [TypeScript](https://www.typescriptlang.org/)
-   **Styling**: [Tailwind CSS](https://tailwindcss.com/) with [shadcn/ui](https://ui.shadcn.com/) for the component library.
-   **State Management**:
    -   **React Context API**: For global, calendar-specific state (`CalendarContext`).
    -   **Zustand**: For client-side state that requires persistence (e.g., booking cart, user preferences).
-   **Date/Time Handling**: [Temporal API Polyfill](https://github.com/js-temporal/temporal-polyfill) to leverage the modern, unambiguous `Temporal` object for all date and time calculations.
-   **Drag and Drop**: [`@dnd-kit`](https://dndkit.com/) for intuitive booking rescheduling.

## Project Structure

The codebase is organized for clarity and maintainability. Below is a more detailed structure of the `apps/booking-proto` directory:

```
apps/booking-proto/
├── app/                  # Next.js App Router pages and layouts
│   ├── layout.tsx        # Main layout
│   ├── page.tsx          # Main entry page
│   └── ...               # Other route files and folders
├── components/           # Reusable React components (UI elements, modals, etc.)
│   ├── CalendarHeader.tsx
│   ├── Sidebar.tsx
│   └── ...
├── context/              # React Context providers (e.g., CalendarContext)
│   └── CalendarContext.tsx
├── data/                 # Mock data for development (bookings, venues)
│   └── mock-data.ts
├── hooks/                # Custom React hooks for shared logic
│   ├── useBookingForm.ts
│   ├── useCartGhostSync.ts
│   └── ...
├── lib/                  # Utility libraries (date, API helpers, etc.)
│   └── utils.ts
├── store/                # Zustand stores for client-side state management
│   ├── booking-cart.ts   # Cart state (persisted)
│   ├── view-preferences.ts # User view preferences (persisted)
│   └── ...
├── types/                # TypeScript type definitions
│   ├── calendar.ts
│   └── ...
├── utils/                # Utility functions (date formatting, etc.)
│   ├── booking-utils.ts
│   ├── calendar-utils.ts
│   └── ...
├── styles/               # Global and component CSS
│   └── globals.css
├── public/               # Static assets (images, icons, etc.)
│   ├── placeholder-logo.png
│   └── ...
├── booking-calendar.tsx  # Main calendar component (entry point)
├── yarn.lock             # yarn package lock file
├── package.json          # Project metadata and scripts
├── README.md             # Project documentation (this file)
└── ...                   # Additional config, test, and doc files
```

- **app/**: Next.js App Router entry points and layouts.
- **components/**: All UI and modal components, including calendar, sidebar, and dialogs.
- **context/**: React Context providers for global state (e.g., calendar data).
- **data/**: Mock data for development and testing.
- **hooks/**: Custom React hooks for business logic and state.
- **lib/**: Utility libraries and helpers.
- **store/**: Zustand stores for persistent client-side state (cart, preferences, etc.).
- **types/**: TypeScript type definitions for strong typing across the app.
- **utils/**: Utility functions for date, calendar, and booking logic.
- **styles/**: CSS files for global and component-level styles.
- **public/**: Static assets served by Next.js.
- **booking-calendar.tsx**: Main calendar component (root of calendar logic).
- **yarn.lock**: Yarn's lockfile for dependency management.
- **package.json**: Project dependencies and scripts.
- **README.md**: Project documentation.

This structure is designed to keep concerns separated and make it easy to locate and maintain code as the project grows.

## Key Features

### 1. Calendar & Views
-   **Multiple Views**: Supports **Month** and **Week** views. Placeholders for **Day** and **Timeline** views are in place for future development.
-   **Dynamic Navigation**: Users can navigate to the next/previous period, or jump directly to "Today".
-   **Mini Calendar**: A sidebar calendar for quick date navigation.

### 2. Resource Management
-   **Collapsible Sidebar**: A filterable list of venues and their associated rooms.
-   **Hierarchical Selection**: Users can select/deselect entire venues or individual rooms to filter the calendar display.
-   **Search**: Instantly filter venues and rooms by name.

### 3. Booking Management
-   **Create Bookings**: Click on a time slot in the calendar to open a creation modal.
-   **Update Bookings**: Click on an existing event to edit its details.
-   **Ghost Previews**: When creating a new booking, a semi-transparent "ghost" event appears on the calendar, updating in real-time as the user fills out the form. This provides immediate visual feedback.
-   **Drag & Drop**: Easily reschedule events by dragging them to a new time or day within the Week view.

### 4. Booking Cart
-   **Add to Cart**: Instead of confirming a booking immediately, users can add it to a booking cart.
-   **Visual Representation**: Cart items are displayed as distinct "ghost" events on the calendar, allowing users to visualize their potential schedule.
-   **Session Persistence**: The cart's state is saved in `sessionStorage`, so items are not lost on page refresh.
-   **Bulk Confirmation**: A "Book Now" feature allows for confirming all items in the cart in a single action (currently simulated).

### 5. State Management Strategy
The application employs a hybrid state management approach:
-   **`CalendarContext`**: Serves as the primary source of truth for core calendar data, including all bookings, ghost events, and resource lists (venues, rooms). This is ideal for data that is central to the application's main function.
-   **Zustand Stores**:
    -   `useBookingCart`: Manages the state of the booking cart. Its state is persisted to `sessionStorage`, making it resilient to page reloads.
    -   `useViewPreferences`: Stores user preferences like the default calendar view, also persisted.
    This separation ensures that the core calendar logic is decoupled from more transient, client-specific state.

### 6. Date/Time with Temporal
To avoid common pitfalls and ambiguity associated with JavaScript's native `Date` object, this project uses the **Temporal API**. All core logic within the `CalendarContext` and utility functions operates on `Temporal.PlainDate` and `Temporal.PlainDateTime` objects. Conversion to and from the native `Date` object is only performed at the boundaries (e.g., when interfacing with a UI component like the mini-calendar that requires it). This ensures accuracy and reliability in all date-related calculations.

## How to Run

1.  **Install dependencies:**
    \`\`\`bash
    yarn install
    \`\`\`
2.  **Run the development server:**
    \`\`\`bash
    yarn dev
    \`\`\`
3.  Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.
\`\`\`

This `README.md` file provides a comprehensive overview of the project, its features, and the technical decisions made so far. It should serve as a great starting point for any developer joining the project.

## User Journeys

This section outlines the primary user flows for creating bookings within the application.

### 1. Direct Booking (Without Using the Cart)

This journey describes a user making a single, immediate booking.

1.  **Navigate & Filter**: The user first navigates to the desired date using the main header controls or the mini-calendar. They use the sidebar to select the specific venue and room, which filters the calendar to show only relevant schedules.
2.  **Initiate Booking**: The user clicks on an empty time slot in the calendar's week view.
3.  **Enter Details**: The "Create New Booking" modal opens, pre-filled with the selected date and time. As the user enters the booking title and other details, a semi-transparent "ghost" preview of the event appears on the calendar, updating in real-time.
4.  **Confirm**: The user clicks the "Confirm Booking" button.
5.  **Completion**: The modal closes, and the ghost preview is replaced by a solid, confirmed event on the calendar. The booking is now saved.

### 2. Checkout with Cart Mode

This journey describes a user planning and confirming multiple bookings in a single session.

1.  **Plan & Add First Item**: The user identifies a time slot and clicks on it. In the booking modal, after filling in the details, they click **"Add to Cart"**.
2.  **Cart Interaction**: The modal closes. A distinct "ghost" event representing the cart item appears on the calendar. Simultaneously, the Booking Cart sidebar slides open, displaying the newly added item.
3.  **Build the Cart**: The user repeats the process for all other desired time slots, potentially across different days or rooms. The calendar populates with multiple ghost events, and the cart list grows with each addition.
4.  **Review and Edit**: The user can open the cart at any time to review their selections. They can click an item to edit its details or remove it. Changes made to a cart item are reflected in its corresponding ghost event on the calendar.
5.  **Confirm All Bookings**: Once satisfied with all the items in the cart, the user clicks the **"Book Now"** button.
6.  **Completion**: All ghost events are converted into solid, confirmed bookings on the calendar. The cart is automatically cleared, and the user has successfully reserved multiple time slots in a single checkout process.
