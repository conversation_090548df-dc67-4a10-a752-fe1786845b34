import { createEnv } from "@t3-oss/env-nextjs"
import { vercel } from "@t3-oss/env-core/presets"
import { z } from "zod"

export const env = createEnv({
  /*
   * Serverside Environment variables, not available on the client.
   * Will throw if you access these variables on the client.
   */
  server: {
    ANALYZE: z
      .enum(["true", "false"])
      .optional()
      .transform((value) => value === "true"),
    NEXTAUTH_SECRET: z.string(),
    OAUTH_CLIENT_ID: z
      .string({
        required_error: "OAUTH_CLIENT_ID is required for NetSuite OAuth 2.0",
      })
      .min(1),
    OAUTH_CLIENT_SECRET: z
      .string({
        required_error: "OAUTH_CLIENT_SECRET is required for NetSuite OAuth 2.0",
      })
      .min(1),
    OAUTH1_ACCESS_TOKEN: z
      .string({
        required_error:
          "OAUTH1_ACCESS_TOKEN is required for NetSuite OAuth 1.0. Create a Access Token against the Restlet User",
      })
      .min(1),
    OAUTH1_TOKEN_SECRET: z.string({
      required_error:
        "OAUTH1_TOKEN_SECRET is required for NetSuite OAuth 1.0. Create a Access Token against the Restlet User",
    }),
    OAUTH1_CONSUMER_KEY: z
      .string({
        required_error:
          "OAUTH1_CONSUMER_KEY is required for NetSuite OAuth 1.0. This should be linked from the Shared Team Environment variables",
      })
      .min(1),
    OAUTH1_CONSUMER_SECRET: z
      .string({
        required_error:
          "OAUTH1_CONSUMER_SECRET is required for NetSuite OAuth 1.0. This should be linked from the Shared Team Environment variables",
      })
      .min(1),
    NEXTAUTH_URL: z.string().url("Invalid NEXTAUTH_URL").optional(),
  },
  /*
   * Environment variables available on the client (and server).
   *
   * 💡 You'll get type errors if these are not prefixed with NEXT_PUBLIC_.
   */
  client: {
    NEXT_PUBLIC_ACCOUNT_ID: z.string(),
    NEXT_PUBLIC_ACCOUNT_URL_ID: z.string(),
  },
  /*
   * Due to how Next.js bundles environment variables on Edge and Client,
   * we need to manually destructure them to make sure all are included in bundle.
   *
   * 💡 You'll get type errors if not all variables from `server` & `client` are included here.
   */
  runtimeEnv: {
    ANALYZE: process.env.ANALYZE,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    OAUTH_CLIENT_ID: process.env.OAUTH_CLIENT_ID,
    OAUTH_CLIENT_SECRET: process.env.OAUTH_CLIENT_SECRET,
    OAUTH1_ACCESS_TOKEN: process.env.OAUTH1_ACCESS_TOKEN,
    OAUTH1_TOKEN_SECRET: process.env.OAUTH1_TOKEN_SECRET,
    OAUTH1_CONSUMER_KEY: process.env.OAUTH1_CONSUMER_KEY,
    OAUTH1_CONSUMER_SECRET: process.env.OAUTH1_CONSUMER_SECRET,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    NEXT_PUBLIC_ACCOUNT_ID: process.env.NEXT_PUBLIC_ACCOUNT_ID,
    NEXT_PUBLIC_ACCOUNT_URL_ID: process.env.NEXT_PUBLIC_ACCOUNT_URL_ID,
  },
  skipValidation: true,
  extends: [vercel()],
})
