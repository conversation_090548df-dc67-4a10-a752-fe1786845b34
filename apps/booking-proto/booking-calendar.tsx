"use client"

import { useState, useEffect } from "react"
import { CalendarProvider } from "src/context/calendar-context"
import { CalendarHeader } from "src/components/calendar-header"
import { CalendarSidebar } from "src/components/calendar-sidebar"
import { Calendar } from "src/components/calendar"
import { CreateBookingModal } from "src/components/create-booking-modal"
import { UpdateBookingModal } from "src/components/update-booking-modal"
import { BookingCart } from "src/components/booking-cart"
import { CartGhostIndicator } from "src/components/cart-ghost-indicator"
import { useTheme } from "next-themes"
import { hydrateBookingCart, useBookingCart } from "src/store/booking-cart"
import { useCartGhostSync } from "src/hooks/use-cart-ghost-sync"
import type { Booking, GhostBooking } from "src/context/calendar-context"
import type { DateSelectArg, EventClickArg } from "@fullcalendar/core"

export default function BookingCalendar() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // Modal states
  const [isCreateModalOpen, setCreateModalOpen] = useState(false)
  const [isUpdateModalOpen, setUpdateModalOpen] = useState(false)
  const [selectedBooking, setSelectedBooking] = useState<Booking | GhostBooking | null>(null)
  const [selectedDates, setSelectedDates] = useState<{ start: Date; end: Date } | null>(null)
  const [selectedRoomId, setSelectedRoomId] = useState<string | null>(null)
  const [selectedVenueId, setSelectedVenueId] = useState<string | null>(null)

  // Handlers for calendar interactions
  const handleEventClick = (arg: EventClickArg) => {
    // For both real and ghost events, open the update modal
    const booking = {
      id: arg.event.id,
      ...arg.event.extendedProps,
      ...arg.event.toPlainObject(),
    }
    setSelectedBooking(booking as Booking | GhostBooking)
    setUpdateModalOpen(true)
  }

  const handleDateSelect = (arg: DateSelectArg) => {
    // For selecting a date range, open the create modal
    setSelectedDates({ start: arg.start, end: arg.end })
    setSelectedRoomId(arg.resource?.id || null)
    setSelectedVenueId(arg.resource?.extendedProps.venueId || null)
    setCreateModalOpen(true)
  }

  // useEffect only runs on the client, so now we can safely show the UI
  useEffect(() => {
    // Hydrate the cart from storage
    hydrateBookingCart()

    // Log the cart state after hydration
    setTimeout(() => {
      const cartState = useBookingCart.getState()
      console.log("Cart hydrated in main component with", cartState.items.length, "items")
    }, 100)

    setMounted(true)
  }, [])

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark")
  }

  // Avoid rendering with incorrect theme
  if (!mounted) {
    return null
  }

  return (
    <CalendarProvider>
      <CalendarInitializer />
      <div className="flex h-screen flex-col">
        <CalendarHeader toggleTheme={toggleTheme} currentTheme={theme === "dark" ? "dark" : "light"} />
        <div className="flex flex-1 overflow-hidden">
          <CalendarSidebar />
          <Calendar 
            onEventClick={handleEventClick}
            onDateSelect={handleDateSelect}
          />
          <BookingCart />
        </div>
        <CartGhostIndicator />
      </div>
      {/* Modals */}
      <CreateBookingModal
        isOpen={isCreateModalOpen}
        onClose={() => setCreateModalOpen(false)}
        startDate={selectedDates?.start}
        endDate={selectedDates?.end}
        initialRoomId={selectedRoomId}
        initialVenueId={selectedVenueId}
      />
      {selectedBooking && (
        <UpdateBookingModal
          isOpen={isUpdateModalOpen}
          onClose={() => {
            setUpdateModalOpen(false)
            setSelectedBooking(null)
          }}
          booking={selectedBooking}
        />
      )}
    </CalendarProvider>
  )
}

// This component handles initialization of ghost events from cart items
function CalendarInitializer() {
  const [initialized, setInitialized] = useState(false)
  const { items } = useBookingCart()

  // Use our custom hook to sync cart items with ghost bookings
  useCartGhostSync()

  // Log initialization status
  useEffect(() => {
    console.log("CalendarInitializer mounted, cart items:", items.length)

    // Mark as initialized after a delay to ensure cart is fully hydrated
    setTimeout(() => {
      setInitialized(true)
      console.log("Calendar initialization complete")
    }, 1000)
  }, [items.length])

  // Force re-render when items change to trigger ghost sync
  useEffect(() => {
    if (initialized && items.length > 0) {
      console.log("Cart items changed, triggering ghost sync:", items.length)
    }
  }, [initialized, items])

  // This is a utility component that doesn't render anything
  return null
}
