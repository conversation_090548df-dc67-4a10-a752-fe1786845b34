# Booking Calendar with NetSuite Integration

## Project Overview

This project aims to create a modern, responsive booking calendar application that integrates with NetSuite ERP. The calendar will enable event and venue management teams to schedule, track, and manage bookings with a visual grid-based interface similar to VenueOps but directly integrated with NetSuite's data model and business flows.

## Business Goals

- Create a visual calendar interface for managing space bookings and event statuses
- Eliminate manual data entry between booking calendar and NetSuite ERP
- Enable real-time conflict detection and resolution
- Provide detailed tracking of event statuses and booking workflows
- Support multi-room and multi-venue scheduling
- Streamline administrative processes for venue and event management staff

## Technical Scope

### Core Functionality

- Interactive calendar grid with different views (day, week, month, timeline)
- Drag-to-create and drag-to-reschedule bookings
- Support for booking statuses (Inquiry, Prospect, Tentative, Definite, Lost, Canceled, Hold)
- Ghost event flow (preview bookings before confirming)
- Conflict detection for overlapping bookings
- Integration with NetSuite for data synchronization
- Responsive design for different devices
- Cart system for batch booking

### Technical Stack

1. **Frontend**:
   - Next.js as the React framework
   - TailwindCSS for styling
   - Shadcn/ui for UI components
   - FullCalendar for calendar visualization
   - React Query for data fetching and caching

2. **Backend**:
   - Next.js API routes for server-side functionality
   - NetSuite RESTful APIs for data integration
   - OAuth 2.0 for authentication with NetSuite
   
3. **Data Flow**:
   - Two-way synchronization between calendar and NetSuite
   - Real-time updates when possible
   - Batch processing for high-volume operations

## NetSuite Integration Strategy

Based on the latest information about NetSuite's capabilities, we'll use the following approach for integration:

1. **Authentication**: 
   - Implement OAuth 2.0 for secure authentication with NetSuite
   - Use NextAuth.js to manage authentication flow

2. **API Access**:
   - Utilize NetSuite's REST API for most operations (newer but more modern approach)
   - Fall back to SOAP API for specific functionality not yet available in REST
   - Use SuiteQL for complex data queries when appropriate

3. **Data Synchronization**:
   - Map booking objects to NetSuite records
   - Implement change tracking to detect modifications
   - Handle conflict resolution when data is modified in both systems

4. **Error Handling**:
   - Robust error detection and reporting
   - Retry mechanisms for transient failures
   - Logging and monitoring for API interactions

## FullCalendar Implementation

FullCalendar will serve as the foundation for the calendar UI with these key features:

1. **Views**:
   - Day, Week, Month standard views
   - Timeline view for resource visualization (rooms across time)
   - List view for compact representation

2. **Interaction**:
   - Drag-and-drop for creating and rescheduling events
   - Resize to change event duration
   - Click for detailed information

3. **Resources**:
   - Configure rooms as resources
   - Group resources by venue
   - Custom rendering for resource cells

4. **Customization**:
   - Custom event rendering based on status
   - Tooltip integration for additional information
   - Theming aligned with application design

## User Roles and Permissions

The system will support the following roles, which will align with NetSuite permission models:

1. **Administrator**: Full access to all features and settings
2. **Manager**: Can create, edit, and delete all bookings
3. **Staff**: Can view all bookings and create booking requests
4. **Venue Manager**: Can manage bookings for specific venues
5. **Client**: Limited access to view own bookings and available slots

## Performance Considerations

1. **Data Loading**:
   - Implement pagination for large data sets
   - Use virtual scrolling for calendar views with many events
   - Cache common queries with React Query

2. **NetSuite Optimization**:
   - Batch API calls when possible
   - Use targeted queries to minimize data transfer
   - Implement rate limiting to avoid API throttling

3. **UI Performance**:
   - Optimize component rendering with memoization
   - Use skeleton loading states for perceived performance
   - Lazy load components not needed for initial render

## Security Considerations

1. **Authentication**:
   - Secure OAuth 2.0 implementation for NetSuite
   - JWT for session management
   - HTTPS for all communication

2. **Data Protection**:
   - Sanitize all user inputs
   - Encrypt sensitive data in transit and at rest
   - Implement proper CORS policies

3. **Access Control**:
   - Role-based access control aligned with NetSuite roles
   - Audit logging for sensitive operations

## Future Expansion Possibilities

1. **Mobile Application**: Native mobile experience beyond responsive web design
2. **Advanced Analytics**: Reporting and business intelligence dashboard
3. **Integration with Other Systems**: Payment processors, marketing tools, CRM
4. **AI-Powered Features**: Suggested booking times, demand forecasting
5. **Public Booking Portal**: Self-service booking for clients

## Success Metrics

1. **Adoption Rate**: Percentage of staff using the system vs. legacy methods
2. **Time Savings**: Reduction in administrative time for booking management
3. **Error Reduction**: Decrease in double-bookings and scheduling conflicts
4. **Data Accuracy**: Reduction in discrepancies between calendar and NetSuite
5. **User Satisfaction**: Feedback scores from staff using the system

## Development Approach

The project will follow an iterative development approach:

1. **Phase 1**: Core calendar functionality with mock data
2. **Phase 2**: NetSuite authentication and basic data synchronization
3. **Phase 3**: Complete booking management with conflict detection
4. **Phase 4**: Advanced features (cart, ghost events, status workflows)
5. **Phase 5**: Testing, optimization, and deployment

Each phase will include:
- Planning and requirement refinement
- Development and internal testing
- Stakeholder review
- Feedback incorporation

## Risks and Mitigations

| Risk | Probability | Impact | Mitigation |
|------|------------|--------|------------|
| NetSuite API changes | Medium | High | Design flexible adapter layer; monitor API announcements |
| Performance issues with large calendars | Medium | Medium | Implement pagination and virtual scrolling early |
| Complex booking rules | High | Medium | Create configurable rule engine; iterative implementation |
| User adoption resistance | Medium | High | Early stakeholder involvement; intuitive UI; proper training |
| Integration complexity | High | Medium | Proof-of-concept for critical paths; iterative approach |

## Timeline Estimate

- **Setup and Foundation**: 2 weeks
- **Core Calendar Implementation**: 3 weeks
- **NetSuite Integration**: 4 weeks
- **Advanced Features**: 3 weeks
- **Testing and Optimization**: 2 weeks
- **Deployment and Training**: 1 week

**Total Estimated Timeline**: 15 weeks (approximately 4 months)

# Booking Cart Sync Debug Plan

## Notes
- The booking cart in `apps/booking-proto` does not sync with the ghost event as expected.
- `apps/v0-booking-calendar` syncs the cart and ghost bookings correctly.
- Focus is on external pieces (hooks, context, etc.), not major changes in `FullCalendar.tsx`.
- Initial directory listings for both apps have been performed.
- Located and reviewed `use-cart-ghost-sync.ts` in both apps; this is the key hook for cart/ghost sync.
- The ongoing sync logic in `booking-proto`'s `use-cart-ghost-sync.ts` is broken/commented out, unlike in `v0-booking-calendar`.

## Task List
- [x] List directories of both `booking-proto` and `v0-booking-calendar` to understand structure
- [x] Identify key files and hooks involved in cart and ghost sync in both apps
- [x] Compare logic for updating cart on drag/ghost move between the two apps
- [x] Pinpoint differences and hypothesize the cause of the sync issue
- [x] Propose and implement minimal changes outside `FullCalendar.tsx` to fix sync
- [x] Test and verify cart sync after ghost event movement

## Current Goal
Pinpoint cause and propose minimal sync fix