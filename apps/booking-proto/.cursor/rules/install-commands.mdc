---
description: 
globs: 
alwaysApply: true
---
# Overview
This document specifies project rules restricting package management and script execution to `bun` only. All project operations must use `bun` commands instead of `npm`, `yarn`, or `pnpm`.

## Installation

```bash
# Install Bun globally
curl -fsSL https://bun.sh/install | bash

# Verify installation
bun --version
```

## Project Initialization

```bash
# Create a new project
bun create [template-name] [project-name]

# Initialize a new project in current directory
bun init
```

## Package Management

```bash
# Install dependencies
bun install

# Install a specific package
bun add [package-name]

# Install dev dependencies
bun add -d [package-name]

# Install global packages
bun add -g [package-name]

# Remove a package
bun remove [package-name]

# Update packages
bun update
```

## Running Scripts

```bash
# Run a JavaScript/TypeScript file
bun run [file.js|file.ts]

# Run a script defined in package.json
bun run [script-name]

# Run tests
bun test

# Run with watch mode
bun --watch [file.js|file.ts]
```

## Building Projects

```bash
# Build a project
bun build [entry-file] --outdir ./dist

# Bundle for production
bun build [entry-file] --minify --outdir ./dist
```

## Development Server

```bash
# Start a development server
bun --hot [server-file.js|server-file.ts]

# Run Bun's built-in dev server
bun serve [directory] --port 3000
```

## Environment Management

```bash
# Run with specific environment variables
bun run --env-file .env [file.js|file.ts]

# Run with node compatibility mode
bun --node [file.js]
```

## Performance Tips

- Use `bun install --frozen-lockfile` for CI/CD environments
- Prefer `bun run` over `npx` for better performance
- Use `bun:test` script type in package.json for native test running 