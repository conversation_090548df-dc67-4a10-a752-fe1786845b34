# Booking Calendar Resources Implementation

This document describes the implementation of fetching and displaying venue/space resources from NetSuite in the booking calendar.

## Overview

The booking calendar now fetches venue and space data from NetSuite's RESTlet API instead of using mock data. The implementation follows these key principles:

1. **SWR for Data Fetching**: Uses SWR (stale-while-revalidate) for efficient data fetching and caching
2. **Hierarchical Structure**: Venues are parent resources, Spaces are children
3. **FullCalendar Integration**: Resources are formatted for FullCalendar's resource timeline view

## Architecture

### Data Flow

```
NetSuite RESTlet → Next.js API Route → SWR Hook → Resource Management Hook → FullCalendar Component
```

### Key Files

1. **Backend RESTlet** (`backend/src/.../ng_cses_rl_booking_resources.js`)
   - Fetches venues and spaces from NetSuite
   - Returns hierarchical JSON structure
   - Implements 1-hour caching

2. **API Route** (`src/app/api/resources/route.ts`)
   - Proxies requests to NetSuite
   - Handles authentication (TODO)
   - Returns mock data for development

3. **<PERSON>WR Hook** (`src/hooks/useBookingResources.ts`)
   - Fetches data from API route
   - Implements client-side caching
   - Provides loading and error states

4. **Resource Management Hook** (`src/components/calendar/hooks/useResourceManagement.ts`)
   - Transforms NetSuite data to FullCalendar format
   - Provides backward compatibility
   - Manages resource selection state

## Data Structure

### NetSuite Response Format

```json
{
  "ok": true,
  "data": {
    "resources": [
      {
        "id": "1",
        "title": "Main Convention Center",
        "children": [
          {
            "id": "101",
            "title": "Hall A",
            "extendedProps": {
              "occupancy": "500",
              "bookingLegendColor": "#4285F4",
              "subSpaces": "A1, A2, A3",
              "isGroupSpace": false
            }
          }
        ]
      }
    ]
  }
}
```

### FullCalendar Resource Format

```typescript
interface ResourceItem {
  id: string
  title: string
  parentId?: string // Links spaces to venues
  eventColor?: string
  extendedProps?: {
    occupancy?: string
    bookingLegendColor?: string
    subSpaces?: string
    isGroupSpace?: boolean
    venueId?: string
    venueName?: string
  }
}
```

## Usage

### In Components

```typescript
import { useResourceManagement } from './hooks/useResourceManagement'

function MyComponent() {
  const { resources, isLoading, isError } = useResourceManagement()
  
  if (isLoading) return <div>Loading...</div>
  if (isError) return <div>Error loading resources</div>
  
  return <FullCalendar resources={resources} />
}
```

### Testing

Visit `/test-resources` to see the raw resource data being fetched.

## Migration from Mock Data

The system maintains backward compatibility by:

1. Providing `rooms` and `venues` arrays in the same format as before
2. Supporting the same selection/expansion state management
3. Keeping the same color and styling properties

## TODO

1. **Authentication**: Implement OAuth for NetSuite API calls
2. **Error Handling**: Add retry logic and better error messages
3. **Filtering**: Add venue-specific filtering support
4. **Real-time Updates**: Consider WebSocket for live updates
5. **Performance**: Implement virtual scrolling for large resource lists

## Environment Variables

```env
# Add to .env.local
NETSUITE_ACCOUNT_ID=tstdrv1516212
NETSUITE_CONSUMER_KEY=your_key
NETSUITE_CONSUMER_SECRET=your_secret
NETSUITE_TOKEN_ID=your_token
NETSUITE_TOKEN_SECRET=your_token_secret
``` 