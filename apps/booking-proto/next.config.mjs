import with<PERSON>undleAnalyzer from "@next/bundle-analyzer"

import { env } from "./env.mjs"

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  transpilePackages: ["@event-services/auth"],
  basePath: "/booking",
  devIndicators: {},
  publicRuntimeConfig: {
    // Available on both server and client
    theme: "DEFAULT",
    currency: "USD",
  },
  // Image rendering
  images: {
    domains: [
      "placeholder.com",
      "unsplash.com",
      "i.imgur.com",
      `${env.NEXT_PUBLIC_ACCOUNT_URL_ID?.toLowerCase()}.app.netsuite.com`,
    ],
    unoptimized: true,
  },
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  experimental: {
    instrumentationHook: true,
    nextScriptWorkers: true,
    manualClientBasePath: true,
    webpackBuildWorker: true,
    parallelServerBuildTraces: true,
    parallelServerCompiles: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  async rewrites() {
    return [
      {
        source: "/booking/api/auth/:path*",
        destination: "/api/auth/:path*",
      },
      {
        source: "/sw.js",
        destination: "/_next/static/sw.js",
      },
      { source: "/healthz", destination: "/api/health" },
      { source: "/api/healthz", destination: "/api/health" },
      { source: "/health", destination: "/api/health" },
      { source: "/ping", destination: "/api/health" },
    ]
  },
}

export default env.ANALYZE ? withBundleAnalyzer({ enabled: env.ANALYZE })(nextConfig) : nextConfig
