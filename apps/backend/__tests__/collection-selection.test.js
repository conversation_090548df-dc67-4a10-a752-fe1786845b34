const record = require('N/record');
const Record = require('N/record/instance');
const serverWidget = require('N/ui/serverWidget');
const Form = require('N/ui/serverWidget/form');
const Sublist = require('N/ui/serverWidget/sublist');

jest.mock('N/record');
jest.mock('N/record/instance');
jest.mock('N/ui/serverWidget');
jest.mock('N/ui/serverWidget/form');
jest.mock('N/ui/serverWidget/sublist');

describe('Collection Selection Tests', () => {
  let sublist;
  let mockData;
  let form;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create form and sublist
    form = Form;
    sublist = Sublist;
    
    // Mock sublist methods
    sublist.addField.mockReturnThis();
    sublist.addMarkAllButtons = jest.fn();
    sublist.setSublistValue = jest.fn();
    sublist.updateDisplayType = jest.fn();

    // Mock form to return our sublist
    form.addSublist.mockReturnValue(sublist);

    // Mock Record instance behavior
    Record.getValue.mockImplementation((params) => {
      const mockValues = {
        '1': {
          internalid: '1',
          parent: '',
          custrecord_ng_cs_itemcoll_display_name: 'Parent Collection'
        },
        '2': {
          internalid: '2',
          parent: '1',
          custrecord_ng_cs_itemcoll_display_name: 'Child Collection'
        }
      };
      
      const recordId = Record.id || '1';
      return mockValues[recordId][params.name];
    });

    // Set up mock data using Record instance
    mockData = ['1', '2'].map(id => {
      record.load.mockReturnValue(Record);
      Record.id = id; // Set ID for getValue implementation
      return Record;
    });
  });

  describe('populateSublist', () => {
    it('should set parent collections as selected by default', () => {
      const options = {
        checkboxes: true,
        columnMapping: {
          custcol_display_name: {
            type: serverWidget.FieldType.TEXT,
            field: 'custrecord_ng_cs_itemcoll_display_name',
            label: 'Display Name'
          }
        }
      };

      Record.id = '1'; // Set for parent record
      populateSublist({ sublist, data: [mockData[0]], options });

      expect(sublist.setSublistValue).toHaveBeenCalledWith({
        id: 'selected',
        line: 0,
        value: 'T'
      });
    });

    it('should set child collections as unselected by default', () => {
      const options = {
        checkboxes: true,
        columnMapping: {
          custcol_display_name: {
            type: serverWidget.FieldType.TEXT,
            field: 'custrecord_ng_cs_itemcoll_display_name',
            label: 'Display Name'
          }
        }
      };

      Record.id = '2'; // Set for child record
      populateSublist({ sublist, data: [mockData[1]], options });

      expect(sublist.setSublistValue).toHaveBeenCalledWith({
        id: 'selected',
        line: 0,
        value: 'F'
      });
    });

    it('should properly format display names with hierarchy', () => {
      const options = {
        columnMapping: {
          custcol_formatted_display: {
            type: serverWidget.FieldType.TEXT,
            field: 'custrecord_ng_cs_itemcoll_display_name',
            label: 'Collection Name',
            isHTML: true,
            formatter: (value, item) => {
              const level = item.getValue({ name: 'parent' }) ? 1 : 0;
              const prefix = level > 0 ? '└─'.repeat(level) + ' ' : '';
              return `<div style="font-family: monospace; padding: 4px;">
                        <span style="color: ${level > 0 ? '#666' : '#000'}; margin-left: ${level * 20}px;">
                          ${prefix}${value}
                        </span>
                      </div>`;
            }
          }
        }
      };

      populateSublist({ sublist, data: mockData, options });

      expect(sublist.setSublistValue).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'custcol_formatted_display',
          line: 0,
          value: expect.stringContaining('Parent Collection')
        })
      );

      expect(sublist.setSublistValue).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'custcol_formatted_display',
          line: 1,
          value: expect.stringContaining('└─ Child Collection')
        })
      );
    });

    it('should handle hidden fields correctly', () => {
      const options = {
        columnMapping: {
          custcol_display_name: {
            type: serverWidget.FieldType.TEXT,
            field: 'custrecord_ng_cs_itemcoll_display_name',
            label: 'Display Name',
            isHidden: true
          }
        }
      };

      populateSublist({ sublist, data: mockData, options });

      expect(sublist.addField).toHaveBeenCalledWith({
        id: 'custcol_display_name',
        type: serverWidget.FieldType.TEXT,
        label: 'Display Name'
      });
      expect(sublist.updateDisplayType).toHaveBeenCalledWith({
        displayType: serverWidget.FieldDisplayType.HIDDEN
      });
    });
  });
}); 