const moment = require('moment');

// Mock console for environment detection
global.console = {
    log: jest.fn(),
    debug: jest.fn(),
    error: jest.fn()
};

const mockEventHelpers = {
    getFirstShowDate: jest.fn(),
    getAdvancedDate: jest.fn()
};

// Mock the define function
const mockDefine = (dependencies, factory) => factory(
    null, // query
    null, // record
    null, // runtime
    null, // search
    null, // url
    moment, // moment
    null, // cache
    mockEventHelpers // eventHelpers
);

// Import the actual module (this will use our mocked define)
jest.mock('../../src/FileCabinet/SuiteApps/com.newgennow.cseventservices/packages/@prices/ng_client_cm_price_hooks.js', () => ({
    define: mockDefine
}));

const priceHooks = require('../../src/FileCabinet/SuiteApps/com.newgennow.cseventservices/packages/@prices/ng_client_cm_price_hooks.js');

describe('Client Price Hooks Tests', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('useItemPriceLevel', () => {
        it('should throw error if no event ID is provided', () => {
            expect(() => priceHooks.useItemPriceLevel()).toThrow('An event id arg is missing for useItemPriceLevel()');
        });

        it('should return advanced price level when before advanced date', () => {
            mockEventHelpers.getAdvancedDate.mockReturnValue(moment().add(3, 'days'));
            mockEventHelpers.getFirstShowDate.mockReturnValue(moment().add(2, 'days'));

            const result = priceHooks.useItemPriceLevel('123');
            expect(result).toBe('ADV_PRICE');
        });
    });

    describe('useItemPrice', () => {
        it('should throw error if event ID is missing', () => {
            expect(() => priceHooks.useItemPrice()).toThrow('An event id followed by a item id needs specified in getItemPrice()');
        });

        it('should throw error if item ID is missing', () => {
            expect(() => priceHooks.useItemPrice('123')).toThrow('A item id arg is missing for getItemPrice()');
        });

        it('should return default price output when no price levels found', () => {
            priceHooks.useItemPriceLevel.mockReturnValue(null);

            const result = priceHooks.useItemPrice('123', '456');
            expect(result).toEqual({ comparePrice: null, price: null });
        });
    });

    describe('useEventPricingDates', () => {
        it('should return empty output object if no dates found', () => {
            const result = priceHooks.useEventPricingDates('123');
            expect(result).toEqual({
                laborDates: [],
                showDates: [],
                advancedDate: null,
                firstShowDate: null
            });
        });

        it('should handle error and return empty output', () => {
            priceHooks.useEventPricingDates.mockImplementation(() => {
                throw new Error('Test error');
            });

            const result = priceHooks.useEventPricingDates('123');
            expect(result).toEqual({
                laborDates: [],
                showDates: [],
                advancedDate: null,
                firstShowDate: null
            });
        });
    });

    describe('useTaxCode', () => {
        it('should handle client-side logging', () => {
            const options = {
                countryCode: 'US',
                stateCode: 'CA'
            };

            priceHooks.useTaxCode(options);
            expect(console.log).toHaveBeenCalled();
        });

        it('should use client console for logging in client environment', () => {
            const logger = priceHooks.useLogger();
            logger.log('test message');
            expect(console.log).toHaveBeenCalledWith('test message');
        });
    });
});
