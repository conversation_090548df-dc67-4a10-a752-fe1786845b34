const moment = require('moment');

// Mock the NetSuite modules
const mockCache = {
    getCache: jest.fn().mockReturnValue({
        get: jest.fn(),
        put: jest.fn()
    })
};

const mockEventHelpers = {
    getFirstShowDate: jest.fn(),
    getAdvancedDate: jest.fn()
};

// Mock the define function
const mockDefine = (dependencies, factory) => factory(
    null, // query
    null, // record
    null, // runtime
    null, // search
    null, // url
    moment, // moment
    mockCache, // cache
    mockEventHelpers // eventHelpers
);

// Import the actual module (this will use our mocked define)
jest.mock('../../src/FileCabinet/SuiteApps/com.newgennow.cseventservices/packages/@prices/ng_server_cm_price_hooks.js', () => ({
    define: mockDefine
}));

const priceHooks = require('../../src/FileCabinet/SuiteApps/com.newgennow.cseventservices/packages/@prices/ng_server_cm_price_hooks.js');

describe('Price Hooks Tests', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('useItemPriceLevel', () => {
        it('should throw error if no event ID is provided', () => {
            expect(() => priceHooks.useItemPriceLevel()).toThrow('An event id arg is missing for useItemPriceLevel()');
        });

        it('should return advanced price level when before advanced date', () => {
            mockEventHelpers.getAdvancedDate.mockReturnValue(moment().add(3, 'days'));
            mockEventHelpers.getFirstShowDate.mockReturnValue(moment().add(2, 'days'));

            const result = priceHooks.useItemPriceLevel('123');
            expect(result).toBe('ADV_PRICE');
        });

        it('should return standard price level when between advanced date and show date', () => {
            mockEventHelpers.getAdvancedDate.mockReturnValue(moment().subtract(1, 'day'));
            mockEventHelpers.getFirstShowDate.mockReturnValue(moment().add(1, 'day'));

            const result = priceHooks.useItemPriceLevel('123');
            expect(result).toBe('STD_PRICE');
        });
    });

    describe('useItemPrice', () => {
        it('should throw error if event ID or product ID is missing', () => {
            expect(() => priceHooks.useItemPrice()).toThrow('Event ID and Product ID are required for useItemPrice()');
            expect(() => priceHooks.useItemPrice('123')).toThrow('Event ID and Product ID are required for useItemPrice()');
        });

        it('should return cached result if available', () => {
            const cachedPrice = { price: 100, comparePrice: 120 };
            mockCache.getCache().get.mockReturnValue(JSON.stringify(cachedPrice));

            const result = priceHooks.useItemPrice('123', '456');
            expect(result).toEqual(cachedPrice);
        });
    });

    describe('useEventPricingDates', () => {
        it('should return empty output object if no dates found', () => {
            const result = priceHooks.useEventPricingDates('123');
            expect(result).toEqual({
                laborDates: [],
                showDates: [],
                advancedDate: null,
                firstShowDate: null
            });
        });
    });

    describe('useTaxCode', () => {
        it('should return default tax code for US when no matches found', () => {
            const options = {
                countryCode: 'US',
                stateCode: 'CA'
            };

            const result = priceHooks.useTaxCode(options);
            expect(result.id).toBe('-8'); // Default US non-taxable code
        });

        it('should return empty tax code for non-US when no matches found', () => {
            const options = {
                countryCode: 'CA',
                stateCode: 'ON'
            };

            const result = priceHooks.useTaxCode(options);
            expect(result.id).toBe(''); // Default non-US code
        });
    });
});
