{"version": 3, "sources": ["webpack://node_modules/@fullcalendar/common/main.css", "webpack://node_modules/@fullcalendar/resource-timeline/main.css", "webpack://node_modules/@fullcalendar/timeline/main.css"], "names": [], "mappings": "AAGA,0CAEE,kBACF,CAEA,iBACE,wBAAyB,CAErB,oBAAqB,CACjB,gBAAiB,CACzB,0BAA2B,CAC3B,yCACF,CACA,IAEE,YAAa,CACb,qBAAsB,CAEtB,aACF,CACA,iCAII,qBACF,CACF,UACI,wBAAyB,CACzB,gBAAiB,CACjB,aACF,CACF,OACI,iBACF,CACF,cAEI,kBAAmB,CACnB,SACF,CACF,oBACI,cACF,CACF,0BACI,yBACF,CACF,kBACE,aAAc,CACd,eACF,CACA,kBACE,aAAc,CACd,gBACF,CACA,4CAGI,qBAA8C,CAA9C,4CACF,CAIF,sCAEI,iBACF,CAEF,WACE,qBAAsB,CACtB,2mGAA4mG,CAC5mG,eAAmB,CACnB,iBACF,CAEA,SAEE,oBAAqB,CACrB,SAAU,CACV,UAAW,CACX,iBAAkB,CAClB,wBAAyB,CAErB,oBAAqB,CACjB,gBAAiB,CAGzB,+BAAiC,CACjC,UAAW,CACX,iBAAkB,CAClB,eAAmB,CACnB,4BAAoB,CAApB,mBAAoB,CACpB,mBAAoB,CACpB,aAAc,CAGd,kCAAmC,CACnC,iCACF,CAEA,6BACE,eACF,CAEA,8BACE,eACF,CAEA,8BACE,eACF,CAEA,+BACE,eACF,CAEA,6BACE,eACF,CAEA,4BACE,eACF,CAEA,kBACE,eACF,CAYA,eACI,eAAgB,CAChB,gBAAiB,CACjB,mBAAoB,CACpB,QAAS,CACT,mBAAoB,CACpB,iBAAkB,CAClB,mBACF,CACF,qBACI,kBAAmB,CACnB,yCACF,CACF,eACI,yBACF,CACF,8BACI,cACF,CACF,iCACI,SAAU,CACV,iBACF,CAMF,eACI,oBAAqB,CACrB,eAAgB,CAChB,iBAAkB,CAClB,qBAAsB,CACtB,wBAAyB,CAErB,oBAAqB,CACjB,gBAAiB,CACzB,4BAA6B,CAC7B,4BAA6B,CAC7B,kBAAqB,CACrB,aAAc,CACd,eAAgB,CAChB,mBACF,CACF,qBACI,oBACF,CACF,qBACI,SAAU,CACV,yCACF,CACF,wBACI,WACF,CAMF,uBAEI,UAAwC,CAAxC,sCAAwC,CAExC,wBAAoD,CAApD,kDAAoD,CAEpD,oBAAoD,CAApD,kDACF,CACF,6BAEI,UAAwC,CAAxC,sCAAwC,CAExC,wBAA0D,CAA1D,wDAA0D,CAE1D,oBAA0D,CAA1D,wDACF,CACF,gCAEI,UAAwC,CAAxC,sCAAwC,CAExC,wBAAoD,CAApD,kDAAoD,CAEpD,oBAAoD,CAApD,kDACF,CACF,6BACI,yCACF,CACF,oGAGI,UAAwC,CAAxC,sCAAwC,CAExC,wBAA2D,CAA3D,yDAA2D,CAE3D,oBAA2D,CAA3D,yDACF,CACF,gHAEI,yCACF,CAMF,wBACI,qBAAsB,CACtB,eACF,CACF,qBACI,iBAAkB,CAClB,mBAAoB,CACpB,qBACF,CACF,gCACI,iBAAkB,CAClB,aACF,CAIF,oKAGI,SACF,CACF,gEACI,gBAAiB,CACjB,wBAAyB,CACzB,2BACF,CACF,+DACI,yBAA0B,CAC1B,4BACF,CACF,gEACI,iBAAkB,CAClB,yBAA0B,CAC1B,4BACF,CACF,+DACI,wBAAyB,CACzB,2BACF,CACF,gBACI,YAAa,CACb,6BAA8B,CAC9B,kBACF,CACF,kCACI,mBACF,CACF,kCACI,gBACF,CACF,sBACI,gBAAiB,CACjB,QACF,CACF,mDACI,iBACF,CACF,mDACI,kBACF,CACF,kCACI,0BACF,CACF,iBACI,gCAAiC,CACjC,iBACF,CACF,wBACI,WACF,CACF,iCACI,iBAAkB,CAClB,KAAM,CACN,OAAQ,CACR,MAAO,CACP,QACF,CACF,yBACI,iBAAkB,CAClB,eAAgB,CAChB,aAGF,CACF,gCACI,WACF,CACF,oDACI,aACF,CACF,kCAEI,qBAA8C,CAA9C,4CACF,CACF,4CAEM,UAAW,CACX,kBACF,CACJ,yBACM,uBAAwB,CACxB,wBAAyB,CACzB,yBACF,CACJ,mBAEI,wBAAyB,CACzB,oBAAqB,CACrB,qBAEF,CACF,0BACI,WACF,CAQF,0FACM,UAGF,CACJ,kCACI,WAEF,CACF,qCACM,WACF,CACJ,6BACI,kBAAmB,CACnB,mBACF,CACF,wEAEI,qBACF,CACF,8EAEI,0BACF,CAMF,oCAEI,eAAyC,CAAzC,uCAAyC,CACzC,uBAAwB,CACxB,eAAgB,CAChB,SAEF,CACF,iEACI,KAEF,CACF,iEACI,QACF,CACF,+BACI,UAAW,CACX,kBACF,CACF,WACE,uBAAwB,CACxB,eACF,CACA,qBACI,WAAY,CACZ,iBACF,CAMF,qCACI,iBAAkB,CAClB,KAAM,CACN,OAAQ,CACR,QAAS,CACT,MACF,CACF,gCACI,oBAAqB,CACrB,eACF,CACF,wDAII,iBAAkB,CAClB,KAAM,CACN,MAAO,CACP,OAAQ,CACR,QACF,CACF,qBAEI,8BAAkE,CAAlE,2DACF,CACF,iBAEI,kBAAwD,CAAxD,2CAAwD,CAExD,UAAuC,CAAvC,qCACF,CACF,iCACM,WAAY,CAEZ,eAA2C,CAA3C,yCAA2C,CAC3C,iBACF,CACJ,kBAEI,+BAA+D,CAA/D,yDACF,CACF,yCAGI,8BAAgE,CAAhE,yDACF,CAGF,4BAEE,oBACF,CAEA,6CAEE,cACF,CAGA,yBACI,iBAAkB,CAClB,SACF,CAGF,2CACI,WACF,CACF,qCACI,mCACF,CAIF,4BACI,YAAa,CACb,iBAAkB,CAClB,SACF,CAKF,uEACI,aACF,CACF,qCAEI,iBAAqE,CAArE,kEAAqE,CAErE,gBAA2D,CAE3D,SAAmD,CAAnD,iDAAmD,CAEnD,UAAoD,CAApD,kDAAoD,CAEpD,yDAAqB,CAArB,oBAAqB,CAErB,eAAwC,CAAxC,uCAIF,CACF,4CACM,UAAW,CACX,iBAAkB,CAClB,SAAU,CACV,UAAW,CACX,WAAY,CACZ,YACF,CAGJ,mBACE,mCAIF,CACA,0BACI,UAAW,CACX,iBAAkB,CAClB,SAAU,CACV,KAAM,CACN,MAAO,CACP,OAAQ,CACR,QACF,CAMF,yBACI,UAAW,CAEX,0BAAuE,CAAvE,iEAAuE,CACvE,iBAAkB,CAClB,SAAU,CAKV,QAAS,CACT,SAAU,CACV,UAAW,CACX,WACF,CAIF,YACE,aAAc,CAEd,wBAAuD,CAAvD,qDAAuD,CAEvD,wBAAkD,CAAlD,iDAEF,CACA,2BAEI,UAAuC,CAAvC,qCACF,CACF,iCACI,YACF,CACF,2BACI,cAAe,CACf,eACF,CACF,sCACI,WAAY,CACZ,aAAc,CACd,WACF,CACF,4BACI,oBAAqB,CACrB,kBAAmB,CACnB,MAAO,CACP,OAAQ,CACR,cAAe,CACf,eACF,CACF,qCAEI,SAAU,CACV,YACF,CAEF,4HAEE,wBAAyB,CACzB,2BAA4B,CAC5B,mBACF,CACA,4HAEE,yBAA0B,CAC1B,4BAA6B,CAC7B,oBACF,CAEA,sDACE,KAAM,CACN,QAAS,CAET,SAA6C,CAA7C,2CACF,CACA,0JAEE,eAAgB,CAEhB,SAAuD,CAAvD,oDACF,CACA,0JAEE,eAAgB,CAEhB,UAAwD,CAAxD,qDACF,CAEA,gDACE,OAAQ,CAER,eAAmE,CAAnE,gEACF,CACA,8IAGE,SAA6D,CAA7D,0DACF,CACA,8IAGE,UAA8D,CAA9D,2DACF,CClpBE,kCACE,SAAU,CACV,iBACF,CAOF,4DAEM,8BAAgE,CAAhE,yDACF,CACJ,4BACI,iBAEF,CACF,6EACI,sBAAuB,CACvB,mBACF,CAEF,+BACE,WACF,CACA,+CACE,WAAY,CACZ,iBAAkB,CAClB,KAAM,CACN,OAAQ,CACR,QAAS,CACT,MACA,CAQF,gDACM,iBAAkB,CAClB,YAAa,CACb,0BAA2B,CAC3B,kBACF,CAMJ,8BACI,iBAAkB,CAClB,SAAU,CACV,KAAM,CACN,QAAS,CACT,SAAU,CACV,iBACF,CAMF,8BACI,WAAY,CACZ,kBAAmB,CACnB,eACF,CAMF,0BACI,cAAe,CACf,WAEF,CACF,mCACM,oBAAqB,CACrB,SACF,CACJ,sCACI,WACF,CACF,iEACM,YACF,CACJ,4CAA8C,UAAY,CAC1D,4CAA8C,SAAW,CACzD,wCAA0C,gBAAkB,CAC5D,wCAA0C,eAAiB,CC7FzD,sBACE,eAAgB,CAChB,iBAAkB,CAClB,SACF,CAIF,uBACI,iBAAkB,CAClB,SAAU,CACV,KAAM,CACN,QACF,CACF,6BACM,WACF,CAMJ,4BACI,mBACF,CAMF,4BACI,YAAa,CACb,kBAAmB,CACnB,sBACF,CAGF,2DACM,0BACF,CACJ,8BACI,eAAgB,CAChB,kBACF,CAUF,oCAAsC,wBAA2B,CACjE,oCAAsC,uBAA0B,CAChE,yCACI,iBAAkB,CAClB,SAAU,CACV,KAAM,CACN,QAAS,CACT,MAAO,CACP,OAAQ,CACR,OACF,CACF,yEAEI,iBAAkB,CAClB,KAAM,CACN,kBAAmB,CAEnB,gBAAgD,CAAhD,8CACF,CACF,qCACI,aAAc,CAGd,sBAA2B,CAC3B,6BAA8B,CAC9B,8BACF,CACF,oCACI,aAAc,CACd,QAAS,CACT,sBACF,CAMF,wBACI,iBAAkB,CAClB,SAAU,CACV,OACF,CAMF,+BACI,iBAAkB,CAClB,KAEF,CAEF,mBAAqB,SAAW,CAChC,mCAAqC,SAAW,CAChD,mBACE,iBAAkB,CAClB,YAAa,CACb,kBAAmB,CACnB,eAAgB,CAChB,eAAgB,CAChB,iBAAkB,CAElB,eAA0C,CAA1C,yCAKF,CACA,kCACI,WAAY,CACZ,aAAc,CACd,WACF,CACF,kCACI,eACF,CACF,qEAEI,kBAAmB,CACnB,aACF,CAEF,kDAAoD,gBAAkB,CACtE,kDAAoD,eAAiB,CAErE,iDACE,eAAgB,CAChB,kBAAmB,CACnB,eACF,CAIA,2FAEE,UAAW,CACX,WAAY,CACZ,aAAc,CACd,UAAW,CAGX,OAAQ,CACR,QAAS,CACT,YAAa,CAGb,6BAAgC,CAAhC,kBAAgC,CAAhC,gBACF,CAEA,+HAEE,aACF,CAEA,+HAEE,cACF,CACA,oBACI,iBAAkB,CAClB,SAAU,CACV,KAAM,CACN,QAAS,CACT,OAAQ,CACR,MAAO,CACP,OACF,CACF,qCAAuC,SAAW,CAClD,iCAAmC,SAAW,CAC9C,kCAAoC,SAAW,CAC/C,4BACI,iBAAkB,CAClB,KAAM,CACN,QACF", "file": "2.856bea34.chunk.css", "sourcesContent": ["\n/* classes attached to <body> */\n\n.fc-not-allowed,\n.fc-not-allowed .fc-event { /* override events' custom cursors */\n  cursor: not-allowed;\n}\n\n.fc-unselectable {\n  -webkit-user-select: none;\n     -moz-user-select: none;\n      -ms-user-select: none;\n          user-select: none;\n  -webkit-touch-callout: none;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n}\n.fc {\n  /* layout of immediate children */\n  display: flex;\n  flex-direction: column;\n\n  font-size: 1em\n}\n.fc,\n  .fc *,\n  .fc *:before,\n  .fc *:after {\n    box-sizing: border-box;\n  }\n.fc table {\n    border-collapse: collapse;\n    border-spacing: 0;\n    font-size: 1em; /* normalize cross-browser */\n  }\n.fc th {\n    text-align: center;\n  }\n.fc th,\n  .fc td {\n    vertical-align: top;\n    padding: 0;\n  }\n.fc a[data-navlink] {\n    cursor: pointer;\n  }\n.fc a[data-navlink]:hover {\n    text-decoration: underline;\n  }\n.fc-direction-ltr {\n  direction: ltr;\n  text-align: left;\n}\n.fc-direction-rtl {\n  direction: rtl;\n  text-align: right;\n}\n.fc-theme-standard td,\n  .fc-theme-standard th {\n    border: 1px solid #ddd;\n    border: 1px solid var(--fc-border-color, #ddd);\n  }\n/* for FF, which doesn't expand a 100% div within a table cell. use absolute positioning */\n/* inner-wrappers are responsible for being absolute */\n/* TODO: best place for this? */\n.fc-liquid-hack td,\n  .fc-liquid-hack th {\n    position: relative;\n  }\n\n@font-face {\n  font-family: 'fcicons';\n  src: url(\"data:application/x-font-ttf;charset=utf-8;base64,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\") format('truetype');\n  font-weight: normal;\n  font-style: normal;\n}\n\n.fc-icon {\n  /* added for fc */\n  display: inline-block;\n  width: 1em;\n  height: 1em;\n  text-align: center;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n      -ms-user-select: none;\n          user-select: none;\n\n  /* use !important to prevent issues with browser extensions that change fonts */\n  font-family: 'fcicons' !important;\n  speak: none;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n\n  /* Better Font Rendering =========== */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.fc-icon-chevron-left:before {\n  content: \"\\e900\";\n}\n\n.fc-icon-chevron-right:before {\n  content: \"\\e901\";\n}\n\n.fc-icon-chevrons-left:before {\n  content: \"\\e902\";\n}\n\n.fc-icon-chevrons-right:before {\n  content: \"\\e903\";\n}\n\n.fc-icon-minus-square:before {\n  content: \"\\e904\";\n}\n\n.fc-icon-plus-square:before {\n  content: \"\\e905\";\n}\n\n.fc-icon-x:before {\n  content: \"\\e906\";\n}\n/*\nLots taken from Flatly (MIT): https://bootswatch.com/4/flatly/bootstrap.css\n\nThese styles only apply when the standard-theme is activated.\nWhen it's NOT activated, the fc-button classes won't even be in the DOM.\n*/\n.fc {\n\n  /* reset */\n\n}\n.fc .fc-button {\n    border-radius: 0;\n    overflow: visible;\n    text-transform: none;\n    margin: 0;\n    font-family: inherit;\n    font-size: inherit;\n    line-height: inherit;\n  }\n.fc .fc-button:focus {\n    outline: 1px dotted;\n    outline: 5px auto -webkit-focus-ring-color;\n  }\n.fc .fc-button {\n    -webkit-appearance: button;\n  }\n.fc .fc-button:not(:disabled) {\n    cursor: pointer;\n  }\n.fc .fc-button::-moz-focus-inner {\n    padding: 0;\n    border-style: none;\n  }\n.fc {\n\n  /* theme */\n\n}\n.fc .fc-button {\n    display: inline-block;\n    font-weight: 400;\n    text-align: center;\n    vertical-align: middle;\n    -webkit-user-select: none;\n       -moz-user-select: none;\n        -ms-user-select: none;\n            user-select: none;\n    background-color: transparent;\n    border: 1px solid transparent;\n    padding: 0.4em 0.65em;\n    font-size: 1em;\n    line-height: 1.5;\n    border-radius: 0.25em;\n  }\n.fc .fc-button:hover {\n    text-decoration: none;\n  }\n.fc .fc-button:focus {\n    outline: 0;\n    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);\n  }\n.fc .fc-button:disabled {\n    opacity: 0.65;\n  }\n.fc {\n\n  /* \"primary\" coloring */\n\n}\n.fc .fc-button-primary {\n    color: #fff;\n    color: var(--fc-button-text-color, #fff);\n    background-color: #2C3E50;\n    background-color: var(--fc-button-bg-color, #2C3E50);\n    border-color: #2C3E50;\n    border-color: var(--fc-button-border-color, #2C3E50);\n  }\n.fc .fc-button-primary:hover {\n    color: #fff;\n    color: var(--fc-button-text-color, #fff);\n    background-color: #1e2b37;\n    background-color: var(--fc-button-hover-bg-color, #1e2b37);\n    border-color: #1a252f;\n    border-color: var(--fc-button-hover-border-color, #1a252f);\n  }\n.fc .fc-button-primary:disabled { /* not DRY */\n    color: #fff;\n    color: var(--fc-button-text-color, #fff);\n    background-color: #2C3E50;\n    background-color: var(--fc-button-bg-color, #2C3E50);\n    border-color: #2C3E50;\n    border-color: var(--fc-button-border-color, #2C3E50); /* overrides :hover */\n  }\n.fc .fc-button-primary:focus {\n    box-shadow: 0 0 0 0.2rem rgba(76, 91, 106, 0.5);\n  }\n.fc .fc-button-primary:not(:disabled):active,\n  .fc .fc-button-primary:not(:disabled).fc-button-active {\n    color: #fff;\n    color: var(--fc-button-text-color, #fff);\n    background-color: #1a252f;\n    background-color: var(--fc-button-active-bg-color, #1a252f);\n    border-color: #151e27;\n    border-color: var(--fc-button-active-border-color, #151e27);\n  }\n.fc .fc-button-primary:not(:disabled):active:focus,\n  .fc .fc-button-primary:not(:disabled).fc-button-active:focus {\n    box-shadow: 0 0 0 0.2rem rgba(76, 91, 106, 0.5);\n  }\n.fc {\n\n  /* icons within buttons */\n\n}\n.fc .fc-button .fc-icon {\n    vertical-align: middle;\n    font-size: 1.5em; /* bump up the size (but don't make it bigger than line-height of button, which is 1.5em also) */\n  }\n.fc .fc-button-group {\n    position: relative;\n    display: inline-flex;\n    vertical-align: middle;\n  }\n.fc .fc-button-group > .fc-button {\n    position: relative;\n    flex: 1 1 auto;\n  }\n.fc .fc-button-group > .fc-button:hover {\n    z-index: 1;\n  }\n.fc .fc-button-group > .fc-button:focus,\n  .fc .fc-button-group > .fc-button:active,\n  .fc .fc-button-group > .fc-button.fc-button-active {\n    z-index: 1;\n  }\n.fc-direction-ltr .fc-button-group > .fc-button:not(:first-child) {\n    margin-left: -1px;\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n  }\n.fc-direction-ltr .fc-button-group > .fc-button:not(:last-child) {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n.fc-direction-rtl .fc-button-group > .fc-button:not(:first-child) {\n    margin-right: -1px;\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n.fc-direction-rtl .fc-button-group > .fc-button:not(:last-child) {\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n  }\n.fc .fc-toolbar {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n  }\n.fc .fc-toolbar.fc-header-toolbar {\n    margin-bottom: 1.5em;\n  }\n.fc .fc-toolbar.fc-footer-toolbar {\n    margin-top: 1.5em;\n  }\n.fc .fc-toolbar-title {\n    font-size: 1.75em;\n    margin: 0;\n  }\n.fc-direction-ltr .fc-toolbar > * > :not(:first-child) {\n    margin-left: .75em; /* space between */\n  }\n.fc-direction-rtl .fc-toolbar > * > :not(:first-child) {\n    margin-right: .75em; /* space between */\n  }\n.fc-direction-rtl .fc-toolbar-ltr { /* when the toolbar-chunk positioning system is explicitly left-to-right */\n    flex-direction: row-reverse;\n  }\n.fc .fc-scroller {\n    -webkit-overflow-scrolling: touch;\n    position: relative; /* for abs-positioned elements within */\n  }\n.fc .fc-scroller-liquid {\n    height: 100%;\n  }\n.fc .fc-scroller-liquid-absolute {\n    position: absolute;\n    top: 0;\n    right: 0;\n    left: 0;\n    bottom: 0;\n  }\n.fc .fc-scroller-harness {\n    position: relative;\n    overflow: hidden;\n    direction: ltr;\n      /* hack for chrome computing the scroller's right/left wrong for rtl. undone below... */\n      /* TODO: demonstrate in codepen */\n  }\n.fc .fc-scroller-harness-liquid {\n    height: 100%;\n  }\n.fc-direction-rtl .fc-scroller-harness > .fc-scroller { /* undo above hack */\n    direction: rtl;\n  }\n.fc-theme-standard .fc-scrollgrid {\n    border: 1px solid #ddd;\n    border: 1px solid var(--fc-border-color, #ddd); /* bootstrap does this. match */\n  }\n.fc .fc-scrollgrid,\n    .fc .fc-scrollgrid table { /* all tables (self included) */\n      width: 100%; /* because tables don't normally do this */\n      table-layout: fixed;\n    }\n.fc .fc-scrollgrid table { /* inner tables */\n      border-top-style: hidden;\n      border-left-style: hidden;\n      border-right-style: hidden;\n    }\n.fc .fc-scrollgrid {\n\n    border-collapse: separate;\n    border-right-width: 0;\n    border-bottom-width: 0;\n\n  }\n.fc .fc-scrollgrid-liquid {\n    height: 100%;\n  }\n.fc .fc-scrollgrid-section { /* a <tr> */\n    height: 1px /* better than 0, for firefox */\n\n  }\n.fc .fc-scrollgrid-section > td {\n      height: 1px; /* needs a height so inner div within grow. better than 0, for firefox */\n    }\n.fc .fc-scrollgrid-section table {\n      height: 1px;\n        /* for most browsers, if a height isn't set on the table, can't do liquid-height within cells */\n        /* serves as a min-height. harmless */\n    }\n.fc .fc-scrollgrid-section-liquid {\n    height: auto\n\n  }\n.fc .fc-scrollgrid-section-liquid > td {\n      height: 100%; /* better than `auto`, for firefox */\n    }\n.fc .fc-scrollgrid-section > * {\n    border-top-width: 0;\n    border-left-width: 0;\n  }\n.fc .fc-scrollgrid-section-header > *,\n  .fc .fc-scrollgrid-section-footer > * {\n    border-bottom-width: 0;\n  }\n.fc .fc-scrollgrid-section-body table,\n  .fc .fc-scrollgrid-section-footer table {\n    border-bottom-style: hidden; /* head keeps its bottom border tho */\n  }\n.fc {\n\n  /* stickiness */\n\n}\n.fc .fc-scrollgrid-section-sticky > * {\n    background: #fff;\n    background: var(--fc-page-bg-color, #fff);\n    position: -webkit-sticky;\n    position: sticky;\n    z-index: 2; /* TODO: var */\n    /* TODO: box-shadow when sticking */\n  }\n.fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky > * {\n    top: 0; /* because border-sharing causes a gap at the top */\n      /* TODO: give safari -1. has bug */\n  }\n.fc .fc-scrollgrid-section-footer.fc-scrollgrid-section-sticky > * {\n    bottom: 0; /* known bug: bottom-stickiness doesn't work in safari */\n  }\n.fc .fc-scrollgrid-sticky-shim { /* for horizontal scrollbar */\n    height: 1px; /* needs height to create scrollbars */\n    margin-bottom: -1px;\n  }\n.fc-sticky { /* no .fc wrap because used as child of body */\n  position: -webkit-sticky;\n  position: sticky;\n}\n.fc .fc-view-harness {\n    flex-grow: 1; /* because this harness is WITHIN the .fc's flexbox */\n    position: relative;\n  }\n.fc {\n\n  /* when the harness controls the height, make the view liquid */\n\n}\n.fc .fc-view-harness-active > .fc-view {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n  }\n.fc .fc-col-header-cell-cushion {\n    display: inline-block; /* x-browser for when sticky (when multi-tier header) */\n    padding: 2px 4px;\n  }\n.fc .fc-bg-event,\n  .fc .fc-non-business,\n  .fc .fc-highlight {\n    /* will always have a harness with position:relative/absolute, so absolutely expand */\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n  }\n.fc .fc-non-business {\n    background: rgba(215, 215, 215, 0.3);\n    background: var(--fc-non-business-color, rgba(215, 215, 215, 0.3));\n  }\n.fc .fc-bg-event {\n    background: rgb(143, 223, 130);\n    background: var(--fc-bg-event-color, rgb(143, 223, 130));\n    opacity: 0.3;\n    opacity: var(--fc-bg-event-opacity, 0.3)\n  }\n.fc .fc-bg-event .fc-event-title {\n      margin: .5em;\n      font-size: .85em;\n      font-size: var(--fc-small-font-size, .85em);\n      font-style: italic;\n    }\n.fc .fc-highlight {\n    background: rgba(188, 232, 241, 0.3);\n    background: var(--fc-highlight-color, rgba(188, 232, 241, 0.3));\n  }\n.fc .fc-cell-shaded,\n  .fc .fc-day-disabled {\n    background: rgba(208, 208, 208, 0.3);\n    background: var(--fc-neutral-bg-color, rgba(208, 208, 208, 0.3));\n  }\n/* link resets */\n/* ---------------------------------------------------------------------------------------------------- */\na.fc-event,\na.fc-event:hover {\n  text-decoration: none;\n}\n/* cursor */\n.fc-event[href],\n.fc-event.fc-event-draggable {\n  cursor: pointer;\n}\n/* event text content */\n/* ---------------------------------------------------------------------------------------------------- */\n.fc-event .fc-event-main {\n    position: relative;\n    z-index: 2;\n  }\n/* dragging */\n/* ---------------------------------------------------------------------------------------------------- */\n.fc-event-dragging:not(.fc-event-selected) { /* MOUSE */\n    opacity: 0.75;\n  }\n.fc-event-dragging.fc-event-selected { /* TOUCH */\n    box-shadow: 0 2px 7px rgba(0, 0, 0, 0.3);\n  }\n/* resizing */\n/* ---------------------------------------------------------------------------------------------------- */\n/* (subclasses should hone positioning for touch and non-touch) */\n.fc-event .fc-event-resizer {\n    display: none;\n    position: absolute;\n    z-index: 4;\n  }\n.fc-event:hover, /* MOUSE */\n.fc-event-selected { /* TOUCH */\n\n}\n.fc-event:hover .fc-event-resizer, .fc-event-selected .fc-event-resizer {\n    display: block;\n  }\n.fc-event-selected .fc-event-resizer {\n    border-radius: 4px;\n    border-radius: calc(var(--fc-event-resizer-dot-total-width, 8px) / 2);\n    border-width: 1px;\n    border-width: var(--fc-event-resizer-dot-border-width, 1px);\n    width: 8px;\n    width: var(--fc-event-resizer-dot-total-width, 8px);\n    height: 8px;\n    height: var(--fc-event-resizer-dot-total-width, 8px);\n    border-style: solid;\n    border-color: inherit;\n    background: #fff;\n    background: var(--fc-page-bg-color, #fff)\n\n    /* expand hit area */\n\n  }\n.fc-event-selected .fc-event-resizer:before {\n      content: '';\n      position: absolute;\n      top: -20px;\n      left: -20px;\n      right: -20px;\n      bottom: -20px;\n    }\n/* selecting (always TOUCH) */\n/* ---------------------------------------------------------------------------------------------------- */\n.fc-event-selected {\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2)\n\n  /* expand hit area (subclasses should expand) */\n\n}\n.fc-event-selected:before {\n    content: \"\";\n    position: absolute;\n    z-index: 3;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n  }\n.fc-event-selected {\n\n  /* dimmer effect */\n\n}\n.fc-event-selected:after {\n    content: \"\";\n    background: rgba(0, 0, 0, 0.25);\n    background: var(--fc-event-selected-overlay-color, rgba(0, 0, 0, 0.25));\n    position: absolute;\n    z-index: 1;\n\n    /* assume there's a border on all sides. overcome it. */\n    /* sometimes there's NOT a border, in which case the dimmer will go over */\n    /* an adjacent border, which looks fine. */\n    top: -1px;\n    left: -1px;\n    right: -1px;\n    bottom: -1px;\n  }\n/*\nA HORIZONTAL event\n*/\n.fc-h-event { /* allowed to be top-level */\n  display: block;\n  border: 1px solid #3788d8;\n  border: 1px solid var(--fc-event-border-color, #3788d8);\n  background-color: #3788d8;\n  background-color: var(--fc-event-bg-color, #3788d8)\n\n}\n.fc-h-event .fc-event-main {\n    color: #fff;\n    color: var(--fc-event-text-color, #fff);\n  }\n.fc-h-event .fc-event-main-frame {\n    display: flex; /* for make fc-event-title-container expand */\n  }\n.fc-h-event .fc-event-time {\n    max-width: 100%; /* clip overflow on this element */\n    overflow: hidden;\n  }\n.fc-h-event .fc-event-title-container { /* serves as a container for the sticky cushion */\n    flex-grow: 1;\n    flex-shrink: 1;\n    min-width: 0; /* important for allowing to shrink all the way */\n  }\n.fc-h-event .fc-event-title {\n    display: inline-block; /* need this to be sticky cross-browser */\n    vertical-align: top; /* for not messing up line-height */\n    left: 0;  /* for sticky */\n    right: 0; /* for sticky */\n    max-width: 100%; /* clip overflow on this element */\n    overflow: hidden;\n  }\n.fc-h-event.fc-event-selected:before {\n    /* expand hit area */\n    top: -10px;\n    bottom: -10px;\n  }\n/* adjust border and border-radius (if there is any) for non-start/end */\n.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-start),\n.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-end) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n  border-left-width: 0;\n}\n.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end),\n.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n  border-right-width: 0;\n}\n/* resizers */\n.fc-h-event:not(.fc-event-selected) .fc-event-resizer {\n  top: 0;\n  bottom: 0;\n  width: 8px;\n  width: var(--fc-event-resizer-thickness, 8px);\n}\n.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start,\n.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end {\n  cursor: w-resize;\n  left: -4px;\n  left: calc(var(--fc-event-resizer-thickness, 8px) / -2);\n}\n.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end,\n.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start {\n  cursor: e-resize;\n  right: -4px;\n  right: calc(var(--fc-event-resizer-thickness, 8px) / -2);\n}\n/* resizers for TOUCH */\n.fc-h-event.fc-event-selected .fc-event-resizer {\n  top: 50%;\n  margin-top: -4px;\n  margin-top: calc(var(--fc-event-resizer-dot-total-width, 8px) / -2);\n}\n.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-start,\n.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-end {\n  left: -4px;\n  left: calc(var(--fc-event-resizer-dot-total-width, 8px) / -2);\n}\n.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-end,\n.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-start {\n  right: -4px;\n  right: calc(var(--fc-event-resizer-dot-total-width, 8px) / -2);\n}\n", "\n\n  .fc .fc-resource-timeline-divider {\n    width: 3px; /* important to have width to shrink this cell. no cross-browser problems */\n    cursor: col-resize;\n  }\n.fc {\n\n\n  /* will match horizontal groups in the datagrid AND group lanes in the timeline area */\n\n}\n.fc .fc-resource-timeline .fc-resource-group:not([rowspan]) {\n      background: rgba(208, 208, 208, 0.3);\n      background: var(--fc-neutral-bg-color, rgba(208, 208, 208, 0.3));\n    }\n.fc .fc-timeline-lane-frame {\n    position: relative; /* contains the fc-timeline-bg container, which liquidly expands */\n    /* the height is explicitly set by row-height-sync */\n  }\n.fc .fc-timeline-overlap-enabled .fc-timeline-lane-frame .fc-timeline-events { /* has height set on it */\n    box-sizing: content-box; /* padding no longer part of height */\n    padding-bottom: 10px; /* give extra spacing underneath for selecting */\n  }\n/* the \"frame\" */\n.fc-datagrid-cell-frame-liquid {\n  height: 100%; /* needs liquid hack */\n}\n.fc-liquid-hack .fc-datagrid-cell-frame-liquid {\n  height: auto;\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  }\n.fc {\n\n  /* the \"frame\" in a HEADER */\n  /* needs to position the column resizer */\n  /* needs to vertically center content */\n\n}\n.fc .fc-datagrid-header .fc-datagrid-cell-frame {\n      position: relative; /* for resizer */\n      display: flex;\n      justify-content: flex-start; /* horizontal align (natural left/right) */\n      align-items: center; /* vertical align */\n    }\n.fc {\n\n  /* the column resizer (only in HEADER) */\n\n}\n.fc .fc-datagrid-cell-resizer {\n    position: absolute;\n    z-index: 1;\n    top: 0;\n    bottom: 0;\n    width: 5px;\n    cursor: col-resize;\n  }\n.fc {\n\n  /* the cushion */\n\n}\n.fc .fc-datagrid-cell-cushion {\n    padding: 8px;\n    white-space: nowrap;\n    overflow: hidden; /* problem for col resizer :( */\n  }\n.fc {\n\n  /* expander icons */\n\n}\n.fc .fc-datagrid-expander {\n    cursor: pointer;\n    opacity: 0.65\n\n  }\n.fc .fc-datagrid-expander .fc-icon { /* the expander and spacers before the expander */\n      display: inline-block;\n      width: 1em; /* ensure constant width, esp for empty icons */\n    }\n.fc .fc-datagrid-expander-placeholder {\n    cursor: auto;\n  }\n.fc .fc-resource-timeline-flat .fc-datagrid-expander-placeholder {\n      display: none;\n    }\n.fc-direction-ltr .fc-datagrid-cell-resizer { right: -3px }\n.fc-direction-rtl .fc-datagrid-cell-resizer { left: -3px }\n.fc-direction-ltr .fc-datagrid-expander { margin-right: 3px }\n.fc-direction-rtl .fc-datagrid-expander { margin-left: 3px }\n", "\n\n  .fc .fc-timeline-body {\n    min-height: 100%;\n    position: relative;\n    z-index: 1; /* scope slots, bg, etc */\n  }\n/*\nvertical slots in both the header AND the body\n*/\n.fc .fc-timeline-slots {\n    position: absolute;\n    z-index: 1;\n    top: 0;\n    bottom: 0\n  }\n.fc .fc-timeline-slots > table {\n      height: 100%;\n    }\n.fc {\n\n  /* border for both header AND body cells */\n\n}\n.fc .fc-timeline-slot-minor {\n    border-style: dotted;\n  }\n.fc {\n\n  /* header cells (aka \"label\") */\n\n}\n.fc .fc-timeline-slot-frame {\n    display: flex;\n    align-items: center; /* vertical align */\n    justify-content: center; /* horizontal align */\n  }\n.fc .fc-timeline-header-row-chrono { /* a row of times */\n  }\n.fc .fc-timeline-header-row-chrono .fc-timeline-slot-frame {\n      justify-content: flex-start; /* horizontal align left or right */\n    }\n.fc .fc-timeline-slot-cushion {\n    padding: 4px 5px; /* TODO: unify with fc-col-header? */\n    white-space: nowrap;\n  }\n.fc {\n\n  /* NOTE: how does the top row of cells get horizontally centered? */\n  /* for the non-chrono-row, the fc-sticky system looks for text-align center, */\n  /* and it's a fluke that the default browser stylesheet already does this for <th> */\n  /* TODO: have StickyScrolling look at natural left coord to detect centeredness. */\n\n}\n/* only owns one side, so can do dotted */\n.fc-direction-ltr .fc-timeline-slot { border-right: 0 !important }\n.fc-direction-rtl .fc-timeline-slot { border-left: 0 !important }\n.fc .fc-timeline-now-indicator-container {\n    position: absolute;\n    z-index: 4;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    width: 0;\n  }\n.fc .fc-timeline-now-indicator-arrow,\n  .fc .fc-timeline-now-indicator-line {\n    position: absolute;\n    top: 0;\n    border-style: solid;\n    border-color: red;\n    border-color: var(--fc-now-indicator-color, red);\n  }\n.fc .fc-timeline-now-indicator-arrow {\n    margin: 0 -6px; /* 5, then one more to counteract scroller's negative margins */\n\n    /* triangle pointing down. TODO: mixin */\n    border-width: 6px 5px 0 5px;\n    border-left-color: transparent;\n    border-right-color: transparent;\n  }\n.fc .fc-timeline-now-indicator-line {\n    margin: 0 -1px; /* counteract scroller's negative margins */\n    bottom: 0;\n    border-width: 0 0 0 1px;\n  }\n.fc {\n\n  /* container */\n\n}\n.fc .fc-timeline-events {\n    position: relative;\n    z-index: 3;\n    width: 0; /* for event positioning. will end up on correct side based on dir */\n  }\n.fc {\n\n  /* harness */\n\n}\n.fc .fc-timeline-event-harness {\n    position: absolute;\n    top: 0; /* for when when top can't be computed yet */\n    /* JS will set tht left/right */\n  }\n/* z-index, scoped within fc-timeline-events */\n.fc-timeline-event { z-index: 1 }\n.fc-timeline-event.fc-event-mirror { z-index: 2 }\n.fc-timeline-event {\n  position: relative; /* contains things. TODO: make part of fc-h-event and fc-v-event */\n  display: flex; /* for v-aligning start/end arrows and making fc-event-main stretch all the way */\n  align-items: center;\n  border-radius: 0;\n  padding: 2px 1px;\n  margin-bottom: 1px;\n  font-size: .85em;\n  font-size: var(--fc-small-font-size, .85em)\n\n  /* time and title spacing */\n  /* ---------------------------------------------------------------------------------------------------- */\n\n}\n.fc-timeline-event .fc-event-main {\n    flex-grow: 1;\n    flex-shrink: 1;\n    min-width: 0; /* important for allowing to shrink all the way */\n  }\n.fc-timeline-event .fc-event-time {\n    font-weight: bold;\n  }\n.fc-timeline-event .fc-event-time,\n  .fc-timeline-event .fc-event-title {\n    white-space: nowrap;\n    padding: 0 2px;\n  }\n/* move 1px away from slot line */\n.fc-direction-ltr .fc-timeline-event.fc-event-end { margin-right: 1px }\n.fc-direction-rtl .fc-timeline-event.fc-event-end { margin-left: 1px }\n/* make event beefier when overlap not allowed */\n.fc-timeline-overlap-disabled .fc-timeline-event {\n  padding-top: 5px;\n  padding-bottom: 5px;\n  margin-bottom: 0;\n}\n/* arrows indicating the event continues into past/future */\n/* ---------------------------------------------------------------------------------------------------- */\n/* part of the flexbox flow */\n.fc-timeline-event:not(.fc-event-start):before,\n.fc-timeline-event:not(.fc-event-end):after {\n  content: \"\";\n  flex-grow: 0;\n  flex-shrink: 0;\n  opacity: .5;\n\n  /* triangle. TODO: mixin */\n  width: 0;\n  height: 0;\n  margin: 0 1px;\n  border: 5px solid #000; /* TODO: var */\n  border-top-color: transparent;\n  border-bottom-color: transparent;\n}\n/* pointing left */\n.fc-direction-ltr .fc-timeline-event:not(.fc-event-start):before,\n.fc-direction-rtl .fc-timeline-event:not(.fc-event-end):after {\n  border-left: 0;\n}\n/* pointing right */\n.fc-direction-ltr .fc-timeline-event:not(.fc-event-end):after,\n.fc-direction-rtl .fc-timeline-event:not(.fc-event-start):before {\n  border-right: 0;\n}\n.fc .fc-timeline-bg { /* a container for bg content */\n    position: absolute;\n    z-index: 2;\n    top: 0;\n    bottom: 0;\n    width: 0;\n    left: 0; /* will take precedence when LTR */\n    right: 0; /* will take precedence when RTL */ /* TODO: kill */\n  }\n.fc .fc-timeline-bg .fc-non-business { z-index: 1 }\n.fc .fc-timeline-bg .fc-bg-event { z-index: 2 }\n.fc .fc-timeline-bg .fc-highlight { z-index: 3 }\n.fc .fc-timeline-bg-harness {\n    position: absolute;\n    top: 0;\n    bottom: 0;\n  }\n\n"]}