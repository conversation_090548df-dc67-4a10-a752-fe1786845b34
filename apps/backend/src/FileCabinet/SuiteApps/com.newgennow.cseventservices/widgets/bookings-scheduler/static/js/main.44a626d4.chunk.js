(this["webpackJsonpbookings-scheduler"]=this["webpackJsonpbookings-scheduler"]||[]).push([[0],{13:function(e,t,n){},14:function(e,t,n){},18:function(e,t,n){"use strict";n.r(t);var r=n(2),i=n(1),c=n.n(i),o=n(3),s=n.n(o),a=(n(13),n(14),n(6)),d=n(5),l=n(7);var u=function(e){return Object(r.jsx)(r.Fragment,{children:Object(r.jsx)("b",{children:"Hi"})})};var b=function(){return Object(r.jsx)(a.a,{plugins:[l.a,d.a],schedulerLicenseKey:"CC-Attribution-NonCommercial-NoDerivatives",headerToolbar:{start:"",center:"title",end:"today prev next"},initialView:"resourceTimeline",editable:!0,droppable:!0,eventDragMinDistance:50,eventContent:Object(r.jsx)(u,{}),dateClick:function(e){alert("Clicked on: "+e.dateStr)},eventClick:function(e){alert("Clicked on: ".concat(e.event.title," from ").concat(e.event.start," to ").concat(e.event.end))},eventColor:"blue",events:[{title:"event 1",resourceId:"a",start:"2021-01-03T14:30",end:"2021-01-03T16:30"},{title:"event 2",resourceId:"b",start:"2021-01-03T14:30",end:"2021-01-03T17:30"}],resources:[{id:"a",groupId:"1",title:"Resource A"},{id:"b",groupdId:"1",title:"Resource B"},{id:"c",groupId:"2",title:"Resource C"}]})};s.a.render(Object(r.jsx)(c.a.StrictMode,{children:Object(r.jsx)(b,{})}),document.getElementById("root"))}},[[18,1,2]]]);
//# sourceMappingURL=main.44a626d4.chunk.js.map