{"version": 3, "sources": ["components/EventNode.js", "App.js", "index.js"], "names": ["EventNode", "event", "App", "plugins", "interactionPlugin", "resourceTimelinePlugin", "schedulerLicenseKey", "headerToolbar", "start", "center", "end", "initialView", "editable", "droppable", "eventDragMinDistance", "eventContent", "dateClick", "info", "alert", "dateStr", "eventClick", "title", "eventColor", "events", "resourceId", "resources", "id", "groupId", "groupdId", "ReactDOM", "render", "StrictMode", "document", "getElementById"], "mappings": "8PAUeA,MARf,SAAmBC,GACjB,OACE,mCACE,sCCkESC,MA9Df,WAUE,OACE,cAAC,IAAD,CACEC,QAAS,CAACC,IAAmBC,KAC7BC,oBAAoB,6CACpBC,cAAe,CACbC,MAAO,GACPC,OAAQ,QACRC,IAAK,mBAEPC,YAAY,mBACZC,UAAU,EACVC,WAAW,EACXC,qBAAsB,GACtBC,aAAc,cAAC,EAAD,IACdC,UAvBoB,SAACC,GACvBC,MAAM,eAAiBD,EAAKE,UAuB1BC,WArBe,SAACH,GAClBC,MAAM,eAAD,OACYD,EAAKhB,MAAMoB,MADvB,iBACqCJ,EAAKhB,MAAMO,MADhD,eAC4DS,EAAKhB,MAAMS,OAoB1EY,WAAY,OACZC,OAAQ,CACN,CACEF,MAAO,UACPG,WAAY,IACZhB,MAAO,mBACPE,IAAK,oBAEP,CACEW,MAAO,UACPG,WAAY,IACZhB,MAAO,mBACPE,IAAK,qBAGTe,UAAW,CACT,CACEC,GAAI,IACJC,QAAS,IACTN,MAAO,cAET,CACEK,GAAI,IACJE,SAAU,IACVP,MAAO,cAET,CACEK,GAAI,IACJC,QAAS,IACTN,MAAO,kBC3DjBQ,IAASC,OACP,cAAC,IAAMC,WAAP,UACE,cAAC,EAAD,MAEFC,SAASC,eAAe,W", "file": "static/js/main.44a626d4.chunk.js", "sourcesContent": ["import React from \"react\";\r\n\r\nfunction EventNode(event) {\r\n  return (\r\n    <>\r\n      <b>Hi</b>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default EventNode;\r\n", "import React from \"react\";\nimport \"./App.css\";\n\nimport FullCalendar from \"@fullcalendar/react\";\nimport resourceTimelinePlugin from \"@fullcalendar/resource-timeline\";\nimport interactionPlugin, { Draggable } from \"@fullcalendar/interaction\";\n\nimport EventNode from \"./components/EventNode\";\n\nfunction App() {\n  const handleDateClick = (info) => {\n    alert(\"Clicked on: \" + info.dateStr);\n  };\n  const eventClick = (info) => {\n    alert(\n      `Clicked on: ${info.event.title} from ${info.event.start} to ${info.event.end}`\n    );\n  };\n\n  return (\n    <FullCalendar\n      plugins={[interactionPlugin, resourceTimelinePlugin]}\n      schedulerLicenseKey=\"CC-Attribution-NonCommercial-NoDerivatives\"\n      headerToolbar={{\n        start: \"\",\n        center: \"title\",\n        end: \"today prev next\",\n      }}\n      initialView=\"resourceTimeline\"\n      editable={true}\n      droppable={true}\n      eventDragMinDistance={50}\n      eventContent={<EventNode />}\n      dateClick={handleDateClick}\n      eventClick={eventClick}\n      eventColor={\"blue\"}\n      events={[\n        {\n          title: \"event 1\",\n          resourceId: \"a\",\n          start: \"2021-01-03T14:30\",\n          end: \"2021-01-03T16:30\",\n        },\n        {\n          title: \"event 2\",\n          resourceId: \"b\",\n          start: \"2021-01-03T14:30\",\n          end: \"2021-01-03T17:30\",\n        },\n      ]}\n      resources={[\n        {\n          id: \"a\",\n          groupId: \"1\",\n          title: \"Resource A\",\n        },\n        {\n          id: \"b\",\n          groupdId: \"1\",\n          title: \"Resource B\",\n        },\n        {\n          id: \"c\",\n          groupId: \"2\",\n          title: \"Resource C\",\n        },\n      ]}\n    />\n  );\n}\n\nexport default App;\n", "import React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport \"./index.css\";\nimport App from \"./App\";\n\nReactDOM.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>,\n  document.getElementById(\"root\")\n);\n"], "sourceRoot": ""}