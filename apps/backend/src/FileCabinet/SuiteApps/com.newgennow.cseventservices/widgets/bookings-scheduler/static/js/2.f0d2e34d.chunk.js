/*! For license information please see 2.f0d2e34d.chunk.js.LICENSE.txt */
(this["webpackJsonpbookings-scheduler"]=this["webpackJsonpbookings-scheduler"]||[]).push([[2],[function(e,t,n){"use strict";n.d(t,"o",(function(){return c})),n.d(t,"ib",(function(){return s})),n.d(t,"ob",(function(){return u})),n.d(t,"a",(function(){return $e})),n.d(t,"b",(function(){return lr})),n.d(t,"c",(function(){return bi})),n.d(t,"d",(function(){return cn})),n.d(t,"e",(function(){return Ho})),n.d(t,"f",(function(){return wo})),n.d(t,"g",(function(){return zo})),n.d(t,"h",(function(){return Tr})),n.d(t,"i",(function(){return Er})),n.d(t,"j",(function(){return lo})),n.d(t,"k",(function(){return Oo})),n.d(t,"l",(function(){return er})),n.d(t,"m",(function(){return Qn})),n.d(t,"n",(function(){return dn})),n.d(t,"p",(function(){return Co})),n.d(t,"q",(function(){return Mr})),n.d(t,"r",(function(){return vi})),n.d(t,"s",(function(){return qo})),n.d(t,"t",(function(){return Kn})),n.d(t,"u",(function(){return Ko})),n.d(t,"v",(function(){return kr})),n.d(t,"w",(function(){return Jn})),n.d(t,"x",(function(){return Qo})),n.d(t,"y",(function(){return Xo})),n.d(t,"z",(function(){return _n})),n.d(t,"A",(function(){return pi})),n.d(t,"B",(function(){return Fo})),n.d(t,"C",(function(){return Vo})),n.d(t,"D",(function(){return or})),n.d(t,"E",(function(){return Nr})),n.d(t,"F",(function(){return tr})),n.d(t,"G",(function(){return F})),n.d(t,"H",(function(){return V})),n.d(t,"I",(function(){return j})),n.d(t,"J",(function(){return P})),n.d(t,"K",(function(){return tn})),n.d(t,"L",(function(){return b})),n.d(t,"M",(function(){return Se})),n.d(t,"N",(function(){return Ce})),n.d(t,"O",(function(){return Re})),n.d(t,"P",(function(){return De})),n.d(t,"Q",(function(){return Pr})),n.d(t,"R",(function(){return pn})),n.d(t,"S",(function(){return qt})),n.d(t,"T",(function(){return Un})),n.d(t,"U",(function(){return pe})),n.d(t,"V",(function(){return vt})),n.d(t,"W",(function(){return _})),n.d(t,"X",(function(){return z})),n.d(t,"Y",(function(){return de})),n.d(t,"Z",(function(){return Gn})),n.d(t,"ab",(function(){return Uo})),n.d(t,"bb",(function(){return Yn})),n.d(t,"cb",(function(){return Xn})),n.d(t,"db",(function(){return Jo})),n.d(t,"eb",(function(){return Rt})),n.d(t,"fb",(function(){return To})),n.d(t,"gb",(function(){return Mn})),n.d(t,"hb",(function(){return ye})),n.d(t,"jb",(function(){return st})),n.d(t,"kb",(function(){return ne})),n.d(t,"lb",(function(){return ht})),n.d(t,"mb",(function(){return Xe})),n.d(t,"nb",(function(){return wr})),n.d(t,"pb",(function(){return kt})),n.d(t,"qb",(function(){return In})),n.d(t,"rb",(function(){return G})),n.d(t,"sb",(function(){return T})),n.d(t,"tb",(function(){return h})),n.d(t,"ub",(function(){return v})),n.d(t,"vb",(function(){return x})),n.d(t,"wb",(function(){return at})),n.d(t,"xb",(function(){return ie})),n.d(t,"yb",(function(){return m})),n.d(t,"zb",(function(){return g})),n.d(t,"Ab",(function(){return L})),n.d(t,"Bb",(function(){return Te})),n.d(t,"Cb",(function(){return ti})),n.d(t,"Db",(function(){return jn})),n.d(t,"Eb",(function(){return Zn})),n.d(t,"Fb",(function(){return Ln})),n.d(t,"Gb",(function(){return An})),n.d(t,"Hb",(function(){return en})),n.d(t,"Ib",(function(){return Ht})),n.d(t,"Jb",(function(){return Fn})),n.d(t,"Kb",(function(){return Pn})),n.d(t,"Lb",(function(){return lt})),n.d(t,"Mb",(function(){return li})),n.d(t,"Nb",(function(){return Vn})),n.d(t,"Ob",(function(){return si})),n.d(t,"Pb",(function(){return ei})),n.d(t,"Qb",(function(){return Vt})),n.d(t,"Rb",(function(){return zn})),n.d(t,"Sb",(function(){return di})),n.d(t,"Tb",(function(){return ci})),n.d(t,"Ub",(function(){return Oe})),n.d(t,"Vb",(function(){return O})),n.d(t,"Wb",(function(){return ai})),n.d(t,"Xb",(function(){return ot})),n.d(t,"Yb",(function(){return ko})),n.d(t,"Zb",(function(){return Ro})),n.d(t,"ac",(function(){return xt})),n.d(t,"bc",(function(){return Tn})),n.d(t,"cc",(function(){return Me})),n.d(t,"dc",(function(){return ri})),n.d(t,"ec",(function(){return Xt})),n.d(t,"fc",(function(){return U})),n.d(t,"gc",(function(){return hr})),n.d(t,"hc",(function(){return ue})),n.d(t,"ic",(function(){return gr})),n.d(t,"jc",(function(){return ee})),n.d(t,"kc",(function(){return ae})),n.d(t,"lc",(function(){return Pe})),n.d(t,"mc",(function(){return je})),n.d(t,"nc",(function(){return Ne})),n.d(t,"oc",(function(){return Ie})),n.d(t,"pc",(function(){return ut})),n.d(t,"qc",(function(){return Ee})),n.d(t,"rc",(function(){return A})),n.d(t,"sc",(function(){return kn})),n.d(t,"tc",(function(){return dt})),n.d(t,"uc",(function(){return Mo})),n.d(t,"vc",(function(){return Ct})),n.d(t,"wc",(function(){return N})),n.d(t,"xc",(function(){return On})),n.d(t,"yc",(function(){return I})),n.d(t,"zc",(function(){return M})),n.d(t,"Ac",(function(){return jt})),n.d(t,"Bc",(function(){return It})),n.d(t,"Cc",(function(){return Mt})),n.d(t,"Dc",(function(){return Pt})),n.d(t,"Ec",(function(){return Et})),n.d(t,"Fc",(function(){return rt})),n.d(t,"Gc",(function(){return p})),n.d(t,"Hc",(function(){return ni})),n.d(t,"Ic",(function(){return yi})),n.d(t,"Jc",(function(){return oi})),n.d(t,"Kc",(function(){return ui})),n.d(t,"Lc",(function(){return to})),n.d(t,"Mc",(function(){return ii})),n.d(t,"Nc",(function(){return cr})),n.d(t,"Oc",(function(){return zt})),n.d(t,"Pc",(function(){return Y})),n.d(t,"Qc",(function(){return xn})),n.d(t,"Rc",(function(){return Kt})),n.d(t,"Sc",(function(){return $n})),n.d(t,"Tc",(function(){return R})),n.d(t,"Uc",(function(){return ke}));n(15);var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function o(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;function a(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),o=0;for(t=0;t<n;t++)for(var i=arguments[t],a=0,l=i.length;a<l;a++,o++)r[o]=i[a];return r}Object.create;if("undefined"===typeof FullCalendarVDom)throw new Error("Please import the top-level fullcalendar lib before attempting to import a plugin.");var l=FullCalendarVDom.Component,s=FullCalendarVDom.createElement,u=(FullCalendarVDom.render,FullCalendarVDom.createRef),c=FullCalendarVDom.Fragment,d=FullCalendarVDom.createContext,f=(FullCalendarVDom.flushToDom,FullCalendarVDom.unmountComponentAtNode,function(){function e(e,t){this.context=e,this.internalEventSource=t}return e.prototype.remove=function(){this.context.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:this.internalEventSource.sourceId})},e.prototype.refetch=function(){this.context.dispatch({type:"FETCH_EVENT_SOURCES",sourceIds:[this.internalEventSource.sourceId]})},Object.defineProperty(e.prototype,"id",{get:function(){return this.internalEventSource.publicId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"url",{get:function(){return this.internalEventSource.meta.url},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"format",{get:function(){return this.internalEventSource.meta.format},enumerable:!1,configurable:!0}),e}());function p(e){e.parentNode&&e.parentNode.removeChild(e)}function h(e,t){if(e.closest)return e.closest(t);if(!document.documentElement.contains(e))return null;do{if(v(e,t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}function v(e,t){return(e.matches||e.matchesSelector||e.msMatchesSelector).call(e,t)}function g(e,t){for(var n=e instanceof HTMLElement?[e]:e,r=[],o=0;o<n.length;o+=1)for(var i=n[o].querySelectorAll(t),a=0;a<i.length;a+=1)r.push(i[a]);return r}function m(e,t){for(var n=e instanceof HTMLElement?[e]:e,r=[],o=0;o<n.length;o+=1)for(var i=n[o].children,a=0;a<i.length;a+=1){var l=i[a];t&&!v(l,t)||r.push(l)}return r}var y=/(top|left|right|bottom|width|height)$/i;function b(e,t){for(var n in t)S(e,n,t[n])}function S(e,t,n){null==n?e.style[t]="":"number"===typeof n&&y.test(t)?e.style[t]=n+"px":e.style[t]=n}function E(e){e.preventDefault()}function w(e,t){return function(n){var r=h(n.target,e);r&&t.call(r,n,r)}}function C(e,t,n,r){var o=w(n,r);return e.addEventListener(t,o),function(){e.removeEventListener(t,o)}}var D=["webkitTransitionEnd","otransitionend","oTransitionEnd","msTransitionEnd","transitionend"];function R(e,t){var n=function n(r){t(r),D.forEach((function(t){e.removeEventListener(t,n)}))};D.forEach((function(t){e.addEventListener(t,n)}))}var k=0;function O(){return String(k+=1)}function T(){document.body.classList.add("fc-not-allowed")}function x(){document.body.classList.remove("fc-not-allowed")}function M(e){e.classList.add("fc-unselectable"),e.addEventListener("selectstart",E)}function P(e){e.classList.remove("fc-unselectable"),e.removeEventListener("selectstart",E)}function I(e){e.addEventListener("contextmenu",E)}function j(e){e.removeEventListener("contextmenu",E)}function N(e){var t,n,r=[],o=[];for("string"===typeof e?o=e.split(/\s*,\s*/):"function"===typeof e?o=[e]:Array.isArray(e)&&(o=e),t=0;t<o.length;t+=1)"string"===typeof(n=o[t])?r.push("-"===n.charAt(0)?{field:n.substring(1),order:-1}:{field:n,order:1}):"function"===typeof n&&r.push({func:n});return r}function _(e,t,n){var r,o;for(r=0;r<n.length;r+=1)if(o=H(e,t,n[r]))return o;return 0}function H(e,t,n){return n.func?n.func(e,t):L(e[n.field],t[n.field])*(n.order||1)}function L(e,t){return e||t?null==t?-1:null==e?1:"string"===typeof e||"string"===typeof t?String(e).localeCompare(String(t)):e-t:0}function A(e,t){var n=String(e);return"000".substr(0,t-n.length)+n}function z(e,t){return e-t}function U(e){return e%1===0}function W(e){var t=e.querySelector(".fc-scrollgrid-shrink-frame"),n=e.querySelector(".fc-scrollgrid-shrink-cushion");if(!t)throw new Error("needs fc-scrollgrid-shrink-frame className");if(!n)throw new Error("needs fc-scrollgrid-shrink-cushion className");return e.getBoundingClientRect().width-t.getBoundingClientRect().width+n.getBoundingClientRect().width}var B=["sun","mon","tue","wed","thu","fri","sat"];function F(e,t){var n=K(e);return n[2]+=t,J(n)}function V(e,t){var n=K(e);return n[6]+=t,J(n)}function q(e,t){return(t.valueOf()-e.valueOf())/864e5}function G(e,t){return te(e)===te(t)?Math.round(q(e,t)):null}function Y(e){return J([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()])}function X(e,t,n,r){var o=J([t,0,1+Z(t,n,r)]),i=Y(e),a=Math.round(q(o,i));return Math.floor(a/7)+1}function Z(e,t,n){var r=7+t-n;return-((7+J([e,0,r]).getUTCDay()-t)%7)+r-1}function $(e){return[e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()]}function Q(e){return new Date(e[0],e[1]||0,null==e[2]?1:e[2],e[3]||0,e[4]||0,e[5]||0)}function K(e){return[e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()]}function J(e){return 1===e.length&&(e=e.concat([0])),new Date(Date.UTC.apply(Date,e))}function ee(e){return!isNaN(e.valueOf())}function te(e){return 1e3*e.getUTCHours()*60*60+1e3*e.getUTCMinutes()*60+1e3*e.getUTCSeconds()+e.getUTCMilliseconds()}function ne(e,t,n,r){return{instanceId:O(),defId:e,range:t,forcedStartTzo:null==n?null:n,forcedEndTzo:null==r?null:r}}var re=Object.prototype.hasOwnProperty;function oe(e,t){var n={};if(t)for(var r in t){for(var o=[],i=e.length-1;i>=0;i-=1){var a=e[i][r];if("object"===typeof a&&a)o.unshift(a);else if(void 0!==a){n[r]=a;break}}o.length&&(n[r]=oe(o))}for(i=e.length-1;i>=0;i-=1){var l=e[i];for(var s in l)s in n||(n[s]=l[s])}return n}function ie(e,t){var n={};for(var r in e)t(e[r],r)&&(n[r]=e[r]);return n}function ae(e,t){var n={};for(var r in e)n[r]=t(e[r],r);return n}function le(e){for(var t={},n=0,r=e;n<r.length;n++){t[r[n]]=!0}return t}function se(e){var t=[];for(var n in e)t.push(e[n]);return t}function ue(e,t){if(e===t)return!0;for(var n in e)if(re.call(e,n)&&!(n in t))return!1;for(var n in t)if(re.call(t,n)&&e[n]!==t[n])return!1;return!0}function ce(e,t){var n=[];for(var r in e)re.call(e,r)&&(r in t||n.push(r));for(var r in t)re.call(t,r)&&e[r]!==t[r]&&n.push(r);return n}function de(e,t,n){if(void 0===n&&(n={}),e===t)return!0;for(var r in t)if(!(r in e)||!fe(e[r],t[r],n[r]))return!1;for(var r in e)if(!(r in t))return!1;return!0}function fe(e,t,n){return e===t||!0===n||!!n&&n(e,t)}function pe(e,t,n,r){void 0===t&&(t=0),void 0===r&&(r=1);var o=[];null==n&&(n=Object.keys(e).length);for(var i=t;i<n;i+=r){var a=e[i];void 0!==a&&o.push(a)}return o}function he(e,t,n){var r=n.dateEnv,o=n.pluginHooks,i=n.options,a=e.defs,l=e.instances;for(var s in l=ie(l,(function(e){return!a[e.defId].recurringDef})),a){var u=a[s];if(u.recurringDef){var c=u.recurringDef.duration;c||(c=u.allDay?i.defaultAllDayEventDuration:i.defaultTimedEventDuration);for(var d=0,f=ve(u,c,t,r,o.recurringTypes);d<f.length;d++){var p=f[d],h=ne(s,{start:p,end:r.add(p,c)});l[h.instanceId]=h}}}return{defs:a,instances:l}}function ve(e,t,n,r,o){var i=o[e.recurringDef.typeId].expand(e.recurringDef.typeData,{start:r.subtract(n.start,t),end:n.end},r);return e.allDay&&(i=i.map(Y)),i}var ge=["years","months","days","milliseconds"],me=/^(-?)(?:(\d+)\.)?(\d+):(\d\d)(?::(\d\d)(?:\.(\d\d\d))?)?/;function ye(e,t){var n;return"string"===typeof e?function(e){var t=me.exec(e);if(t){var n=t[1]?-1:1;return{years:0,months:0,days:n*(t[2]?parseInt(t[2],10):0),milliseconds:n*(60*(t[3]?parseInt(t[3],10):0)*60*1e3+60*(t[4]?parseInt(t[4],10):0)*1e3+1e3*(t[5]?parseInt(t[5],10):0)+(t[6]?parseInt(t[6],10):0))}}return null}(e):"object"===typeof e&&e?be(e):"number"===typeof e?be(((n={})[t||"milliseconds"]=e,n)):null}function be(e){var t={years:e.years||e.year||0,months:e.months||e.month||0,days:e.days||e.day||0,milliseconds:60*(e.hours||e.hour||0)*60*1e3+60*(e.minutes||e.minute||0)*1e3+1e3*(e.seconds||e.second||0)+(e.milliseconds||e.millisecond||e.ms||0)},n=e.weeks||e.week;return n&&(t.days+=7*n,t.specifiedWeeks=!0),t}function Se(e){return e.years||e.months||e.milliseconds?0:e.days}function Ee(e,t){return{years:e.years*t,months:e.months*t,days:e.days*t,milliseconds:e.milliseconds*t}}function we(e){return Re(e)/864e5}function Ce(e){return Re(e)/6e4}function De(e){return Re(e)/1e3}function Re(e){return 31536e6*e.years+2592e6*e.months+864e5*e.days+e.milliseconds}function ke(e,t){for(var n=null,r=0;r<ge.length;r+=1){var o=ge[r];if(t[o]){var i=e[o]/t[o];if(!U(i)||null!==n&&n!==i)return null;n=i}else if(e[o])return null}return n}function Oe(e){var t=e.milliseconds;if(t){if(t%1e3!==0)return{unit:"millisecond",value:t};if(t%6e4!==0)return{unit:"second",value:t/1e3};if(t%36e5!==0)return{unit:"minute",value:t/6e4};if(t)return{unit:"hour",value:t/36e5}}return e.days?e.specifiedWeeks&&e.days%7===0?{unit:"week",value:e.days/7}:{unit:"day",value:e.days}:e.months?{unit:"month",value:e.months}:e.years?{unit:"year",value:e.years}:{unit:"millisecond",value:0}}function Te(e){return e.toISOString().replace(/T.*$/,"")}function xe(e,t){void 0===t&&(t=!1);var n=e<0?"-":"+",r=Math.abs(e),o=Math.floor(r/60),i=Math.round(r%60);return t?n+A(o,2)+":"+A(i,2):"GMT"+n+o+(i?":"+A(i,2):"")}function Me(e,t,n){if(e===t)return!0;var r,o=e.length;if(o!==t.length)return!1;for(r=0;r<o;r+=1)if(!(n?n(e[r],t[r]):e[r]===t[r]))return!1;return!0}function Pe(e,t,n){var r,o;return function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];if(r){if(!Me(r,i)){n&&n(o);var l=e.apply(this,i);t&&t(l,o)||(o=l)}}else o=e.apply(this,i);return r=i,o}}function Ie(e,t,n){var r,o,i=this;return function(a){if(r){if(!ue(r,a)){n&&n(o);var l=e.call(i,a);t&&t(l,o)||(o=l)}}else o=e.call(i,a);return r=a,o}}function je(e,t,n){var r=this,o=[],i=[];return function(a){for(var l=o.length,s=a.length,u=0;u<l;u+=1)if(a[u]){if(!Me(o[u],a[u])){n&&n(i[u]);var c=e.apply(r,a[u]);t&&t(c,i[u])||(i[u]=c)}}else n&&n(i[u]);for(;u<s;u+=1)i[u]=e.apply(r,a[u]);return o=a,i.splice(s),i}}function Ne(e,t,n){var r=this,o={},i={};return function(a){var l={};for(var s in a)if(i[s])if(Me(o[s],a[s]))l[s]=i[s];else{n&&n(i[s]);var u=e.apply(r,a[s]);l[s]=t&&t(u,i[s])?i[s]:u}else l[s]=e.apply(r,a[s]);return o=a,i=l,l}}var _e={week:3,separator:0,omitZeroMinute:0,meridiem:0,omitCommas:0},He={timeZoneName:7,era:6,year:5,month:4,day:2,weekday:2,hour:1,minute:1,second:1},Le=/\s*([ap])\.?m\.?/i,Ae=/,/g,ze=/\s+/g,Ue=/\u200e/g,We=/UTC|GMT/,Be=function(){function e(e){var t={},n={},r=0;for(var o in e)o in _e?(n[o]=e[o],r=Math.max(_e[o],r)):(t[o]=e[o],o in He&&(r=Math.max(He[o],r)));this.standardDateProps=t,this.extendedSettings=n,this.severity=r,this.buildFormattingFunc=Pe(Fe)}return e.prototype.format=function(e,t){return this.buildFormattingFunc(this.standardDateProps,this.extendedSettings,t)(e)},e.prototype.formatRange=function(e,t,n,r){var o=this.standardDateProps,i=this.extendedSettings,a=function(e,t,n){if(n.getMarkerYear(e)!==n.getMarkerYear(t))return 5;if(n.getMarkerMonth(e)!==n.getMarkerMonth(t))return 4;if(n.getMarkerDay(e)!==n.getMarkerDay(t))return 2;if(te(e)!==te(t))return 1;return 0}(e.marker,t.marker,n.calendarSystem);if(!a)return this.format(e,n);var l=a;!(l>1)||"numeric"!==o.year&&"2-digit"!==o.year||"numeric"!==o.month&&"2-digit"!==o.month||"numeric"!==o.day&&"2-digit"!==o.day||(l=1);var s=this.format(e,n),u=this.format(t,n);if(s===u)return s;var c=Fe(function(e,t){var n={};for(var r in e)(!(r in He)||He[r]<=t)&&(n[r]=e[r]);return n}(o,l),i,n),d=c(e),f=c(t),p=function(e,t,n,r){var o=0;for(;o<e.length;){var i=e.indexOf(t,o);if(-1===i)break;var a=e.substr(0,i);o=i+t.length;for(var l=e.substr(o),s=0;s<n.length;){var u=n.indexOf(r,s);if(-1===u)break;var c=n.substr(0,u);s=u+r.length;var d=n.substr(s);if(a===c&&l===d)return{before:a,after:l}}}return null}(s,d,u,f),h=i.separator||r||n.defaultSeparator||"";return p?p.before+d+h+f+p.after:s+h+u},e.prototype.getLargestUnit=function(){switch(this.severity){case 7:case 6:case 5:return"year";case 4:return"month";case 3:return"week";case 2:return"day";default:return"time"}},e}();function Fe(e,t,n){var r=Object.keys(e).length;return 1===r&&"short"===e.timeZoneName?function(e){return xe(e.timeZoneOffset)}:0===r&&t.week?function(e){return function(e,t,n,r){var o=[];"narrow"===r?o.push(t):"short"===r&&o.push(t," ");o.push(n.simpleNumberFormat.format(e)),"rtl"===n.options.direction&&o.reverse();return o.join("")}(n.computeWeekNumber(e.marker),n.weekText,n.locale,t.week)}:function(e,t,n){e=i({},e),t=i({},t),function(e,t){e.timeZoneName&&(e.hour||(e.hour="2-digit"),e.minute||(e.minute="2-digit"));"long"===e.timeZoneName&&(e.timeZoneName="short");t.omitZeroMinute&&(e.second||e.millisecond)&&delete t.omitZeroMinute}(e,t),e.timeZone="UTC";var r,o=new Intl.DateTimeFormat(n.locale.codes,e);if(t.omitZeroMinute){var a=i({},e);delete a.minute,r=new Intl.DateTimeFormat(n.locale.codes,a)}return function(i){var a=i.marker;return function(e,t,n,r,o){e=e.replace(Ue,""),"short"===n.timeZoneName&&(e=function(e,t){var n=!1;e=e.replace(We,(function(){return n=!0,t})),n||(e+=" "+t);return e}(e,"UTC"===o.timeZone||null==t.timeZoneOffset?"UTC":xe(t.timeZoneOffset)));r.omitCommas&&(e=e.replace(Ae,"").trim());r.omitZeroMinute&&(e=e.replace(":00",""));!1===r.meridiem?e=e.replace(Le,"").trim():"narrow"===r.meridiem?e=e.replace(Le,(function(e,t){return t.toLocaleLowerCase()})):"short"===r.meridiem?e=e.replace(Le,(function(e,t){return t.toLocaleLowerCase()+"m"})):"lowercase"===r.meridiem&&(e=e.replace(Le,(function(e){return e.toLocaleLowerCase()})));return e=(e=e.replace(ze," ")).trim()}((r&&!a.getUTCMinutes()?r:o).format(a),i,e,t,n)}}(e,t,n)}function Ve(e,t){var n=t.markerToArray(e.marker);return{marker:e.marker,timeZoneOffset:e.timeZoneOffset,array:n,year:n[0],month:n[1],day:n[2],hour:n[3],minute:n[4],second:n[5],millisecond:n[6]}}function qe(e,t,n,r){var o=Ve(e,n.calendarSystem);return{date:o,start:o,end:t?Ve(t,n.calendarSystem):null,timeZone:n.timeZone,localeCodes:n.locale.codes,defaultSeparator:r||n.defaultSeparator}}var Ge=function(){function e(e){this.cmdStr=e}return e.prototype.format=function(e,t,n){return t.cmdFormatter(this.cmdStr,qe(e,null,t,n))},e.prototype.formatRange=function(e,t,n,r){return n.cmdFormatter(this.cmdStr,qe(e,t,n,r))},e}(),Ye=function(){function e(e){this.func=e}return e.prototype.format=function(e,t,n){return this.func(qe(e,null,t,n))},e.prototype.formatRange=function(e,t,n,r){return this.func(qe(e,t,n,r))},e}();function Xe(e){return"object"===typeof e&&e?new Be(e):"string"===typeof e?new Ge(e):"function"===typeof e?new Ye(e):null}var Ze={navLinkDayClick:ot,navLinkWeekClick:ot,duration:ye,bootstrapFontAwesome:ot,buttonIcons:ot,customButtons:ot,defaultAllDayEventDuration:ye,defaultTimedEventDuration:ye,nextDayThreshold:ye,scrollTime:ye,slotMinTime:ye,slotMaxTime:ye,dayPopoverFormat:Xe,slotDuration:ye,snapDuration:ye,headerToolbar:ot,footerToolbar:ot,defaultRangeSeparator:String,titleRangeSeparator:String,forceEventDuration:Boolean,dayHeaders:Boolean,dayHeaderFormat:Xe,dayHeaderClassNames:ot,dayHeaderContent:ot,dayHeaderDidMount:ot,dayHeaderWillUnmount:ot,dayCellClassNames:ot,dayCellContent:ot,dayCellDidMount:ot,dayCellWillUnmount:ot,initialView:String,aspectRatio:Number,weekends:Boolean,weekNumberCalculation:ot,weekNumbers:Boolean,weekNumberClassNames:ot,weekNumberContent:ot,weekNumberDidMount:ot,weekNumberWillUnmount:ot,editable:Boolean,viewClassNames:ot,viewDidMount:ot,viewWillUnmount:ot,nowIndicator:Boolean,nowIndicatorClassNames:ot,nowIndicatorContent:ot,nowIndicatorDidMount:ot,nowIndicatorWillUnmount:ot,showNonCurrentDates:Boolean,lazyFetching:Boolean,startParam:String,endParam:String,timeZoneParam:String,timeZone:String,locales:ot,locale:ot,themeSystem:String,dragRevertDuration:Number,dragScroll:Boolean,allDayMaintainDuration:Boolean,unselectAuto:Boolean,dropAccept:ot,eventOrder:N,handleWindowResize:Boolean,windowResizeDelay:Number,longPressDelay:Number,eventDragMinDistance:Number,expandRows:Boolean,height:ot,contentHeight:ot,direction:String,weekNumberFormat:Xe,eventResizableFromStart:Boolean,displayEventTime:Boolean,displayEventEnd:Boolean,weekText:String,progressiveEventRendering:Boolean,businessHours:ot,initialDate:ot,now:ot,eventDataTransform:ot,stickyHeaderDates:ot,stickyFooterScrollbar:ot,viewHeight:ot,defaultAllDay:Boolean,eventSourceFailure:ot,eventSourceSuccess:ot,eventDisplay:String,eventStartEditable:Boolean,eventDurationEditable:Boolean,eventOverlap:ot,eventConstraint:ot,eventAllow:ot,eventBackgroundColor:String,eventBorderColor:String,eventTextColor:String,eventColor:String,eventClassNames:ot,eventContent:ot,eventDidMount:ot,eventWillUnmount:ot,selectConstraint:ot,selectOverlap:ot,selectAllow:ot,droppable:Boolean,unselectCancel:String,slotLabelFormat:ot,slotLaneClassNames:ot,slotLaneContent:ot,slotLaneDidMount:ot,slotLaneWillUnmount:ot,slotLabelClassNames:ot,slotLabelContent:ot,slotLabelDidMount:ot,slotLabelWillUnmount:ot,dayMaxEvents:ot,dayMaxEventRows:ot,dayMinWidth:Number,slotLabelInterval:ye,allDayText:String,allDayClassNames:ot,allDayContent:ot,allDayDidMount:ot,allDayWillUnmount:ot,slotMinWidth:Number,navLinks:Boolean,eventTimeFormat:Xe,rerenderDelay:Number,moreLinkText:ot,selectMinDistance:Number,selectable:Boolean,selectLongPressDelay:Number,eventLongPressDelay:Number,selectMirror:Boolean,eventMinHeight:Number,slotEventOverlap:Boolean,plugins:ot,firstDay:Number,dayCount:Number,dateAlignment:String,dateIncrement:ye,hiddenDays:ot,monthMode:Boolean,fixedWeekCount:Boolean,validRange:ot,visibleRange:ot,titleFormat:ot,noEventsText:String},$e={eventDisplay:"auto",defaultRangeSeparator:" - ",titleRangeSeparator:" \u2013 ",defaultTimedEventDuration:"01:00:00",defaultAllDayEventDuration:{day:1},forceEventDuration:!1,nextDayThreshold:"00:00:00",dayHeaders:!0,initialView:"",aspectRatio:1.35,headerToolbar:{start:"title",center:"",end:"today prev,next"},weekends:!0,weekNumbers:!1,weekNumberCalculation:"local",editable:!1,nowIndicator:!1,scrollTime:"06:00:00",slotMinTime:"00:00:00",slotMaxTime:"24:00:00",showNonCurrentDates:!0,lazyFetching:!0,startParam:"start",endParam:"end",timeZoneParam:"timeZone",timeZone:"local",locales:[],locale:"",themeSystem:"standard",dragRevertDuration:500,dragScroll:!0,allDayMaintainDuration:!1,unselectAuto:!0,dropAccept:"*",eventOrder:"start,-duration,allDay,title",dayPopoverFormat:{month:"long",day:"numeric",year:"numeric"},handleWindowResize:!0,windowResizeDelay:100,longPressDelay:1e3,eventDragMinDistance:5,expandRows:!1,navLinks:!1,selectable:!1},Qe={datesSet:ot,eventsSet:ot,eventAdd:ot,eventChange:ot,eventRemove:ot,windowResize:ot,eventClick:ot,eventMouseEnter:ot,eventMouseLeave:ot,select:ot,unselect:ot,loading:ot,_unmount:ot,_beforeprint:ot,_afterprint:ot,_noEventDrop:ot,_noEventResize:ot,_resize:ot,_scrollRequest:ot},Ke={buttonText:ot,views:ot,plugins:ot,initialEvents:ot,events:ot,eventSources:ot},Je={headerToolbar:et,footerToolbar:et,buttonText:et,buttonIcons:et};function et(e,t){return"object"===typeof e&&"object"===typeof t&&e&&t?ue(e,t):e===t}var tt={type:String,component:ot,buttonText:String,buttonTextKey:String,dateProfileGeneratorClass:ot,usesMinMaxTime:Boolean,classNames:ot,content:ot,didMount:ot,willUnmount:ot};function nt(e){return oe(e,Je)}function rt(e,t){var n={},r={};for(var o in t)o in e&&(n[o]=t[o](e[o]));for(var o in e)o in t||(r[o]=e[o]);return{refined:n,extra:r}}function ot(e){return e}function it(e,t,n,r){for(var o={defs:{},instances:{}},i=wt(n),a=0,l=e;a<l.length;a++){var s=St(l[a],t,n,r,i);s&&at(s,o)}return o}function at(e,t){return void 0===t&&(t={defs:{},instances:{}}),t.defs[e.def.defId]=e.def,e.instance&&(t.instances[e.instance.instanceId]=e.instance),t}function lt(e,t){var n=e.instances[t];if(n){var r=e.defs[n.defId],o=ct(e,(function(e){return t=r,n=e,Boolean(t.groupId&&t.groupId===n.groupId);var t,n}));return o.defs[r.defId]=r,o.instances[n.instanceId]=n,o}return{defs:{},instances:{}}}function st(){return{defs:{},instances:{}}}function ut(e,t){return{defs:i(i({},e.defs),t.defs),instances:i(i({},e.instances),t.instances)}}function ct(e,t){var n=ie(e.defs,t),r=ie(e.instances,(function(e){return n[e.defId]}));return{defs:n,instances:r}}function dt(e){return Array.isArray(e)?e:"string"===typeof e?e.split(/\s+/):[]}var ft={display:String,editable:Boolean,startEditable:Boolean,durationEditable:Boolean,constraint:ot,overlap:ot,allow:ot,className:dt,classNames:dt,color:String,backgroundColor:String,borderColor:String,textColor:String},pt={display:null,startEditable:null,durationEditable:null,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]};function ht(e,t){var n=function(e,t){return Array.isArray(e)?it(e,null,t,!0):"object"===typeof e&&e?it([e],null,t,!0):null!=e?String(e):null}(e.constraint,t);return{display:e.display||null,startEditable:null!=e.startEditable?e.startEditable:e.editable,durationEditable:null!=e.durationEditable?e.durationEditable:e.editable,constraints:null!=n?[n]:[],overlap:null!=e.overlap?e.overlap:null,allows:null!=e.allow?[e.allow]:[],backgroundColor:e.backgroundColor||e.color||"",borderColor:e.borderColor||e.color||"",textColor:e.textColor||"",classNames:(e.className||[]).concat(e.classNames||[])}}function vt(e){return e.reduce(gt,pt)}function gt(e,t){return{display:null!=t.display?t.display:e.display,startEditable:null!=t.startEditable?t.startEditable:e.startEditable,durationEditable:null!=t.durationEditable?t.durationEditable:e.durationEditable,constraints:e.constraints.concat(t.constraints),overlap:"boolean"===typeof t.overlap?t.overlap:e.overlap,allows:e.allows.concat(t.allows),backgroundColor:t.backgroundColor||e.backgroundColor,borderColor:t.borderColor||e.borderColor,textColor:t.textColor||e.textColor,classNames:e.classNames.concat(t.classNames)}}var mt={id:String,groupId:String,title:String,url:String},yt={start:ot,end:ot,date:ot,allDay:Boolean},bt=i(i(i({},mt),yt),{extendedProps:ot});function St(e,t,n,r,o){void 0===o&&(o=wt(n));var i=Et(e,n,o),a=i.refined,l=i.extra,s=function(e,t){var n=null;e&&(n=e.defaultAllDay);null==n&&(n=t.options.defaultAllDay);return n}(t,n),u=function(e,t,n,r){for(var o=0;o<r.length;o+=1){var i=r[o].parse(e,n);if(i){var a=e.allDay;return null==a&&null==(a=t)&&null==(a=i.allDayGuess)&&(a=!1),{allDay:a,duration:i.duration,typeData:i.typeData,typeId:o}}}return null}(a,s,n.dateEnv,n.pluginHooks.recurringTypes);if(u)return(c=Ct(a,l,t?t.sourceId:"",u.allDay,Boolean(u.duration),n)).recurringDef={typeId:u.typeId,typeData:u.typeData,duration:u.duration},{def:c,instance:null};var c,d=function(e,t,n,r){var o,i,a=e.allDay,l=null,s=!1,u=null,c=null!=e.start?e.start:e.date;if(o=n.dateEnv.createMarkerMeta(c))l=o.marker;else if(!r)return null;null!=e.end&&(i=n.dateEnv.createMarkerMeta(e.end));null==a&&(a=null!=t?t:(!o||o.isTimeUnspecified)&&(!i||i.isTimeUnspecified));a&&l&&(l=Y(l));i&&(u=i.marker,a&&(u=Y(u)),l&&u<=l&&(u=null));u?s=!0:r||(s=n.options.forceEventDuration||!1,u=n.dateEnv.add(l,a?n.options.defaultAllDayEventDuration:n.options.defaultTimedEventDuration));return{allDay:a,hasEnd:s,range:{start:l,end:u},forcedStartTzo:o?o.forcedTzo:null,forcedEndTzo:i?i.forcedTzo:null}}(a,s,n,r);return d?{def:c=Ct(a,l,t?t.sourceId:"",d.allDay,d.hasEnd,n),instance:ne(c.defId,d.range,d.forcedStartTzo,d.forcedEndTzo)}:null}function Et(e,t,n){return void 0===n&&(n=wt(t)),rt(e,n)}function wt(e){return i(i(i({},ft),bt),e.pluginHooks.eventRefiners)}function Ct(e,t,n,r,o,a){for(var l={title:e.title||"",groupId:e.groupId||"",publicId:e.id||"",url:e.url||"",recurringDef:null,defId:O(),sourceId:n,allDay:r,hasEnd:o,ui:ht(e,a),extendedProps:i(i({},e.extendedProps||{}),t)},s=0,u=a.pluginHooks.eventDefMemberAdders;s<u.length;s++){var c=u[s];i(l,c(e))}return Object.freeze(l.ui.classNames),Object.freeze(l.extendedProps),l}function Dt(e){var t=Math.floor(q(e.start,e.end))||1,n=Y(e.start);return{start:n,end:F(n,t)}}function Rt(e,t){void 0===t&&(t=ye(0));var n=null,r=null;if(e.end){r=Y(e.end);var o=e.end.valueOf()-r.valueOf();o&&o>=Re(t)&&(r=F(r,1))}return e.start&&(n=Y(e.start),r&&r<=n&&(r=F(n,1))),{start:n,end:r}}function kt(e,t,n,r){return"year"===r?ye(n.diffWholeYears(e,t),"year"):"month"===r?ye(n.diffWholeMonths(e,t),"month"):function(e,t){var n=Y(e),r=Y(t);return{years:0,months:0,days:Math.round(q(n,r)),milliseconds:t.valueOf()-r.valueOf()-(e.valueOf()-n.valueOf())}}(e,t)}function Ot(e,t){var n,r,o=[],i=t.start;for(e.sort(Tt),n=0;n<e.length;n+=1)(r=e[n]).start>i&&o.push({start:i,end:r.start}),r.end>i&&(i=r.end);return i<t.end&&o.push({start:i,end:t.end}),o}function Tt(e,t){return e.start.valueOf()-t.start.valueOf()}function xt(e,t){var n=e.start,r=e.end,o=null;return null!==t.start&&(n=null===n?t.start:new Date(Math.max(n.valueOf(),t.start.valueOf()))),null!=t.end&&(r=null===r?t.end:new Date(Math.min(r.valueOf(),t.end.valueOf()))),(null===n||null===r||n<r)&&(o={start:n,end:r}),o}function Mt(e,t){return(null===e.start?null:e.start.valueOf())===(null===t.start?null:t.start.valueOf())&&(null===e.end?null:e.end.valueOf())===(null===t.end?null:t.end.valueOf())}function Pt(e,t){return(null===e.end||null===t.start||e.end>t.start)&&(null===e.start||null===t.end||e.start<t.end)}function It(e,t){return(null===e.start||null!==t.start&&t.start>=e.start)&&(null===e.end||null!==t.end&&t.end<=e.end)}function jt(e,t){return(null===e.start||t>=e.start)&&(null===e.end||t<e.end)}function Nt(e,t,n,r){var o={},i={},a={},l=[],s=[],u=Lt(e.defs,t);for(var c in e.defs){"inverse-background"===(p=u[(S=e.defs[c]).defId]).display&&(S.groupId?(o[S.groupId]=[],a[S.groupId]||(a[S.groupId]=S)):i[c]=[])}for(var d in e.instances){var f=e.instances[d],p=u[(S=e.defs[f.defId]).defId],h=f.range,v=!S.allDay&&r?Rt(h,r):h,g=xt(v,n);g&&("inverse-background"===p.display?S.groupId?o[S.groupId].push(g):i[f.defId].push(g):"none"!==p.display&&("background"===p.display?l:s).push({def:S,ui:p,instance:f,range:g,isStart:v.start&&v.start.valueOf()===g.start.valueOf(),isEnd:v.end&&v.end.valueOf()===g.end.valueOf()}))}for(var m in o)for(var y=0,b=Ot(o[m],n);y<b.length;y++){var S,E=b[y];p=u[(S=a[m]).defId];l.push({def:S,ui:p,instance:null,range:E,isStart:!1,isEnd:!1})}for(var c in i)for(var w=0,C=Ot(i[c],n);w<C.length;w++){E=C[w];l.push({def:e.defs[c],ui:u[c],instance:null,range:E,isStart:!1,isEnd:!1})}return{bg:l,fg:s}}function _t(e,t){e.fcSeg=t}function Ht(e){return e.fcSeg||e.parentNode.fcSeg||null}function Lt(e,t){return ae(e,(function(e){return At(e,t)}))}function At(e,t){var n=[];return t[""]&&n.push(t[""]),t[e.defId]&&n.push(t[e.defId]),n.push(e.ui),vt(n)}function zt(e,t){var n=e.map(Ut);return n.sort((function(e,n){return _(e,n,t)})),n.map((function(e){return e._seg}))}function Ut(e){var t=e.eventRange,n=t.def,r=t.instance?t.instance.range:t.range,o=r.start?r.start.valueOf():0,a=r.end?r.end.valueOf():0;return i(i(i({},n.extendedProps),n),{id:n.publicId,start:o,end:a,duration:a-o,allDay:Number(n.allDay),_seg:e})}function Wt(e,t){for(var n=t.pluginHooks.isDraggableTransformers,r=e.eventRange,o=r.def,i=r.ui,a=i.startEditable,l=0,s=n;l<s.length;l++){a=(0,s[l])(a,o,i,t)}return a}function Bt(e,t){return e.isStart&&e.eventRange.ui.durationEditable&&t.options.eventResizableFromStart}function Ft(e,t){return e.isEnd&&e.eventRange.ui.durationEditable}function Vt(e,t,n){var r=e.eventRange.range;return{isPast:r.end<(n||t.start),isFuture:r.start>=(n||t.end),isToday:t&&jt(t,r.start)}}function qt(e){return e.instance?e.instance.instanceId:e.def.defId+":"+e.range.start.toISOString()}var Gt={start:ot,end:ot,allDay:Boolean};function Yt(e,t,n){var r=function(e,t){var n=rt(e,Gt),r=n.refined,o=n.extra,a=r.start?t.createMarkerMeta(r.start):null,l=r.end?t.createMarkerMeta(r.end):null,s=r.allDay;null==s&&(s=a&&a.isTimeUnspecified&&(!l||l.isTimeUnspecified));return i({range:{start:a?a.marker:null,end:l?l.marker:null},allDay:s},o)}(e,t),o=r.range;if(!o.start)return null;if(!o.end){if(null==n)return null;o.end=t.add(o.start,n)}return r}function Xt(e,t){return Mt(e.range,t.range)&&e.allDay===t.allDay&&function(e,t){for(var n in t)if("range"!==n&&"allDay"!==n&&e[n]!==t[n])return!1;for(var n in e)if(!(n in t))return!1;return!0}(e,t)}function Zt(e,t,n){return i(i({},$t(e,t,n)),{timeZone:t.timeZone})}function $t(e,t,n){return{start:t.toDate(e.start),end:t.toDate(e.end),startStr:t.formatIso(e.start,{omitTime:n}),endStr:t.formatIso(e.end,{omitTime:n})}}function Qt(e,t,n){var r=Et({editable:!1},n),o=Ct(r.refined,r.extra,"",e.allDay,!0,n);return{def:o,ui:At(o,t),instance:ne(o.defId,e.range),range:e.range,isStart:!0,isEnd:!0}}function Kt(e,t,n){n.emitter.trigger("select",i(i({},Jt(e,n)),{jsEvent:t?t.origEvent:null,view:n.viewApi||n.calendarApi.view}))}function Jt(e,t){for(var n,r,o={},a=0,l=t.pluginHooks.dateSpanTransforms;a<l.length;a++){var s=l[a];i(o,s(e,t))}return i(o,(n=e,r=t.dateEnv,i(i({},$t(n.range,r,n.allDay)),{allDay:n.allDay}))),o}function en(e,t,n){var r=n.dateEnv,o=n.options,i=t;return e?(i=Y(i),i=r.add(i,o.defaultAllDayEventDuration)):i=r.add(i,o.defaultTimedEventDuration),i}function tn(e,t,n,r){var o=Lt(e.defs,t),i={defs:{},instances:{}};for(var a in e.defs){var l=e.defs[a];i.defs[a]=nn(l,o[a],n,r)}for(var s in e.instances){var u=e.instances[s];l=i.defs[u.defId];i.instances[s]=rn(u,l,o[u.defId],n,r)}return i}function nn(e,t,n,r){var o=n.standardProps||{};null==o.hasEnd&&t.durationEditable&&(n.startDelta||n.endDelta)&&(o.hasEnd=!0);var a=i(i(i({},e),o),{ui:i(i({},e.ui),o.ui)});n.extendedProps&&(a.extendedProps=i(i({},a.extendedProps),n.extendedProps));for(var l=0,s=r.pluginHooks.eventDefMutationAppliers;l<s.length;l++){(0,s[l])(a,n,r)}return!a.hasEnd&&r.options.forceEventDuration&&(a.hasEnd=!0),a}function rn(e,t,n,r,o){var a=o.dateEnv,l=r.standardProps&&!0===r.standardProps.allDay,s=r.standardProps&&!1===r.standardProps.hasEnd,u=i({},e);return l&&(u.range=Dt(u.range)),r.datesDelta&&n.startEditable&&(u.range={start:a.add(u.range.start,r.datesDelta),end:a.add(u.range.end,r.datesDelta)}),r.startDelta&&n.durationEditable&&(u.range={start:a.add(u.range.start,r.startDelta),end:u.range.end}),r.endDelta&&n.durationEditable&&(u.range={start:u.range.start,end:a.add(u.range.end,r.endDelta)}),s&&(u.range={start:u.range.start,end:en(t.allDay,u.range.start,o)}),t.allDay&&(u.range={start:Y(u.range.start),end:Y(u.range.end)}),u.range.end<u.range.start&&(u.range.end=en(t.allDay,u.range.start,o)),u}var on=function(){function e(e,t,n){this.type=e,this.getCurrentData=t,this.dateEnv=n}return Object.defineProperty(e.prototype,"calendar",{get:function(){return this.getCurrentData().calendarApi},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"title",{get:function(){return this.getCurrentData().viewTitle},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"activeStart",{get:function(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.start)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"activeEnd",{get:function(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.end)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentStart",{get:function(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.start)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentEnd",{get:function(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.end)},enumerable:!1,configurable:!0}),e.prototype.getOption=function(e){return this.getCurrentData().options[e]},e}(),an={id:String,defaultAllDay:Boolean,url:String,format:String,events:ot,eventDataTransform:ot,success:ot,failure:ot};function ln(e,t,n){var r;if(void 0===n&&(n=sn(t)),"string"===typeof e?r={url:e}:"function"===typeof e||Array.isArray(e)?r={events:e}:"object"===typeof e&&e&&(r=e),r){var o=rt(r,n),i=o.refined,a=o.extra,l=function(e,t){for(var n=t.pluginHooks.eventSourceDefs,r=n.length-1;r>=0;r-=1){var o=n[r].parseMeta(e);if(o)return{sourceDefId:r,meta:o}}return null}(i,t);if(l)return{_raw:e,isFetching:!1,latestFetchId:"",fetchRange:null,defaultAllDay:i.defaultAllDay,eventDataTransform:i.eventDataTransform,success:i.success,failure:i.failure,publicId:i.id||"",sourceId:O(),sourceDefId:l.sourceDefId,meta:l.meta,ui:ht(i,t),extendedProps:a}}return null}function sn(e){return i(i(i({},ft),an),e.pluginHooks.eventSourceRefiners)}function un(e,t){return"function"===typeof e&&(e=e()),null==e?t.createNowMarker():t.createMarker(e)}var cn=function(){function e(){}return e.prototype.getCurrentData=function(){return this.currentDataManager.getCurrentData()},e.prototype.dispatch=function(e){return this.currentDataManager.dispatch(e)},Object.defineProperty(e.prototype,"view",{get:function(){return this.getCurrentData().viewApi},enumerable:!1,configurable:!0}),e.prototype.batchRendering=function(e){e()},e.prototype.updateSize=function(){this.trigger("_resize",!0)},e.prototype.setOption=function(e,t){this.dispatch({type:"SET_OPTION",optionName:e,rawOptionValue:t})},e.prototype.getOption=function(e){return this.currentDataManager.currentCalendarOptionsInput[e]},e.prototype.getAvailableLocaleCodes=function(){return Object.keys(this.getCurrentData().availableRawLocales)},e.prototype.on=function(e,t){var n=this.currentDataManager;n.currentCalendarOptionsRefiners[e]?n.emitter.on(e,t):console.warn("Unknown listener name '"+e+"'")},e.prototype.off=function(e,t){this.currentDataManager.emitter.off(e,t)},e.prototype.trigger=function(e){for(var t,n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];(t=this.currentDataManager.emitter).trigger.apply(t,a([e],n))},e.prototype.changeView=function(e,t){var n=this;this.batchRendering((function(){if(n.unselect(),t)if(t.start&&t.end)n.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e}),n.dispatch({type:"SET_OPTION",optionName:"visibleRange",rawOptionValue:t});else{var r=n.getCurrentData().dateEnv;n.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e,dateMarker:r.createMarker(t)})}else n.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e})}))},e.prototype.zoomTo=function(e,t){var n;t=t||"day",n=this.getCurrentData().viewSpecs[t]||this.getUnitViewSpec(t),this.unselect(),n?this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:n.type,dateMarker:e}):this.dispatch({type:"CHANGE_DATE",dateMarker:e})},e.prototype.getUnitViewSpec=function(e){var t,n,r=this.getCurrentData(),o=r.viewSpecs,i=r.toolbarConfig,a=[].concat(i.viewsWithButtons);for(var l in o)a.push(l);for(t=0;t<a.length;t+=1)if((n=o[a[t]])&&n.singleUnit===e)return n;return null},e.prototype.prev=function(){this.unselect(),this.dispatch({type:"PREV"})},e.prototype.next=function(){this.unselect(),this.dispatch({type:"NEXT"})},e.prototype.prevYear=function(){var e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,-1)})},e.prototype.nextYear=function(){var e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,1)})},e.prototype.today=function(){var e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:un(e.calendarOptions.now,e.dateEnv)})},e.prototype.gotoDate=function(e){var t=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:t.dateEnv.createMarker(e)})},e.prototype.incrementDate=function(e){var t=this.getCurrentData(),n=ye(e);n&&(this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:t.dateEnv.add(t.currentDate,n)}))},e.prototype.getDate=function(){var e=this.getCurrentData();return e.dateEnv.toDate(e.currentDate)},e.prototype.formatDate=function(e,t){var n=this.getCurrentData().dateEnv;return n.format(n.createMarker(e),Xe(t))},e.prototype.formatRange=function(e,t,n){var r=this.getCurrentData().dateEnv;return r.formatRange(r.createMarker(e),r.createMarker(t),Xe(n),n)},e.prototype.formatIso=function(e,t){var n=this.getCurrentData().dateEnv;return n.formatIso(n.createMarker(e),{omitTime:t})},e.prototype.select=function(e,t){var n;n=null==t?null!=e.start?e:{start:e,end:null}:{start:e,end:t};var r=this.getCurrentData(),o=Yt(n,r.dateEnv,ye({days:1}));o&&(this.dispatch({type:"SELECT_DATES",selection:o}),Kt(o,null,r))},e.prototype.unselect=function(e){var t=this.getCurrentData();t.dateSelection&&(this.dispatch({type:"UNSELECT_DATES"}),function(e,t){t.emitter.trigger("unselect",{jsEvent:e?e.origEvent:null,view:t.viewApi||t.calendarApi.view})}(e,t))},e.prototype.addEvent=function(e,t){if(e instanceof dn){var n=e._def,r=e._instance;return this.getCurrentData().eventStore.defs[n.defId]||(this.dispatch({type:"ADD_EVENTS",eventStore:at({def:n,instance:r})}),this.triggerEventAdd(e)),e}var o,i=this.getCurrentData();if(t instanceof f)o=t.internalEventSource;else if("boolean"===typeof t)t&&(o=se(i.eventSources)[0]);else if(null!=t){var a=this.getEventSourceById(t);if(!a)return console.warn('Could not find an event source with ID "'+t+'"'),null;o=a.internalEventSource}var l=St(e,o,i,!1);if(l){var s=new dn(i,l.def,l.def.recurringDef?null:l.instance);return this.dispatch({type:"ADD_EVENTS",eventStore:at(l)}),this.triggerEventAdd(s),s}return null},e.prototype.triggerEventAdd=function(e){var t=this;this.getCurrentData().emitter.trigger("eventAdd",{event:e,relatedEvents:[],revert:function(){t.dispatch({type:"REMOVE_EVENTS",eventStore:fn(e)})}})},e.prototype.getEventById=function(e){var t=this.getCurrentData(),n=t.eventStore,r=n.defs,o=n.instances;for(var i in e=String(e),r){var a=r[i];if(a.publicId===e){if(a.recurringDef)return new dn(t,a,null);for(var l in o){var s=o[l];if(s.defId===a.defId)return new dn(t,a,s)}}}return null},e.prototype.getEvents=function(){var e=this.getCurrentData();return pn(e.eventStore,e)},e.prototype.removeAllEvents=function(){this.dispatch({type:"REMOVE_ALL_EVENTS"})},e.prototype.getEventSources=function(){var e=this.getCurrentData(),t=e.eventSources,n=[];for(var r in t)n.push(new f(e,t[r]));return n},e.prototype.getEventSourceById=function(e){var t=this.getCurrentData(),n=t.eventSources;for(var r in e=String(e),n)if(n[r].publicId===e)return new f(t,n[r]);return null},e.prototype.addEventSource=function(e){var t=this.getCurrentData();if(e instanceof f)return t.eventSources[e.internalEventSource.sourceId]||this.dispatch({type:"ADD_EVENT_SOURCES",sources:[e.internalEventSource]}),e;var n=ln(e,t);return n?(this.dispatch({type:"ADD_EVENT_SOURCES",sources:[n]}),new f(t,n)):null},e.prototype.removeAllEventSources=function(){this.dispatch({type:"REMOVE_ALL_EVENT_SOURCES"})},e.prototype.refetchEvents=function(){this.dispatch({type:"FETCH_EVENT_SOURCES"})},e.prototype.scrollToTime=function(e){var t=ye(e);t&&this.trigger("_scrollRequest",{time:t})},e}(),dn=function(){function e(e,t,n){this._context=e,this._def=t,this._instance=n||null}return e.prototype.setProp=function(e,t){var n,r;if(e in yt)console.warn("Could not set date-related prop 'name'. Use one of the date-related methods instead.");else if(e in mt)t=mt[e](t),this.mutate({standardProps:(n={},n[e]=t,n)});else if(e in ft){var o=ft[e](t);"color"===e?o={backgroundColor:t,borderColor:t}:"editable"===e?o={startEditable:t,durationEditable:t}:((r={})[e]=t,o=r),this.mutate({standardProps:{ui:o}})}else console.warn("Could not set prop '"+e+"'. Use setExtendedProp instead.")},e.prototype.setExtendedProp=function(e,t){var n;this.mutate({extendedProps:(n={},n[e]=t,n)})},e.prototype.setStart=function(e,t){void 0===t&&(t={});var n=this._context.dateEnv,r=n.createMarker(e);if(r&&this._instance){var o=kt(this._instance.range.start,r,n,t.granularity);t.maintainDuration?this.mutate({datesDelta:o}):this.mutate({startDelta:o})}},e.prototype.setEnd=function(e,t){void 0===t&&(t={});var n,r=this._context.dateEnv;if((null==e||(n=r.createMarker(e)))&&this._instance)if(n){var o=kt(this._instance.range.end,n,r,t.granularity);this.mutate({endDelta:o})}else this.mutate({standardProps:{hasEnd:!1}})},e.prototype.setDates=function(e,t,n){void 0===n&&(n={});var r,o,i,a=this._context.dateEnv,l={allDay:n.allDay},s=a.createMarker(e);if(s&&((null==t||(r=a.createMarker(t)))&&this._instance)){var u=this._instance.range;!0===n.allDay&&(u=Dt(u));var c=kt(u.start,s,a,n.granularity);if(r){var d=kt(u.end,r,a,n.granularity);i=d,(o=c).years===i.years&&o.months===i.months&&o.days===i.days&&o.milliseconds===i.milliseconds?this.mutate({datesDelta:c,standardProps:l}):this.mutate({startDelta:c,endDelta:d,standardProps:l})}else l.hasEnd=!1,this.mutate({datesDelta:c,standardProps:l})}},e.prototype.moveStart=function(e){var t=ye(e);t&&this.mutate({startDelta:t})},e.prototype.moveEnd=function(e){var t=ye(e);t&&this.mutate({endDelta:t})},e.prototype.moveDates=function(e){var t=ye(e);t&&this.mutate({datesDelta:t})},e.prototype.setAllDay=function(e,t){void 0===t&&(t={});var n={allDay:e},r=t.maintainDuration;null==r&&(r=this._context.options.allDayMaintainDuration),this._def.allDay!==e&&(n.hasEnd=r),this.mutate({standardProps:n})},e.prototype.formatRange=function(e){var t=this._context.dateEnv,n=this._instance,r=Xe(e);return this._def.hasEnd?t.formatRange(n.range.start,n.range.end,r,{forcedStartTzo:n.forcedStartTzo,forcedEndTzo:n.forcedEndTzo}):t.format(n.range.start,r,{forcedTzo:n.forcedStartTzo})},e.prototype.mutate=function(t){var n=this._instance;if(n){var r=this._def,o=this._context,i=o.getCurrentData().eventStore,a=lt(i,n.instanceId);a=tn(a,{"":{display:"",startEditable:!0,durationEditable:!0,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]}},t,o);var l=new e(o,r,n);this._def=a.defs[r.defId],this._instance=a.instances[n.instanceId],o.dispatch({type:"MERGE_EVENTS",eventStore:a}),o.emitter.trigger("eventChange",{oldEvent:l,event:this,relatedEvents:pn(a,o,n),revert:function(){o.dispatch({type:"RESET_EVENTS",eventStore:i})}})}},e.prototype.remove=function(){var e=this._context,t=fn(this);e.dispatch({type:"REMOVE_EVENTS",eventStore:t}),e.emitter.trigger("eventRemove",{event:this,relatedEvents:[],revert:function(){e.dispatch({type:"MERGE_EVENTS",eventStore:t})}})},Object.defineProperty(e.prototype,"source",{get:function(){var e=this._def.sourceId;return e?new f(this._context,this._context.getCurrentData().eventSources[e]):null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"start",{get:function(){return this._instance?this._context.dateEnv.toDate(this._instance.range.start):null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"end",{get:function(){return this._instance&&this._def.hasEnd?this._context.dateEnv.toDate(this._instance.range.end):null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"startStr",{get:function(){var e=this._instance;return e?this._context.dateEnv.formatIso(e.range.start,{omitTime:this._def.allDay,forcedTzo:e.forcedStartTzo}):""},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"endStr",{get:function(){var e=this._instance;return e&&this._def.hasEnd?this._context.dateEnv.formatIso(e.range.end,{omitTime:this._def.allDay,forcedTzo:e.forcedEndTzo}):""},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"id",{get:function(){return this._def.publicId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"groupId",{get:function(){return this._def.groupId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"allDay",{get:function(){return this._def.allDay},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"title",{get:function(){return this._def.title},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"url",{get:function(){return this._def.url},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"display",{get:function(){return this._def.ui.display||"auto"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"startEditable",{get:function(){return this._def.ui.startEditable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"durationEditable",{get:function(){return this._def.ui.durationEditable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"constraint",{get:function(){return this._def.ui.constraints[0]||null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"overlap",{get:function(){return this._def.ui.overlap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"allow",{get:function(){return this._def.ui.allows[0]||null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"backgroundColor",{get:function(){return this._def.ui.backgroundColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"borderColor",{get:function(){return this._def.ui.borderColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textColor",{get:function(){return this._def.ui.textColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"classNames",{get:function(){return this._def.ui.classNames},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"extendedProps",{get:function(){return this._def.extendedProps},enumerable:!1,configurable:!0}),e.prototype.toPlainObject=function(e){void 0===e&&(e={});var t=this._def,n=t.ui,r=this.startStr,o=this.endStr,a={};return t.title&&(a.title=t.title),r&&(a.start=r),o&&(a.end=o),t.publicId&&(a.id=t.publicId),t.groupId&&(a.groupId=t.groupId),t.url&&(a.url=t.url),n.display&&"auto"!==n.display&&(a.display=n.display),e.collapseColor&&n.backgroundColor&&n.backgroundColor===n.borderColor?a.color=n.backgroundColor:(n.backgroundColor&&(a.backgroundColor=n.backgroundColor),n.borderColor&&(a.borderColor=n.borderColor)),n.textColor&&(a.textColor=n.textColor),n.classNames.length&&(a.classNames=n.classNames),Object.keys(t.extendedProps).length&&(e.collapseExtendedProps?i(a,t.extendedProps):a.extendedProps=t.extendedProps),a},e.prototype.toJSON=function(){return this.toPlainObject()},e}();function fn(e){var t,n,r=e._def,o=e._instance;return{defs:(t={},t[r.defId]=r,t),instances:o?(n={},n[o.instanceId]=o,n):{}}}function pn(e,t,n){var r=e.defs,o=e.instances,i=[],a=n?n.instanceId:"";for(var l in o){var s=o[l],u=r[s.defId];s.instanceId!==a&&i.push(new dn(t,u,s))}return i}var hn={};var vn,gn=function(){function e(){}return e.prototype.getMarkerYear=function(e){return e.getUTCFullYear()},e.prototype.getMarkerMonth=function(e){return e.getUTCMonth()},e.prototype.getMarkerDay=function(e){return e.getUTCDate()},e.prototype.arrayToMarker=function(e){return J(e)},e.prototype.markerToArray=function(e){return K(e)},e}();vn=gn,hn["gregory"]=vn;var mn=/^\s*(\d{4})(-?(\d{2})(-?(\d{2})([T ](\d{2}):?(\d{2})(:?(\d{2})(\.(\d+))?)?(Z|(([-+])(\d{2})(:?(\d{2}))?))?)?)?)?$/;var yn=function(){function e(e){var t=this.timeZone=e.timeZone,n="local"!==t&&"UTC"!==t;e.namedTimeZoneImpl&&n&&(this.namedTimeZoneImpl=new e.namedTimeZoneImpl(t)),this.canComputeOffset=Boolean(!n||this.namedTimeZoneImpl),this.calendarSystem=function(e){return new hn[e]}(e.calendarSystem),this.locale=e.locale,this.weekDow=e.locale.week.dow,this.weekDoy=e.locale.week.doy,"ISO"===e.weekNumberCalculation&&(this.weekDow=1,this.weekDoy=4),"number"===typeof e.firstDay&&(this.weekDow=e.firstDay),"function"===typeof e.weekNumberCalculation&&(this.weekNumberFunc=e.weekNumberCalculation),this.weekText=null!=e.weekText?e.weekText:e.locale.options.weekText,this.cmdFormatter=e.cmdFormatter,this.defaultSeparator=e.defaultSeparator}return e.prototype.createMarker=function(e){var t=this.createMarkerMeta(e);return null===t?null:t.marker},e.prototype.createNowMarker=function(){return this.canComputeOffset?this.timestampToMarker((new Date).valueOf()):J($(new Date))},e.prototype.createMarkerMeta=function(e){if("string"===typeof e)return this.parse(e);var t=null;return"number"===typeof e?t=this.timestampToMarker(e):e instanceof Date?(e=e.valueOf(),isNaN(e)||(t=this.timestampToMarker(e))):Array.isArray(e)&&(t=J(e)),null!==t&&ee(t)?{marker:t,isTimeUnspecified:!1,forcedTzo:null}:null},e.prototype.parse=function(e){var t=function(e){var t=mn.exec(e);if(t){var n=new Date(Date.UTC(Number(t[1]),t[3]?Number(t[3])-1:0,Number(t[5]||1),Number(t[7]||0),Number(t[8]||0),Number(t[10]||0),t[12]?1e3*Number("0."+t[12]):0));if(ee(n)){var r=null;return t[13]&&(r=("-"===t[15]?-1:1)*(60*Number(t[16]||0)+Number(t[18]||0))),{marker:n,isTimeUnspecified:!t[6],timeZoneOffset:r}}}return null}(e);if(null===t)return null;var n=t.marker,r=null;return null!==t.timeZoneOffset&&(this.canComputeOffset?n=this.timestampToMarker(n.valueOf()-60*t.timeZoneOffset*1e3):r=t.timeZoneOffset),{marker:n,isTimeUnspecified:t.isTimeUnspecified,forcedTzo:r}},e.prototype.getYear=function(e){return this.calendarSystem.getMarkerYear(e)},e.prototype.getMonth=function(e){return this.calendarSystem.getMarkerMonth(e)},e.prototype.add=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[0]+=t.years,n[1]+=t.months,n[2]+=t.days,n[6]+=t.milliseconds,this.calendarSystem.arrayToMarker(n)},e.prototype.subtract=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[0]-=t.years,n[1]-=t.months,n[2]-=t.days,n[6]-=t.milliseconds,this.calendarSystem.arrayToMarker(n)},e.prototype.addYears=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[0]+=t,this.calendarSystem.arrayToMarker(n)},e.prototype.addMonths=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[1]+=t,this.calendarSystem.arrayToMarker(n)},e.prototype.diffWholeYears=function(e,t){var n=this.calendarSystem;return te(e)===te(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)&&n.getMarkerMonth(e)===n.getMarkerMonth(t)?n.getMarkerYear(t)-n.getMarkerYear(e):null},e.prototype.diffWholeMonths=function(e,t){var n=this.calendarSystem;return te(e)===te(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)?n.getMarkerMonth(t)-n.getMarkerMonth(e)+12*(n.getMarkerYear(t)-n.getMarkerYear(e)):null},e.prototype.greatestWholeUnit=function(e,t){var n=this.diffWholeYears(e,t);return null!==n?{unit:"year",value:n}:null!==(n=this.diffWholeMonths(e,t))?{unit:"month",value:n}:null!==(n=function(e,t){var n=G(e,t);return null!==n&&n%7===0?n/7:null}(e,t))?{unit:"week",value:n}:null!==(n=G(e,t))?{unit:"day",value:n}:U(n=function(e,t){return(t.valueOf()-e.valueOf())/36e5}(e,t))?{unit:"hour",value:n}:U(n=function(e,t){return(t.valueOf()-e.valueOf())/6e4}(e,t))?{unit:"minute",value:n}:U(n=function(e,t){return(t.valueOf()-e.valueOf())/1e3}(e,t))?{unit:"second",value:n}:{unit:"millisecond",value:t.valueOf()-e.valueOf()}},e.prototype.countDurationsBetween=function(e,t,n){var r;return n.years&&null!==(r=this.diffWholeYears(e,t))?r/(we(n)/365):n.months&&null!==(r=this.diffWholeMonths(e,t))?r/function(e){return we(e)/30}(n):n.days&&null!==(r=G(e,t))?r/we(n):(t.valueOf()-e.valueOf())/Re(n)},e.prototype.startOf=function(e,t){return"year"===t?this.startOfYear(e):"month"===t?this.startOfMonth(e):"week"===t?this.startOfWeek(e):"day"===t?Y(e):"hour"===t?function(e){return J([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours()])}(e):"minute"===t?function(e){return J([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes()])}(e):"second"===t?function(e){return J([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds()])}(e):null},e.prototype.startOfYear=function(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e)])},e.prototype.startOfMonth=function(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e)])},e.prototype.startOfWeek=function(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e),e.getUTCDate()-(e.getUTCDay()-this.weekDow+7)%7])},e.prototype.computeWeekNumber=function(e){return this.weekNumberFunc?this.weekNumberFunc(this.toDate(e)):function(e,t,n){var r=e.getUTCFullYear(),o=X(e,r,t,n);if(o<1)return X(e,r-1,t,n);var i=X(e,r+1,t,n);return i>=1?Math.min(o,i):o}(e,this.weekDow,this.weekDoy)},e.prototype.format=function(e,t,n){return void 0===n&&(n={}),t.format({marker:e,timeZoneOffset:null!=n.forcedTzo?n.forcedTzo:this.offsetForMarker(e)},this)},e.prototype.formatRange=function(e,t,n,r){return void 0===r&&(r={}),r.isEndExclusive&&(t=V(t,-1)),n.formatRange({marker:e,timeZoneOffset:null!=r.forcedStartTzo?r.forcedStartTzo:this.offsetForMarker(e)},{marker:t,timeZoneOffset:null!=r.forcedEndTzo?r.forcedEndTzo:this.offsetForMarker(t)},this,r.defaultSeparator)},e.prototype.formatIso=function(e,t){void 0===t&&(t={});var n=null;return t.omitTimeZoneOffset||(n=null!=t.forcedTzo?t.forcedTzo:this.offsetForMarker(e)),function(e,t,n){void 0===n&&(n=!1);var r=e.toISOString();return r=r.replace(".000",""),n&&(r=r.replace("T00:00:00Z","")),r.length>10&&(null==t?r=r.replace("Z",""):0!==t&&(r=r.replace("Z",xe(t,!0)))),r}(e,n,t.omitTime)},e.prototype.timestampToMarker=function(e){return"local"===this.timeZone?J($(new Date(e))):"UTC"!==this.timeZone&&this.namedTimeZoneImpl?J(this.namedTimeZoneImpl.timestampToArray(e)):new Date(e)},e.prototype.offsetForMarker=function(e){return"local"===this.timeZone?-Q(K(e)).getTimezoneOffset():"UTC"===this.timeZone?0:this.namedTimeZoneImpl?this.namedTimeZoneImpl.offsetForArray(K(e)):null},e.prototype.toDate=function(e,t){return"local"===this.timeZone?Q(K(e)):"UTC"===this.timeZone?new Date(e.valueOf()):this.namedTimeZoneImpl?new Date(e.valueOf()-1e3*this.namedTimeZoneImpl.offsetForArray(K(e))*60):new Date(e.valueOf()-(t||0))},e}(),bn=[],Sn={code:"en",week:{dow:0,doy:4},direction:"ltr",buttonText:{prev:"prev",next:"next",prevYear:"prev year",nextYear:"next year",year:"year",today:"today",month:"month",week:"week",day:"day",list:"list"},weekText:"W",allDayText:"all-day",moreLinkText:"more",noEventsText:"No events to display"};function En(e){for(var t=e.length>0?e[0].code:"en",n=bn.concat(e),r={en:Sn},o=0,i=n;o<i.length;o++){var a=i[o];r[a.code]=a}return{map:r,defaultCode:t}}function wn(e,t){return"object"!==typeof e||Array.isArray(e)?function(e,t){var n=[].concat(e||[]),r=function(e,t){for(var n=0;n<e.length;n+=1)for(var r=e[n].toLocaleLowerCase().split("-"),o=r.length;o>0;o-=1){var i=r.slice(0,o).join("-");if(t[i])return t[i]}return null}(n,t)||Sn;return Cn(e,n,r)}(e,t):Cn(e.code,[e.code],e)}function Cn(e,t,n){var r=oe([Sn,n],["buttonText"]);delete r.code;var o=r.week;return delete r.week,{codeArg:e,codes:t,week:o,simpleNumberFormat:new Intl.NumberFormat(e),options:r}}var Dn,Rn={startTime:"09:00",endTime:"17:00",daysOfWeek:[1,2,3,4,5],display:"inverse-background",classNames:"fc-non-business",groupId:"_businessHours"};function kn(e,t){return it(function(e){var t;t=!0===e?[{}]:Array.isArray(e)?e.filter((function(e){return e.daysOfWeek})):"object"===typeof e&&e?[e]:[];return t=t.map((function(e){return i(i({},Rn),e)}))}(e),null,t)}function On(e,t){return e.left>=t.left&&e.left<t.right&&e.top>=t.top&&e.top<t.bottom}function Tn(e,t){var n={left:Math.max(e.left,t.left),right:Math.min(e.right,t.right),top:Math.max(e.top,t.top),bottom:Math.min(e.bottom,t.bottom)};return n.left<n.right&&n.top<n.bottom&&n}function xn(e,t,n){return{left:e.left+t,right:e.right+t,top:e.top+n,bottom:e.bottom+n}}function Mn(e,t){return{left:Math.min(Math.max(e.left,t.left),t.right),top:Math.min(Math.max(e.top,t.top),t.bottom)}}function Pn(e){return{left:(e.left+e.right)/2,top:(e.top+e.bottom)/2}}function In(e,t){return{left:e.left-t.left,top:e.top-t.top}}function jn(){return null==Dn&&(Dn=function(){if("undefined"===typeof document)return!0;var e=document.createElement("div");e.style.position="absolute",e.style.top="0px",e.style.left="0px",e.innerHTML="<table><tr><td><div></div></td></tr></table>",e.querySelector("table").style.height="100px",e.querySelector("div").style.height="100%",document.body.appendChild(e);var t=e.querySelector("div").offsetHeight>0;return document.body.removeChild(e),t}()),Dn}var Nn={defs:{},instances:{}},_n=function(){function e(){this.getKeysForEventDefs=Pe(this._getKeysForEventDefs),this.splitDateSelection=Pe(this._splitDateSpan),this.splitEventStore=Pe(this._splitEventStore),this.splitIndividualUi=Pe(this._splitIndividualUi),this.splitEventDrag=Pe(this._splitInteraction),this.splitEventResize=Pe(this._splitInteraction),this.eventUiBuilders={}}return e.prototype.splitProps=function(e){var t=this,n=this.getKeyInfo(e),r=this.getKeysForEventDefs(e.eventStore),o=this.splitDateSelection(e.dateSelection),i=this.splitIndividualUi(e.eventUiBases,r),a=this.splitEventStore(e.eventStore,r),l=this.splitEventDrag(e.eventDrag),s=this.splitEventResize(e.eventResize),u={};for(var c in this.eventUiBuilders=ae(n,(function(e,n){return t.eventUiBuilders[n]||Pe(Hn)})),n){var d=n[c],f=a[c]||Nn,p=this.eventUiBuilders[c];u[c]={businessHours:d.businessHours||e.businessHours,dateSelection:o[c]||null,eventStore:f,eventUiBases:p(e.eventUiBases[""],d.ui,i[c]),eventSelection:f.instances[e.eventSelection]?e.eventSelection:"",eventDrag:l[c]||null,eventResize:s[c]||null}}return u},e.prototype._splitDateSpan=function(e){var t={};if(e)for(var n=0,r=this.getKeysForDateSpan(e);n<r.length;n++){t[r[n]]=e}return t},e.prototype._getKeysForEventDefs=function(e){var t=this;return ae(e.defs,(function(e){return t.getKeysForEventDef(e)}))},e.prototype._splitEventStore=function(e,t){var n=e.defs,r=e.instances,o={};for(var i in n)for(var a=0,l=t[i];a<l.length;a++){o[f=l[a]]||(o[f]={defs:{},instances:{}}),o[f].defs[i]=n[i]}for(var s in r)for(var u=r[s],c=0,d=t[u.defId];c<d.length;c++){var f;o[f=d[c]]&&(o[f].instances[s]=u)}return o},e.prototype._splitIndividualUi=function(e,t){var n={};for(var r in e)if(r)for(var o=0,i=t[r];o<i.length;o++){var a=i[o];n[a]||(n[a]={}),n[a][r]=e[r]}return n},e.prototype._splitInteraction=function(e){var t={};if(e){var n=this._splitEventStore(e.affectedEvents,this._getKeysForEventDefs(e.affectedEvents)),r=this._getKeysForEventDefs(e.mutatedEvents),o=this._splitEventStore(e.mutatedEvents,r),i=function(r){t[r]||(t[r]={affectedEvents:n[r]||Nn,mutatedEvents:o[r]||Nn,isEvent:e.isEvent})};for(var a in n)i(a);for(var a in o)i(a)}return t},e}();function Hn(e,t,n){var r=[];e&&r.push(e),t&&r.push(t);var o={"":vt(r)};return n&&i(o,n),o}function Ln(e,t,n,r){return{dow:e.getUTCDay(),isDisabled:Boolean(r&&!jt(r.activeRange,e)),isOther:Boolean(r&&!jt(r.currentRange,e)),isToday:Boolean(t&&jt(t,e)),isPast:Boolean(n?e<n:!!t&&e<t.start),isFuture:Boolean(n?e>n:!!t&&e>=t.end)}}function An(e,t){var n=["fc-day","fc-day-"+B[e.dow]];return e.isDisabled?n.push("fc-day-disabled"):(e.isToday&&(n.push("fc-day-today"),n.push(t.getClass("today"))),e.isPast&&n.push("fc-day-past"),e.isFuture&&n.push("fc-day-future"),e.isOther&&n.push("fc-day-other")),n}function zn(e,t){var n=["fc-slot","fc-slot-"+B[e.dow]];return e.isDisabled?n.push("fc-slot-disabled"):(e.isToday&&(n.push("fc-slot-today"),n.push(t.getClass("today"))),e.isPast&&n.push("fc-slot-past"),e.isFuture&&n.push("fc-slot-future")),n}function Un(e,t){return void 0===t&&(t="day"),JSON.stringify({date:Te(e),type:t})}var Wn,Bn=null;function Fn(){return null===Bn&&(Bn=function(){var e=document.createElement("div");b(e,{position:"absolute",top:-1e3,left:0,border:0,padding:0,overflow:"scroll",direction:"rtl"}),e.innerHTML="<div></div>",document.body.appendChild(e);var t=e.firstChild.getBoundingClientRect().left>e.getBoundingClientRect().left;return p(e),t}()),Bn}function Vn(){return Wn||(Wn=function(){var e=document.createElement("div");e.style.overflow="scroll",e.style.position="absolute",e.style.top="-9999px",e.style.left="-9999px",document.body.appendChild(e);var t=qn(e);return document.body.removeChild(e),t}()),Wn}function qn(e){return{x:e.offsetHeight-e.clientHeight,y:e.offsetWidth-e.clientWidth}}function Gn(e,t){void 0===t&&(t=!1);var n=window.getComputedStyle(e),r=parseInt(n.borderLeftWidth,10)||0,o=parseInt(n.borderRightWidth,10)||0,i=parseInt(n.borderTopWidth,10)||0,a=parseInt(n.borderBottomWidth,10)||0,l=qn(e),s=l.y-r-o,u={borderLeft:r,borderRight:o,borderTop:i,borderBottom:a,scrollbarBottom:l.x-i-a,scrollbarLeft:0,scrollbarRight:0};return Fn()&&"rtl"===n.direction?u.scrollbarLeft=s:u.scrollbarRight=s,t&&(u.paddingLeft=parseInt(n.paddingLeft,10)||0,u.paddingRight=parseInt(n.paddingRight,10)||0,u.paddingTop=parseInt(n.paddingTop,10)||0,u.paddingBottom=parseInt(n.paddingBottom,10)||0),u}function Yn(e,t,n){void 0===t&&(t=!1);var r=n?e.getBoundingClientRect():Xn(e),o=Gn(e,t),i={left:r.left+o.borderLeft+o.scrollbarLeft,right:r.right-o.borderRight-o.scrollbarRight,top:r.top+o.borderTop,bottom:r.bottom-o.borderBottom-o.scrollbarBottom};return t&&(i.left+=o.paddingLeft,i.right-=o.paddingRight,i.top+=o.paddingTop,i.bottom-=o.paddingBottom),i}function Xn(e){var t=e.getBoundingClientRect();return{left:t.left+window.pageXOffset,top:t.top+window.pageYOffset,right:t.right+window.pageXOffset,bottom:t.bottom+window.pageYOffset}}function Zn(e){for(var t=[];e instanceof HTMLElement;){var n=window.getComputedStyle(e);if("fixed"===n.position)break;/(auto|scroll)/.test(n.overflow+n.overflowY+n.overflowX)&&t.push(e),e=e.parentNode}return t}function $n(e,t,n){var r=!1,o=function(){r||(r=!0,t.apply(this,arguments))},i=function(){r||(r=!0,n&&n.apply(this,arguments))},a=e(o,i);a&&"function"===typeof a.then&&a.then(o,i)}var Qn=function(){function e(){this.handlers={},this.thisContext=null}return e.prototype.setThisContext=function(e){this.thisContext=e},e.prototype.setOptions=function(e){this.options=e},e.prototype.on=function(e,t){!function(e,t,n){(e[t]||(e[t]=[])).push(n)}(this.handlers,e,t)},e.prototype.off=function(e,t){!function(e,t,n){n?e[t]&&(e[t]=e[t].filter((function(e){return e!==n}))):delete e[t]}(this.handlers,e,t)},e.prototype.trigger=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=this.handlers[e]||[],o=this.options&&this.options[e],i=[].concat(o||[],r),a=0,l=i;a<l.length;a++){var s=l[a];s.apply(this.thisContext,t)}},e.prototype.hasHandlers=function(e){return this.handlers[e]&&this.handlers[e].length||this.options&&this.options[e]},e}();var Kn=function(){function e(e,t,n,r){this.els=t;var o=this.originClientRect=e.getBoundingClientRect();n&&this.buildElHorizontals(o.left),r&&this.buildElVerticals(o.top)}return e.prototype.buildElHorizontals=function(e){for(var t=[],n=[],r=0,o=this.els;r<o.length;r++){var i=o[r].getBoundingClientRect();t.push(i.left-e),n.push(i.right-e)}this.lefts=t,this.rights=n},e.prototype.buildElVerticals=function(e){for(var t=[],n=[],r=0,o=this.els;r<o.length;r++){var i=o[r].getBoundingClientRect();t.push(i.top-e),n.push(i.bottom-e)}this.tops=t,this.bottoms=n},e.prototype.leftToIndex=function(e){var t,n=this.lefts,r=this.rights,o=n.length;for(t=0;t<o;t+=1)if(e>=n[t]&&e<r[t])return t},e.prototype.topToIndex=function(e){var t,n=this.tops,r=this.bottoms,o=n.length;for(t=0;t<o;t+=1)if(e>=n[t]&&e<r[t])return t},e.prototype.getWidth=function(e){return this.rights[e]-this.lefts[e]},e.prototype.getHeight=function(e){return this.bottoms[e]-this.tops[e]},e}(),Jn=function(){function e(){}return e.prototype.getMaxScrollTop=function(){return this.getScrollHeight()-this.getClientHeight()},e.prototype.getMaxScrollLeft=function(){return this.getScrollWidth()-this.getClientWidth()},e.prototype.canScrollVertically=function(){return this.getMaxScrollTop()>0},e.prototype.canScrollHorizontally=function(){return this.getMaxScrollLeft()>0},e.prototype.canScrollUp=function(){return this.getScrollTop()>0},e.prototype.canScrollDown=function(){return this.getScrollTop()<this.getMaxScrollTop()},e.prototype.canScrollLeft=function(){return this.getScrollLeft()>0},e.prototype.canScrollRight=function(){return this.getScrollLeft()<this.getMaxScrollLeft()},e}(),er=function(e){function t(t){var n=e.call(this)||this;return n.el=t,n}return o(t,e),t.prototype.getScrollTop=function(){return this.el.scrollTop},t.prototype.getScrollLeft=function(){return this.el.scrollLeft},t.prototype.setScrollTop=function(e){this.el.scrollTop=e},t.prototype.setScrollLeft=function(e){this.el.scrollLeft=e},t.prototype.getScrollWidth=function(){return this.el.scrollWidth},t.prototype.getScrollHeight=function(){return this.el.scrollHeight},t.prototype.getClientHeight=function(){return this.el.clientHeight},t.prototype.getClientWidth=function(){return this.el.clientWidth},t}(Jn),tr=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.getScrollTop=function(){return window.pageYOffset},t.prototype.getScrollLeft=function(){return window.pageXOffset},t.prototype.setScrollTop=function(e){window.scroll(window.pageXOffset,e)},t.prototype.setScrollLeft=function(e){window.scroll(e,window.pageYOffset)},t.prototype.getScrollWidth=function(){return document.documentElement.scrollWidth},t.prototype.getScrollHeight=function(){return document.documentElement.scrollHeight},t.prototype.getClientHeight=function(){return document.documentElement.clientHeight},t.prototype.getClientWidth=function(){return document.documentElement.clientWidth},t}(Jn),nr=function(){function e(e){this.iconOverrideOption&&this.setIconOverride(e[this.iconOverrideOption])}return e.prototype.setIconOverride=function(e){var t,n;if("object"===typeof e&&e){for(n in t=i({},this.iconClasses),e)t[n]=this.applyIconOverridePrefix(e[n]);this.iconClasses=t}else!1===e&&(this.iconClasses={})},e.prototype.applyIconOverridePrefix=function(e){var t=this.iconOverridePrefix;return t&&0!==e.indexOf(t)&&(e=t+e),e},e.prototype.getClass=function(e){return this.classes[e]||""},e.prototype.getIconClass=function(e,t){var n;return(n=t&&this.rtlIconClasses&&this.rtlIconClasses[e]||this.iconClasses[e])?this.baseIconClass+" "+n:""},e.prototype.getCustomButtonIconClass=function(e){var t;return this.iconOverrideCustomButtonOption&&(t=e[this.iconOverrideCustomButtonOption])?this.baseIconClass+" "+this.applyIconOverridePrefix(t):""},e}();nr.prototype.classes={},nr.prototype.iconClasses={},nr.prototype.baseIconClass="",nr.prototype.iconOverridePrefix="";var rr=function(){function e(e,t,n){var r=this;this.execFunc=e,this.emitter=t,this.scrollTime=n,this.handleScrollRequest=function(e){r.queuedRequest=i({},r.queuedRequest||{},e),r.drain()},t.on("_scrollRequest",this.handleScrollRequest),this.fireInitialScroll()}return e.prototype.detach=function(){this.emitter.off("_scrollRequest",this.handleScrollRequest)},e.prototype.update=function(e){e?this.fireInitialScroll():this.drain()},e.prototype.fireInitialScroll=function(){this.handleScrollRequest({time:this.scrollTime})},e.prototype.drain=function(){this.queuedRequest&&this.execFunc(this.queuedRequest)&&(this.queuedRequest=null)},e}(),or=d({});function ir(e,t,n,r,o,i,a,l,s,u,c,d,f){return{dateEnv:o,options:n,pluginHooks:a,emitter:u,dispatch:l,getCurrentData:s,calendarApi:c,viewSpec:e,viewApi:t,dateProfileGenerator:r,theme:i,isRtl:"rtl"===n.direction,addResizeHandler:function(e){u.on("_resize",e)},removeResizeHandler:function(e){u.off("_resize",e)},createScrollResponder:function(e){return new rr(e,u,ye(n.scrollTime))},registerInteractiveComponent:d,unregisterInteractiveComponent:f}}var ar=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.shouldComponentUpdate=function(e,t){return this.debug&&console.log(ce(e,this.props),ce(t,this.state)),!de(this.props,e,this.propEquality)||!de(this.state,t,this.stateEquality)},t.addPropsEquality=sr,t.addStateEquality=ur,t.contextType=or,t}(l);ar.prototype.propEquality={},ar.prototype.stateEquality={};var lr=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.contextType=or,t}(ar);function sr(e){var t=Object.create(this.prototype.propEquality);i(t,e),this.prototype.propEquality=t}function ur(e){var t=Object.create(this.prototype.stateEquality);i(t,e),this.prototype.stateEquality=t}function cr(e,t){"function"===typeof e?e(t):e&&(e.current=t)}function dr(e,t,n,r,o){switch(t.type){case"RECEIVE_EVENTS":return function(e,t,n,r,o,i){if(t&&n===t.latestFetchId){var a=it(function(e,t,n){var r=n.options.eventDataTransform,o=t?t.eventDataTransform:null;o&&(e=fr(e,o));r&&(e=fr(e,r));return e}(o,t,i),t,i);return r&&(a=he(a,r,i)),ut(pr(e,t.sourceId),a)}return e}(e,n[t.sourceId],t.fetchId,t.fetchRange,t.rawEvents,o);case"ADD_EVENTS":return function(e,t,n,r){n&&(t=he(t,n,r));return ut(e,t)}(e,t.eventStore,r?r.activeRange:null,o);case"RESET_EVENTS":return t.eventStore;case"MERGE_EVENTS":return ut(e,t.eventStore);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return r?he(e,r.activeRange,o):e;case"REMOVE_EVENTS":return function(e,t){var n=e.defs,r=e.instances,o={},i={};for(var a in n)t.defs[a]||(o[a]=n[a]);for(var l in r)!t.instances[l]&&o[r[l].defId]&&(i[l]=r[l]);return{defs:o,instances:i}}(e,t.eventStore);case"REMOVE_EVENT_SOURCE":return pr(e,t.sourceId);case"REMOVE_ALL_EVENT_SOURCES":return ct(e,(function(e){return!e.sourceId}));case"REMOVE_ALL_EVENTS":return{defs:{},instances:{}};default:return e}}function fr(e,t){var n;if(t){n=[];for(var r=0,o=e;r<o.length;r++){var i=o[r],a=t(i);a?n.push(a):null==a&&n.push(i)}}else n=e;return n}function pr(e,t){return ct(e,(function(e){return e.sourceId!==t}))}function hr(e,t){return vr({eventDrag:e},t)}function vr(e,t){var n=t.getCurrentData(),r=i({businessHours:n.businessHours,dateSelection:"",eventStore:n.eventStore,eventUiBases:n.eventUiBases,eventSelection:"",eventDrag:null,eventResize:null},e);return(t.pluginHooks.isPropsValid||gr)(r,t)}function gr(e,t,n,r){return void 0===n&&(n={}),!(e.eventDrag&&!function(e,t,n,r){var o=t.getCurrentData(),a=e.eventDrag,l=a.mutatedEvents,s=l.defs,u=l.instances,c=Lt(s,a.isEvent?e.eventUiBases:{"":o.selectionConfig});r&&(c=ae(c,r));var d=(v=e.eventStore,g=a.affectedEvents.instances,{defs:v.defs,instances:ie(v.instances,(function(e){return!g[e.instanceId]}))}),f=d.defs,p=d.instances,h=Lt(f,e.eventUiBases);var v,g;for(var m in u){var y=u[m],b=y.range,S=c[y.defId],E=s[y.defId];if(!mr(S.constraints,b,d,e.businessHours,t))return!1;var w=t.options.eventOverlap,C="function"===typeof w?w:null;for(var D in p){var R=p[D];if(Pt(b,R.range)){if(!1===h[R.defId].overlap&&a.isEvent)return!1;if(!1===S.overlap)return!1;if(C&&!C(new dn(t,f[R.defId],R),new dn(t,E,y)))return!1}}for(var k=o.eventStore,O=0,T=S.allows;O<T.length;O++){var x=T[O],M=i(i({},n),{range:y.range,allDay:E.allDay}),P=k.defs[E.defId],I=k.instances[m],j=void 0;if(j=P?new dn(t,P,I):new dn(t,E),!x(Jt(M,t),j))return!1}}return!0}(e,t,n,r))&&!(e.dateSelection&&!function(e,t,n,r){var o=e.eventStore,a=o.defs,l=o.instances,s=e.dateSelection,u=s.range,c=t.getCurrentData().selectionConfig;r&&(c=r(c));if(!mr(c.constraints,u,o,e.businessHours,t))return!1;var d=t.options.selectOverlap,f="function"===typeof d?d:null;for(var p in l){var h=l[p];if(Pt(u,h.range)){if(!1===c.overlap)return!1;if(f&&!f(new dn(t,a[h.defId],h),null))return!1}}for(var v=0,g=c.allows;v<g.length;v++){if(!(0,g[v])(Jt(i(i({},n),s),t),null))return!1}return!0}(e,t,n,r))}function mr(e,t,n,r,o){for(var i=0,a=e;i<a.length;i++){if(!Sr(yr(a[i],t,n,r,o),t))return!1}return!0}function yr(e,t,n,r,o){return"businessHours"===e?br(he(r,t,o)):"string"===typeof e?br(ct(n,(function(t){return t.groupId===e}))):"object"===typeof e&&e?br(he(e,t,o)):[]}function br(e){var t=e.instances,n=[];for(var r in t)n.push(t[r].range);return n}function Sr(e,t){for(var n=0,r=e;n<r.length;n++){if(It(r[n],t))return!0}return!1}var Er=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.uid=O(),t}return o(t,e),t.prototype.prepareHits=function(){},t.prototype.queryHit=function(e,t,n,r){return null},t.prototype.isInteractionValid=function(e){var t=this.props.dateProfile,n=e.mutatedEvents.instances;if(t)for(var r in n)if(!It(t.validRange,n[r].range))return!1;return hr(e,this.context)},t.prototype.isDateSelectionValid=function(e){var t,n,r=this.props.dateProfile;return!(r&&!It(r.validRange,e.range))&&(t=e,n=this.context,vr({dateSelection:t},n))},t.prototype.isValidSegDownEl=function(e){return!this.props.eventDrag&&!this.props.eventResize&&!h(e,".fc-event-mirror")},t.prototype.isValidDateDownEl=function(e){return!h(e,".fc-event:not(.fc-bg-event)")&&!h(e,".fc-daygrid-more-link")&&!h(e,"a[data-navlink]")&&!h(e,".fc-popover")},t}(lr);function wr(e){return{id:O(),deps:e.deps||[],reducers:e.reducers||[],isLoadingFuncs:e.isLoadingFuncs||[],contextInit:[].concat(e.contextInit||[]),eventRefiners:e.eventRefiners||{},eventDefMemberAdders:e.eventDefMemberAdders||[],eventSourceRefiners:e.eventSourceRefiners||{},isDraggableTransformers:e.isDraggableTransformers||[],eventDragMutationMassagers:e.eventDragMutationMassagers||[],eventDefMutationAppliers:e.eventDefMutationAppliers||[],dateSelectionTransformers:e.dateSelectionTransformers||[],datePointTransforms:e.datePointTransforms||[],dateSpanTransforms:e.dateSpanTransforms||[],views:e.views||{},viewPropsTransformers:e.viewPropsTransformers||[],isPropsValid:e.isPropsValid||null,externalDefTransforms:e.externalDefTransforms||[],eventResizeJoinTransforms:e.eventResizeJoinTransforms||[],viewContainerAppends:e.viewContainerAppends||[],eventDropTransformers:e.eventDropTransformers||[],componentInteractions:e.componentInteractions||[],calendarInteractions:e.calendarInteractions||[],themeClasses:e.themeClasses||{},eventSourceDefs:e.eventSourceDefs||[],cmdFormatter:e.cmdFormatter,recurringTypes:e.recurringTypes||[],namedTimeZonedImpl:e.namedTimeZonedImpl,initialView:e.initialView||"",elementDraggingImpl:e.elementDraggingImpl,optionChangeHandlers:e.optionChangeHandlers||{},scrollGridImpl:e.scrollGridImpl||null,contentTypeHandlers:e.contentTypeHandlers||{},listenerRefiners:e.listenerRefiners||{},optionRefiners:e.optionRefiners||{},propSetHandlers:e.propSetHandlers||{}}}function Cr(){var e,t=[],n=[];return function(r,o){return e&&Me(r,t)&&Me(o,n)||(e=function(e,t){var n={},r={reducers:[],isLoadingFuncs:[],contextInit:[],eventRefiners:{},eventDefMemberAdders:[],eventSourceRefiners:{},isDraggableTransformers:[],eventDragMutationMassagers:[],eventDefMutationAppliers:[],dateSelectionTransformers:[],datePointTransforms:[],dateSpanTransforms:[],views:{},viewPropsTransformers:[],isPropsValid:null,externalDefTransforms:[],eventResizeJoinTransforms:[],viewContainerAppends:[],eventDropTransformers:[],componentInteractions:[],calendarInteractions:[],themeClasses:{},eventSourceDefs:[],cmdFormatter:null,recurringTypes:[],namedTimeZonedImpl:null,initialView:"",elementDraggingImpl:null,optionChangeHandlers:{},scrollGridImpl:null,contentTypeHandlers:{},listenerRefiners:{},optionRefiners:{},propSetHandlers:{}};function o(e){for(var t=0,a=e;t<a.length;t++){var l=a[t];n[l.id]||(n[l.id]=!0,o(l.deps),u=l,r={reducers:(s=r).reducers.concat(u.reducers),isLoadingFuncs:s.isLoadingFuncs.concat(u.isLoadingFuncs),contextInit:s.contextInit.concat(u.contextInit),eventRefiners:i(i({},s.eventRefiners),u.eventRefiners),eventDefMemberAdders:s.eventDefMemberAdders.concat(u.eventDefMemberAdders),eventSourceRefiners:i(i({},s.eventSourceRefiners),u.eventSourceRefiners),isDraggableTransformers:s.isDraggableTransformers.concat(u.isDraggableTransformers),eventDragMutationMassagers:s.eventDragMutationMassagers.concat(u.eventDragMutationMassagers),eventDefMutationAppliers:s.eventDefMutationAppliers.concat(u.eventDefMutationAppliers),dateSelectionTransformers:s.dateSelectionTransformers.concat(u.dateSelectionTransformers),datePointTransforms:s.datePointTransforms.concat(u.datePointTransforms),dateSpanTransforms:s.dateSpanTransforms.concat(u.dateSpanTransforms),views:i(i({},s.views),u.views),viewPropsTransformers:s.viewPropsTransformers.concat(u.viewPropsTransformers),isPropsValid:u.isPropsValid||s.isPropsValid,externalDefTransforms:s.externalDefTransforms.concat(u.externalDefTransforms),eventResizeJoinTransforms:s.eventResizeJoinTransforms.concat(u.eventResizeJoinTransforms),viewContainerAppends:s.viewContainerAppends.concat(u.viewContainerAppends),eventDropTransformers:s.eventDropTransformers.concat(u.eventDropTransformers),calendarInteractions:s.calendarInteractions.concat(u.calendarInteractions),componentInteractions:s.componentInteractions.concat(u.componentInteractions),themeClasses:i(i({},s.themeClasses),u.themeClasses),eventSourceDefs:s.eventSourceDefs.concat(u.eventSourceDefs),cmdFormatter:u.cmdFormatter||s.cmdFormatter,recurringTypes:s.recurringTypes.concat(u.recurringTypes),namedTimeZonedImpl:u.namedTimeZonedImpl||s.namedTimeZonedImpl,initialView:s.initialView||u.initialView,elementDraggingImpl:s.elementDraggingImpl||u.elementDraggingImpl,optionChangeHandlers:i(i({},s.optionChangeHandlers),u.optionChangeHandlers),scrollGridImpl:u.scrollGridImpl||s.scrollGridImpl,contentTypeHandlers:i(i({},s.contentTypeHandlers),u.contentTypeHandlers),listenerRefiners:i(i({},s.listenerRefiners),u.listenerRefiners),optionRefiners:i(i({},s.optionRefiners),u.optionRefiners),propSetHandlers:i(i({},s.propSetHandlers),u.propSetHandlers)})}var s,u}return e&&o(e),o(t),r}(r,o)),t=r,n=o,e}}var Dr=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t}(nr);function Rr(e,t,n,r){if(t[e])return t[e];var o=function(e,t,n,r){var o=n[e],a=r[e],l=function(e){return o&&null!==o[e]?o[e]:a&&null!==a[e]?a[e]:null},s=l("component"),u=l("superType"),c=null;if(u){if(u===e)throw new Error("Can't have a custom view type that references itself");c=Rr(u,t,n,r)}!s&&c&&(s=c.component);if(!s)return null;return{type:e,component:s,defaults:i(i({},c?c.defaults:{}),o?o.rawOptions:{}),overrides:i(i({},c?c.overrides:{}),a?a.rawOptions:{})}}(e,t,n,r);return o&&(t[e]=o),o}Dr.prototype.classes={root:"fc-theme-standard",tableCellShaded:"fc-cell-shaded",buttonGroup:"fc-button-group",button:"fc-button fc-button-primary",buttonActive:"fc-button-active"},Dr.prototype.baseIconClass="fc-icon",Dr.prototype.iconClasses={close:"fc-icon-x",prev:"fc-icon-chevron-left",next:"fc-icon-chevron-right",prevYear:"fc-icon-chevrons-left",nextYear:"fc-icon-chevrons-right"},Dr.prototype.rtlIconClasses={prev:"fc-icon-chevron-right",next:"fc-icon-chevron-left",prevYear:"fc-icon-chevrons-right",nextYear:"fc-icon-chevrons-left"},Dr.prototype.iconOverrideOption="buttonIcons",Dr.prototype.iconOverrideCustomButtonOption="icon",Dr.prototype.iconOverridePrefix="fc-icon-";var kr=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.rootElRef=u(),t.handleRootEl=function(e){cr(t.rootElRef,e),t.props.elRef&&cr(t.props.elRef,e)},t}return o(t,e),t.prototype.render=function(){var e=this,t=this.props,n=t.hookProps;return s(Mr,{hookProps:n,didMount:t.didMount,willUnmount:t.willUnmount,elRef:this.handleRootEl},(function(r){return s(Tr,{hookProps:n,content:t.content,defaultContent:t.defaultContent,backupElRef:e.rootElRef},(function(e,o){return t.children(r,Ir(t.classNames,n),e,o)}))}))},t}(lr),Or=d(0);function Tr(e){return s(Or.Consumer,null,(function(t){return s(xr,i({renderId:t},e))}))}var xr=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.innerElRef=u(),t}return o(t,e),t.prototype.render=function(){return this.props.children(this.innerElRef,this.renderInnerContent())},t.prototype.componentDidMount=function(){this.updateCustomContent()},t.prototype.componentDidUpdate=function(){this.updateCustomContent()},t.prototype.componentWillUnmount=function(){this.customContentInfo&&this.customContentInfo.destroy&&this.customContentInfo.destroy()},t.prototype.renderInnerContent=function(){var e=this.context.pluginHooks.contentTypeHandlers,t=this.props,n=this.customContentInfo,r=jr(t.content,t.hookProps),o=null;if(void 0===r&&(r=jr(t.defaultContent,t.hookProps)),void 0!==r){if(n)n.contentVal=r[n.contentKey];else if("object"===typeof r)for(var a in e)if(void 0!==r[a]){var l=e[a]();n=this.customContentInfo=i({contentKey:a,contentVal:r[a]},l);break}o=n?[]:r}return o},t.prototype.updateCustomContent=function(){this.customContentInfo&&this.customContentInfo.render(this.innerElRef.current||this.props.backupElRef.current,this.customContentInfo.contentVal)},t}(lr),Mr=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.handleRootEl=function(e){t.rootEl=e,t.props.elRef&&cr(t.props.elRef,e)},t}return o(t,e),t.prototype.render=function(){return this.props.children(this.handleRootEl)},t.prototype.componentDidMount=function(){var e=this.props.didMount;e&&e(i(i({},this.props.hookProps),{el:this.rootEl}))},t.prototype.componentWillUnmount=function(){var e=this.props.willUnmount;e&&e(i(i({},this.props.hookProps),{el:this.rootEl}))},t}(lr);function Pr(){var e,t,n=[];return function(r,o){return t&&ue(t,o)&&r===e||(e=r,t=o,n=Ir(r,o)),n}}function Ir(e,t){return"function"===typeof e&&(e=e(t)),dt(e)}function jr(e,t){return"function"===typeof e?e(t,s):e}var Nr=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.normalizeClassNames=Pr(),t}return o(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r={view:t.viewApi},o=this.normalizeClassNames(n.viewClassNames,r);return s(Mr,{hookProps:r,didMount:n.viewDidMount,willUnmount:n.viewWillUnmount,elRef:e.elRef},(function(t){return e.children(t,["fc-"+e.viewSpec.type+"-view","fc-view"].concat(o))}))},t}(lr);function _r(e){return ae(e,Hr)}function Hr(e){var t,n="function"===typeof e?{component:e}:e,r=n.component;return n.content&&(t=n,r=function(e){return s(or.Consumer,null,(function(n){return s(Nr,{viewSpec:n.viewSpec},(function(r,o){var a=i(i({},e),{nextDayThreshold:n.options.nextDayThreshold});return s(kr,{hookProps:a,classNames:t.classNames,content:t.content,didMount:t.didMount,willUnmount:t.willUnmount,elRef:r},(function(e,t,n,r){return s("div",{className:o.concat(t).join(" "),ref:e},r)}))}))}))}),{superType:n.type,component:r,rawOptions:n}}function Lr(e,t,n,r){var o=_r(e),a=_r(t.views);return ae(function(e,t){var n,r={};for(n in e)Rr(n,r,e,t);for(n in t)Rr(n,r,e,t);return r}(o,a),(function(e){return function(e,t,n,r,o){var a=e.overrides.duration||e.defaults.duration||r.duration||n.duration,l=null,s="",u="",c={};if(a&&(l=function(e){var t=JSON.stringify(e),n=Ar[t];void 0===n&&(n=ye(e),Ar[t]=n);return n}(a))){var d=Oe(l);s=d.unit,1===d.value&&(u=s,c=t[s]?t[s].rawOptions:{})}var f=function(t){var n=t.buttonText||{},r=e.defaults.buttonTextKey;return null!=r&&null!=n[r]?n[r]:null!=n[e.type]?n[e.type]:null!=n[u]?n[u]:null};return{type:e.type,component:e.component,duration:l,durationUnit:s,singleUnit:u,optionDefaults:e.defaults,optionOverrides:i(i({},c),e.overrides),buttonTextOverride:f(r)||f(n)||e.overrides.buttonText,buttonTextDefault:f(o)||e.defaults.buttonText||f($e)||e.type}}(e,a,t,n,r)}))}var Ar={};var zr=function(){function e(e){this.props=e,this.nowDate=un(e.nowInput,e.dateEnv),this.initHiddenDays()}return e.prototype.buildPrev=function(e,t,n){var r=this.props.dateEnv,o=r.subtract(r.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(o,-1,n)},e.prototype.buildNext=function(e,t,n){var r=this.props.dateEnv,o=r.add(r.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(o,1,n)},e.prototype.build=function(e,t,n){void 0===n&&(n=!0);var r,o,i,a,l,s,u,c,d=this.props;return r=this.buildValidRange(),r=this.trimHiddenDays(r),n&&(u=e,e=null!=(c=r).start&&u<c.start?c.start:null!=c.end&&u>=c.end?new Date(c.end.valueOf()-1):u),o=this.buildCurrentRangeInfo(e,t),i=/^(year|month|week|day)$/.test(o.unit),a=this.buildRenderRange(this.trimHiddenDays(o.range),o.unit,i),l=a=this.trimHiddenDays(a),d.showNonCurrentDates||(l=xt(l,o.range)),l=xt(l=this.adjustActiveRange(l),r),s=Pt(o.range,r),{validRange:r,currentRange:o.range,currentRangeUnit:o.unit,isRangeAllDay:i,activeRange:l,renderRange:a,slotMinTime:d.slotMinTime,slotMaxTime:d.slotMaxTime,isValid:s,dateIncrement:this.buildDateIncrement(o.duration)}},e.prototype.buildValidRange=function(){var e=this.props.validRangeInput,t="function"===typeof e?e.call(this.props.calendarApi,this.nowDate):e;return this.refineRange(t)||{start:null,end:null}},e.prototype.buildCurrentRangeInfo=function(e,t){var n,r=this.props,o=null,i=null,a=null;return r.duration?(o=r.duration,i=r.durationUnit,a=this.buildRangeFromDuration(e,t,o,i)):(n=this.props.dayCount)?(i="day",a=this.buildRangeFromDayCount(e,t,n)):(a=this.buildCustomVisibleRange(e))?i=r.dateEnv.greatestWholeUnit(a.start,a.end).unit:(i=Oe(o=this.getFallbackDuration()).unit,a=this.buildRangeFromDuration(e,t,o,i)),{duration:o,unit:i,range:a}},e.prototype.getFallbackDuration=function(){return ye({day:1})},e.prototype.adjustActiveRange=function(e){var t=this.props,n=t.dateEnv,r=t.usesMinMaxTime,o=t.slotMinTime,i=t.slotMaxTime,a=e.start,l=e.end;return r&&(we(o)<0&&(a=Y(a),a=n.add(a,o)),we(i)>1&&(l=F(l=Y(l),-1),l=n.add(l,i))),{start:a,end:l}},e.prototype.buildRangeFromDuration=function(e,t,n,r){var o,i,a,l=this.props,s=l.dateEnv,u=l.dateAlignment;if(!u){var c=this.props.dateIncrement;u=c&&Re(c)<Re(n)?Oe(c).unit:r}function d(){o=s.startOf(e,u),i=s.add(o,n),a={start:o,end:i}}return we(n)<=1&&this.isHiddenDay(o)&&(o=Y(o=this.skipHiddenDays(o,t))),d(),this.trimHiddenDays(a)||(e=this.skipHiddenDays(e,t),d()),a},e.prototype.buildRangeFromDayCount=function(e,t,n){var r,o=this.props,i=o.dateEnv,a=o.dateAlignment,l=0,s=e;a&&(s=i.startOf(s,a)),s=Y(s),r=s=this.skipHiddenDays(s,t);do{r=F(r,1),this.isHiddenDay(r)||(l+=1)}while(l<n);return{start:s,end:r}},e.prototype.buildCustomVisibleRange=function(e){var t=this.props,n=t.visibleRangeInput,r="function"===typeof n?n.call(t.calendarApi,t.dateEnv.toDate(e)):n,o=this.refineRange(r);return!o||null!=o.start&&null!=o.end?o:null},e.prototype.buildRenderRange=function(e,t,n){return e},e.prototype.buildDateIncrement=function(e){var t,n=this.props.dateIncrement;return n||((t=this.props.dateAlignment)?ye(1,t):e||ye({days:1}))},e.prototype.refineRange=function(e){if(e){var t=function(e,t){var n=null,r=null;return e.start&&(n=t.createMarker(e.start)),e.end&&(r=t.createMarker(e.end)),n||r?n&&r&&r<n?null:{start:n,end:r}:null}(e,this.props.dateEnv);return t&&(t=Rt(t)),t}return null},e.prototype.initHiddenDays=function(){var e,t=this.props.hiddenDays||[],n=[],r=0;for(!1===this.props.weekends&&t.push(0,6),e=0;e<7;e+=1)(n[e]=-1!==t.indexOf(e))||(r+=1);if(!r)throw new Error("invalid hiddenDays");this.isHiddenDayHash=n},e.prototype.trimHiddenDays=function(e){var t=e.start,n=e.end;return t&&(t=this.skipHiddenDays(t)),n&&(n=this.skipHiddenDays(n,-1,!0)),null==t||null==n||t<n?{start:t,end:n}:null},e.prototype.isHiddenDay=function(e){return e instanceof Date&&(e=e.getUTCDay()),this.isHiddenDayHash[e]},e.prototype.skipHiddenDays=function(e,t,n){for(void 0===t&&(t=1),void 0===n&&(n=!1);this.isHiddenDayHash[(e.getUTCDay()+(n?t:0)+7)%7];)e=F(e,t);return e},e}();function Ur(e,t,n){var r=t?t.activeRange:null;return Fr({},function(e,t){var n=sn(t),r=[].concat(e.eventSources||[]),o=[];e.initialEvents&&r.unshift(e.initialEvents);e.events&&r.unshift(e.events);for(var i=0,a=r;i<a.length;i++){var l=ln(a[i],t,n);l&&o.push(l)}return o}(e,n),r,n)}function Wr(e,t,n,r){var o,a,l=n?n.activeRange:null;switch(t.type){case"ADD_EVENT_SOURCES":return Fr(e,t.sources,l,r);case"REMOVE_EVENT_SOURCE":return o=e,a=t.sourceId,ie(o,(function(e){return e.sourceId!==a}));case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return n?Vr(e,l,r):e;case"FETCH_EVENT_SOURCES":return qr(e,t.sourceIds?le(t.sourceIds):Yr(e,r),l,r);case"RECEIVE_EVENTS":case"RECEIVE_EVENT_ERROR":return function(e,t,n,r){var o,a=e[t];if(a&&n===a.latestFetchId)return i(i({},e),((o={})[t]=i(i({},a),{isFetching:!1,fetchRange:r}),o));return e}(e,t.sourceId,t.fetchId,t.fetchRange);case"REMOVE_ALL_EVENT_SOURCES":return{};default:return e}}function Br(e){for(var t in e)if(e[t].isFetching)return!0;return!1}function Fr(e,t,n,r){for(var o={},a=0,l=t;a<l.length;a++){var s=l[a];o[s.sourceId]=s}return n&&(o=Vr(o,n,r)),i(i({},e),o)}function Vr(e,t,n){return qr(e,ie(e,(function(e){return function(e,t,n){if(!Xr(e,n))return!e.latestFetchId;return!n.options.lazyFetching||!e.fetchRange||e.isFetching||t.start<e.fetchRange.start||t.end>e.fetchRange.end}(e,t,n)})),t,n)}function qr(e,t,n,r){var o={};for(var i in e){var a=e[i];t[i]?o[i]=Gr(a,n,r):o[i]=a}return o}function Gr(e,t,n){var r=n.options,o=n.calendarApi,a=n.pluginHooks.eventSourceDefs[e.sourceDefId],l=O();return a.fetch({eventSource:e,range:t,context:n},(function(i){var a=i.rawEvents;r.eventSourceSuccess&&(a=r.eventSourceSuccess.call(o,a,i.xhr)||a),e.success&&(a=e.success.call(o,a,i.xhr)||a),n.dispatch({type:"RECEIVE_EVENTS",sourceId:e.sourceId,fetchId:l,fetchRange:t,rawEvents:a})}),(function(i){console.warn(i.message,i),r.eventSourceFailure&&r.eventSourceFailure.call(o,i),e.failure&&e.failure(i),n.dispatch({type:"RECEIVE_EVENT_ERROR",sourceId:e.sourceId,fetchId:l,fetchRange:t,error:i})})),i(i({},e),{isFetching:!0,latestFetchId:l})}function Yr(e,t){return ie(e,(function(e){return Xr(e,t)}))}function Xr(e,t){return!t.pluginHooks.eventSourceDefs[e.sourceDefId].ignoreRange}function Zr(e,t){switch(t.type){case"UNSELECT_DATES":return null;case"SELECT_DATES":return t.selection;default:return e}}function $r(e,t){switch(t.type){case"UNSELECT_EVENT":return"";case"SELECT_EVENT":return t.eventInstanceId;default:return e}}function Qr(e,t){var n;switch(t.type){case"UNSET_EVENT_DRAG":return null;case"SET_EVENT_DRAG":return{affectedEvents:(n=t.state).affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return e}}function Kr(e,t){var n;switch(t.type){case"UNSET_EVENT_RESIZE":return null;case"SET_EVENT_RESIZE":return{affectedEvents:(n=t.state).affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return e}}function Jr(e,t,n,r,o){var i=[];return{headerToolbar:e.headerToolbar?eo(e.headerToolbar,e,t,n,r,o,i):null,footerToolbar:e.footerToolbar?eo(e.footerToolbar,e,t,n,r,o,i):null,viewsWithButtons:i}}function eo(e,t,n,r,o,i,a){return ae(e,(function(e){return function(e,t,n,r,o,i,a){var l="rtl"===t.direction,s=t.customButtons||{},u=n.buttonText||{},c=t.buttonText||{};return(e?e.split(" "):[]).map((function(e){return e.split(",").map((function(e){return"title"===e?{buttonName:e}:((t=s[e])?(d=function(e){t.click&&t.click.call(e.target,e,e.target)},(f=r.getCustomButtonIconClass(t))||(f=r.getIconClass(e,l))||(p=t.text)):(n=o[e])?(a.push(e),d=function(){i.changeView(e)},(p=n.buttonTextOverride)||(f=r.getIconClass(e,l))||(p=n.buttonTextDefault)):i[e]&&(d=function(){i[e]()},(p=u[e])||(f=r.getIconClass(e,l))||(p=c[e])),{buttonName:e,buttonClick:d,buttonIcon:f,buttonText:p});var t,n,d,f,p}))}))}(e,t,n,r,o,i,a)}))}function to(e,t,n,r,o){var i=null;"GET"===(e=e.toUpperCase())?t=function(e,t){return e+(-1===e.indexOf("?")?"?":"&")+no(t)}(t,n):i=no(n);var a=new XMLHttpRequest;a.open(e,t,!0),"GET"!==e&&a.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),a.onload=function(){if(a.status>=200&&a.status<400){var e=!1,t=void 0;try{t=JSON.parse(a.responseText),e=!0}catch(n){}e?r(t,a):o("Failure parsing JSON",a)}else o("Request failed",a)},a.onerror=function(){o("Request failed",a)},a.send(i)}function no(e){var t=[];for(var n in e)t.push(encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t.join("&")}function ro(e,t){for(var n=se(t.getCurrentData().eventSources),r=[],o=0,i=e;o<i.length;o++){for(var a=i[o],l=!1,s=0;s<n.length;s+=1)if(n[s]._raw===a){n.splice(s,1),l=!0;break}l||r.push(a)}for(var u=0,c=n;u<c.length;u++){var d=c[u];t.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:d.sourceId})}for(var f=0,p=r;f<p.length;f++){var h=p[f];t.calendarApi.addEventSource(h)}}var oo=[wr({eventSourceDefs:[{ignoreRange:!0,parseMeta:function(e){return Array.isArray(e.events)?e.events:null},fetch:function(e,t){t({rawEvents:e.eventSource.meta})}}]}),wr({eventSourceDefs:[{parseMeta:function(e){return"function"===typeof e.events?e.events:null},fetch:function(e,t,n){var r=e.context.dateEnv;$n(e.eventSource.meta.bind(null,Zt(e.range,r)),(function(e){t({rawEvents:e})}),n)}}]}),wr({eventSourceRefiners:{method:String,extraParams:ot,startParam:String,endParam:String,timeZoneParam:String},eventSourceDefs:[{parseMeta:function(e){return!e.url||"json"!==e.format&&e.format?null:{url:e.url,format:"json",method:(e.method||"GET").toUpperCase(),extraParams:e.extraParams,startParam:e.startParam,endParam:e.endParam,timeZoneParam:e.timeZoneParam}},fetch:function(e,t,n){var r=e.eventSource.meta,o=function(e,t,n){var r,o,a,l,s=n.dateEnv,u=n.options,c={};null==(r=e.startParam)&&(r=u.startParam);null==(o=e.endParam)&&(o=u.endParam);null==(a=e.timeZoneParam)&&(a=u.timeZoneParam);l="function"===typeof e.extraParams?e.extraParams():e.extraParams||{};i(c,l),c[r]=s.formatIso(t.start),c[o]=s.formatIso(t.end),"local"!==s.timeZone&&(c[a]=s.timeZone);return c}(r,e.range,e.context);to(r.method,r.url,o,(function(e,n){t({rawEvents:e,xhr:n})}),(function(e,t){n({message:e,xhr:t})}))}}]}),wr({recurringTypes:[{parse:function(e,t){if(e.daysOfWeek||e.startTime||e.endTime||e.startRecur||e.endRecur){var n={daysOfWeek:e.daysOfWeek||null,startTime:e.startTime||null,endTime:e.endTime||null,startRecur:e.startRecur?t.createMarker(e.startRecur):null,endRecur:e.endRecur?t.createMarker(e.endRecur):null},r=void 0;return e.duration&&(r=e.duration),!r&&e.startTime&&e.endTime&&(o=e.endTime,i=e.startTime,r={years:o.years-i.years,months:o.months-i.months,days:o.days-i.days,milliseconds:o.milliseconds-i.milliseconds}),{allDayGuess:Boolean(!e.startTime&&!e.endTime),duration:r,typeData:n}}var o,i;return null},expand:function(e,t,n){var r=xt(t,{start:e.startRecur,end:e.endRecur});return r?function(e,t,n,r){var o=e?le(e):null,i=Y(n.start),a=n.end,l=[];for(;i<a;){var s=void 0;o&&!o[i.getUTCDay()]||(s=t?r.add(i,t):i,l.push(s)),i=F(i,1)}return l}(e.daysOfWeek,e.startTime,r,n):[]}}],eventRefiners:{daysOfWeek:ot,startTime:ye,endTime:ye,duration:ye,startRecur:ot,endRecur:ot}}),wr({optionChangeHandlers:{events:function(e,t){ro([e],t)},eventSources:ro}}),wr({isLoadingFuncs:[function(e){return Br(e.eventSources)}],contentTypeHandlers:{html:function(){return{render:io}},domNodes:function(){return{render:ao}}},propSetHandlers:{dateProfile:function(e,t){t.emitter.trigger("datesSet",i(i({},Zt(e.activeRange,t.dateEnv)),{view:t.viewApi}))},eventStore:function(e,t){var n=t.emitter;n.hasHandlers("eventsSet")&&n.trigger("eventsSet",pn(e,t))}}})];function io(e,t){e.innerHTML=t}function ao(e,t){var n=Array.prototype.slice.call(e.childNodes),r=Array.prototype.slice.call(t);if(!Me(n,r)){for(var o=0,i=r;o<i.length;o++){var a=i[o];e.appendChild(a)}n.forEach(p)}}var lo=function(){function e(e){this.drainedOption=e,this.isRunning=!1,this.isDirty=!1,this.pauseDepths={},this.timeoutId=0}return e.prototype.request=function(e){this.isDirty=!0,this.isPaused()||(this.clearTimeout(),null==e?this.tryDrain():this.timeoutId=setTimeout(this.tryDrain.bind(this),e))},e.prototype.pause=function(e){void 0===e&&(e="");var t=this.pauseDepths;t[e]=(t[e]||0)+1,this.clearTimeout()},e.prototype.resume=function(e,t){void 0===e&&(e="");var n=this.pauseDepths;if(e in n){if(t)delete n[e];else n[e]-=1,n[e]<=0&&delete n[e];this.tryDrain()}},e.prototype.isPaused=function(){return Object.keys(this.pauseDepths).length},e.prototype.tryDrain=function(){if(!this.isRunning&&!this.isPaused()){for(this.isRunning=!0;this.isDirty;)this.isDirty=!1,this.drained();this.isRunning=!1}},e.prototype.clear=function(){this.clearTimeout(),this.isDirty=!1,this.pauseDepths={}},e.prototype.clearTimeout=function(){this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=0)},e.prototype.drained=function(){this.drainedOption&&this.drainedOption()},e}(),so=function(){function e(e,t){this.runTaskOption=e,this.drainedOption=t,this.queue=[],this.delayedRunner=new lo(this.drain.bind(this))}return e.prototype.request=function(e,t){this.queue.push(e),this.delayedRunner.request(t)},e.prototype.pause=function(e){this.delayedRunner.pause(e)},e.prototype.resume=function(e,t){this.delayedRunner.resume(e,t)},e.prototype.drain=function(){for(var e=this.queue;e.length;){for(var t=[],n=void 0;n=e.shift();)this.runTask(n),t.push(n);this.drained(t)}},e.prototype.runTask=function(e){this.runTaskOption&&this.runTaskOption(e)},e.prototype.drained=function(e){this.drainedOption&&this.drainedOption(e)},e}();function uo(e,t,n){var r;return r=/^(year|month)$/.test(e.currentRangeUnit)?e.currentRange:e.activeRange,n.formatRange(r.start,r.end,Xe(t.titleFormat||function(e){var t=e.currentRangeUnit;if("year"===t)return{year:"numeric"};if("month"===t)return{year:"numeric",month:"long"};var n=G(e.currentRange.start,e.currentRange.end);if(null!==n&&n>1)return{year:"numeric",month:"short",day:"numeric"};return{year:"numeric",month:"long",day:"numeric"}}(e)),{isEndExclusive:e.isRangeAllDay,defaultSeparator:t.titleRangeSeparator})}var co=function(){function e(e){var t=this;this.computeOptionsData=Pe(this._computeOptionsData),this.computeCurrentViewData=Pe(this._computeCurrentViewData),this.organizeRawLocales=Pe(En),this.buildLocale=Pe(wn),this.buildPluginHooks=Cr(),this.buildDateEnv=Pe(fo),this.buildTheme=Pe(po),this.parseToolbars=Pe(Jr),this.buildViewSpecs=Pe(Lr),this.buildDateProfileGenerator=Ie(ho),this.buildViewApi=Pe(vo),this.buildViewUiProps=Ie(yo),this.buildEventUiBySource=Pe(go,ue),this.buildEventUiBases=Pe(mo),this.parseContextBusinessHours=Ie(So),this.buildTitle=Pe(uo),this.emitter=new Qn,this.actionRunner=new so(this._handleAction.bind(this),this.updateData.bind(this)),this.currentCalendarOptionsInput={},this.currentCalendarOptionsRefined={},this.currentViewOptionsInput={},this.currentViewOptionsRefined={},this.currentCalendarOptionsRefiners={},this.getCurrentData=function(){return t.data},this.dispatch=function(e){t.actionRunner.request(e)},this.props=e,this.actionRunner.pause();var n={},r=this.computeOptionsData(e.optionOverrides,n,e.calendarApi),o=r.calendarOptions.initialView||r.pluginHooks.initialView,a=this.computeCurrentViewData(o,r,e.optionOverrides,n);e.calendarApi.currentDataManager=this,this.emitter.setThisContext(e.calendarApi),this.emitter.setOptions(a.options);var l=function(e,t){var n=e.initialDate;return null!=n?t.createMarker(n):un(e.now,t)}(r.calendarOptions,r.dateEnv),s=a.dateProfileGenerator.build(l);jt(s.activeRange,l)||(l=s.currentRange.start);for(var u={dateEnv:r.dateEnv,options:r.calendarOptions,pluginHooks:r.pluginHooks,calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},c=0,d=r.pluginHooks.contextInit;c<d.length;c++){(0,d[c])(u)}for(var f=Ur(r.calendarOptions,s,u),p={dynamicOptionOverrides:n,currentViewType:o,currentDate:l,dateProfile:s,businessHours:this.parseContextBusinessHours(u),eventSources:f,eventUiBases:{},eventStore:{defs:{},instances:{}},renderableEventStore:{defs:{},instances:{}},dateSelection:null,eventSelection:"",eventDrag:null,eventResize:null,selectionConfig:this.buildViewUiProps(u).selectionConfig},h=i(i({},u),p),v=0,g=r.pluginHooks.reducers;v<g.length;v++){var m=g[v];i(p,m(null,null,h))}bo(p,u)&&this.emitter.trigger("loading",!0),this.state=p,this.updateData(),this.actionRunner.resume()}return e.prototype.resetOptions=function(e,t){var n=this.props;n.optionOverrides=t?i(i({},n.optionOverrides),e):e,this.actionRunner.request({type:"NOTHING"})},e.prototype._handleAction=function(e){var t=this,n=t.props,r=t.state,o=t.emitter,a=function(e,t){var n;switch(t.type){case"SET_OPTION":return i(i({},e),((n={})[t.optionName]=t.rawOptionValue,n));default:return e}}(r.dynamicOptionOverrides,e),l=this.computeOptionsData(n.optionOverrides,a,n.calendarApi),s=function(e,t){switch(t.type){case"CHANGE_VIEW_TYPE":e=t.viewType}return e}(r.currentViewType,e),u=this.computeCurrentViewData(s,l,n.optionOverrides,a);n.calendarApi.currentDataManager=this,o.setThisContext(n.calendarApi),o.setOptions(u.options);var c={dateEnv:l.dateEnv,options:l.calendarOptions,pluginHooks:l.pluginHooks,calendarApi:n.calendarApi,dispatch:this.dispatch,emitter:o,getCurrentData:this.getCurrentData},d=r.currentDate,f=r.dateProfile;this.data&&this.data.dateProfileGenerator!==u.dateProfileGenerator&&(f=u.dateProfileGenerator.build(d)),f=function(e,t,n,r){var o;switch(t.type){case"CHANGE_VIEW_TYPE":return r.build(t.dateMarker||n);case"CHANGE_DATE":if(!e.activeRange||!jt(e.currentRange,t.dateMarker))return r.build(t.dateMarker);break;case"PREV":if((o=r.buildPrev(e,n)).isValid)return o;break;case"NEXT":if((o=r.buildNext(e,n)).isValid)return o}return e}(f,e,d=function(e,t){switch(t.type){case"CHANGE_DATE":return t.dateMarker;default:return e}}(d,e),u.dateProfileGenerator),jt(f.currentRange,d)||(d=f.currentRange.start);for(var p=Wr(r.eventSources,e,f,c),h=dr(r.eventStore,e,p,f,c),v=Br(p)&&!u.options.progressiveEventRendering&&r.renderableEventStore||h,g=this.buildViewUiProps(c),m=g.eventUiSingleBase,y=g.selectionConfig,b=this.buildEventUiBySource(p),S={dynamicOptionOverrides:a,currentViewType:s,currentDate:d,dateProfile:f,eventSources:p,eventStore:h,renderableEventStore:v,selectionConfig:y,eventUiBases:this.buildEventUiBases(v.defs,m,b),businessHours:this.parseContextBusinessHours(c),dateSelection:Zr(r.dateSelection,e),eventSelection:$r(r.eventSelection,e),eventDrag:Qr(r.eventDrag,e),eventResize:Kr(r.eventResize,e)},E=i(i({},c),S),w=0,C=l.pluginHooks.reducers;w<C.length;w++){var D=C[w];i(S,D(r,e,E))}var R=bo(r,c),k=bo(S,c);!R&&k?o.trigger("loading",!0):R&&!k&&o.trigger("loading",!1),this.state=S,n.onAction&&n.onAction(e)},e.prototype.updateData=function(){var e=this.props,t=this.state,n=this.data,r=this.computeOptionsData(e.optionOverrides,t.dynamicOptionOverrides,e.calendarApi),o=this.computeCurrentViewData(t.currentViewType,r,e.optionOverrides,t.dynamicOptionOverrides),a=this.data=i(i(i({viewTitle:this.buildTitle(t.dateProfile,o.options,r.dateEnv),calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},r),o),t),l=r.pluginHooks.optionChangeHandlers,s=n&&n.calendarOptions,u=r.calendarOptions;if(s&&s!==u)for(var c in s.timeZone!==u.timeZone&&(t.eventSources=a.eventSources=function(e,t,n){var r=t?t.activeRange:null;return qr(e,Yr(e,n),r,n)}(a.eventSources,t.dateProfile,a),t.eventStore=a.eventStore=function(e,t,n){var r=e.defs,o=ae(e.instances,(function(e){var o=r[e.defId];return o.allDay||o.recurringDef?e:i(i({},e),{range:{start:n.createMarker(t.toDate(e.range.start,e.forcedStartTzo)),end:n.createMarker(t.toDate(e.range.end,e.forcedEndTzo))},forcedStartTzo:n.canComputeOffset?null:e.forcedStartTzo,forcedEndTzo:n.canComputeOffset?null:e.forcedEndTzo})}));return{defs:r,instances:o}}(a.eventStore,n.dateEnv,a.dateEnv)),l)s[c]!==u[c]&&l[c](u[c],a);e.onData&&e.onData(a)},e.prototype._computeOptionsData=function(e,t,n){var r=this.processRawCalendarOptions(e,t),o=r.refinedOptions,i=r.pluginHooks,a=r.localeDefaults,l=r.availableLocaleData;Eo(r.extra);var s=this.buildDateEnv(o.timeZone,o.locale,o.weekNumberCalculation,o.firstDay,o.weekText,i,l,o.defaultRangeSeparator),u=this.buildViewSpecs(i.views,e,t,a),c=this.buildTheme(o,i);return{calendarOptions:o,pluginHooks:i,dateEnv:s,viewSpecs:u,theme:c,toolbarConfig:this.parseToolbars(o,e,c,u,n),localeDefaults:a,availableRawLocales:l.map}},e.prototype.processRawCalendarOptions=function(e,t){var n=nt([$e,e,t]),r=n.locales,o=n.locale,a=this.organizeRawLocales(r),l=a.map,s=this.buildLocale(o||a.defaultCode,l).options,u=this.buildPluginHooks(e.plugins||[],oo),c=this.currentCalendarOptionsRefiners=i(i(i(i(i({},Ze),Qe),Ke),u.listenerRefiners),u.optionRefiners),d={},f=nt([$e,s,e,t]),p={},h=this.currentCalendarOptionsInput,v=this.currentCalendarOptionsRefined,g=!1;for(var m in f)"plugins"!==m&&(f[m]===h[m]||Je[m]&&m in h&&Je[m](h[m],f[m])?p[m]=v[m]:c[m]?(p[m]=c[m](f[m]),g=!0):d[m]=h[m]);return g&&(this.currentCalendarOptionsInput=f,this.currentCalendarOptionsRefined=p),{rawOptions:this.currentCalendarOptionsInput,refinedOptions:this.currentCalendarOptionsRefined,pluginHooks:u,availableLocaleData:a,localeDefaults:s,extra:d}},e.prototype._computeCurrentViewData=function(e,t,n,r){var o=t.viewSpecs[e];if(!o)throw new Error('viewType "'+e+"\" is not available. Please make sure you've loaded all neccessary plugins");var i=this.processRawViewOptions(o,t.pluginHooks,t.localeDefaults,n,r),a=i.refinedOptions;return Eo(i.extra),{viewSpec:o,options:a,dateProfileGenerator:this.buildDateProfileGenerator({dateProfileGeneratorClass:o.optionDefaults.dateProfileGeneratorClass,duration:o.duration,durationUnit:o.durationUnit,usesMinMaxTime:o.optionDefaults.usesMinMaxTime,dateEnv:t.dateEnv,calendarApi:this.props.calendarApi,slotMinTime:a.slotMinTime,slotMaxTime:a.slotMaxTime,showNonCurrentDates:a.showNonCurrentDates,dayCount:a.dayCount,dateAlignment:a.dateAlignment,dateIncrement:a.dateIncrement,hiddenDays:a.hiddenDays,weekends:a.weekends,nowInput:a.now,validRangeInput:a.validRange,visibleRangeInput:a.visibleRange,monthMode:a.monthMode,fixedWeekCount:a.fixedWeekCount}),viewApi:this.buildViewApi(e,this.getCurrentData,t.dateEnv)}},e.prototype.processRawViewOptions=function(e,t,n,r,o){var a=nt([$e,e.optionDefaults,n,r,e.optionOverrides,o]),l=i(i(i(i(i(i({},Ze),Qe),Ke),tt),t.listenerRefiners),t.optionRefiners),s={},u=this.currentViewOptionsInput,c=this.currentViewOptionsRefined,d=!1,f={};for(var p in a)a[p]===u[p]?s[p]=c[p]:(a[p]===this.currentCalendarOptionsInput[p]?p in this.currentCalendarOptionsRefined&&(s[p]=this.currentCalendarOptionsRefined[p]):l[p]?s[p]=l[p](a[p]):f[p]=a[p],d=!0);return d&&(this.currentViewOptionsInput=a,this.currentViewOptionsRefined=s),{rawOptions:this.currentViewOptionsInput,refinedOptions:this.currentViewOptionsRefined,extra:f}},e}();function fo(e,t,n,r,o,i,a,l){var s=wn(t||a.defaultCode,a.map);return new yn({calendarSystem:"gregory",timeZone:e,namedTimeZoneImpl:i.namedTimeZonedImpl,locale:s,weekNumberCalculation:n,firstDay:r,weekText:o,cmdFormatter:i.cmdFormatter,defaultSeparator:l})}function po(e,t){return new(t.themeClasses[e.themeSystem]||Dr)(e)}function ho(e){return new(e.dateProfileGeneratorClass||zr)(e)}function vo(e,t,n){return new on(e,t,n)}function go(e){return ae(e,(function(e){return e.ui}))}function mo(e,t,n){var r={"":t};for(var o in e){var i=e[o];i.sourceId&&n[i.sourceId]&&(r[o]=n[i.sourceId])}return r}function yo(e){var t=e.options;return{eventUiSingleBase:ht({display:t.eventDisplay,editable:t.editable,startEditable:t.eventStartEditable,durationEditable:t.eventDurationEditable,constraint:t.eventConstraint,overlap:"boolean"===typeof t.eventOverlap?t.eventOverlap:void 0,allow:t.eventAllow,backgroundColor:t.eventBackgroundColor,borderColor:t.eventBorderColor,textColor:t.eventTextColor,color:t.eventColor},e),selectionConfig:ht({constraint:t.selectConstraint,overlap:"boolean"===typeof t.selectOverlap?t.selectOverlap:void 0,allow:t.selectAllow},e)}}function bo(e,t){for(var n=0,r=t.pluginHooks.isLoadingFuncs;n<r.length;n++){if((0,r[n])(e))return!0}return!1}function So(e){return kn(e.options.businessHours,e)}function Eo(e,t){for(var n in e)console.warn("Unknown option '"+n+"'"+(t?" for view '"+t+"'":""))}var wo=function(e){function t(t){var n=e.call(this,t)||this;return n.handleData=function(e){n.dataManager?n.setState(e):n.state=e},n.dataManager=new co({optionOverrides:t.optionOverrides,calendarApi:t.calendarApi,onData:n.handleData}),n}return o(t,e),t.prototype.render=function(){return this.props.children(this.state)},t.prototype.componentDidUpdate=function(e){var t=this.props.optionOverrides;t!==e.optionOverrides&&this.dataManager.resetOptions(t)},t}(l);var Co=function(){function e(e){this.component=e.component}return e.prototype.destroy=function(){},e}();function Do(e,t){return{component:e,el:t.el,useEventCenter:null==t.useEventCenter||t.useEventCenter}}function Ro(e){var t;return(t={})[e.component.uid]=e,t}var ko={},Oo=function(){function e(e,t){this.emitter=new Qn}return e.prototype.destroy=function(){},e.prototype.setMirrorIsVisible=function(e){},e.prototype.setMirrorNeedsRevert=function(e){},e.prototype.setAutoScrollEnabled=function(e){},e}(),To={},xo={startTime:ye,duration:ye,create:Boolean,sourceId:String};function Mo(e){var t=rt(e,xo),n=t.refined,r=t.extra;return{startTime:n.startTime||null,duration:n.duration||null,create:null==n.create||n.create,sourceId:n.sourceId,leftoverProps:r}}var Po=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.render=function(){var e=this,t=this.props.widgetGroups.map((function(t){return e.renderWidgetGroup(t)}));return s.apply(void 0,a(["div",{className:"fc-toolbar-chunk"}],t))},t.prototype.renderWidgetGroup=function(e){for(var t=this.props,n=this.context.theme,r=[],o=!0,l=0,u=e;l<u.length;l++){var c=u[l],d=c.buttonName,f=c.buttonClick,p=c.buttonText,h=c.buttonIcon;if("title"===d)o=!1,r.push(s("h2",{className:"fc-toolbar-title"},t.title));else{var v=h?{"aria-label":d}:{},g=["fc-"+d+"-button",n.getClass("button")];d===t.activeButton&&g.push(n.getClass("buttonActive"));var m=!t.isTodayEnabled&&"today"===d||!t.isPrevEnabled&&"prev"===d||!t.isNextEnabled&&"next"===d;r.push(s("button",i({disabled:m,className:g.join(" "),onClick:f,type:"button"},v),p||(h?s("span",{className:h}):"")))}}if(r.length>1){var y=o&&n.getClass("buttonGroup")||"";return s.apply(void 0,a(["div",{className:y}],r))}return r[0]},t}(lr),Io=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.render=function(){var e,t,n=this.props,r=n.model,o=n.extraClassName,i=!1,a=r.center;return r.left?(i=!0,e=r.left):e=r.start,r.right?(i=!0,t=r.right):t=r.end,s("div",{className:[o||"","fc-toolbar",i?"fc-toolbar-ltr":""].join(" ")},this.renderSection("start",e||[]),this.renderSection("center",a||[]),this.renderSection("end",t||[]))},t.prototype.renderSection=function(e,t){var n=this.props;return s(Po,{key:e,widgetGroups:t,title:n.title,activeButton:n.activeButton,isTodayEnabled:n.isTodayEnabled,isPrevEnabled:n.isPrevEnabled,isNextEnabled:n.isNextEnabled})},t}(lr),jo=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={availableWidth:null},t.handleEl=function(e){t.el=e,cr(t.props.elRef,e),t.updateAvailableWidth()},t.handleResize=function(){t.updateAvailableWidth()},t}return o(t,e),t.prototype.render=function(){var e=this.props,t=this.state,n=e.aspectRatio,r=["fc-view-harness",n||e.liquid||e.height?"fc-view-harness-active":"fc-view-harness-passive"],o="",i="";return n?null!==t.availableWidth?o=t.availableWidth/n:i=1/n*100+"%":o=e.height||"",s("div",{ref:this.handleEl,onClick:e.onClick,className:r.join(" "),style:{height:o,paddingBottom:i}},e.children)},t.prototype.componentDidMount=function(){this.context.addResizeHandler(this.handleResize)},t.prototype.componentWillUnmount=function(){this.context.removeResizeHandler(this.handleResize)},t.prototype.updateAvailableWidth=function(){this.el&&this.props.aspectRatio&&this.setState({availableWidth:this.el.offsetWidth})},t}(lr),No=function(e){function t(t){var n=e.call(this,t)||this;return n.handleSegClick=function(e,t){var r=n.component,o=r.context,i=Ht(t);if(i&&r.isValidSegDownEl(e.target)){var a=h(e.target,".fc-event-forced-url"),l=a?a.querySelector("a[href]").href:"";o.emitter.trigger("eventClick",{el:t,event:new dn(r.context,i.eventRange.def,i.eventRange.instance),jsEvent:e,view:o.viewApi}),l&&!e.defaultPrevented&&(window.location.href=l)}},n.destroy=C(t.el,"click",".fc-event",n.handleSegClick),n}return o(t,e),t}(Co),_o=function(e){function t(t){var n=e.call(this,t)||this;return n.handleEventElRemove=function(e){e===n.currentSegEl&&n.handleSegLeave(null,n.currentSegEl)},n.handleSegEnter=function(e,t){Ht(t)&&(n.currentSegEl=t,n.triggerEvent("eventMouseEnter",e,t))},n.handleSegLeave=function(e,t){n.currentSegEl&&(n.currentSegEl=null,n.triggerEvent("eventMouseLeave",e,t))},n.removeHoverListeners=function(e,t,n,r){var o;return C(e,"mouseover",t,(function(e,t){t!==o&&(o=t,n(e,t),t.addEventListener("mouseleave",(function e(n){o=null,r(n,t),t.removeEventListener("mouseleave",e)})))}))}(t.el,".fc-event",n.handleSegEnter,n.handleSegLeave),n}return o(t,e),t.prototype.destroy=function(){this.removeHoverListeners()},t.prototype.triggerEvent=function(e,t,n){var r=this.component,o=r.context,i=Ht(n);t&&!r.isValidSegDownEl(t.target)||o.emitter.trigger(e,{el:n,event:new dn(o,i.eventRange.def,i.eventRange.instance),jsEvent:t,view:o.viewApi})},t}(Co),Ho=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.buildViewContext=Pe(ir),t.buildViewPropTransformers=Pe(Ao),t.buildToolbarProps=Pe(Lo),t.handleNavLinkClick=w("a[data-navlink]",t._handleNavLinkClick.bind(t)),t.headerRef=u(),t.footerRef=u(),t.interactionsStore={},t.registerInteractiveComponent=function(e,n){var r=Do(e,n),o=[No,_o].concat(t.props.pluginHooks.componentInteractions).map((function(e){return new e(r)}));t.interactionsStore[e.uid]=o,ko[e.uid]=r},t.unregisterInteractiveComponent=function(e){for(var n=0,r=t.interactionsStore[e.uid];n<r.length;n++){r[n].destroy()}delete t.interactionsStore[e.uid],delete ko[e.uid]},t.resizeRunner=new lo((function(){t.props.emitter.trigger("_resize",!0),t.props.emitter.trigger("windowResize",{view:t.props.viewApi})})),t.handleWindowResize=function(e){var n=t.props.options;n.handleWindowResize&&e.target===window&&t.resizeRunner.request(n.windowResizeDelay)},t}return o(t,e),t.prototype.render=function(){var e,t=this.props,n=t.toolbarConfig,r=t.options,o=this.buildToolbarProps(t.viewSpec,t.dateProfile,t.dateProfileGenerator,t.currentDate,un(t.options.now,t.dateEnv),t.viewTitle),a=!1,l="";t.isHeightAuto||t.forPrint?l="":null!=r.height?a=!0:null!=r.contentHeight?l=r.contentHeight:e=Math.max(r.aspectRatio,.5);var u=this.buildViewContext(t.viewSpec,t.viewApi,t.options,t.dateProfileGenerator,t.dateEnv,t.theme,t.pluginHooks,t.dispatch,t.getCurrentData,t.emitter,t.calendarApi,this.registerInteractiveComponent,this.unregisterInteractiveComponent);return s(or.Provider,{value:u},n.headerToolbar&&s(Io,i({ref:this.headerRef,extraClassName:"fc-header-toolbar",model:n.headerToolbar},o)),s(jo,{liquid:a,height:l,aspectRatio:e,onClick:this.handleNavLinkClick},this.renderView(t),this.buildAppendContent()),n.footerToolbar&&s(Io,i({ref:this.footerRef,extraClassName:"fc-footer-toolbar",model:n.footerToolbar},o)))},t.prototype.componentDidMount=function(){var e=this.props;this.calendarInteractions=e.pluginHooks.calendarInteractions.map((function(t){return new t(e)})),window.addEventListener("resize",this.handleWindowResize);var t=e.pluginHooks.propSetHandlers;for(var n in t)t[n](e[n],e)},t.prototype.componentDidUpdate=function(e){var t=this.props,n=t.pluginHooks.propSetHandlers;for(var r in n)t[r]!==e[r]&&n[r](t[r],t)},t.prototype.componentWillUnmount=function(){window.removeEventListener("resize",this.handleWindowResize),this.resizeRunner.clear();for(var e=0,t=this.calendarInteractions;e<t.length;e++){t[e].destroy()}this.props.emitter.trigger("_unmount")},t.prototype._handleNavLinkClick=function(e,t){var n=this.props,r=n.dateEnv,o=n.options,i=n.calendarApi,a=t.getAttribute("data-navlink");a=a?JSON.parse(a):{};var l=r.createMarker(a.date),s=a.type,u="day"===s?o.navLinkDayClick:"week"===s?o.navLinkWeekClick:null;"function"===typeof u?u.call(i,r.toDate(l),e):("string"===typeof u&&(s=u),i.zoomTo(l,s))},t.prototype.buildAppendContent=function(){var e=this.props,t=e.pluginHooks.viewContainerAppends.map((function(t){return t(e)}));return s.apply(void 0,a([c,{}],t))},t.prototype.renderView=function(e){for(var t=e.pluginHooks,n=e.viewSpec,r={dateProfile:e.dateProfile,businessHours:e.businessHours,eventStore:e.renderableEventStore,eventUiBases:e.eventUiBases,dateSelection:e.dateSelection,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,isHeightAuto:e.isHeightAuto,forPrint:e.forPrint},o=0,a=this.buildViewPropTransformers(t.viewPropsTransformers);o<a.length;o++){var l=a[o];i(r,l.transform(r,e))}var u=n.component;return s(u,i({},r))},t}(ar);function Lo(e,t,n,r,o,i){var a=n.build(o,void 0,!1),l=n.buildPrev(t,r,!1),s=n.buildNext(t,r,!1);return{title:i,activeButton:e.type,isTodayEnabled:a.isValid&&!jt(t.currentRange,o),isPrevEnabled:l.isValid,isNextEnabled:s.isValid}}function Ao(e){return e.map((function(e){return new e}))}var zo=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={forPrint:!1},t.handleBeforePrint=function(){t.setState({forPrint:!0})},t.handleAfterPrint=function(){t.setState({forPrint:!1})},t}return o(t,e),t.prototype.render=function(){var e=this.props,t=e.options,n=this.state.forPrint,r=n||"auto"===t.height||"auto"===t.contentHeight,o=r||null==t.height?"":t.height,i=["fc",n?"fc-media-print":"fc-media-screen","fc-direction-"+t.direction,e.theme.getClass("root")];return jn()||i.push("fc-liquid-hack"),e.children(i,o,r,n)},t.prototype.componentDidMount=function(){var e=this.props.emitter;e.on("_beforeprint",this.handleBeforePrint),e.on("_afterprint",this.handleAfterPrint)},t.prototype.componentWillUnmount=function(){var e=this.props.emitter;e.off("_beforeprint",this.handleBeforePrint),e.off("_afterprint",this.handleAfterPrint)},t}(lr);function Uo(e,t){return Xe(!e||t>10?{weekday:"short"}:t>1?{weekday:"short",month:"numeric",day:"numeric",omitCommas:!0}:{weekday:"long"})}var Wo="fc-col-header-cell";function Bo(e){return e.text}var Fo=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.render=function(){var e=this.context,t=e.dateEnv,n=e.options,r=e.theme,o=e.viewApi,a=this.props,l=a.date,u=a.dateProfile,c=Ln(l,a.todayRange,null,u),d=[Wo].concat(An(c,r)),f=t.format(l,a.dayHeaderFormat),p=n.navLinks&&!c.isDisabled&&a.colCnt>1?{"data-navlink":Un(l),tabIndex:0}:{},h=i(i(i({date:t.toDate(l),view:o},a.extraHookProps),{text:f}),c);return s(kr,{hookProps:h,classNames:n.dayHeaderClassNames,content:n.dayHeaderContent,defaultContent:Bo,didMount:n.dayHeaderDidMount,willUnmount:n.dayHeaderWillUnmount},(function(e,t,n,r){return s("th",i({ref:e,className:d.concat(t).join(" "),"data-date":c.isDisabled?void 0:Te(l),colSpan:a.colSpan},a.extraDataAttrs),s("div",{className:"fc-scrollgrid-sync-inner"},!c.isDisabled&&s("a",i({ref:n,className:["fc-col-header-cell-cushion",a.isSticky?"fc-sticky":""].join(" ")},p),r)))}))},t}(lr),Vo=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.dateEnv,r=t.theme,o=t.viewApi,a=t.options,l=F(new Date(2592e5),e.dow),u={dow:e.dow,isDisabled:!1,isFuture:!1,isPast:!1,isToday:!1,isOther:!1},c=[Wo].concat(An(u,r),e.extraClassNames||[]),d=n.format(l,e.dayHeaderFormat),f=i(i(i(i({date:l},u),{view:o}),e.extraHookProps),{text:d});return s(kr,{hookProps:f,classNames:a.dayHeaderClassNames,content:a.dayHeaderContent,defaultContent:Bo,didMount:a.dayHeaderDidMount,willUnmount:a.dayHeaderWillUnmount},(function(t,n,r,o){return s("th",i({ref:t,className:c.concat(n).join(" "),colSpan:e.colSpan},e.extraDataAttrs),s("div",{className:"fc-scrollgrid-sync-inner"},s("a",{className:["fc-col-header-cell-cushion",e.isSticky?"fc-sticky":""].join(" "),ref:r},o)))}))},t}(lr),qo=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.initialNowDate=un(n.options.now,n.dateEnv),r.initialNowQueriedMs=(new Date).valueOf(),r.state=r.computeTiming().currentState,r}return o(t,e),t.prototype.render=function(){var e=this.props,t=this.state;return e.children(t.nowDate,t.todayRange)},t.prototype.componentDidMount=function(){this.setTimeout()},t.prototype.componentDidUpdate=function(e){e.unit!==this.props.unit&&(this.clearTimeout(),this.setTimeout())},t.prototype.componentWillUnmount=function(){this.clearTimeout()},t.prototype.computeTiming=function(){var e=this.props,t=this.context,n=V(this.initialNowDate,(new Date).valueOf()-this.initialNowQueriedMs),r=t.dateEnv.startOf(n,e.unit),o=t.dateEnv.add(r,ye(1,e.unit)),i=o.valueOf()-n.valueOf();return i=Math.min(864e5,i),{currentState:{nowDate:r,todayRange:Go(r)},nextState:{nowDate:o,todayRange:Go(o)},waitMs:i}},t.prototype.setTimeout=function(){var e=this,t=this.computeTiming(),n=t.nextState,r=t.waitMs;this.timeoutId=setTimeout((function(){e.setState(n,(function(){e.setTimeout()}))}),r)},t.prototype.clearTimeout=function(){this.timeoutId&&clearTimeout(this.timeoutId)},t.contextType=or,t}(l);function Go(e){var t=Y(e);return{start:t,end:F(t,1)}}!function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.createDayHeaderFormatter=Pe(Yo),t}o(t,e),t.prototype.render=function(){var e=this.context,t=this.props,n=t.dates,r=t.dateProfile,o=t.datesRepDistinctDays,i=t.renderIntro,a=this.createDayHeaderFormatter(e.options.dayHeaderFormat,o,n.length);return s(qo,{unit:"day"},(function(e,t){return s("tr",null,i&&i("day"),n.map((function(e){return o?s(Fo,{key:e.toISOString(),date:e,dateProfile:r,todayRange:t,colCnt:n.length,dayHeaderFormat:a}):s(Vo,{key:e.getUTCDay(),dow:e.getUTCDay(),dayHeaderFormat:a})})))}))}}(lr);function Yo(e,t,n){return e||Uo(t,n)}(function(){function e(e,t){for(var n=e.start,r=e.end,o=[],i=[],a=-1;n<r;)t.isHiddenDay(n)?o.push(a+.5):(a+=1,o.push(a),i.push(n)),n=F(n,1);this.dates=i,this.indices=o,this.cnt=i.length}e.prototype.sliceRange=function(e){var t=this.getDateDayIndex(e.start),n=this.getDateDayIndex(F(e.end,-1)),r=Math.max(0,t),o=Math.min(this.cnt-1,n);return(r=Math.ceil(r))<=(o=Math.floor(o))?{firstIndex:r,lastIndex:o,isStart:t===r,isEnd:n===o}:null},e.prototype.getDateDayIndex=function(e){var t=this.indices,n=Math.floor(q(this.dates[0],e));return n<0?t[0]-1:n>=t.length?t[t.length-1]+1:t[n]}})(),function(){function e(e,t){var n,r,o,i=e.dates;if(t){for(r=i[0].getUTCDay(),n=1;n<i.length&&i[n].getUTCDay()!==r;n+=1);o=Math.ceil(i.length/n)}else o=1,n=i.length;this.rowCnt=o,this.colCnt=n,this.daySeries=e,this.cells=this.buildCells(),this.headerDates=this.buildHeaderDates()}e.prototype.buildCells=function(){for(var e=[],t=0;t<this.rowCnt;t+=1){for(var n=[],r=0;r<this.colCnt;r+=1)n.push(this.buildCell(t,r));e.push(n)}return e},e.prototype.buildCell=function(e,t){var n=this.daySeries.dates[e*this.colCnt+t];return{key:n.toISOString(),date:n}},e.prototype.buildHeaderDates=function(){for(var e=[],t=0;t<this.colCnt;t+=1)e.push(this.cells[0][t].date);return e},e.prototype.sliceRange=function(e){var t=this.colCnt,n=this.daySeries.sliceRange(e),r=[];if(n)for(var o=n.firstIndex,i=n.lastIndex,a=o;a<=i;){var l=Math.floor(a/t),s=Math.min((l+1)*t,i+1);r.push({row:l,firstCol:a%t,lastCol:(s-1)%t,isStart:n.isStart&&a===o,isEnd:n.isEnd&&s-1===i}),a=s}return r}}();var Xo=function(){function e(){this.sliceBusinessHours=Pe(this._sliceBusinessHours),this.sliceDateSelection=Pe(this._sliceDateSpan),this.sliceEventStore=Pe(this._sliceEventStore),this.sliceEventDrag=Pe(this._sliceInteraction),this.sliceEventResize=Pe(this._sliceInteraction),this.forceDayIfListItem=!1}return e.prototype.sliceProps=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];var l=e.eventUiBases,s=this.sliceEventStore.apply(this,a([e.eventStore,l,t,n],o));return{dateSelectionSegs:this.sliceDateSelection.apply(this,a([e.dateSelection,l,r],o)),businessHourSegs:this.sliceBusinessHours.apply(this,a([e.businessHours,t,n,r],o)),fgEventSegs:s.fg,bgEventSegs:s.bg,eventDrag:this.sliceEventDrag.apply(this,a([e.eventDrag,l,t,n],o)),eventResize:this.sliceEventResize.apply(this,a([e.eventResize,l,t,n],o)),eventSelection:e.eventSelection}},e.prototype.sliceNowDate=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return this._sliceDateSpan.apply(this,a([{range:{start:e,end:V(e,1)},allDay:!1},{},t],n))},e.prototype._sliceBusinessHours=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];return e?this._sliceEventStore.apply(this,a([he(e,Zo(t,Boolean(n)),r),{},t,n],o)).bg:[]},e.prototype._sliceEventStore=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];if(e){var a=Nt(e,t,Zo(n,Boolean(r)),r);return{bg:this.sliceEventRanges(a.bg,o),fg:this.sliceEventRanges(a.fg,o)}}return{bg:[],fg:[]}},e.prototype._sliceInteraction=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];if(!e)return null;var a=Nt(e.mutatedEvents,t,Zo(n,Boolean(r)),r);return{segs:this.sliceEventRanges(a.fg,o),affectedInstances:e.affectedEvents.instances,isEvent:e.isEvent}},e.prototype._sliceDateSpan=function(e,t,n){for(var r=[],o=3;o<arguments.length;o++)r[o-3]=arguments[o];if(!e)return[];for(var i=Qt(e,t,n),l=this.sliceRange.apply(this,a([e.range],r)),s=0,u=l;s<u.length;s++){var c=u[s];c.eventRange=i}return l},e.prototype.sliceEventRanges=function(e,t){for(var n=[],r=0,o=e;r<o.length;r++){var i=o[r];n.push.apply(n,this.sliceEventRange(i,t))}return n},e.prototype.sliceEventRange=function(e,t){var n=e.range;this.forceDayIfListItem&&"list-item"===e.ui.display&&(n={start:n.start,end:F(n.start,1)});for(var r=this.sliceRange.apply(this,a([n],t)),o=0,i=r;o<i.length;o++){var l=i[o];l.eventRange=e,l.isStart=e.isStart&&l.isStart,l.isEnd=e.isEnd&&l.isEnd}return r},e}();function Zo(e,t){var n=e.activeRange;return t?n:{start:V(n.start,e.slotMinTime.milliseconds),end:V(n.end,e.slotMaxTime.milliseconds-864e5)}}var $o=/^(visible|hidden)$/,Qo=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.handleEl=function(e){t.el=e,cr(t.props.elRef,e)},t}return o(t,e),t.prototype.render=function(){var e=this.props,t=e.liquid,n=e.liquidIsAbsolute,r=t&&n,o=["fc-scroller"];return t&&(n?o.push("fc-scroller-liquid-absolute"):o.push("fc-scroller-liquid")),s("div",{ref:this.handleEl,className:o.join(" "),style:{overflowX:e.overflowX,overflowY:e.overflowY,left:r&&-(e.overcomeLeft||0)||"",right:r&&-(e.overcomeRight||0)||"",bottom:r&&-(e.overcomeBottom||0)||"",marginLeft:!r&&-(e.overcomeLeft||0)||"",marginRight:!r&&-(e.overcomeRight||0)||"",marginBottom:!r&&-(e.overcomeBottom||0)||"",maxHeight:e.maxHeight||""}},e.children)},t.prototype.needsXScrolling=function(){if($o.test(this.props.overflowX))return!1;for(var e=this.el,t=this.el.getBoundingClientRect().width-this.getYScrollbarWidth(),n=e.children,r=0;r<n.length;r+=1){if(n[r].getBoundingClientRect().width>t)return!0}return!1},t.prototype.needsYScrolling=function(){if($o.test(this.props.overflowY))return!1;for(var e=this.el,t=this.el.getBoundingClientRect().height-this.getXScrollbarWidth(),n=e.children,r=0;r<n.length;r+=1){if(n[r].getBoundingClientRect().height>t)return!0}return!1},t.prototype.getXScrollbarWidth=function(){return $o.test(this.props.overflowX)?0:this.el.offsetHeight-this.el.clientHeight},t.prototype.getYScrollbarWidth=function(){return $o.test(this.props.overflowY)?0:this.el.offsetWidth-this.el.clientWidth},t}(lr),Ko=function(){function e(e){var t=this;this.masterCallback=e,this.currentMap={},this.depths={},this.callbackMap={},this.handleValue=function(e,n){var r=t,o=r.depths,i=r.currentMap,a=!1,l=!1;null!==e?(a=n in i,i[n]=e,o[n]=(o[n]||0)+1,l=!0):(o[n]-=1,o[n]||(delete i[n],delete t.callbackMap[n],a=!0)),t.masterCallback&&(a&&t.masterCallback(null,String(n)),l&&t.masterCallback(e,String(n)))}}return e.prototype.createRef=function(e){var t=this,n=this.callbackMap[e];return n||(n=this.callbackMap[e]=function(n){t.handleValue(n,String(e))}),n},e.prototype.collect=function(e,t,n){return pe(this.currentMap,e,t,n)},e.prototype.getAll=function(){return se(this.currentMap)},e}();function Jo(e){for(var t=0,n=0,r=g(e,".fc-scrollgrid-shrink");n<r.length;n++){var o=r[n];t=Math.max(t,W(o))}return Math.ceil(t)}function ei(e,t){return e.liquid&&t.liquid}function ti(e,t){return null!=t.maxHeight||ei(e,t)}function ni(e,t,n){var r=n.expandRows;return"function"===typeof t.content?t.content(n):s("table",{className:[t.tableClassName,e.syncRowHeights?"fc-scrollgrid-sync-table":""].join(" "),style:{minWidth:n.tableMinWidth,width:n.clientWidth,height:r?n.clientHeight:""}},n.tableColGroupNode,s("tbody",{},"function"===typeof t.rowContent?t.rowContent(n):t.rowContent))}function ri(e,t){return Me(e,t,ue)}function oi(e,t){for(var n=[],r=0,o=e;r<o.length;r++)for(var i=o[r],l=i.span||1,u=0;u<l;u+=1)n.push(s("col",{style:{width:"shrink"===i.width?ii(t):i.width||"",minWidth:i.minWidth||""}}));return s.apply(void 0,a(["colgroup",{}],n))}function ii(e){return null==e?4:e}function ai(e){for(var t=0,n=e;t<n.length;t++){if("shrink"===n[t].width)return!0}return!1}function li(e,t){var n=["fc-scrollgrid",t.theme.getClass("table")];return e&&n.push("fc-scrollgrid-liquid"),n}function si(e,t){var n=["fc-scrollgrid-section","fc-scrollgrid-section-"+e.type,e.className];return t&&e.liquid&&null==e.maxHeight&&n.push("fc-scrollgrid-section-liquid"),e.isSticky&&n.push("fc-scrollgrid-section-sticky"),n}function ui(e){return s("div",{className:"fc-scrollgrid-sticky-shim",style:{width:e.clientWidth,minWidth:e.tableMinWidth}})}function ci(e){var t=e.stickyHeaderDates;return null!=t&&"auto"!==t||(t="auto"===e.height||"auto"===e.viewHeight),t}function di(e){var t=e.stickyFooterScrollbar;return null!=t&&"auto"!==t||(t="auto"===e.height||"auto"===e.viewHeight),t}(function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.processCols=Pe((function(e){return e}),ri),t.renderMicroColGroup=Pe(oi),t.scrollerRefs=new Ko,t.scrollerElRefs=new Ko(t._handleScrollerEl.bind(t)),t.state={shrinkWidth:null,forceYScrollbars:!1,scrollerClientWidths:{},scrollerClientHeights:{}},t.handleSizing=function(){t.setState(i({shrinkWidth:t.computeShrinkWidth()},t.computeScrollerDims()))},t}return o(t,e),t.prototype.render=function(){for(var e,t=this,n=t.props,r=t.state,o=t.context,i=n.sections||[],l=this.processCols(n.cols),u=this.renderMicroColGroup(l,r.shrinkWidth),c=li(n.liquid,o),d=i.length,f=0,p=[],h=[],v=[];f<d&&"header"===(e=i[f]).type;)p.push(this.renderSection(e,f,u)),f+=1;for(;f<d&&"body"===(e=i[f]).type;)h.push(this.renderSection(e,f,u)),f+=1;for(;f<d&&"footer"===(e=i[f]).type;)v.push(this.renderSection(e,f,u)),f+=1;var g=!jn();return s("table",{className:c.join(" "),style:{height:n.height}},Boolean(!g&&p.length)&&s.apply(void 0,a(["thead",{}],p)),Boolean(!g&&h.length)&&s.apply(void 0,a(["tbody",{}],h)),Boolean(!g&&v.length)&&s.apply(void 0,a(["tfoot",{}],v)),g&&s.apply(void 0,a(["tbody",{}],p,h,v)))},t.prototype.renderSection=function(e,t,n){return"outerContent"in e?s(c,{key:e.key},e.outerContent):s("tr",{key:e.key,className:si(e,this.props.liquid).join(" ")},this.renderChunkTd(e,t,n,e.chunk))},t.prototype.renderChunkTd=function(e,t,n,r){if("outerContent"in r)return r.outerContent;var o=this.props,i=this.state,a=i.forceYScrollbars,l=i.scrollerClientWidths,u=i.scrollerClientHeights,c=ti(o,e),d=ei(o,e),f=o.liquid?a?"scroll":c?"auto":"hidden":"visible",p=ni(e,r,{tableColGroupNode:n,tableMinWidth:"",clientWidth:void 0!==l[t]?l[t]:null,clientHeight:void 0!==u[t]?u[t]:null,expandRows:e.expandRows,syncRowHeights:!1,rowSyncHeights:[],reportRowHeightChange:function(){}});return s("td",{ref:r.elRef},s("div",{className:"fc-scroller-harness"+(d?" fc-scroller-harness-liquid":"")},s(Qo,{ref:this.scrollerRefs.createRef(t),elRef:this.scrollerElRefs.createRef(t),overflowY:f,overflowX:o.liquid?"hidden":"visible",maxHeight:e.maxHeight,liquid:d,liquidIsAbsolute:!0},p)))},t.prototype._handleScrollerEl=function(e,t){var n=parseInt(t,10);cr(this.props.sections[n].chunk.scrollerElRef,e)},t.prototype.componentDidMount=function(){this.handleSizing(),this.context.addResizeHandler(this.handleSizing)},t.prototype.componentDidUpdate=function(){this.handleSizing()},t.prototype.componentWillUnmount=function(){this.context.removeResizeHandler(this.handleSizing)},t.prototype.computeShrinkWidth=function(){return ai(this.props.cols)?Jo(this.scrollerElRefs.getAll()):0},t.prototype.computeScrollerDims=function(){for(var e=Vn(),t=this.props.sections.length,n=this.scrollerRefs,r=this.scrollerElRefs,o=!1,i={},a={},l=0;l<t;l+=1){var s=n.currentMap[l];if(s&&s.needsYScrolling()){o=!0;break}}for(l=0;l<t;l+=1){var u=r.currentMap[l];if(u){var c=u.parentNode;i[l]=Math.floor(c.getBoundingClientRect().width-(o?e.y:0)),a[l]=Math.floor(c.getBoundingClientRect().height)}}return{forceYScrollbars:o,scrollerClientWidths:i,scrollerClientHeights:a}},t})(lr).addStateEquality({scrollerClientWidths:ue,scrollerClientHeights:ue});var fi=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.elRef=u(),t}return o(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r=e.seg,o=r.eventRange,i=o.ui,a={event:new dn(t,o.def,o.instance),view:t.viewApi,timeText:e.timeText,textColor:i.textColor,backgroundColor:i.backgroundColor,borderColor:i.borderColor,isDraggable:!e.disableDragging&&Wt(r,t),isStartResizable:!e.disableResizing&&Bt(r,t),isEndResizable:!e.disableResizing&&Ft(r),isMirror:Boolean(e.isDragging||e.isResizing||e.isDateSelecting),isStart:Boolean(r.isStart),isEnd:Boolean(r.isEnd),isPast:Boolean(e.isPast),isFuture:Boolean(e.isFuture),isToday:Boolean(e.isToday),isSelected:Boolean(e.isSelected),isDragging:Boolean(e.isDragging),isResizing:Boolean(e.isResizing)},l=function(e){var t=["fc-event"];return e.isMirror&&t.push("fc-event-mirror"),e.isDraggable&&t.push("fc-event-draggable"),(e.isStartResizable||e.isEndResizable)&&t.push("fc-event-resizable"),e.isDragging&&t.push("fc-event-dragging"),e.isResizing&&t.push("fc-event-resizing"),e.isSelected&&t.push("fc-event-selected"),e.isStart&&t.push("fc-event-start"),e.isEnd&&t.push("fc-event-end"),e.isPast&&t.push("fc-event-past"),e.isToday&&t.push("fc-event-today"),e.isFuture&&t.push("fc-event-future"),t}(a).concat(i.classNames);return s(kr,{hookProps:a,classNames:n.eventClassNames,content:n.eventContent,defaultContent:e.defaultContent,didMount:n.eventDidMount,willUnmount:n.eventWillUnmount,elRef:this.elRef},(function(t,n,r,o){return e.children(t,l.concat(n),r,o,a)}))},t.prototype.componentDidMount=function(){_t(this.elRef.current,this.props.seg)},t.prototype.componentDidUpdate=function(e){var t=this.props.seg;t!==e.seg&&_t(this.elRef.current,t)},t}(lr),pi=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=e.seg,r=t.options.eventTimeFormat||e.defaultTimeFormat,o=function(e,t,n,r,o,i,a){var l=n.dateEnv,s=n.options,u=s.displayEventTime,c=s.displayEventEnd,d=e.eventRange.def,f=e.eventRange.instance;if(null==u&&(u=!1!==r),null==c&&(c=!1!==o),u&&!d.allDay&&(e.isStart||e.isEnd)){var p=i||(e.isStart?f.range.start:e.start||e.eventRange.range.start),h=a||(e.isEnd?f.range.end:e.end||e.eventRange.range.end);return c&&d.hasEnd?l.formatRange(p,h,t,{forcedStartTzo:i?null:f.forcedStartTzo,forcedEndTzo:a?null:f.forcedEndTzo}):l.format(p,t,{forcedTzo:i?null:f.forcedStartTzo})}return""}(n,r,t,e.defaultDisplayEventTime,e.defaultDisplayEventEnd);return s(fi,{seg:n,timeText:o,disableDragging:e.disableDragging,disableResizing:e.disableResizing,defaultContent:e.defaultContent||hi,isDragging:e.isDragging,isResizing:e.isResizing,isDateSelecting:e.isDateSelecting,isSelected:e.isSelected,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday},(function(t,r,o,a,l){return s("a",i({className:e.extraClassNames.concat(r).join(" "),style:{borderColor:l.borderColor,backgroundColor:l.backgroundColor},ref:t},function(e){var t=e.eventRange.def.url;return t?{href:t}:{}}(n)),s("div",{className:"fc-event-main",ref:o,style:{color:l.textColor}},a),l.isStartResizable&&s("div",{className:"fc-event-resizer fc-event-resizer-start"}),l.isEndResizable&&s("div",{className:"fc-event-resizer fc-event-resizer-end"}))}))},t}(lr);function hi(e){return s("div",{className:"fc-event-main-frame"},e.timeText&&s("div",{className:"fc-event-time"},e.timeText),s("div",{className:"fc-event-title-container"},s("div",{className:"fc-event-title fc-sticky"},e.event.title||s(c,null,"\xa0"))))}var vi=function(e){return s(or.Consumer,null,(function(t){var n=t.options,r={isAxis:e.isAxis,date:t.dateEnv.toDate(e.date),view:t.viewApi};return s(kr,{hookProps:r,classNames:n.nowIndicatorClassNames,content:n.nowIndicatorContent,didMount:n.nowIndicatorDidMount,willUnmount:n.nowIndicatorWillUnmount},e.children)}))},gi=Xe({day:"numeric"});!function(e){function t(){return null!==e&&e.apply(this,arguments)||this}o(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r=mi({date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,showDayNumber:e.showDayNumber,extraProps:e.extraHookProps,viewApi:t.viewApi,dateEnv:t.dateEnv});return s(Tr,{hookProps:r,content:n.dayCellContent,defaultContent:e.defaultContent},e.children)}}(lr);function mi(e){var t=e.date,n=e.dateEnv,r=Ln(t,e.todayRange,null,e.dateProfile);return i(i(i({date:n.toDate(t),view:e.viewApi},r),{dayNumberText:e.showDayNumber?n.format(t,gi):""}),e.extraProps)}!function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.refineHookProps=Ie(mi),t.normalizeClassNames=Pr(),t}o(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r=this.refineHookProps({date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,showDayNumber:e.showDayNumber,extraProps:e.extraHookProps,viewApi:t.viewApi,dateEnv:t.dateEnv}),o=An(r,t.theme).concat(r.isDisabled?[]:this.normalizeClassNames(n.dayCellClassNames,r)),i=r.isDisabled?{}:{"data-date":Te(e.date)};return s(Mr,{hookProps:r,didMount:n.dayCellDidMount,willUnmount:n.dayCellWillUnmount,elRef:e.elRef},(function(t){return e.children(t,o,i,r.isDisabled)}))}}(lr);function yi(e){return s("div",{className:"fc-"+e})}var bi=function(e){return s(fi,{defaultContent:Si,seg:e.seg,timeText:"",disableDragging:!0,disableResizing:!0,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday},(function(e,t,n,r,o){return s("div",{ref:e,className:["fc-bg-event"].concat(t).join(" "),style:{backgroundColor:o.backgroundColor}},r)}))};function Si(e){return e.event.title&&s("div",{className:"fc-event-title"},e.event.title)}},function(e,t,n){"use strict";e.exports=n(9)},function(e,t,n){"use strict";e.exports=n(8)},function(e,t,n){"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(10)},function(e,t,n){"use strict";var r=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable;function a(e){if(null===e||void 0===e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("0123456789"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(e){r[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(o){return!1}}()?Object.assign:function(e,t){for(var n,l,s=a(e),u=1;u<arguments.length;u++){for(var c in n=Object(arguments[u]))o.call(n,c)&&(s[c]=n[c]);if(r){l=r(n);for(var d=0;d<l.length;d++)i.call(n,l[d])&&(s[l[d]]=n[l[d]])}}return s}},function(e,t,n){"use strict";n(16);var r=n(0),o="2020-12-19",i=["GPL-My-Project-Is-Open-Source","CC-Attribution-NonCommercial-NoDerivatives"],a={position:"absolute",zIndex:99999,bottom:"1px",left:"1px",background:"#eee",borderColor:"#ddd",borderStyle:"solid",borderWidth:"1px 1px 0 0",padding:"2px 4px",fontSize:"12px",borderTopRightRadius:"3px"};var l={schedulerLicenseKey:String},s=Object(r.nb)({optionRefiners:l,viewContainerAppends:[function(e){var t,n=e.options.schedulerLicenseKey;if(t=window.location.href,!/\w+:\/\/fullcalendar\.io\/|\/examples\/[\w-]+\.html$/.test(t)){var l=function(e){if(-1!==i.indexOf(e))return"valid";var t=(e||"").match(/^(\d+)-fcs-(\d+)$/);if(t&&10===t[1].length){var n=new Date(1e3*parseInt(t[2],10)),a=new Date(r.fb.mockSchedulerReleaseDate||o);if(Object(r.jc)(a))return Object(r.G)(a,-372)<n?"valid":"outdated"}return"invalid"}(n);if("valid"!==l)return Object(r.ib)("div",{className:"fc-license-message",style:a},"outdated"===l?Object(r.ib)(r.o,null,"Your license key is too old to work with this version. ",Object(r.ib)("a",{href:"http://fullcalendar.io/docs/schedulerLicenseKey#outdated"},"More Info")):Object(r.ib)(r.o,null,"Your license key is invalid. ",Object(r.ib)("a",{href:"http://fullcalendar.io/docs/schedulerLicenseKey#invalid"},"More Info")))}return null}]}),u=(n(17),function(e,t){return(u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)});function c(e,t){function n(){this.constructor=e}u(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var d=function(){return(d=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;Object.create;var f=function(e,t){return(f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function p(e,t){function n(){this.constructor=e}f(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var h=function(){return(h=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;function v(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),o=0;for(t=0;t<n;t++)for(var i=arguments[t],a=0,l=i.length;a<l;a++,o++)r[o]=i[a];return r}Object.create;var g,m="wheel mousewheel DomMouseScroll MozMousePixelScroll".split(" "),y=function(){function e(e){var t=this;this.el=e,this.emitter=new r.m,this.isScrolling=!1,this.isTouching=!1,this.isRecentlyWheeled=!1,this.isRecentlyScrolled=!1,this.wheelWaiter=new r.j(this._handleWheelWaited.bind(this)),this.scrollWaiter=new r.j(this._handleScrollWaited.bind(this)),this.handleScroll=function(){t.startScroll(),t.emitter.trigger("scroll",t.isRecentlyWheeled,t.isTouching),t.isRecentlyScrolled=!0,t.scrollWaiter.request(500)},this.handleWheel=function(){t.isRecentlyWheeled=!0,t.wheelWaiter.request(500)},this.handleTouchStart=function(){t.isTouching=!0},this.handleTouchEnd=function(){t.isTouching=!1,t.isRecentlyScrolled||t.endScroll()},e.addEventListener("scroll",this.handleScroll),e.addEventListener("touchstart",this.handleTouchStart,{passive:!0}),e.addEventListener("touchend",this.handleTouchEnd);for(var n=0,o=m;n<o.length;n++){var i=o[n];e.addEventListener(i,this.handleWheel)}}return e.prototype.destroy=function(){var e=this.el;e.removeEventListener("scroll",this.handleScroll),e.removeEventListener("touchstart",this.handleTouchStart,{passive:!0}),e.removeEventListener("touchend",this.handleTouchEnd);for(var t=0,n=m;t<n.length;t++){var r=n[t];e.removeEventListener(r,this.handleWheel)}},e.prototype.startScroll=function(){this.isScrolling||(this.isScrolling=!0,this.emitter.trigger("scrollStart",this.isRecentlyWheeled,this.isTouching))},e.prototype.endScroll=function(){this.isScrolling&&(this.emitter.trigger("scrollEnd"),this.isScrolling=!1,this.isRecentlyScrolled=!0,this.isRecentlyWheeled=!1,this.scrollWaiter.clear(),this.wheelWaiter.clear())},e.prototype._handleScrollWaited=function(){this.isRecentlyScrolled=!1,this.isTouching||this.endScroll()},e.prototype._handleWheelWaited=function(){this.isRecentlyWheeled=!1},e}();function b(e){var t=e.scrollLeft;if("rtl"===window.getComputedStyle(e).direction)switch(E()){case"negative":t=e.scrollWidth-e.clientWidth+t;break;case"reverse":t=e.scrollWidth-e.clientWidth-t}return t}function S(e,t){if("rtl"===window.getComputedStyle(e).direction)switch(E()){case"positive":t=e.scrollWidth-e.clientWidth+t;break;case"reverse":t=-t}e.scrollLeft=t}function E(){return g||(g=function(){var e,t=document.createElement("div");t.style.position="absolute",t.style.top="-1000px",t.style.width="1px",t.style.height="1px",t.style.overflow="scroll",t.style.direction="rtl",t.style.fontSize="100px",t.innerHTML="A",document.body.appendChild(t),t.scrollLeft>0?e="positive":(t.scrollLeft=1,e=t.scrollLeft>0?"reverse":"negative");return Object(r.Gc)(t),e}())}var w=/Edge/.test(navigator.userAgent),C=function(){function e(e,t){var n=this;this.scrollEl=e,this.isRtl=t,this.usingRelative=null,this.updateSize=function(){var e=n.scrollEl,t=Object(r.zb)(e,".fc-sticky"),o=n.queryElGeoms(t),i=e.clientWidth,a=e.clientHeight;n.usingRelative?function(e,t,n,o,i){e.forEach((function(e,a){var l,s,u=t[a],c=u.naturalBound,d=u.parentBound,f=d.right-d.left,p=d.bottom-d.bottom;f>o||p>i?(l=n[a].left-c.left,s=n[a].top-c.top):(l="",s=""),Object(r.L)(e,{position:"relative",left:l,right:-l,top:s})}))}(t,o,n.computeElDestinations(o,i),i,a):function(e,t,n){e.forEach((function(e,o){var i,a=t[o],l=a.textAlign,s=a.elWidth,u=a.parentBound,c=u.right-u.left;i="center"===l&&c>n?(n-s)/2:"",Object(r.L)(e,{left:i,right:i,top:0})}))}(t,o,i)},this.usingRelative=!function(){var e=document.createElement("div");e.className="fc-sticky",document.body.appendChild(e);var t=window.getComputedStyle(e).position;if(Object(r.Gc)(e),-1!==t.indexOf("sticky"))return t;return null}()||w&&t,this.usingRelative&&(this.listener=new y(e),this.listener.emitter.on("scrollEnd",this.updateSize))}return e.prototype.destroy=function(){this.listener&&this.listener.destroy()},e.prototype.queryElGeoms=function(e){for(var t=this.scrollEl,n=this.isRtl,o=function(e){var t=e.getBoundingClientRect(),n=Object(r.Z)(e);return{left:t.left+n.borderLeft+n.scrollbarLeft-b(e),top:t.top+n.borderTop-e.scrollTop}}(t),i=[],a=0,l=e;a<l.length;a++){var s=l[a],u=Object(r.Qc)(Object(r.bb)(s.parentNode,!0,!0),-o.left,-o.top),c=s.getBoundingClientRect(),d=window.getComputedStyle(s),f=window.getComputedStyle(s.parentNode).textAlign,p=null;"start"===f?f=n?"right":"left":"end"===f&&(f=n?"left":"right"),"sticky"!==d.position&&(p=Object(r.Qc)(c,-o.left-(parseFloat(d.left)||0),-o.top-(parseFloat(d.top)||0))),i.push({parentBound:u,naturalBound:p,elWidth:c.width,elHeight:c.height,textAlign:f})}return i},e.prototype.computeElDestinations=function(e,t){var n=this.scrollEl,r=n.scrollTop,o=b(n),i=o+t;return e.map((function(e){var t,n,a=e.elWidth,l=e.elHeight,s=e.parentBound,u=e.naturalBound;switch(e.textAlign){case"left":t=o;break;case"right":t=i-a;break;case"center":t=(o+i)/2-a/2}return t=Math.min(t,s.right-a),t=Math.max(t,s.left),n=r,n=Math.min(n,s.bottom-l),{left:t,top:n=Math.max(n,u.top)}}))},e}();var D=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.elRef=Object(r.ob)(),t.state={xScrollbarWidth:Object(r.Nb)().x,yScrollbarWidth:Object(r.Nb)().y},t.handleScroller=function(e){t.scroller=e,Object(r.Nc)(t.props.scrollerRef,e)},t.handleSizing=function(){var e=t.props;"scroll-hidden"===e.overflowY&&t.setState({yScrollbarWidth:t.scroller.getYScrollbarWidth()}),"scroll-hidden"===e.overflowX&&t.setState({xScrollbarWidth:t.scroller.getXScrollbarWidth()})},t}return p(t,e),t.prototype.render=function(){var e=this,t=e.props,n=e.state,o=e.context.isRtl&&Object(r.Jb)(),i=0,a=0,l=0;return"scroll-hidden"===t.overflowX&&(l=n.xScrollbarWidth),"scroll-hidden"===t.overflowY&&null!=n.yScrollbarWidth&&(o?i=n.yScrollbarWidth:a=n.yScrollbarWidth),Object(r.ib)("div",{ref:this.elRef,className:"fc-scroller-harness"+(t.liquid?" fc-scroller-harness-liquid":"")},Object(r.ib)(r.x,{ref:this.handleScroller,elRef:this.props.scrollerElRef,overflowX:"scroll-hidden"===t.overflowX?"scroll":t.overflowX,overflowY:"scroll-hidden"===t.overflowY?"scroll":t.overflowY,overcomeLeft:i,overcomeRight:a,overcomeBottom:l,maxHeight:"number"===typeof t.maxHeight?t.maxHeight+("scroll-hidden"===t.overflowX?n.xScrollbarWidth:0):"",liquid:t.liquid,liquidIsAbsolute:!0},t.children))},t.prototype.componentDidMount=function(){this.handleSizing(),this.context.addResizeHandler(this.handleSizing)},t.prototype.componentDidUpdate=function(e){Object(r.hc)(e,this.props)||this.handleSizing()},t.prototype.componentWillUnmount=function(){this.context.removeResizeHandler(this.handleSizing)},t.prototype.needsXScrolling=function(){return this.scroller.needsXScrolling()},t.prototype.needsYScrolling=function(){return this.scroller.needsYScrolling()},t}(r.b),R=function(){function e(e,t){var n=this;this.isVertical=e,this.scrollEls=t,this.isPaused=!1,this.scrollListeners=t.map((function(e){return n.bindScroller(e)}))}return e.prototype.destroy=function(){for(var e=0,t=this.scrollListeners;e<t.length;e++){t[e].destroy()}},e.prototype.bindScroller=function(e){var t=this,n=this.scrollEls,r=this.isVertical,o=new y(e);return o.emitter.on("scroll",(function(o,i){if(!t.isPaused&&((!t.masterEl||t.masterEl!==e&&(o||i))&&t.assignMaster(e),t.masterEl===e))for(var a=0,l=n;a<l.length;a++){var s=l[a];s!==e&&(r?s.scrollTop=e.scrollTop:s.scrollLeft=e.scrollLeft)}})),o.emitter.on("scrollEnd",(function(){t.masterEl===e&&(t.masterEl=null)})),o},e.prototype.assignMaster=function(e){this.masterEl=e;for(var t=0,n=this.scrollListeners;t<n.length;t++){var r=n[t];r.el!==e&&r.endScroll()}},e.prototype.forceScrollLeft=function(e){this.isPaused=!0;for(var t=0,n=this.scrollListeners;t<n.length;t++){S(n[t].el,e)}this.isPaused=!1},e.prototype.forceScrollTop=function(e){this.isPaused=!0;for(var t=0,n=this.scrollListeners;t<n.length;t++){n[t].el.scrollTop=e}this.isPaused=!1},e}(),k=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.compileColGroupStats=Object(r.mc)(M,j),t.renderMicroColGroups=Object(r.mc)(r.Jc),t.clippedScrollerRefs=new r.u,t.scrollerElRefs=new r.u(t._handleScrollerEl.bind(t)),t.chunkElRefs=new r.u(t._handleChunkEl.bind(t)),t.getStickyScrolling=Object(r.mc)(H,null,L),t.getScrollSyncersBySection=Object(r.nc)(N.bind(t,!0),null,_),t.getScrollSyncersByColumn=Object(r.nc)(N.bind(t,!1),null,_),t.stickyScrollings=[],t.scrollSyncersBySection={},t.scrollSyncersByColumn={},t.rowUnstableMap=new Map,t.rowInnerMaxHeightMap=new Map,t.anyRowHeightsChanged=!1,t.recentSizingCnt=0,t.state={shrinkWidths:[],forceYScrollbars:!1,forceXScrollbars:!1,scrollerClientWidths:{},scrollerClientHeights:{},sectionRowMaxHeights:[]},t.handleSizing=function(e,n){if(t.allowSizing()){n||(t.anyRowHeightsChanged=!0);var r={};(e||!n&&!t.rowUnstableMap.size)&&(r.sectionRowMaxHeights=t.computeSectionRowMaxHeights()),t.setState(h(h({shrinkWidths:t.computeShrinkWidths()},t.computeScrollerDims()),r),(function(){t.rowUnstableMap.size||t.updateStickyScrolling()}))}},t.handleRowHeightChange=function(e,n){var r=t,o=r.rowUnstableMap,i=r.rowInnerMaxHeightMap;if(n){o.delete(e);var a=T(e);i.has(e)&&i.get(e)===a||(i.set(e,a),t.anyRowHeightsChanged=!0),!o.size&&t.anyRowHeightsChanged&&(t.anyRowHeightsChanged=!1,t.setState({sectionRowMaxHeights:t.computeSectionRowMaxHeights()}))}else o.set(e,!0)},t}return p(t,e),t.prototype.render=function(){for(var e,t=this,n=t.props,o=t.state,i=t.context,a=o.shrinkWidths,l=this.compileColGroupStats(n.colGroups.map((function(e){return[e]}))),s=this.renderMicroColGroups(l.map((function(e,t){return[e.cols,a[t]]}))),u=Object(r.Mb)(n.liquid,i),c=this.getDims(),d=(c[0],c[1],n.sections),f=d.length,p=0,h=[],g=[],m=[];p<f&&"header"===(e=d[p]).type;)h.push(this.renderSection(e,p,l,s,o.sectionRowMaxHeights)),p+=1;for(;p<f&&"body"===(e=d[p]).type;)g.push(this.renderSection(e,p,l,s,o.sectionRowMaxHeights)),p+=1;for(;p<f&&"footer"===(e=d[p]).type;)m.push(this.renderSection(e,p,l,s,o.sectionRowMaxHeights)),p+=1;var y=!Object(r.Db)();return Object(r.ib)("table",{ref:n.elRef,className:u.join(" ")},function(e,t){var n=e.map((function(e,n){var o=e.width;return"shrink"===o&&(o=e.totalColWidth+Object(r.Mc)(t[n])+1),Object(r.ib)("col",{style:{width:o}})}));return r.ib.apply(void 0,v(["colgroup",{}],n))}(l,a),Boolean(!y&&h.length)&&r.ib.apply(void 0,v(["thead",{}],h)),Boolean(!y&&g.length)&&r.ib.apply(void 0,v(["tbody",{}],g)),Boolean(!y&&m.length)&&r.ib.apply(void 0,v(["tfoot",{}],m)),y&&r.ib.apply(void 0,v(["tbody",{}],h,g,m)))},t.prototype.renderSection=function(e,t,n,o,i){var a=this;return"outerContent"in e?Object(r.ib)(r.o,{key:e.key},e.outerContent):Object(r.ib)("tr",{key:e.key,className:Object(r.Ob)(e,this.props.liquid).join(" ")},e.chunks.map((function(r,l){return a.renderChunk(e,t,n[l],o[l],r,l,(i[t]||[])[l]||[])})))},t.prototype.renderChunk=function(e,t,n,o,i,a,l){if("outerContent"in i)return Object(r.ib)(r.o,{key:i.key},i.outerContent);var s=this.state,u=s.scrollerClientWidths,c=s.scrollerClientHeights,d=this.getDims(),f=d[0],p=d[1],h=t*p+a,v=a===(!this.context.isRtl||Object(r.Jb)()?p-1:0),g=t===f-1,m=g&&s.forceXScrollbars,y=v&&s.forceYScrollbars,b=n&&n.allowXScrolling,S=Object(r.Cb)(this.props,e),E=Object(r.Pb)(this.props,e),w=e.expandRows&&E,C=n&&n.totalColMinWidth||"",R=Object(r.Hc)(e,i,{tableColGroupNode:o,tableMinWidth:C,clientWidth:void 0!==u[h]?u[h]:null,clientHeight:void 0!==c[h]?c[h]:null,expandRows:w,syncRowHeights:Boolean(e.syncRowHeights),rowSyncHeights:l,reportRowHeightChange:this.handleRowHeightChange}),k=m?g?"scroll":"scroll-hidden":b?g?"auto":"scroll-hidden":"hidden",O=y?v?"scroll":"scroll-hidden":S?v?"auto":"scroll-hidden":"hidden";return R=Object(r.ib)(D,{ref:this.clippedScrollerRefs.createRef(h),scrollerElRef:this.scrollerElRefs.createRef(h),overflowX:k,overflowY:O,liquid:E,maxHeight:e.maxHeight},R),Object(r.ib)("td",{key:i.key,ref:this.chunkElRefs.createRef(h)},R)},t.prototype.componentDidMount=function(){this.updateScrollSyncers(),this.handleSizing(!1),this.context.addResizeHandler(this.handleSizing)},t.prototype.componentDidUpdate=function(e,t){this.updateScrollSyncers(),this.handleSizing(!1,t.sectionRowMaxHeights!==this.state.sectionRowMaxHeights)},t.prototype.componentWillUnmount=function(){this.context.removeResizeHandler(this.handleSizing),this.destroyStickyScrolling(),this.destroyScrollSyncers()},t.prototype.allowSizing=function(){var e=new Date;return!this.lastSizingDate||e.valueOf()>this.lastSizingDate.valueOf()+1e3?(this.lastSizingDate=e,this.recentSizingCnt=0,!0):(this.recentSizingCnt+=1)<=10},t.prototype.computeShrinkWidths=function(){var e=this,t=this.compileColGroupStats(this.props.colGroups.map((function(e){return[e]}))),n=this.getDims(),o=n[0],i=n[1],a=o*i,l=[];return t.forEach((function(t,n){if(t.hasShrinkCol){var o=e.chunkElRefs.collect(n,a,i);l[n]=Object(r.db)(o)}})),l},t.prototype.computeSectionRowMaxHeights=function(){for(var e=new Map,t=this.getDims(),n=t[0],o=t[1],i=[],a=0;a<n;a+=1){var l=this.props.sections[a],s=[];if(l&&l.syncRowHeights){for(var u=[],c=0;c<o;c+=1){var d=a*o+c,f=[],p=this.chunkElRefs.currentMap[d];f=p?Object(r.zb)(p,".fc-scrollgrid-sync-table tr").map((function(t){var n=T(t);return e.set(t,n),n})):[],u.push(f)}var h=u[0].length,v=!0;for(c=1;c<o;c+=1){if(!(l.chunks[c]&&void 0!==l.chunks[c].outerContent)&&u[c].length!==h){v=!1;break}}if(v){for(c=0;c<o;c+=1)s.push([]);for(E=0;E<h;E+=1){var g=[];for(c=0;c<o;c+=1){var m=u[c][E];null!=m&&g.push(m)}var y=Math.max.apply(Math,g);for(c=0;c<o;c+=1)s[c].push(y)}}else{for(var b=[],c=0;c<o;c+=1)b.push(O(u[c])+u[c].length);for(var S=Math.max.apply(Math,b),c=0;c<o;c+=1){var E,w=u[c].length,C=S-w,D=Math.floor(C/w),R=C-D*(w-1),k=[];for((E=0)<w&&(k.push(R),E+=1);E<w;)k.push(D),E+=1;s.push(k)}}}i.push(s)}return this.rowInnerMaxHeightMap=e,i},t.prototype.computeScrollerDims=function(){for(var e=Object(r.Nb)(),t=this.getDims(),n=t[0],o=t[1],i=!this.context.isRtl||Object(r.Jb)()?o-1:0,a=n-1,l=this.clippedScrollerRefs.currentMap,s=this.scrollerElRefs.currentMap,u=!1,c=!1,d={},f={},p=0;p<n;p+=1){if((v=l[g=p*o+i])&&v.needsYScrolling()){u=!0;break}}for(var h=0;h<o;h+=1){var v;if((v=l[g=a*o+h])&&v.needsXScrolling()){c=!0;break}}for(p=0;p<n;p+=1)for(h=0;h<o;h+=1){var g,m=s[g=p*o+h];if(m){var y=m.parentNode;d[g]=Math.floor(y.getBoundingClientRect().width-(h===i&&u?e.y:0)),f[g]=Math.floor(y.getBoundingClientRect().height-(p===a&&c?e.x:0))}}return{forceYScrollbars:u,forceXScrollbars:c,scrollerClientWidths:d,scrollerClientHeights:f}},t.prototype.updateStickyScrolling=function(){var e=this.context.isRtl,t=this.scrollerElRefs.getAll().map((function(t){return[t,e]})),n=this.getStickyScrolling(t);n.forEach((function(e){return e.updateSize()})),this.stickyScrollings=n},t.prototype.destroyStickyScrolling=function(){this.stickyScrollings.forEach(L)},t.prototype.updateScrollSyncers=function(){for(var e=this.getDims(),t=e[0],n=e[1],o=t*n,i={},a={},l=this.scrollerElRefs.currentMap,s=0;s<t;s+=1){var u=s*n,c=u+n;i[s]=Object(r.U)(l,u,c,1)}for(var d=0;d<n;d+=1)a[d]=this.scrollerElRefs.collect(d,o,n);this.scrollSyncersBySection=this.getScrollSyncersBySection(i),this.scrollSyncersByColumn=this.getScrollSyncersByColumn(a)},t.prototype.destroyScrollSyncers=function(){Object(r.kc)(this.scrollSyncersBySection,_),Object(r.kc)(this.scrollSyncersByColumn,_)},t.prototype.getChunkConfigByIndex=function(e){var t=this.getDims()[1],n=Math.floor(e/t),r=e%t,o=this.props.sections[n];return o&&o.chunks[r]},t.prototype.forceScrollLeft=function(e,t){var n=this.scrollSyncersByColumn[e];n&&n.forceScrollLeft(t)},t.prototype.forceScrollTop=function(e,t){var n=this.scrollSyncersBySection[e];n&&n.forceScrollTop(t)},t.prototype._handleChunkEl=function(e,t){var n=this.getChunkConfigByIndex(parseInt(t,10));n&&Object(r.Nc)(n.elRef,e)},t.prototype._handleScrollerEl=function(e,t){var n=this.getChunkConfigByIndex(parseInt(t,10));n&&Object(r.Nc)(n.scrollerElRef,e)},t.prototype.getDims=function(){var e=this.props.sections.length;return[e,e?this.props.sections[0].chunks.length:0]},t}(r.b);function O(e){for(var t=0,n=0,r=e;n<r.length;n++){t+=r[n]}return t}function T(e){var t=Object(r.zb)(e,".fc-scrollgrid-sync-inner").map(x);return t.length?Math.max.apply(Math,t):0}function x(e){return e.offsetHeight}function M(e){var t=P(e.cols,"width"),n=P(e.cols,"minWidth"),o=Object(r.Wb)(e.cols);return{hasShrinkCol:o,totalColWidth:t,totalColMinWidth:n,allowXScrolling:"shrink"!==e.width&&Boolean(t||n||o),cols:e.cols,width:e.width}}function P(e,t){for(var n=0,r=0,o=e;r<o.length;r++){var i=o[r],a=i[t];"number"===typeof a&&(n+=a*(i.span||1))}return n}k.addStateEquality({shrinkWidths:r.cc,scrollerClientWidths:r.hc,scrollerClientHeights:r.hc});var I={cols:r.dc};function j(e,t){return Object(r.Y)(e,t,I)}function N(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return new R(e,t)}function _(e){e.destroy()}function H(e,t){return new C(e,t)}function L(e){e.destroy()}Object(r.nb)({deps:[s],scrollGridImpl:k});r.fb.MAX_TIMELINE_SLOTS=1e3;var A=[{years:1},{months:1},{days:1},{hours:1},{minutes:30},{minutes:15},{minutes:10},{minutes:5},{minutes:1},{seconds:30},{seconds:15},{seconds:10},{seconds:5},{seconds:1},{milliseconds:500},{milliseconds:100},{milliseconds:10},{milliseconds:1}];function z(e,t,n,o){var i={labelInterval:n.slotLabelInterval,slotDuration:n.slotDuration};!function(e,t,n){var o=t.currentRange;if(e.labelInterval){n.countDurationsBetween(o.start,o.end,e.labelInterval)>r.fb.MAX_TIMELINE_SLOTS&&(console.warn("slotLabelInterval results in too many cells"),e.labelInterval=null)}if(e.slotDuration){n.countDurationsBetween(o.start,o.end,e.slotDuration)>r.fb.MAX_TIMELINE_SLOTS&&(console.warn("slotDuration results in too many cells"),e.slotDuration=null)}if(e.labelInterval&&e.slotDuration){var i=Object(r.Uc)(e.labelInterval,e.slotDuration);(null===i||i<1)&&(console.warn("slotLabelInterval must be a multiple of slotDuration"),e.slotDuration=null)}}(i,e,t),B(i,e,t),function(e,t,n){var o=t.currentRange,i=e.slotDuration;if(!i){for(var a=B(e,t,n),l=0,s=A;l<s.length;l++){var u=s[l],c=Object(r.hb)(u),d=Object(r.Uc)(a,c);if(null!==d&&d>1&&d<=6){i=c;break}}if(i)n.countDurationsBetween(o.start,o.end,i)>200&&(i=null);i||(i=a),e.slotDuration=i}}(i,e,t);var a=n.slotLabelFormat,l=Array.isArray(a)?a:null!=a?[a]:function(e,t,n,o){var i,a,l=e.labelInterval,s=Object(r.Ub)(l).unit,u=o.weekNumbers,c=i=a=null;"week"!==s||u||(s="day");switch(s){case"year":c={year:"numeric"};break;case"month":F("years",t,n)>1&&(c={year:"numeric"}),i={month:"short"};break;case"week":F("years",t,n)>1&&(c={year:"numeric"}),i={week:"narrow"};break;case"day":F("years",t,n)>1?c={year:"numeric",month:"long"}:F("months",t,n)>1&&(c={month:"long"}),u&&(i={week:"short"}),a={weekday:"narrow",day:"numeric"};break;case"hour":u&&(c={week:"short"}),F("days",t,n)>1&&(i={weekday:"short",day:"numeric",month:"numeric",omitCommas:!0}),a={hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"short"};break;case"minute":Object(r.N)(l)/60>=6?(c={hour:"numeric",meridiem:"short"},i=function(e){return":"+Object(r.rc)(e.date.minute,2)}):c={hour:"numeric",minute:"numeric",meridiem:"short"};break;case"second":Object(r.P)(l)/60>=6?(c={hour:"numeric",minute:"2-digit",meridiem:"lowercase"},i=function(e){return":"+Object(r.rc)(e.date.second,2)}):c={hour:"numeric",minute:"2-digit",second:"2-digit",meridiem:"lowercase"};break;case"millisecond":c={hour:"numeric",minute:"2-digit",second:"2-digit",meridiem:"lowercase"},i=function(e){return"."+Object(r.rc)(e.millisecond,3)}}return[].concat(c||[],i||[],a||[])}(i,e,t,n);i.headerFormats=l.map((function(e){return Object(r.mb)(e)})),i.isTimeScale=Boolean(i.slotDuration.milliseconds);var s=null;if(!i.isTimeScale){var u=Object(r.Ub)(i.slotDuration).unit;/year|month|week/.test(u)&&(s=u)}i.largeUnit=s,i.emphasizeWeeks=1===Object(r.M)(i.slotDuration)&&F("weeks",e,t)>=2&&!n.businessHours;var c,d,f=n.snapDuration;f&&(c=Object(r.hb)(f),d=Object(r.Uc)(i.slotDuration,c)),null==d&&(c=i.slotDuration,d=1),i.snapDuration=c,i.snapsPerSlot=d;var p=Object(r.O)(e.slotMaxTime)-Object(r.O)(e.slotMinTime),h=U(e.renderRange.start,i,t),v=U(e.renderRange.end,i,t);i.isTimeScale&&(h=t.add(h,e.slotMinTime),v=t.add(Object(r.G)(v,-1),e.slotMaxTime)),i.timeWindowMs=p,i.normalizedRange={start:h,end:v};for(var g=[],m=h;m<v;)W(m,i,e,o)&&g.push(m),m=t.add(m,i.slotDuration);i.slotDates=g;var y=-1,b=0,S=[],E=[];for(m=h;m<v;)W(m,i,e,o)?(y+=1,S.push(y),E.push(b)):S.push(y+.5),m=t.add(m,i.snapDuration),b+=1;return i.snapDiffToIndex=S,i.snapIndexToDiff=E,i.snapCnt=y+1,i.slotCnt=i.snapCnt/i.snapsPerSlot,i.isWeekStarts=function(e,t){for(var n=e.slotDates,r=e.emphasizeWeeks,o=null,i=[],a=0,l=n;a<l.length;a++){var s=l[a],u=t.computeWeekNumber(s),c=r&&null!==o&&o!==u;o=u,i.push(c)}return i}(i,t),i.cellRows=function(e,t){for(var n=e.slotDates,o=e.headerFormats,i=o.map((function(){return[]})),a=Object(r.M)(e.slotDuration),l=7===a?"week":1===a?"day":null,s=o.map((function(e){return e.getLargestUnit?e.getLargestUnit():null})),u=0;u<n.length;u+=1)for(var c=n[u],d=e.isWeekStarts[u],f=0;f<o.length;f+=1){var p=o[f],h=i[f],v=h[h.length-1],g=f===o.length-1,m=o.length>1&&!g,y=null,b=s[f]||(g?l:null);if(m){var S=t.format(c,p);v&&v.text===S?v.colspan+=1:y=V(c,S,b)}else if(!v||Object(r.fc)(t.countDurationsBetween(e.normalizedRange.start,c,e.labelInterval))){y=V(c,S=t.format(c,p),b)}else v.colspan+=1;y&&(y.weekStart=d,h.push(y))}return i}(i,t),i.slotsPerLabel=Object(r.Uc)(i.labelInterval,i.slotDuration),i}function U(e,t,n){var o=e;return t.isTimeScale||(o=Object(r.Pc)(o),t.largeUnit&&(o=n.startOf(o,t.largeUnit))),o}function W(e,t,n,o){if(o.isHiddenDay(e))return!1;if(t.isTimeScale){var i=Object(r.Pc)(e),a=e.valueOf()-i.valueOf()-Object(r.O)(n.slotMinTime);return(a=(a%864e5+864e5)%864e5)<t.timeWindowMs}return!0}function B(e,t,n){var o=t.currentRange,i=e.labelInterval;if(!i){var a=void 0;if(e.slotDuration){for(var l=0,s=A;l<s.length;l++){a=s[l];var u=Object(r.hb)(a),c=Object(r.Uc)(u,e.slotDuration);if(null!==c&&c<=6){i=u;break}}i||(i=e.slotDuration)}else for(var d=0,f=A;d<f.length;d++){if(a=f[d],i=Object(r.hb)(a),n.countDurationsBetween(o.start,o.end,i)>=18)break}e.labelInterval=i}return i}function F(e,t,n){var o=t.currentRange,i=null;return"years"===e?i=n.diffWholeYears(o.start,o.end):"months"===e||"weeks"===e?i=n.diffWholeMonths(o.start,o.end):"days"===e&&(i=Object(r.rb)(o.start,o.end)),i||0}function V(e,t,n){return{date:e,text:t,rowUnit:n,colspan:1,isWeekStart:!1}}var q=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return c(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=e.navLinkData?{"data-navlink":e.navLinkData,tabIndex:0}:{};return Object(r.ib)(r.h,{hookProps:e.hookProps,content:t.options.slotLabelContent,defaultContent:G},(function(t,o){return Object(r.ib)("a",d({ref:t,className:"fc-timeline-slot-cushion fc-scrollgrid-sync-inner"+(e.isSticky?" fc-sticky":"")},n),o)}))},t}(r.b);function G(e){return e.text}function Y(e){return{level:e.level,date:e.dateEnv.toDate(e.dateMarker),view:e.viewApi,text:e.text}}var X=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.refineHookProps=Object(r.oc)(Y),t.normalizeClassNames=Object(r.Q)(),t}return c(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.dateEnv,o=t.options,i=e.cell,a=e.dateProfile,l=e.tDateProfile,s=Object(r.Fb)(i.date,e.todayRange,e.nowDate,a),u=["fc-timeline-slot","fc-timeline-slot-label"].concat("time"===i.rowUnit?Object(r.Rb)(s,t.theme):Object(r.Gb)(s,t.theme));i.isWeekStart&&u.push("fc-timeline-slot-em");var c=o.navLinks&&i.rowUnit&&"time"!==i.rowUnit?Object(r.T)(i.date,i.rowUnit):null,d=this.refineHookProps({level:e.rowLevel,dateMarker:i.date,text:i.text,dateEnv:t.dateEnv,viewApi:t.viewApi}),f=this.normalizeClassNames(o.slotLabelClassNames,d);return Object(r.ib)(r.q,{hookProps:d,didMount:o.slotLabelDidMount,willUnmount:o.slotLabelWillUnmount},(function(t){return Object(r.ib)("th",{ref:t,className:u.concat(f).join(" "),"data-date":n.formatIso(i.date,{omitTime:!l.isTimeScale,omitTimeZoneOffset:!0}),colSpan:i.colspan},Object(r.ib)("div",{className:"fc-timeline-slot-frame",style:{height:e.rowInnerHeight}},Object(r.ib)(q,{hookProps:d,isSticky:e.isSticky,navLinkData:c})))}))},t}(r.b),Z=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return c(t,e),t.prototype.render=function(){var e=this.props,t=e.dateProfile,n=e.tDateProfile,o=e.rowInnerHeights,i=e.todayRange,a=e.nowDate,l=n.cellRows;return Object(r.ib)(r.o,null,l.map((function(e,s){var u=s===l.length-1,c=["fc-timeline-header-row",n.isTimeScale&&u?"fc-timeline-header-row-chrono":""];return Object(r.ib)("tr",{key:s,className:c.join(" ")},e.map((function(e){return Object(r.ib)(X,{key:e.date.toISOString(),cell:e,rowLevel:s,dateProfile:t,tDateProfile:n,todayRange:i,nowDate:a,rowInnerHeight:o&&o[s],isSticky:!u})})))})))},t}(r.b),$=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.rootElRef=Object(r.ob)(),t}return c(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.context,o=Object(r.Ub)(t.tDateProfile.slotDuration).unit,i=t.slatCoords&&t.slatCoords.dateProfile===t.dateProfile?t.slatCoords:null;return Object(r.ib)(r.s,{unit:o},(function(o,a){return Object(r.ib)("div",{className:"fc-timeline-header",ref:e.rootElRef},Object(r.ib)("table",{className:"fc-scrollgrid-sync-table",style:{minWidth:t.tableMinWidth,width:t.clientWidth}},t.tableColGroupNode,Object(r.ib)("tbody",null,Object(r.ib)(Z,{dateProfile:t.dateProfile,tDateProfile:t.tDateProfile,nowDate:o,todayRange:a,rowInnerHeights:t.rowInnerHeights}))),n.options.nowIndicator&&Object(r.ib)("div",{className:"fc-timeline-now-indicator-container"},i&&i.isDateInRange(o)&&Object(r.ib)(r.r,{isAxis:!0,date:o},(function(e,t,n,a){return Object(r.ib)("div",{ref:e,className:["fc-timeline-now-indicator-arrow"].concat(t).join(" "),style:{left:i.dateToCoord(o)}},a)}))))}))},t.prototype.componentDidMount=function(){this.updateSize()},t.prototype.componentDidUpdate=function(){this.updateSize()},t.prototype.updateSize=function(){this.props.onMaxCushionWidth&&this.props.onMaxCushionWidth(this.computeMaxCushionWidth())},t.prototype.computeMaxCushionWidth=function(){return Math.max.apply(Math,Object(r.zb)(this.rootElRef.current,".fc-timeline-header-row:last-child .fc-timeline-slot-cushion").map((function(e){return e.getBoundingClientRect().width})))},t}(r.b),Q=function(){function e(e,t,n,o,i,a){this.slatRootEl=e,this.dateProfile=n,this.tDateProfile=o,this.dateEnv=i,this.isRtl=a,this.outerCoordCache=new r.t(e,t,!0,!1),this.innerCoordCache=new r.t(e,Object(r.yb)(t,"div"),!0,!1)}return e.prototype.rangeToCoords=function(e){return this.isRtl?{right:this.dateToCoord(e.start),left:this.dateToCoord(e.end)}:{left:this.dateToCoord(e.start),right:this.dateToCoord(e.end)}},e.prototype.isDateInRange=function(e){return Object(r.Ac)(this.dateProfile.currentRange,e)},e.prototype.dateToCoord=function(e){var t=this.tDateProfile,n=this.computeDateSnapCoverage(e)/t.snapsPerSlot,r=Math.floor(n),o=n-(r=Math.min(r,t.slotCnt-1)),i=this.innerCoordCache,a=this.outerCoordCache;return this.isRtl?a.rights[r]-i.getWidth(r)*o-a.originClientRect.width:a.lefts[r]+i.getWidth(r)*o},e.prototype.computeDateSnapCoverage=function(e){return K(e,this.tDateProfile,this.dateEnv)},e.prototype.computeDurationLeft=function(e){var t=this,n=t.dateProfile,o=t.tDateProfile,i=t.dateEnv,a=t.isRtl,l=0;if(n){var s=i.add(n.activeRange.start,e);o.isTimeScale||(s=Object(r.Pc)(s)),l=this.dateToCoord(s),!a&&l&&(l+=1)}return l},e}();function K(e,t,n){var o=n.countDurationsBetween(t.normalizedRange.start,e,t.snapDuration);if(o<0)return 0;if(o>=t.snapDiffToIndex.length)return t.snapCnt;var i=Math.floor(o),a=t.snapDiffToIndex[i];return Object(r.fc)(a)?a+=o-i:a=Math.ceil(a),a}var J=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return c(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.dateEnv,o=t.options,i=t.theme,a=e.date,l=e.tDateProfile,s=e.isEm,u=Object(r.Fb)(e.date,e.todayRange,e.nowDate,e.dateProfile),c=["fc-timeline-slot","fc-timeline-slot-lane"],f={"data-date":n.formatIso(a,{omitTimeZoneOffset:!0,omitTime:!l.isTimeScale})},p=d(d({date:n.toDate(e.date)},u),{view:t.viewApi});return s&&c.push("fc-timeline-slot-em"),l.isTimeScale&&c.push(Object(r.fc)(n.countDurationsBetween(l.normalizedRange.start,e.date,l.labelInterval))?"fc-timeline-slot-major":"fc-timeline-slot-minor"),c.push.apply(c,e.isDay?Object(r.Gb)(u,i):Object(r.Rb)(u,i)),Object(r.ib)(r.v,{hookProps:p,classNames:o.slotLaneClassNames,content:o.slotLaneContent,didMount:o.slotLaneDidMount,willUnmount:o.slotLaneWillUnmount,elRef:e.elRef},(function(e,t,n,o){return Object(r.ib)("td",d({ref:e,className:c.concat(t).join(" ")},f),Object(r.ib)("div",{ref:n},o))}))},t}(r.b),ee=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return c(t,e),t.prototype.render=function(){var e=this.props,t=e.tDateProfile,n=e.cellElRefs,o=t.slotDates,i=t.isWeekStarts,a=!t.isTimeScale&&!t.largeUnit;return Object(r.ib)("tbody",null,Object(r.ib)("tr",null,o.map((function(o,l){var s=o.toISOString();return Object(r.ib)(J,{key:s,elRef:n.createRef(s),date:o,dateProfile:e.dateProfile,tDateProfile:t,nowDate:e.nowDate,todayRange:e.todayRange,isEm:i[l],isDay:a})}))))},t}(r.b),te=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.rootElRef=Object(r.ob)(),t.cellElRefs=new r.u,t.handleScrollRequest=function(e){var n=t.props.onScrollLeftRequest,r=t.coords;if(n&&r){if(e.time)n(r.computeDurationLeft(e.time));return!0}return null},t}return c(t,e),t.prototype.render=function(){var e=this.props,t=this.context;return Object(r.ib)("div",{className:"fc-timeline-slots",ref:this.rootElRef},Object(r.ib)("table",{className:t.theme.getClass("table"),style:{minWidth:e.tableMinWidth,width:e.clientWidth}},e.tableColGroupNode,Object(r.ib)(ee,{cellElRefs:this.cellElRefs,dateProfile:e.dateProfile,tDateProfile:e.tDateProfile,nowDate:e.nowDate,todayRange:e.todayRange})))},t.prototype.componentDidMount=function(){this.updateSizing(),this.scrollResponder=this.context.createScrollResponder(this.handleScrollRequest)},t.prototype.componentDidUpdate=function(e){this.updateSizing(),this.scrollResponder.update(e.dateProfile!==this.props.dateProfile)},t.prototype.componentWillUnmount=function(){this.scrollResponder.detach(),this.props.onCoords&&this.props.onCoords(null)},t.prototype.updateSizing=function(){var e,t=this.props,n=this.context;null!==t.clientWidth&&this.scrollResponder&&(this.rootElRef.current.offsetWidth&&(this.coords=new Q(this.rootElRef.current,(e=this.cellElRefs.currentMap,t.tDateProfile.slotDates.map((function(t){var n=t.toISOString();return e[n]}))),t.dateProfile,t.tDateProfile,n.dateEnv,n.isRtl),t.onCoords&&t.onCoords(this.coords),this.scrollResponder.update(!1)))},t.prototype.positionToHit=function(e){var t=this.coords.outerCoordCache,n=this.context,o=n.dateEnv,i=n.isRtl,a=this.props.tDateProfile,l=t.leftToIndex(e);if(null!=l){var s=t.getWidth(l),u=i?(t.rights[l]-e)/s:(e-t.lefts[l])/s,c=Math.floor(u*a.snapsPerSlot),d=o.add(a.slotDates[l],Object(r.qc)(a.snapDuration,c));return{dateSpan:{range:{start:d,end:o.add(d,a.snapDuration)},allDay:!this.props.tDateProfile.isTimeScale},dayEl:this.cellElRefs.currentMap[l],left:t.lefts[l],right:t.rights[l]}}return null},t}(r.b);var ne=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return c(t,e),t.prototype.render=function(){var e=this.props,t=[].concat(e.eventResizeSegs,e.dateSelectionSegs);return e.timelineCoords&&Object(r.ib)("div",{className:"fc-timeline-bg"},this.renderSegs(e.businessHourSegs||[],e.timelineCoords,"non-business"),this.renderSegs(e.bgEventSegs||[],e.timelineCoords,"bg-event"),this.renderSegs(t,e.timelineCoords,"highlight"))},t.prototype.renderSegs=function(e,t,n){var o=this.props,i=o.todayRange,a=o.nowDate,l=e.map((function(e){var o=t.rangeToCoords(e);return Object(r.ib)("div",{key:Object(r.S)(e.eventRange),className:"fc-timeline-bg-harness",style:{left:o.left,right:-o.right}},"bg-event"===n?Object(r.ib)(r.c,d({seg:e},Object(r.Qb)(e,i,a))):Object(r.Ic)(n))}));return Object(r.ib)(r.o,null,l)},t}(r.b),re=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return c(t,e),t.prototype.sliceRange=function(e,t,n,o,i){var a=function(e,t,n){if(!t.isTimeScale&&(e=Object(r.eb)(e),t.largeUnit)){var o=e;((e={start:n.startOf(e.start,t.largeUnit),end:n.startOf(e.end,t.largeUnit)}).end.valueOf()!==o.end.valueOf()||e.end<=e.start)&&(e={start:e.start,end:n.add(e.end,t.slotDuration)})}return e}(e,o,i),l=[];if(K(a.start,o,i)<K(a.end,o,i)){var s=Object(r.ac)(a,o.normalizedRange);s&&l.push({start:s.start,end:s.end,isStart:s.start.valueOf()===a.start.valueOf()&&W(s.start,o,t,n),isEnd:s.end.valueOf()===a.end.valueOf()&&W(Object(r.H)(s.end,-1),o,t,n)})}return l},t}(r.y),oe=Object(r.mb)({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"narrow"}),ie=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return c(t,e),t.prototype.render=function(){var e=this.props;return Object(r.ib)(r.A,d({},e,{extraClassNames:["fc-timeline-event","fc-h-event"],defaultTimeFormat:oe,defaultDisplayEventTime:!e.isTimeScale}))},t}(r.b);function ae(e,t){var n={};if(t)for(var r=0,o=e;r<o.length;r++){var i=o[r];n[i.eventRange.instance.instanceId]=t.rangeToCoords(i)}return n}function le(e,t,n){var o,i,a,l,s=[],u=0;if(n)for(var c=0,d=e=Object(r.Oc)(e,t);c<d.length;c++){var f=d[c].eventRange.instance.instanceId,p=n[f];if(p){for(var h=0,v=0,g=0;g<s.length;g+=1){var m=s[g];o=p,i=h,a=m.dims,l=m.top,o.right>a.left&&o.left<a.right&&i+o.height>l&&i<l+a.height&&(h=m.top+m.dims.height,v=g)}for(;v<s.length&&h>=s[v].top;)v+=1;s.splice(v,0,{key:f,dims:p,top:h}),u=Math.max(u,h+p.height)}}for(var y={},b=0,S=s;b<S.length;b++){y[(m=S[b]).key]=m.top}return{segTops:y,height:u}}var se=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.slicer=new re,t.computeFgSegHorizontals=Object(r.lc)(ae),t.computeSegVerticals=Object(r.lc)(le),t.harnessElRefs=new r.u,t.innerElRef=Object(r.ob)(),t.state={segDims:null},t}return c(t,e),t.prototype.render=function(){var e=this,t=e.props,n=e.state,o=e.context,i=t.dateProfile,a=t.tDateProfile,l=this.slicer.sliceProps(t,i,a.isTimeScale?null:t.nextDayThreshold,o,i,o.dateProfileGenerator,a,o.dateEnv),s=(l.eventDrag?l.eventDrag.segs:null)||(l.eventResize?l.eventResize.segs:null)||[],u=this.computeFgSegHorizontals(l.fgEventSegs,t.timelineCoords),c=this.computeSegVerticals(l.fgEventSegs,o.options.eventOrder,n.segDims),d=c.segTops,f=c.height,p=(l.eventDrag?l.eventDrag.affectedInstances:null)||(l.eventResize?l.eventResize.affectedInstances:null)||{};return Object(r.ib)(r.o,null,Object(r.ib)(ne,{businessHourSegs:l.businessHourSegs,bgEventSegs:l.bgEventSegs,timelineCoords:t.timelineCoords,eventResizeSegs:l.eventResize?l.eventResize.segs:[],dateSelectionSegs:l.dateSelectionSegs,nowDate:t.nowDate,todayRange:t.todayRange}),Object(r.ib)("div",{className:"fc-timeline-events fc-scrollgrid-sync-inner",ref:this.innerElRef,style:{height:f}},this.renderFgSegs(l.fgEventSegs,u,d,p,!1,!1,!1),this.renderFgSegs(s,ae(s,t.timelineCoords),d,{},Boolean(l.eventDrag),Boolean(l.eventResize),!1)))},t.prototype.componentDidMount=function(){this.updateSize()},t.prototype.componentDidUpdate=function(e,t){e.eventStore===this.props.eventStore&&e.timelineCoords===this.props.timelineCoords||this.updateSize()},t.prototype.updateSize=function(){var e=this,t=this.props,n=t.timelineCoords;if(t.onHeightChange&&t.onHeightChange(this.innerElRef.current,!1),n){var o=n.slatRootEl.getBoundingClientRect();this.setState({segDims:Object(r.kc)(this.harnessElRefs.currentMap,(function(e){var t=e.getBoundingClientRect();return{left:Math.round(t.left-o.left),right:Math.round(t.right-o.left),height:Math.round(t.height)}}))},(function(){t.onHeightChange&&t.onHeightChange(e.innerElRef.current,!0)}))}},t.prototype.renderFgSegs=function(e,t,n,o,i,a,l){var s=this,u=this.harnessElRefs,c=this.props,f=i||a||l;return Object(r.ib)(r.o,null,e.map((function(e){var p=e.eventRange.instance.instanceId,h=t[p],v=n[p];return Object(r.ib)("div",{key:p,ref:f?null:u.createRef(p),className:"fc-timeline-event-harness",style:{left:h?h.left:"",right:h?-h.right:"",top:null!=v?v:"",visibility:o[p]?"hidden":""}},Object(r.ib)(ie,d({isTimeScale:s.props.tDateProfile.isTimeScale,seg:e,isDragging:i,isResizing:a,isDateSelecting:l,isSelected:p===s.props.eventSelection},Object(r.Qb)(e,c.todayRange,c.nowDate))))})))},t}(r.b),ue=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.slatsRef=Object(r.ob)(),t.state={coords:null},t.handeEl=function(e){e?t.context.registerInteractiveComponent(t,{el:e}):t.context.unregisterInteractiveComponent(t)},t.handleCoords=function(e){t.setState({coords:e}),t.props.onSlatCoords&&t.props.onSlatCoords(e)},t}return c(t,e),t.prototype.render=function(){var e=this,t=this,n=t.props,o=t.state,i=t.context.options,a=n.dateProfile,l=n.tDateProfile,s=Object(r.Ub)(l.slotDuration).unit;return Object(r.ib)("div",{className:"fc-timeline-body",ref:this.handeEl,style:{minWidth:n.tableMinWidth,height:n.clientHeight,width:n.clientWidth}},Object(r.ib)(r.s,{unit:s},(function(t,s){return Object(r.ib)(r.o,null,Object(r.ib)(te,{ref:e.slatsRef,dateProfile:a,tDateProfile:l,nowDate:t,todayRange:s,clientWidth:n.clientWidth,tableColGroupNode:n.tableColGroupNode,tableMinWidth:n.tableMinWidth,onCoords:e.handleCoords,onScrollLeftRequest:n.onScrollLeftRequest}),Object(r.ib)(se,{dateProfile:a,tDateProfile:n.tDateProfile,nowDate:t,todayRange:s,nextDayThreshold:i.nextDayThreshold,businessHours:n.businessHours,eventStore:n.eventStore,eventUiBases:n.eventUiBases,dateSelection:n.dateSelection,eventSelection:n.eventSelection,eventDrag:n.eventDrag,eventResize:n.eventResize,timelineCoords:o.coords}),i.nowIndicator&&o.coords&&o.coords.isDateInRange(t)&&Object(r.ib)("div",{className:"fc-timeline-now-indicator-container"},Object(r.ib)(r.r,{isAxis:!1,date:t},(function(e,n,i,a){return Object(r.ib)("div",{ref:e,className:["fc-timeline-now-indicator-line"].concat(n).join(" "),style:{left:o.coords.dateToCoord(t)}},a)}))))})))},t.prototype.queryHit=function(e,t,n,r){var o=this.slatsRef.current.positionToHit(e);return o?{component:this,dateSpan:o.dateSpan,rect:{left:o.left,right:o.right,top:0,bottom:r},dayEl:o.dayEl,layer:0}:null},t}(r.i),ce=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.buildTimelineDateProfile=Object(r.lc)(z),t.scrollGridRef=Object(r.ob)(),t.state={slatCoords:null,slotCushionMaxWidth:null},t.handleSlatCoords=function(e){t.setState({slatCoords:e})},t.handleScrollLeftRequest=function(e){t.scrollGridRef.current.forceScrollLeft(0,e)},t.handleMaxCushionWidth=function(e){t.setState({slotCushionMaxWidth:Math.ceil(e)})},t}return c(t,e),t.prototype.render=function(){var e=this,t=this,n=t.props,o=t.state,i=t.context,a=i.options,l=!n.forPrint&&Object(r.Tb)(a),s=!n.forPrint&&Object(r.Sb)(a),u=this.buildTimelineDateProfile(n.dateProfile,i.dateEnv,a,i.dateProfileGenerator),c=["fc-timeline",!1===a.eventOverlap?"fc-timeline-overlap-disabled":""],f=a.slotMinWidth,p=de(u,f||this.computeFallbackSlotMinWidth(u)),h=[{type:"header",key:"header",isSticky:l,chunks:[{key:"timeline",content:function(t){return Object(r.ib)($,{dateProfile:n.dateProfile,clientWidth:t.clientWidth,clientHeight:t.clientHeight,tableMinWidth:t.tableMinWidth,tableColGroupNode:t.tableColGroupNode,tDateProfile:u,slatCoords:o.slatCoords,onMaxCushionWidth:f?null:e.handleMaxCushionWidth})}}]},{type:"body",key:"body",liquid:!0,chunks:[{key:"timeline",content:function(t){return Object(r.ib)(ue,d({},n,{clientWidth:t.clientWidth,clientHeight:t.clientHeight,tableMinWidth:t.tableMinWidth,tableColGroupNode:t.tableColGroupNode,tDateProfile:u,onSlatCoords:e.handleSlatCoords,onScrollLeftRequest:e.handleScrollLeftRequest}))}}]}];return s&&h.push({type:"footer",key:"footer",isSticky:!0,chunks:[{key:"timeline",content:r.Kc}]}),Object(r.ib)(r.E,{viewSpec:i.viewSpec},(function(t,o){return Object(r.ib)("div",{ref:t,className:c.concat(o).join(" ")},Object(r.ib)(k,{ref:e.scrollGridRef,liquid:!n.isHeightAuto&&!n.forPrint,colGroups:[{cols:p}],sections:h}))}))},t.prototype.computeFallbackSlotMinWidth=function(e){return Math.max(30,(this.state.slotCushionMaxWidth||0)/e.slotsPerLabel)},t}(r.i);function de(e,t){return[{span:e.slotCnt,minWidth:t||1}]}var fe=Object(r.nb)({deps:[s],initialView:"timelineDay",views:{timeline:{component:ce,usesMinMaxTime:!0,eventResizableFromStart:!0},timelineDay:{type:"timeline",duration:{days:1}},timelineWeek:{type:"timeline",duration:{weeks:1}},timelineMonth:{type:"timeline",duration:{months:1}},timelineYear:{type:"timeline",duration:{years:1}}}}),pe=function(e,t){return(pe=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function he(e,t){function n(){this.constructor=e}pe(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var ve=function(){return(ve=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;function ge(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),o=0;for(t=0;t<n;t++)for(var i=arguments[t],a=0,l=i.length;a<l;a++,o++)r[o]=i[a];return r}Object.create;function me(e,t){var n=e.resourceEditable;if(null==n){var r=e.sourceId&&t.getCurrentData().eventSources[e.sourceId];r&&(n=r.extendedProps.resourceEditable),null==n&&null==(n=t.options.eventResourceEditable)&&(n=t.options.editable)}return n}var ye=function(){function e(){this.filterResources=Object(r.lc)(be)}return e.prototype.transform=function(e,t){return t.viewSpec.optionDefaults.needsResourceData?{resourceStore:this.filterResources(t.resourceStore,t.options.filterResourcesWithEvents,t.eventStore,t.dateProfile.activeRange),resourceEntityExpansions:t.resourceEntityExpansions}:null},e}();function be(e,t,n,o){if(t){var i=function(e,t){var n={};for(var r in e)for(var o=0,i=t[e[r].defId].resourceIds;o<i.length;o++){n[i[o]]=!0}return n}(function(e,t){return Object(r.xb)(e,(function(e){return Object(r.Dc)(e.range,t)}))}(n.instances,o),n.defs);return ve(i,function(e,t){var n={};for(var r in e)for(var o=void 0;(o=t[r])&&(r=o.parentId);)n[r]=!0;return n}(i,e)),Object(r.xb)(e,(function(e,t){return i[t]}))}return e}var Se=function(){function e(){this.buildResourceEventUis=Object(r.lc)(Ee,r.hc),this.injectResourceEventUis=Object(r.lc)(we)}return e.prototype.transform=function(e,t){return t.viewSpec.optionDefaults.needsResourceData?null:{eventUiBases:this.injectResourceEventUis(e.eventUiBases,e.eventStore.defs,this.buildResourceEventUis(t.resourceStore))}},e}();function Ee(e){return Object(r.kc)(e,(function(e){return e.ui}))}function we(e,t,n){return Object(r.kc)(e,(function(e,o){return o?function(e,t,n){for(var o=[],i=0,a=t.resourceIds;i<a.length;i++){var l=a[i];n[l]&&o.unshift(n[l])}return o.unshift(e),Object(r.V)(o)}(e,t[o],n):e}))}var Ce=[];function De(e){Ce.push(e)}function Re(e){return Ce[e]}var ke={id:String,resources:r.Xb,url:String,method:String,startParam:String,endParam:String,timeZoneParam:String,extraParams:r.Xb};function Oe(e){var t;if("string"===typeof e?t={url:e}:"function"===typeof e||Array.isArray(e)?t={resources:e}:"object"===typeof e&&e&&(t=e),t){var n=Object(r.Fc)(t,ke),o=n.refined;!function(e){for(var t in e)console.warn("Unknown resource prop '"+t+"'")}(n.extra);var i=function(e){for(var t=Ce,n=t.length-1;n>=0;n-=1){var r=t[n].parseMeta(e);if(r)return{meta:r,sourceDefId:n}}return null}(o);if(i)return{_raw:e,sourceId:Object(r.Vb)(),sourceDefId:i.sourceDefId,meta:i.meta,publicId:o.id||"",isFetching:!1,latestFetchId:"",fetchRange:null}}return null}function Te(e,t,n){var o=n.options,i=n.dateProfile;if(!e||!t)return xe(o.initialResources||o.resources,i.activeRange,o.refetchResourcesOnNavigate,n);switch(t.type){case"RESET_RESOURCE_SOURCE":return xe(t.resourceSourceInput,i.activeRange,o.refetchResourcesOnNavigate,n);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return function(e,t,n,o){if(n&&!function(e){return Boolean(Re(e.sourceDefId).ignoreRange)}(e)&&(!e.fetchRange||!Object(r.Cc)(e.fetchRange,t)))return Me(e,t,o);return e}(e,i.activeRange,o.refetchResourcesOnNavigate,n);case"RECEIVE_RESOURCES":case"RECEIVE_RESOURCE_ERROR":return function(e,t,n){if(t===e.latestFetchId)return ve(ve({},e),{isFetching:!1,fetchRange:n});return e}(e,t.fetchId,t.fetchRange);case"REFETCH_RESOURCES":return Me(e,i.activeRange,n);default:return e}}function xe(e,t,n,r){if(e){var o=Oe(e);return o=Me(o,n?t:null,r)}return null}function Me(e,t,n){var o=Re(e.sourceDefId),i=Object(r.Vb)();return o.fetch({resourceSource:e,range:t,context:n},(function(e){n.dispatch({type:"RECEIVE_RESOURCES",fetchId:i,fetchRange:t,rawResources:e.rawResources})}),(function(e){n.dispatch({type:"RECEIVE_RESOURCE_ERROR",fetchId:i,fetchRange:t,error:e})})),ve(ve({},e),{isFetching:!0,latestFetchId:i})}var Pe="_fc:",Ie={id:String,parentId:String,children:r.Xb,title:String,businessHours:r.Xb,extendedProps:r.Xb,eventEditable:Boolean,eventStartEditable:Boolean,eventDurationEditable:Boolean,eventConstraint:r.Xb,eventOverlap:Boolean,eventAllow:r.Xb,eventClassNames:r.tc,eventBackgroundColor:String,eventBorderColor:String,eventTextColor:String,eventColor:String};function je(e,t,n,o){void 0===t&&(t="");var i=Object(r.Fc)(e,Ie),a=i.refined,l=i.extra,s={id:a.id||Pe+Object(r.Vb)(),parentId:a.parentId||t,title:a.title||"",businessHours:a.businessHours?Object(r.sc)(a.businessHours,o):null,ui:Object(r.lb)({editable:a.eventEditable,startEditable:a.eventStartEditable,durationEditable:a.eventDurationEditable,constraint:a.eventConstraint,overlap:a.eventOverlap,allow:a.eventAllow,classNames:a.eventClassNames,backgroundColor:a.eventBackgroundColor,borderColor:a.eventBorderColor,textColor:a.eventTextColor,color:a.eventColor},o),extendedProps:ve(ve({},l),a.extendedProps)};if(Object.freeze(s.ui.classNames),Object.freeze(s.extendedProps),n[s.id]);else if(n[s.id]=s,a.children)for(var u=0,c=a.children;u<c.length;u++){je(c[u],s.id,n,o)}return s}function Ne(e){return 0===e.indexOf(Pe)?"":e}function _e(e,t,n,r){if(!e||!t)return{};switch(t.type){case"RECEIVE_RESOURCES":return function(e,t,n,r,o){if(r.latestFetchId===n){for(var i={},a=0,l=t;a<l.length;a++){je(l[a],"",i,o)}return i}return e}(e,t.rawResources,t.fetchId,n,r);case"ADD_RESOURCE":return o=e,i=t.resourceHash,ve(ve({},o),i);case"REMOVE_RESOURCE":return function(e,t){var n=ve({},e);for(var r in delete n[t],n)n[r].parentId===t&&(n[r]=ve(ve({},n[r]),{parentId:""}));return n}(e,t.resourceId);case"SET_RESOURCE_PROP":return function(e,t,n,r){var o,i,a=e[t];if(a)return ve(ve({},e),((o={})[t]=ve(ve({},a),((i={})[n]=r,i)),o));return e}(e,t.resourceId,t.propName,t.propValue);case"SET_RESOURCE_EXTENDED_PROP":return function(e,t,n,r){var o,i,a=e[t];if(a)return ve(ve({},e),((o={})[t]=ve(ve({},a),{extendedProps:ve(ve({},a.extendedProps),(i={},i[n]=r,i))}),o));return e}(e,t.resourceId,t.propName,t.propValue);default:return e}var o,i}var He={resourceId:String,resourceIds:r.Xb,resourceEditable:Boolean};var Le=function(){function e(e,t){this._context=e,this._resource=t}return e.prototype.setProp=function(e,t){var n=this._resource;this._context.dispatch({type:"SET_RESOURCE_PROP",resourceId:n.id,propName:e,propValue:t}),this.sync(n)},e.prototype.setExtendedProp=function(e,t){var n=this._resource;this._context.dispatch({type:"SET_RESOURCE_EXTENDED_PROP",resourceId:n.id,propName:e,propValue:t}),this.sync(n)},e.prototype.sync=function(t){var n=this._context,r=t.id;this._resource=n.getCurrentData().resourceStore[r],n.emitter.trigger("resourceChange",{oldResource:new e(n,t),resource:this,revert:function(){var e;n.dispatch({type:"ADD_RESOURCE",resourceHash:(e={},e[r]=t,e)})}})},e.prototype.remove=function(){var e=this._context,t=this._resource,n=t.id;e.dispatch({type:"REMOVE_RESOURCE",resourceId:n}),e.emitter.trigger("resourceRemove",{resource:this,revert:function(){var r;e.dispatch({type:"ADD_RESOURCE",resourceHash:(r={},r[n]=t,r)})}})},e.prototype.getParent=function(){var t=this._context,n=this._resource.parentId;return n?new e(t,t.getCurrentData().resourceSource[n]):null},e.prototype.getChildren=function(){var t=this._resource.id,n=this._context,r=n.getCurrentData().resourceStore,o=[];for(var i in r)r[i].parentId===t&&o.push(new e(n,r[i]));return o},e.prototype.getEvents=function(){var e=this._resource.id,t=this._context,n=t.getCurrentData().eventStore,o=n.defs,i=n.instances,a=[];for(var l in i){var s=i[l],u=o[s.defId];-1!==u.resourceIds.indexOf(e)&&a.push(new r.n(t,u,s))}return a},Object.defineProperty(e.prototype,"id",{get:function(){return Ne(this._resource.id)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"title",{get:function(){return this._resource.title},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventConstraint",{get:function(){return this._resource.ui.constraints[0]||null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventOverlap",{get:function(){return this._resource.ui.overlap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventAllow",{get:function(){return this._resource.ui.allows[0]||null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventBackgroundColor",{get:function(){return this._resource.ui.backgroundColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventBorderColor",{get:function(){return this._resource.ui.borderColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventTextColor",{get:function(){return this._resource.ui.textColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventClassNames",{get:function(){return this._resource.ui.classNames},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"extendedProps",{get:function(){return this._resource.extendedProps},enumerable:!1,configurable:!0}),e.prototype.toPlainObject=function(e){void 0===e&&(e={});var t=this._resource,n=t.ui,r=this.id,o={};return r&&(o.id=r),t.title&&(o.title=t.title),e.collapseEventColor&&n.backgroundColor&&n.backgroundColor===n.borderColor?o.eventColor=n.backgroundColor:(n.backgroundColor&&(o.eventBackgroundColor=n.backgroundColor),n.borderColor&&(o.eventBorderColor=n.borderColor)),n.textColor&&(o.eventTextColor=n.textColor),n.classNames.length&&(o.eventClassNames=n.classNames),Object.keys(t.extendedProps).length&&(e.collapseExtendedProps?ve(o,t.extendedProps):o.extendedProps=t.extendedProps),o},e.prototype.toJSON=function(){return this.toPlainObject()},e}();r.d.prototype.addResource=function(e,t){var n,r=this;void 0===t&&(t=!0);var o,i,a=this.getCurrentData();e instanceof Le?((n={})[(i=e._resource).id]=i,o=n):i=je(e,"",o={},a),this.dispatch({type:"ADD_RESOURCE",resourceHash:o}),t&&this.trigger("_scrollRequest",{resourceId:i.id});var l=new Le(a,i);return a.emitter.trigger("resourceAdd",{resource:l,revert:function(){r.dispatch({type:"REMOVE_RESOURCE",resourceId:i.id})}}),l},r.d.prototype.getResourceById=function(e){e=String(e);var t=this.getCurrentData();if(t.resourceStore){var n=t.resourceStore[e];if(n)return new Le(t,n)}return null},r.d.prototype.getResources=function(){var e=this.getCurrentData(),t=e.resourceStore,n=[];if(t)for(var r in t)n.push(new Le(e,t[r]));return n},r.d.prototype.getTopLevelResources=function(){var e=this.getCurrentData(),t=e.resourceStore,n=[];if(t)for(var r in t)t[r].parentId||n.push(new Le(e,t[r]));return n},r.d.prototype.refetchResources=function(){this.dispatch({type:"REFETCH_RESOURCES"})};var Ae=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return he(t,e),t.prototype.getKeyInfo=function(e){return ve({"":{}},e.resourceStore)},t.prototype.getKeysForDateSpan=function(e){return[e.resourceId||""]},t.prototype.getKeysForEventDef=function(e){var t=e.resourceIds;return t.length?t:[""]},t}(r.z);function ze(e,t){return ve(ve({},t),{constraints:Ue(e,t.constraints)})}function Ue(e,t){return t.map((function(t){var n=t.defs;if(n)for(var r in n){var o=n[r].resourceIds;if(o.length&&-1===o.indexOf(e))return!1}return t}))}r.n.prototype.getResources=function(){var e=this._context.calendarApi;return this._def.resourceIds.map((function(t){return e.getResourceById(t)}))},r.n.prototype.setResources=function(e){for(var t=[],n=0,r=e;n<r.length;n++){var o=r[n],i=null;"string"===typeof o?i=o:"number"===typeof o?i=String(o):o instanceof Le?i=o.id:console.warn("unknown resource type: "+o),i&&t.push(i)}this.mutate({standardProps:{resourceIds:t}})};var We={resources:function(e,t){t.getCurrentData().resourceSource._raw!==e&&t.dispatch({type:"RESET_RESOURCE_SOURCE",resourceSourceInput:e})}};var Be=Object(r.wc)("id,title");var Fe={initialResources:r.Xb,resources:r.Xb,eventResourceEditable:Boolean,refetchResourcesOnNavigate:Boolean,resourceOrder:r.wc,filterResourcesWithEvents:Boolean,resourceGroupField:String,resourceAreaWidth:r.Xb,resourceAreaColumns:r.Xb,resourcesInitiallyExpanded:Boolean,datesAboveResources:Boolean,needsResourceData:Boolean,resourceAreaHeaderClassNames:r.Xb,resourceAreaHeaderContent:r.Xb,resourceAreaHeaderDidMount:r.Xb,resourceAreaHeaderWillUnmount:r.Xb,resourceGroupLabelClassNames:r.Xb,resourceGroupLabelContent:r.Xb,resourceGroupLabelDidMount:r.Xb,resourceGroupLabelWillUnmount:r.Xb,resourceLabelClassNames:r.Xb,resourceLabelContent:r.Xb,resourceLabelDidMount:r.Xb,resourceLabelWillUnmount:r.Xb,resourceLaneClassNames:r.Xb,resourceLaneContent:r.Xb,resourceLaneDidMount:r.Xb,resourceLaneWillUnmount:r.Xb,resourceGroupLaneClassNames:r.Xb,resourceGroupLaneContent:r.Xb,resourceGroupLaneDidMount:r.Xb,resourceGroupLaneWillUnmount:r.Xb},Ve={resourcesSet:r.Xb,resourceAdd:r.Xb,resourceChange:r.Xb,resourceRemove:r.Xb};function qe(e){return Object(r.ib)(r.D.Consumer,null,(function(t){var n=t.options,o={resource:new Le(t,e.resource),date:e.date?t.dateEnv.toDate(e.date):null,view:t.viewApi},i={"data-resource-id":e.resource.id,"data-date":e.date?Object(r.Bb)(e.date):void 0};return Object(r.ib)(r.v,{hookProps:o,classNames:n.resourceLabelClassNames,content:n.resourceLabelContent,defaultContent:Ge,didMount:n.resourceLabelDidMount,willUnmount:n.resourceLabelWillUnmount},(function(t,n,r,o){return e.children(t,n,i,r,o)}))}))}function Ge(e){return e.resource.title||e.resource.id}De({ignoreRange:!0,parseMeta:function(e){return Array.isArray(e.resources)?e.resources:null},fetch:function(e,t){t({rawResources:e.resourceSource.meta})}}),De({parseMeta:function(e){return"function"===typeof e.resources?e.resources:null},fetch:function(e,t,n){var o=e.context.dateEnv,i=e.resourceSource.meta,a=e.range?{start:o.toDate(e.range.start),end:o.toDate(e.range.end),startStr:o.formatIso(e.range.start),endStr:o.formatIso(e.range.end),timeZone:o.timeZone}:{};Object(r.Sc)(i.bind(null,a),(function(e){t({rawResources:e})}),n)}}),De({parseMeta:function(e){return e.url?{url:e.url,method:(e.method||"GET").toUpperCase(),extraParams:e.extraParams}:null},fetch:function(e,t,n){var o=e.resourceSource.meta,i=function(e,t,n){var r,o,i,a,l=n.dateEnv,s=n.options,u={};t&&(null==(r=e.startParam)&&(r=s.startParam),null==(o=e.endParam)&&(o=s.endParam),null==(i=e.timeZoneParam)&&(i=s.timeZoneParam),u[r]=l.formatIso(t.start),u[o]=l.formatIso(t.end),"local"!==l.timeZone&&(u[i]=l.timeZone));a="function"===typeof e.extraParams?e.extraParams():e.extraParams||{};return ve(u,a),u}(o,e.range,e.context);Object(r.Lc)(o.method,o.url,i,(function(e,n){t({rawResources:e,xhr:n})}),(function(e,t){n({message:e,xhr:t})}))}});var Ye=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return he(t,e),t.prototype.render=function(){var e=this.props;return Object(r.ib)(qe,{resource:e.resource,date:e.date},(function(t,n,o,i,a){return Object(r.ib)("th",ve({ref:t,className:["fc-col-header-cell","fc-resource"].concat(n).join(" "),colSpan:e.colSpan},o),Object(r.ib)("div",{className:"fc-scrollgrid-sync-inner"},Object(r.ib)("span",{className:["fc-col-header-cell-cushion",e.isSticky?"fc-sticky":""].join(" "),ref:i},a)))}))},t}(r.b);!function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.buildDateFormat=Object(r.lc)(Xe),t}he(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.context,o=this.buildDateFormat(n.options.dayHeaderFormat,t.datesRepDistinctDays,t.dates.length);return Object(r.ib)(r.s,{unit:"day"},(function(r,i){return 1===t.dates.length?e.renderResourceRow(t.resources,t.dates[0]):n.options.datesAboveResources?e.renderDayAndResourceRows(t.dates,o,i,t.resources):e.renderResourceAndDayRows(t.resources,t.dates,o,i)}))},t.prototype.renderResourceRow=function(e,t){var n=e.map((function(e){return Object(r.ib)(Ye,{key:e.id,resource:e,colSpan:1,date:t})}));return this.buildTr(n,"resources")},t.prototype.renderDayAndResourceRows=function(e,t,n,o){for(var i=[],a=[],l=0,s=e;l<s.length;l++){var u=s[l];i.push(this.renderDateCell(u,t,n,o.length,null,!0));for(var c=0,d=o;c<d.length;c++){var f=d[c];a.push(Object(r.ib)(Ye,{key:f.id+":"+u.toISOString(),resource:f,colSpan:1,date:u}))}}return Object(r.ib)(r.o,null,this.buildTr(i,"day"),this.buildTr(a,"resources"))},t.prototype.renderResourceAndDayRows=function(e,t,n,o){for(var i=[],a=[],l=0,s=e;l<s.length;l++){var u=s[l];i.push(Object(r.ib)(Ye,{key:u.id,resource:u,colSpan:t.length,isSticky:!0}));for(var c=0,d=t;c<d.length;c++){var f=d[c];a.push(this.renderDateCell(f,n,o,1,u))}}return Object(r.ib)(r.o,null,this.buildTr(i,"resources"),this.buildTr(a,"day"))},t.prototype.renderDateCell=function(e,t,n,o,i,a){var l=this.props,s=i?":"+i.id:"",u=i?{resource:new Le(this.context,i)}:{},c=i?{"data-resource-id":i.id}:{};return l.datesRepDistinctDays?Object(r.ib)(r.B,{key:e.toISOString()+s,date:e,dateProfile:l.dateProfile,todayRange:n,colCnt:l.dates.length*l.resources.length,dayHeaderFormat:t,colSpan:o,isSticky:a,extraHookProps:u,extraDataAttrs:c}):Object(r.ib)(r.C,{key:e.getUTCDay()+s,dow:e.getUTCDay(),dayHeaderFormat:t,colSpan:o,isSticky:a,extraHookProps:u,extraDataAttrs:c})},t.prototype.buildTr=function(e,t){var n=this.props.renderIntro;return e.length||(e=[Object(r.ib)("td",{key:0},"\xa0")]),Object(r.ib)("tr",{key:t},n&&n(t),e)}}(r.b);function Xe(e,t,n){return e||Object(r.ab)(t,n)}var Ze=function(e){for(var t={},n=[],r=0;r<e.length;r+=1){var o=e[r].id;n.push(o),t[o]=r}this.ids=n,this.indicesById=t,this.length=e.length},$e=function(){function e(e,t,n){this.dayTableModel=e,this.resources=t,this.context=n,this.resourceIndex=new Ze(t),this.rowCnt=e.rowCnt,this.colCnt=e.colCnt*t.length,this.cells=this.buildCells()}return e.prototype.buildCells=function(){for(var e=this,t=e.rowCnt,n=e.dayTableModel,r=e.resources,o=[],i=0;i<t;i+=1){for(var a=[],l=0;l<n.colCnt;l+=1)for(var s=0;s<r.length;s+=1){var u=r[s],c={resource:new Le(this.context,u)},d={"data-resource-id":u.id},f=n.cells[i][l].date;a[this.computeCol(l,s)]={key:u.id+":"+f.toISOString(),date:f,resource:u,extraHookProps:c,extraDataAttrs:d,extraClassNames:["fc-resource"]}}o.push(a)}return o},e}(),Qe=(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}he(t,e),t.prototype.computeCol=function(e,t){return t*this.dayTableModel.colCnt+e},t.prototype.computeColRanges=function(e,t,n){return[{firstCol:this.computeCol(e,n),lastCol:this.computeCol(t,n),isStart:!0,isEnd:!0}]}}($e),function(e){function t(){return null!==e&&e.apply(this,arguments)||this}he(t,e),t.prototype.computeCol=function(e,t){return e*this.resources.length+t},t.prototype.computeColRanges=function(e,t,n){for(var r=[],o=e;o<=t;o+=1){var i=this.computeCol(o,n);r.push({firstCol:i,lastCol:i,isStart:o===e,isEnd:o===t})}return r}}($e),[]);(function(){function e(){this.joinDateSelection=Object(r.lc)(this.joinSegs),this.joinBusinessHours=Object(r.lc)(this.joinSegs),this.joinFgEvents=Object(r.lc)(this.joinSegs),this.joinBgEvents=Object(r.lc)(this.joinSegs),this.joinEventDrags=Object(r.lc)(this.joinInteractions),this.joinEventResizes=Object(r.lc)(this.joinInteractions)}e.prototype.joinProps=function(e,t){for(var n=[],r=[],o=[],i=[],a=[],l=[],s="",u=0,c=t.resourceIndex.ids.concat([""]);u<c.length;u++){var d=c[u],f=e[d];n.push(f.dateSelectionSegs),r.push(d?f.businessHourSegs:Qe),o.push(d?f.fgEventSegs:Qe),i.push(f.bgEventSegs),a.push(f.eventDrag),l.push(f.eventResize),s=s||f.eventSelection}return{dateSelectionSegs:this.joinDateSelection.apply(this,ge([t],n)),businessHourSegs:this.joinBusinessHours.apply(this,ge([t],r)),fgEventSegs:this.joinFgEvents.apply(this,ge([t],o)),bgEventSegs:this.joinBgEvents.apply(this,ge([t],i)),eventDrag:this.joinEventDrags.apply(this,ge([t],a)),eventResize:this.joinEventResizes.apply(this,ge([t],l)),eventSelection:s}},e.prototype.joinSegs=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=e.resources.length,o=[],i=0;i<r;i+=1){for(var a=0,l=t[i];a<l.length;a++){var s=l[a];o.push.apply(o,this.transformSeg(s,e,i))}for(var u=0,c=t[r];u<c.length;u++){s=c[u];o.push.apply(o,this.transformSeg(s,e,i))}}return o},e.prototype.expandSegs=function(e,t){for(var n=e.resources.length,r=[],o=0;o<n;o+=1)for(var i=0,a=t;i<a.length;i++){var l=a[i];r.push.apply(r,this.transformSeg(l,e,o))}return r},e.prototype.joinInteractions=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=e.resources.length,o={},i=[],a=!1,l=!1,s=0;s<r;s+=1){var u=t[s];if(u){a=!0;for(var c=0,d=u.segs;c<d.length;c++){var f=d[c];i.push.apply(i,this.transformSeg(f,e,s))}ve(o,u.affectedInstances),l=l||u.isEvent}if(t[r])for(var p=0,h=t[r].segs;p<h.length;p++){f=h[p];i.push.apply(i,this.transformSeg(f,e,s))}}return a?{affectedInstances:o,segs:i,isEvent:l}:null}})(),function(e){function t(){return null!==e&&e.apply(this,arguments)||this}he(t,e),t.prototype.getKeyInfo=function(e){var t=e.resourceDayTableModel,n=Object(r.kc)(t.resourceIndex.indicesById,(function(e){return t.resources[e]}));return n[""]={},n},t.prototype.getKeysForDateSpan=function(e){return[e.resourceId||""]},t.prototype.getKeysForEventDef=function(e){var t=e.resourceIds;return t.length?t:[""]}}(r.z);function Ke(e,t,n,r,o,i){var a=[];return Je(function(e,t,n,r){var o=function(e,t){var n={};for(var r in e){var o=e[r];n[r]={resource:o,resourceFields:nt(o),children:[]}}for(var r in e){if((o=e[r]).parentId){var i=n[o.parentId];i&&tt(n[r],i.children,t)}}return n}(e,r),i=[];for(var a in o){var l=o[a];l.resource.parentId||et(l,i,n,0,t,r)}return i}(e,r?-1:1,t,n),a,r,[],0,o,i),a}function Je(e,t,n,r,o,i,a){for(var l=0;l<e.length;l+=1){var s=e[l],u=s.group;if(u)if(n){var c=t.length,d=r.length;if(Je(s.children,t,n,r.concat(0),o,i,a),c<t.length){var f=t[c];(f.rowSpans=f.rowSpans.slice())[d]=t.length-c}}else{var p=null!=i[h=u.spec.field+":"+u.value]?i[h]:a;t.push({id:h,group:u,isExpanded:p}),p&&Je(s.children,t,n,r,o+1,i,a)}else if(s.resource){var h;p=null!=i[h=s.resource.id]?i[h]:a;t.push({id:h,rowSpans:r,depth:o,isExpanded:p,hasChildren:Boolean(s.children.length),resource:s.resource,resourceFields:s.resourceFields}),p&&Je(s.children,t,n,r,o+1,i,a)}}}function et(e,t,n,o,i,a){n.length&&(-1===i||o<=i)?et(e,function(e,t,n){var o,i,a=e.resourceFields[n.field];if(n.order)for(i=0;i<t.length;i+=1){if((s=t[i]).group){var l=Object(r.Ab)(a,s.group.value)*n.order;if(0===l){o=s;break}if(l<0)break}}else for(i=0;i<t.length;i+=1){var s;if((s=t[i]).group&&a===s.group.value){o=s;break}}o||(o={group:{value:a,spec:n},children:[]},t.splice(i,0,o));return o}(e,t,n[0]).children,n.slice(1),o+1,i,a):tt(e,t,a)}function tt(e,t,n){var o;for(o=0;o<t.length;o+=1){if(Object(r.W)(t[o].resourceFields,e.resourceFields,n)>0)break}t.splice(o,0,e)}function nt(e){var t=ve(ve(ve({},e.extendedProps),e.ui),e);return delete t.ui,delete t.extendedProps,t}var rt=Object(r.nb)({deps:[s],reducers:[function(e,t,n){var r=Te(e&&e.resourceSource,t,n);return{resourceSource:r,resourceStore:_e(e&&e.resourceStore,t,r,n),resourceEntityExpansions:function(e,t){var n;if(!e||!t)return{};switch(t.type){case"SET_RESOURCE_ENTITY_EXPANDED":return ve(ve({},e),((n={})[t.id]=t.isExpanded,n));default:return e}}(e&&e.resourceEntityExpansions,t)}}],isLoadingFuncs:[function(e){return e.resourceSource&&e.resourceSource.isFetching}],eventRefiners:He,eventDefMemberAdders:[function(e){return{resourceIds:(t=e.resourceIds,(t||[]).map((function(e){return String(e)}))).concat(e.resourceId?[e.resourceId]:[]),resourceEditable:e.resourceEditable};var t}],isDraggableTransformers:[function(e,t,n,r){if(!e){var o=r.getCurrentData();if(o.viewSpecs[o.currentViewType].optionDefaults.needsResourceData&&me(t,r))return!0}return e}],eventDragMutationMassagers:[function(e,t,n){var r=t.dateSpan.resourceId,o=n.dateSpan.resourceId;r&&o&&r!==o&&(e.resourceMutation={matchResourceId:r,setResourceId:o})}],eventDefMutationAppliers:[function(e,t,n){var r=t.resourceMutation;if(r&&me(e,n)){var o=e.resourceIds.indexOf(r.matchResourceId);if(-1!==o){var i=e.resourceIds.slice();i.splice(o,1),-1===i.indexOf(r.setResourceId)&&i.push(r.setResourceId),e.resourceIds=i}}}],dateSelectionTransformers:[function(e,t){var n=e.dateSpan.resourceId,r=t.dateSpan.resourceId;return n&&r?(!1!==e.component.allowAcrossResources||n===r)&&{resourceId:n}:null}],datePointTransforms:[function(e,t){return e.resourceId?{resource:t.calendarApi.getResourceById(e.resourceId)}:{}}],dateSpanTransforms:[function(e,t){return e.resourceId?{resource:t.calendarApi.getResourceById(e.resourceId)}:{}}],viewPropsTransformers:[ye,Se],isPropsValid:function(e,t){var n=(new Ae).splitProps(ve(ve({},e),{resourceStore:t.getCurrentData().resourceStore}));for(var o in n){var i=n[o];if(o&&n[""]&&(i=ve(ve({},i),{eventStore:Object(r.pc)(n[""].eventStore,i.eventStore),eventUiBases:ve(ve({},n[""].eventUiBases),i.eventUiBases)})),!Object(r.ic)(i,t,{resourceId:o},ze.bind(null,o)))return!1}return!0},externalDefTransforms:[function(e){return e.resourceId?{resourceId:e.resourceId}:{}}],eventResizeJoinTransforms:[function(e,t){return(!1!==e.component.allowAcrossResources||e.dateSpan.resourceId===t.dateSpan.resourceId)&&null}],eventDropTransformers:[function(e,t){var n=e.resourceMutation;if(n){var r=t.calendarApi;return{oldResource:r.getResourceById(n.matchResourceId),newResource:r.getResourceById(n.setResourceId)}}return{oldResource:null,newResource:null}}],optionChangeHandlers:We,optionRefiners:Fe,listenerRefiners:Ve,propSetHandlers:{resourceStore:function(e,t){var n=t.emitter;n.hasHandlers("resourcesSet")&&n.trigger("resourcesSet",function(e,t){var n=[];for(var r in e)n.push(new Le(t,e[r]));return n}(e,t))}}}),ot=function(e,t){return(ot=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function it(e,t){function n(){this.constructor=e}ot(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var at=function(){return(at=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;Object.create;function lt(e){for(var t=e.depth,n=e.hasChildren,o=e.isExpanded,i=e.onExpanderClick,a=[],l=0;l<t;l+=1)a.push(Object(r.ib)("span",{className:"fc-icon"}));var s=["fc-icon"];return n&&(o?s.push("fc-icon-minus-square"):s.push("fc-icon-plus-square")),a.push(Object(r.ib)("span",{className:"fc-datagrid-expander"+(n?"":" fc-datagrid-expander-placeholder"),onClick:i},Object(r.ib)("span",{className:s.join(" ")}))),r.ib.apply(void 0,function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),o=0;for(t=0;t<n;t++)for(var i=arguments[t],a=0,l=i.length;a<l;a++,o++)r[o]=i[a];return r}([r.o,{}],a))}function st(e){return{resource:new Le(e.context,e.resource),fieldValue:e.fieldValue,view:e.context.viewApi}}var ut=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return it(t,e),t.prototype.render=function(){var e=this.props;return Object(r.ib)(r.h,{hookProps:e.hookProps,content:e.colSpec.cellContent,defaultContent:ct},(function(e,t){return Object(r.ib)("span",{className:"fc-datagrid-cell-main",ref:e},t)}))},t}(r.b);function ct(e){return e.fieldValue||Object(r.ib)(r.o,null,"\xa0")}var dt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.refineHookProps=Object(r.oc)(st),t.normalizeClassNames=Object(r.Q)(),t.onExpanderClick=function(e){var n=t.props;n.hasChildren&&t.context.dispatch({type:"SET_RESOURCE_ENTITY_EXPANDED",id:n.resource.id,isExpanded:!n.isExpanded})},t}return it(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.context,o=t.colSpec,i=this.refineHookProps({resource:t.resource,fieldValue:t.fieldValue,context:n}),a=this.normalizeClassNames(o.cellClassNames,i);return Object(r.ib)(r.q,{hookProps:i,didMount:o.cellDidMount,willUnmount:o.cellWillUnmount},(function(n){return Object(r.ib)("td",{ref:n,"data-resource-id":t.resource.id,className:["fc-datagrid-cell","fc-resource"].concat(a).join(" ")},Object(r.ib)("div",{className:"fc-datagrid-cell-frame",style:{height:t.innerHeight}},Object(r.ib)("div",{className:"fc-datagrid-cell-cushion fc-scrollgrid-sync-inner"},o.isMain&&Object(r.ib)(lt,{depth:t.depth,hasChildren:t.hasChildren,isExpanded:t.isExpanded,onExpanderClick:e.onExpanderClick}),Object(r.ib)(ut,{hookProps:i,colSpec:o}))))}))},t}(r.b),ft=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return it(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=e.colSpec,o={groupValue:e.fieldValue,view:t.viewApi};return Object(r.ib)(r.v,{hookProps:o,classNames:n.cellClassNames,content:n.cellContent,defaultContent:pt,didMount:n.cellDidMount,willUnmount:n.cellWillUnmount},(function(t,n,o,i){return Object(r.ib)("td",{className:["fc-datagrid-cell","fc-resource-group"].concat(n).join(" "),rowSpan:e.rowSpan,ref:t},Object(r.ib)("div",{className:"fc-datagrid-cell-frame fc-datagrid-cell-frame-liquid"},Object(r.ib)("div",{className:"fc-datagrid-cell-cushion fc-sticky",ref:o},i)))}))},t}(r.b);function pt(e){return e.groupValue||Object(r.ib)(r.o,null,"\xa0")}var ht=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return it(t,e),t.prototype.render=function(){var e=this.props,t=e.resource,n=e.rowSpans,o=e.depth,i=nt(t);return Object(r.ib)("tr",null,e.colSpecs.map((function(a,l){var s=n[l];if(0===s)return null;null==s&&(s=1);var u=a.field?i[a.field]:t.title||Ne(t.id);return s>1?Object(r.ib)(ft,{key:l,colSpec:a,fieldValue:u,rowSpan:s}):Object(r.ib)(dt,{key:l,colSpec:a,resource:t,fieldValue:u,depth:o,hasChildren:e.hasChildren,isExpanded:e.isExpanded,innerHeight:e.innerHeight})})))},t}(r.b);ht.addPropsEquality({rowSpans:r.cc});var vt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.innerInnerRef=Object(r.ob)(),t.onExpanderClick=function(){var e=t.props;t.context.dispatch({type:"SET_RESOURCE_ENTITY_EXPANDED",id:e.id,isExpanded:!e.isExpanded})},t}return it(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.context,o={groupValue:t.group.value,view:n.viewApi},i=t.group.spec;return Object(r.ib)("tr",null,Object(r.ib)(r.v,{hookProps:o,classNames:i.labelClassNames,content:i.labelContent,defaultContent:gt,didMount:i.labelDidMount,willUnmount:i.labelWillUnmount},(function(o,i,a,l){return Object(r.ib)("td",{ref:o,colSpan:t.spreadsheetColCnt,className:["fc-datagrid-cell","fc-resource-group",n.theme.getClass("tableCellShaded")].concat(i).join(" ")},Object(r.ib)("div",{className:"fc-datagrid-cell-frame",style:{height:t.innerHeight}},Object(r.ib)("div",{className:"fc-datagrid-cell-cushion fc-scrollgrid-sync-inner",ref:e.innerInnerRef},Object(r.ib)(lt,{depth:0,hasChildren:!0,isExpanded:t.isExpanded,onExpanderClick:e.onExpanderClick}),Object(r.ib)("span",{className:"fc-datagrid-cell-main",ref:a},l))))})))},t}(r.b);function gt(e){return e.groupValue||Object(r.ib)(r.o,null,"\xa0")}vt.addPropsEquality({group:function(e,t){return e.spec===t.spec&&e.value===t.value}});var mt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.resizerElRefs=new r.u(t._handleColResizerEl.bind(t)),t.colDraggings={},t}return it(t,e),t.prototype.render=function(){var e=this,t=this.props,n=t.colSpecs,o=t.superHeaderRendering,i=t.rowInnerHeights,a={view:this.context.viewApi},l=[];if(i=i.slice(),o){var s=i.shift();l.push(Object(r.ib)("tr",{key:"row-super"},Object(r.ib)(r.v,{hookProps:a,classNames:o.headerClassNames,content:o.headerContent,didMount:o.headerDidMount,willUnmount:o.headerWillUnmount},(function(e,t,o,i){return Object(r.ib)("th",{colSpan:n.length,ref:e,className:["fc-datagrid-cell","fc-datagrid-cell-super"].concat(t).join(" ")},Object(r.ib)("div",{className:"fc-datagrid-cell-frame",style:{height:s}},Object(r.ib)("div",{className:"fc-datagrid-cell-cushion fc-scrollgrid-sync-inner",ref:o},i)))}))))}var u=i.shift();return l.push(Object(r.ib)("tr",{key:"row"},n.map((function(t,o){var i=o===n.length-1;return Object(r.ib)(r.v,{key:o,hookProps:a,classNames:t.headerClassNames,content:t.headerContent,didMount:t.headerDidMount,willUnmount:t.headerWillUnmount},(function(n,a,l,s){return Object(r.ib)("th",{ref:n,className:["fc-datagrid-cell"].concat(a).join(" ")},Object(r.ib)("div",{className:"fc-datagrid-cell-frame",style:{height:u}},Object(r.ib)("div",{className:"fc-datagrid-cell-cushion fc-scrollgrid-sync-inner"},t.isMain&&Object(r.ib)("span",{className:"fc-datagrid-expander fc-datagrid-expander-placeholder"},Object(r.ib)("span",{className:"fc-icon"})),Object(r.ib)("span",{className:"fc-datagrid-cell-main",ref:l},s)),!i&&Object(r.ib)("div",{className:"fc-datagrid-cell-resizer",ref:e.resizerElRefs.createRef(o)})))}))})))),Object(r.ib)(r.o,null,l)},t.prototype._handleColResizerEl=function(e,t){var n,r=this.colDraggings;e?(n=this.initColResizing(e,parseInt(t,10)))&&(r[t]=n):(n=r[t])&&(n.destroy(),delete r[t])},t.prototype.initColResizing=function(e,t){var n=this.context,o=n.pluginHooks,i=n.isRtl,a=this.props.onColWidthChange,l=o.elementDraggingImpl;if(l){var s,u,c=new l(e);return c.emitter.on("dragstart",(function(){var n=Object(r.zb)(Object(r.tb)(e,"tr"),"th");u=n.map((function(e){return e.getBoundingClientRect().width})),s=u[t]})),c.emitter.on("dragmove",(function(e){u[t]=Math.max(s+e.deltaX*(i?-1:1),20),a&&a(u.slice())})),c.setAutoScrollEnabled(!1),c}return null},t}(r.b),yt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return it(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n={resource:new Le(t,e.resource)};return Object(r.ib)(r.h,{hookProps:n,content:t.options.resourceLaneContent},(function(e,t){return t&&Object(r.ib)("div",{className:"fc-timeline-lane-misc",ref:e},t)}))},t}(r.b),bt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.refineHookProps=Object(r.oc)(St),t.normalizeClassNames=Object(r.Q)(),t.handleHeightChange=function(e,n){t.props.onHeightChange&&t.props.onHeightChange(Object(r.tb)(e,"tr"),n)},t}return it(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.context,o=n.options,i=this.refineHookProps({resource:t.resource,context:n}),a=this.normalizeClassNames(o.resourceLaneClassNames,i);return Object(r.ib)("tr",{ref:t.elRef},Object(r.ib)(r.q,{hookProps:i,didMount:o.resourceLaneDidMount,willUnmount:o.resourceLaneWillUnmount},(function(n){return Object(r.ib)("td",{ref:n,className:["fc-timeline-lane","fc-resource"].concat(a).join(" "),"data-resource-id":t.resource.id},Object(r.ib)("div",{className:"fc-timeline-lane-frame",style:{height:t.innerHeight}},Object(r.ib)(yt,{resource:t.resource}),Object(r.ib)(se,{dateProfile:t.dateProfile,tDateProfile:t.tDateProfile,nowDate:t.nowDate,todayRange:t.todayRange,nextDayThreshold:t.nextDayThreshold,businessHours:t.businessHours,eventStore:t.eventStore,eventUiBases:t.eventUiBases,dateSelection:t.dateSelection,eventSelection:t.eventSelection,eventDrag:t.eventDrag,eventResize:t.eventResize,timelineCoords:t.timelineCoords,onHeightChange:e.handleHeightChange})))})))},t}(r.b);function St(e){return{resource:new Le(e.context,e.resource)}}var Et=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return it(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.props.renderingHooks,o={groupValue:t.groupValue,view:this.context.viewApi};return Object(r.ib)("tr",{ref:t.elRef},Object(r.ib)(r.v,{hookProps:o,classNames:n.laneClassNames,content:n.laneContent,didMount:n.laneDidMount,willUnmount:n.laneWillUnmount},(function(n,o,i,a){return Object(r.ib)("td",{ref:n,className:["fc-timeline-lane","fc-resource-group",e.context.theme.getClass("tableCellShaded")].concat(o).join(" ")},Object(r.ib)("div",{style:{height:t.innerHeight},ref:i},a))})))},t}(r.b),wt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return it(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=e.rowElRefs,o=e.innerHeights;return Object(r.ib)("tbody",null,e.rowNodes.map((function(i,a){if(i.group)return Object(r.ib)(Et,{key:i.id,elRef:n.createRef(i.id),groupValue:i.group.value,renderingHooks:i.group.spec,innerHeight:o[a]||""});if(i.resource){var l=i.resource;return Object(r.ib)(bt,at({key:i.id,elRef:n.createRef(i.id)},e.splitProps[l.id],{resource:l,dateProfile:e.dateProfile,tDateProfile:e.tDateProfile,nowDate:e.nowDate,todayRange:e.todayRange,nextDayThreshold:t.options.nextDayThreshold,businessHours:l.businessHours||e.fallbackBusinessHours,innerHeight:o[a]||"",timelineCoords:e.slatCoords,onHeightChange:e.onRowHeightChange}))}return null})))},t}(r.b),Ct=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.rootElRef=Object(r.ob)(),t.rowElRefs=new r.u,t}return it(t,e),t.prototype.render=function(){var e=this.props,t=this.context;return Object(r.ib)("table",{ref:this.rootElRef,className:"fc-scrollgrid-sync-table "+t.theme.getClass("table"),style:{minWidth:e.tableMinWidth,width:e.clientWidth,height:e.minHeight}},Object(r.ib)(wt,{rowElRefs:this.rowElRefs,rowNodes:e.rowNodes,dateProfile:e.dateProfile,tDateProfile:e.tDateProfile,nowDate:e.nowDate,todayRange:e.todayRange,splitProps:e.splitProps,fallbackBusinessHours:e.fallbackBusinessHours,slatCoords:e.slatCoords,innerHeights:e.innerHeights,onRowHeightChange:e.onRowHeightChange}))},t.prototype.componentDidMount=function(){this.updateCoords()},t.prototype.componentDidUpdate=function(){this.updateCoords()},t.prototype.componentWillUnmount=function(){this.props.onRowCoords&&this.props.onRowCoords(null)},t.prototype.updateCoords=function(){var e,t=this.props;t.onRowCoords&&null!==t.clientWidth&&this.props.onRowCoords(new r.t(this.rootElRef.current,(e=this.rowElRefs.currentMap,t.rowNodes.map((function(t){return e[t.id]}))),!1,!0))},t}(r.b);var Dt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.computeHasResourceBusinessHours=Object(r.lc)(Rt),t.resourceSplitter=new Ae,t.bgSlicer=new re,t.slatsRef=Object(r.ob)(),t.state={slatCoords:null},t.handleEl=function(e){e?t.context.registerInteractiveComponent(t,{el:e}):t.context.unregisterInteractiveComponent(t)},t.handleSlatCoords=function(e){t.setState({slatCoords:e}),t.props.onSlatCoords&&t.props.onSlatCoords(e)},t.handleRowCoords=function(e){t.rowCoords=e,t.props.onRowCoords&&t.props.onRowCoords(e)},t}return it(t,e),t.prototype.render=function(){var e=this,t=this,n=t.props,o=t.state,i=t.context,a=n.dateProfile,l=n.tDateProfile,s=Object(r.Ub)(l.slotDuration).unit,u=this.computeHasResourceBusinessHours(n.rowNodes),c=this.resourceSplitter.splitProps(n),d=c[""],f=this.bgSlicer.sliceProps(d,a,l.isTimeScale?null:n.nextDayThreshold,i,a,i.dateProfileGenerator,l,i.dateEnv),p=o.slatCoords&&o.slatCoords.dateProfile===n.dateProfile?o.slatCoords:null;return Object(r.ib)("div",{ref:this.handleEl,className:"fc-timeline-body",style:{minWidth:n.tableMinWidth}},Object(r.ib)(r.s,{unit:s},(function(t,o){return Object(r.ib)(r.o,null,Object(r.ib)(te,{ref:e.slatsRef,dateProfile:a,tDateProfile:l,nowDate:t,todayRange:o,clientWidth:n.clientWidth,tableColGroupNode:n.tableColGroupNode,tableMinWidth:n.tableMinWidth,onCoords:e.handleSlatCoords,onScrollLeftRequest:n.onScrollLeftRequest}),Object(r.ib)(ne,{businessHourSegs:u?null:f.businessHourSegs,bgEventSegs:f.bgEventSegs,timelineCoords:p,eventResizeSegs:f.eventResize?f.eventResize.segs:[],dateSelectionSegs:f.dateSelectionSegs,nowDate:t,todayRange:o}),Object(r.ib)(Ct,{rowNodes:n.rowNodes,dateProfile:a,tDateProfile:n.tDateProfile,nowDate:t,todayRange:o,splitProps:c,fallbackBusinessHours:u?n.businessHours:null,clientWidth:n.clientWidth,minHeight:n.expandRows?n.clientHeight:"",tableMinWidth:n.tableMinWidth,innerHeights:n.rowInnerHeights,slatCoords:p,onRowCoords:e.handleRowCoords,onRowHeightChange:n.onRowHeightChange}),i.options.nowIndicator&&p&&p.isDateInRange(t)&&Object(r.ib)("div",{className:"fc-timeline-now-indicator-container"},Object(r.ib)(r.r,{isAxis:!1,date:t},(function(e,n,o,i){return Object(r.ib)("div",{ref:e,className:["fc-timeline-now-indicator-line"].concat(n).join(" "),style:{left:p.dateToCoord(t)}},i)}))))})))},t.prototype.queryHit=function(e,t){var n=this.rowCoords,r=n.topToIndex(t);if(null!=r){var o=this.props.rowNodes[r].resource;if(o){var i=this.slatsRef.current.positionToHit(e);if(i)return{component:this,dateSpan:{range:i.dateSpan.range,allDay:i.dateSpan.allDay,resourceId:o.id},rect:{left:i.left,right:i.right,top:n.tops[r],bottom:n.bottoms[r]},dayEl:i.dayEl,layer:0}}}return null},t}(r.i);function Rt(e){for(var t=0,n=e;t<n.length;t++){var r=n[t].resource;if(r&&r.businessHours)return!0}return!1}var kt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.scrollGridRef=Object(r.ob)(),t.timeBodyScrollerElRef=Object(r.ob)(),t.spreadsheetHeaderChunkElRef=Object(r.ob)(),t.rootElRef=Object(r.ob)(),t.state={resourceAreaWidthOverride:null},t}return it(t,e),t.prototype.render=function(){var e=this,t=e.props,n=e.state,o=e.context,i=o.options,a=!t.forPrint&&Object(r.Tb)(i),l=!t.forPrint&&Object(r.Sb)(i),s=[{type:"header",key:"header",syncRowHeights:!0,isSticky:a,chunks:[{key:"datagrid",elRef:this.spreadsheetHeaderChunkElRef,tableClassName:"fc-datagrid-header",rowContent:t.spreadsheetHeaderRows},{key:"divider",outerContent:Object(r.ib)("td",{className:"fc-resource-timeline-divider "+o.theme.getClass("tableCellShaded")})},{key:"timeline",content:t.timeHeaderContent}]},{type:"body",key:"body",syncRowHeights:!0,liquid:!0,expandRows:Boolean(i.expandRows),chunks:[{key:"datagrid",tableClassName:"fc-datagrid-body",rowContent:t.spreadsheetBodyRows},{key:"divider",outerContent:Object(r.ib)("td",{className:"fc-resource-timeline-divider "+o.theme.getClass("tableCellShaded")})},{key:"timeline",scrollerElRef:this.timeBodyScrollerElRef,content:t.timeBodyContent}]}];l&&s.push({type:"footer",key:"footer",isSticky:!0,chunks:[{key:"datagrid",content:r.Kc},{key:"divider",outerContent:Object(r.ib)("td",{className:"fc-resource-timeline-divider "+o.theme.getClass("tableCellShaded")})},{key:"timeline",content:r.Kc}]});var u=null!=n.resourceAreaWidthOverride?n.resourceAreaWidthOverride:i.resourceAreaWidth;return Object(r.ib)(k,{ref:this.scrollGridRef,elRef:this.rootElRef,liquid:!t.isHeightAuto&&!t.forPrint,colGroups:[{cols:t.spreadsheetCols,width:u},{cols:[]},{cols:t.timeCols}],sections:s})},t.prototype.forceTimeScroll=function(e){this.scrollGridRef.current.forceScrollLeft(2,e)},t.prototype.forceResourceScroll=function(e){this.scrollGridRef.current.forceScrollTop(1,e)},t.prototype.getResourceScroll=function(){return this.timeBodyScrollerElRef.current.scrollTop},t.prototype.componentDidMount=function(){this.initSpreadsheetResizing()},t.prototype.componentWillUnmount=function(){this.destroySpreadsheetResizing()},t.prototype.initSpreadsheetResizing=function(){var e=this,t=this.context,n=t.isRtl,r=t.pluginHooks.elementDraggingImpl,o=this.spreadsheetHeaderChunkElRef.current;if(r){var i,a,l=this.rootElRef.current,s=this.spreadsheetResizerDragging=new r(l,".fc-resource-timeline-divider");s.emitter.on("dragstart",(function(){i=o.getBoundingClientRect().width,a=l.getBoundingClientRect().width})),s.emitter.on("dragmove",(function(t){var r=i+t.deltaX*(n?-1:1);r=Math.max(r,30),r=Math.min(r,a-30),e.setState({resourceAreaWidthOverride:r})})),s.setAutoScrollEnabled(!1)}},t.prototype.destroySpreadsheetResizing=function(){this.spreadsheetResizerDragging&&this.spreadsheetResizerDragging.destroy()},t}(r.b),Ot=function(e){function t(t,n){var o=e.call(this,t,n)||this;return o.processColOptions=Object(r.lc)(Pt),o.buildTimelineDateProfile=Object(r.lc)(z),o.hasNesting=Object(r.lc)(Mt),o.buildRowNodes=Object(r.lc)(Ke),o.layoutRef=Object(r.ob)(),o.rowNodes=[],o.renderedRowNodes=[],o.buildRowIndex=Object(r.lc)(Tt),o.handleSlatCoords=function(e){o.setState({slatCoords:e})},o.handleRowCoords=function(e){o.rowCoords=e,o.scrollResponder.update(!1)},o.handleMaxCushionWidth=function(e){o.setState({slotCushionMaxWidth:Math.ceil(e)})},o.handleScrollLeftRequest=function(e){o.layoutRef.current.forceTimeScroll(e)},o.handleScrollRequest=function(e){var t=o.rowCoords,n=o.layoutRef.current,r=e.rowId||e.resourceId;if(t){if(r){var i=o.buildRowIndex(o.renderedRowNodes)[r];if(null!=i){var a=null!=e.fromBottom?t.bottoms[i]-e.fromBottom:t.tops[i];n.forceResourceScroll(a)}}return!0}return null},o.handleColWidthChange=function(e){o.setState({spreadsheetColWidths:e})},o.state={resourceAreaWidth:n.options.resourceAreaWidth,spreadsheetColWidths:[]},o}return it(t,e),t.prototype.render=function(){var e=this,t=this,n=t.props,o=t.state,i=t.context,a=i.options,l=i.viewSpec,s=this.processColOptions(i.options),u=s.superHeaderRendering,c=s.groupSpecs,d=s.orderSpecs,f=s.isVGrouping,p=s.colSpecs,h=this.buildTimelineDateProfile(n.dateProfile,i.dateEnv,a,i.dateProfileGenerator),v=this.rowNodes=this.buildRowNodes(n.resourceStore,c,d,f,n.resourceEntityExpansions,a.resourcesInitiallyExpanded),g=["fc-resource-timeline",this.hasNesting(v)?"":"fc-resource-timeline-flat","fc-timeline",!1===a.eventOverlap?"fc-timeline-overlap-disabled":"fc-timeline-overlap-enabled"],m=a.slotMinWidth,y=de(h,m||this.computeFallbackSlotMinWidth(h));return Object(r.ib)(r.E,{viewSpec:l},(function(t,a){return Object(r.ib)("div",{ref:t,className:g.concat(a).join(" ")},Object(r.ib)(kt,{ref:e.layoutRef,forPrint:n.forPrint,isHeightAuto:n.isHeightAuto,spreadsheetCols:xt(p,o.spreadsheetColWidths,""),spreadsheetHeaderRows:function(t){return Object(r.ib)(mt,{superHeaderRendering:u,colSpecs:p,onColWidthChange:e.handleColWidthChange,rowInnerHeights:t.rowSyncHeights})},spreadsheetBodyRows:function(t){return Object(r.ib)(r.o,null,e.renderSpreadsheetRows(v,p,t.rowSyncHeights))},timeCols:y,timeHeaderContent:function(t){return Object(r.ib)($,{clientWidth:t.clientWidth,clientHeight:t.clientHeight,tableMinWidth:t.tableMinWidth,tableColGroupNode:t.tableColGroupNode,dateProfile:n.dateProfile,tDateProfile:h,slatCoords:o.slatCoords,rowInnerHeights:t.rowSyncHeights,onMaxCushionWidth:m?null:e.handleMaxCushionWidth})},timeBodyContent:function(t){return Object(r.ib)(Dt,{dateProfile:n.dateProfile,clientWidth:t.clientWidth,clientHeight:t.clientHeight,tableMinWidth:t.tableMinWidth,tableColGroupNode:t.tableColGroupNode,expandRows:t.expandRows,tDateProfile:h,rowNodes:v,businessHours:n.businessHours,dateSelection:n.dateSelection,eventStore:n.eventStore,eventUiBases:n.eventUiBases,eventSelection:n.eventSelection,eventDrag:n.eventDrag,eventResize:n.eventResize,resourceStore:n.resourceStore,nextDayThreshold:i.options.nextDayThreshold,rowInnerHeights:t.rowSyncHeights,onSlatCoords:e.handleSlatCoords,onRowCoords:e.handleRowCoords,onScrollLeftRequest:e.handleScrollLeftRequest,onRowHeightChange:t.reportRowHeightChange})}}))}))},t.prototype.renderSpreadsheetRows=function(e,t,n){return e.map((function(e,o){return e.group?Object(r.ib)(vt,{key:e.id,id:e.id,spreadsheetColCnt:t.length,isExpanded:e.isExpanded,group:e.group,innerHeight:n[o]||""}):e.resource?Object(r.ib)(ht,{key:e.id,colSpecs:t,rowSpans:e.rowSpans,depth:e.depth,isExpanded:e.isExpanded,hasChildren:e.hasChildren,resource:e.resource,innerHeight:n[o]||""}):null}))},t.prototype.componentDidMount=function(){this.renderedRowNodes=this.rowNodes,this.scrollResponder=this.context.createScrollResponder(this.handleScrollRequest)},t.prototype.getSnapshotBeforeUpdate=function(){return this.props.forPrint?{}:{resourceScroll:this.queryResourceScroll()}},t.prototype.componentDidUpdate=function(e,t,n){this.renderedRowNodes=this.rowNodes,this.scrollResponder.update(e.dateProfile!==this.props.dateProfile),n.resourceScroll&&this.handleScrollRequest(n.resourceScroll)},t.prototype.componentWillUnmount=function(){this.scrollResponder.detach()},t.prototype.computeFallbackSlotMinWidth=function(e){return Math.max(30,(this.state.slotCushionMaxWidth||0)/e.slotsPerLabel)},t.prototype.queryResourceScroll=function(){var e=this.rowCoords,t=this.renderedRowNodes;if(e){for(var n=this.layoutRef.current,r=e.bottoms,o=n.getResourceScroll(),i={},a=0;a<r.length;a+=1){var l=t[a],s=r[a]-o;if(s>0){i.rowId=l.id,i.fromBottom=s;break}}return i}return null},t}(r.b);function Tt(e){for(var t={},n=0;n<e.length;n+=1)t[e[n].id]=n;return t}function xt(e,t,n){return void 0===n&&(n=""),e.map((function(e,r){return{className:e.isMain?"fc-main-col":"",width:t[r]||e.width||n}}))}function Mt(e){for(var t=0,n=e;t<n.length;t++){var r=n[t];if(r.group)return!0;if(r.resource&&r.hasChildren)return!0}return!1}function Pt(e){var t=e.resourceAreaColumns||[],n=null;t.length?e.resourceAreaHeaderContent&&(n={headerClassNames:e.resourceAreaHeaderClassNames,headerContent:e.resourceAreaHeaderContent,headerDidMount:e.resourceAreaHeaderDidMount,headerWillUnmount:e.resourceAreaHeaderWillUnmount}):t.push({headerClassNames:e.resourceAreaHeaderClassNames,headerContent:e.resourceAreaHeaderContent||"Resources",headerDidMount:e.resourceAreaHeaderDidMount,headerWillUnmount:e.resourceAreaHeaderWillUnmount});for(var r=[],o=[],i=[],a=!1,l=0,s=t;l<s.length;l++){var u=s[l];u.group?o.push(at(at({},u),{cellClassNames:u.cellClassNames||e.resourceGroupLabelClassNames,cellContent:u.cellContent||e.resourceGroupLabelContent,cellDidMount:u.cellDidMount||e.resourceGroupLabelDidMount,cellWillUnmount:u.cellWillUnmount||e.resourceGroupLaneWillUnmount})):r.push(u)}var c=r[0];if(c.isMain=!0,c.cellClassNames=c.cellClassNames||e.resourceLabelClassNames,c.cellContent=c.cellContent||e.resourceLabelContent,c.cellDidMount=c.cellDidMount||e.resourceLabelDidMount,c.cellWillUnmount=c.cellWillUnmount||e.resourceLabelWillUnmount,o.length)i=o,a=!0;else{var d=e.resourceGroupField;d&&i.push({field:d,labelClassNames:e.resourceGroupLabelClassNames,labelContent:e.resourceGroupLabelContent,labelDidMount:e.resourceGroupLabelDidMount,labelWillUnmount:e.resourceGroupLabelWillUnmount,laneClassNames:e.resourceGroupLaneClassNames,laneContent:e.resourceGroupLaneContent,laneDidMount:e.resourceGroupLaneDidMount,laneWillUnmount:e.resourceGroupLaneWillUnmount})}for(var f=[],p=0,h=e.resourceOrder||Be;p<h.length;p++){for(var v=h[p],g=!1,m=0,y=i;m<y.length;m++){var b=y[m];if(b.field===v.field){b.order=v.order,g=!0;break}}g||f.push(v)}return{superHeaderRendering:n,isVGrouping:a,groupSpecs:i,colSpecs:o.concat(r),orderSpecs:f}}Ot.addStateEquality({spreadsheetColWidths:r.cc});var It=Object(r.nb)({deps:[s,rt,fe],initialView:"resourceTimelineDay",views:{resourceTimeline:{type:"timeline",component:Ot,needsResourceData:!0,resourceAreaWidth:"30%",resourcesInitiallyExpanded:!0,eventResizableFromStart:!0},resourceTimelineDay:{type:"resourceTimeline",duration:{days:1}},resourceTimelineWeek:{type:"resourceTimeline",duration:{weeks:1}},resourceTimelineMonth:{type:"resourceTimeline",duration:{months:1}},resourceTimelineYear:{type:"resourceTimeline",duration:{years:1}}}});t.a=It},function(e,t,n){"use strict";var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};var o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;Object.create;var i=n(1),a=n(3);("undefined"!==typeof globalThis?globalThis:window).FullCalendarVDom={Component:i.Component,createElement:i.createElement,render:a.render,createRef:i.createRef,Fragment:i.Fragment,createContext:i.createContext,flushToDom:function(){},unmountComponentAtNode:a.unmountComponentAtNode};var l=n(0),s=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._calendarApi=new l.d,t}return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(t,e),t.prototype.render=function(){return i.createElement(l.f,{optionOverrides:this.props,calendarApi:this._calendarApi},(function(e){return i.createElement(l.g,{options:e.calendarOptions,theme:e.theme,emitter:e.emitter},(function(t,n,r,a){return i.createElement("div",{className:t.join(" "),style:{height:n}},i.createElement(l.e,o({isHeightAuto:r,forPrint:a},e)))}))}))},t.prototype.getApi=function(){return this._calendarApi},t}(i.Component);t.a=s},function(e,t,n){"use strict";var r=n(0),o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function i(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;Object.create;r.fb.touchMouseIgnoreWait=500;var l=0,s=0,u=!1,c=function(){function e(e){var t=this;this.subjectEl=null,this.selector="",this.handleSelector="",this.shouldIgnoreMove=!1,this.shouldWatchScroll=!0,this.isDragging=!1,this.isTouchDragging=!1,this.wasTouchScroll=!1,this.handleMouseDown=function(e){if(!t.shouldIgnoreMouse()&&function(e){return 0===e.button&&!e.ctrlKey}(e)&&t.tryStart(e)){var n=t.createEventFromMouse(e,!0);t.emitter.trigger("pointerdown",n),t.initScrollWatch(n),t.shouldIgnoreMove||document.addEventListener("mousemove",t.handleMouseMove),document.addEventListener("mouseup",t.handleMouseUp)}},this.handleMouseMove=function(e){var n=t.createEventFromMouse(e);t.recordCoords(n),t.emitter.trigger("pointermove",n)},this.handleMouseUp=function(e){document.removeEventListener("mousemove",t.handleMouseMove),document.removeEventListener("mouseup",t.handleMouseUp),t.emitter.trigger("pointerup",t.createEventFromMouse(e)),t.cleanup()},this.handleTouchStart=function(e){if(t.tryStart(e)){t.isTouchDragging=!0;var n=t.createEventFromTouch(e,!0);t.emitter.trigger("pointerdown",n),t.initScrollWatch(n);var r=e.target;t.shouldIgnoreMove||r.addEventListener("touchmove",t.handleTouchMove),r.addEventListener("touchend",t.handleTouchEnd),r.addEventListener("touchcancel",t.handleTouchEnd),window.addEventListener("scroll",t.handleTouchScroll,!0)}},this.handleTouchMove=function(e){var n=t.createEventFromTouch(e);t.recordCoords(n),t.emitter.trigger("pointermove",n)},this.handleTouchEnd=function(e){if(t.isDragging){var n=e.target;n.removeEventListener("touchmove",t.handleTouchMove),n.removeEventListener("touchend",t.handleTouchEnd),n.removeEventListener("touchcancel",t.handleTouchEnd),window.removeEventListener("scroll",t.handleTouchScroll,!0),t.emitter.trigger("pointerup",t.createEventFromTouch(e)),t.cleanup(),t.isTouchDragging=!1,l+=1,setTimeout((function(){l-=1}),r.fb.touchMouseIgnoreWait)}},this.handleTouchScroll=function(){t.wasTouchScroll=!0},this.handleScroll=function(e){if(!t.shouldIgnoreMove){var n=window.pageXOffset-t.prevScrollX+t.prevPageX,r=window.pageYOffset-t.prevScrollY+t.prevPageY;t.emitter.trigger("pointermove",{origEvent:e,isTouch:t.isTouchDragging,subjectEl:t.subjectEl,pageX:n,pageY:r,deltaX:n-t.origPageX,deltaY:r-t.origPageY})}},this.containerEl=e,this.emitter=new r.m,e.addEventListener("mousedown",this.handleMouseDown),e.addEventListener("touchstart",this.handleTouchStart,{passive:!0}),1===(s+=1)&&window.addEventListener("touchmove",d,{passive:!1})}return e.prototype.destroy=function(){this.containerEl.removeEventListener("mousedown",this.handleMouseDown),this.containerEl.removeEventListener("touchstart",this.handleTouchStart,{passive:!0}),(s-=1)||window.removeEventListener("touchmove",d,{passive:!1})},e.prototype.tryStart=function(e){var t=this.querySubjectEl(e),n=e.target;return!(!t||this.handleSelector&&!Object(r.tb)(n,this.handleSelector))&&(this.subjectEl=t,this.isDragging=!0,this.wasTouchScroll=!1,!0)},e.prototype.cleanup=function(){u=!1,this.isDragging=!1,this.subjectEl=null,this.destroyScrollWatch()},e.prototype.querySubjectEl=function(e){return this.selector?Object(r.tb)(e.target,this.selector):this.containerEl},e.prototype.shouldIgnoreMouse=function(){return l||this.isTouchDragging},e.prototype.cancelTouchScroll=function(){this.isDragging&&(u=!0)},e.prototype.initScrollWatch=function(e){this.shouldWatchScroll&&(this.recordCoords(e),window.addEventListener("scroll",this.handleScroll,!0))},e.prototype.recordCoords=function(e){this.shouldWatchScroll&&(this.prevPageX=e.pageX,this.prevPageY=e.pageY,this.prevScrollX=window.pageXOffset,this.prevScrollY=window.pageYOffset)},e.prototype.destroyScrollWatch=function(){this.shouldWatchScroll&&window.removeEventListener("scroll",this.handleScroll,!0)},e.prototype.createEventFromMouse=function(e,t){var n=0,r=0;return t?(this.origPageX=e.pageX,this.origPageY=e.pageY):(n=e.pageX-this.origPageX,r=e.pageY-this.origPageY),{origEvent:e,isTouch:!1,subjectEl:this.subjectEl,pageX:e.pageX,pageY:e.pageY,deltaX:n,deltaY:r}},e.prototype.createEventFromTouch=function(e,t){var n,r,o=e.touches,i=0,a=0;return o&&o.length?(n=o[0].pageX,r=o[0].pageY):(n=e.pageX,r=e.pageY),t?(this.origPageX=n,this.origPageY=r):(i=n-this.origPageX,a=r-this.origPageY),{origEvent:e,isTouch:!0,subjectEl:this.subjectEl,pageX:n,pageY:r,deltaX:i,deltaY:a}},e}();function d(e){u&&e.preventDefault()}var f=function(){function e(){this.isVisible=!1,this.sourceEl=null,this.mirrorEl=null,this.sourceElRect=null,this.parentNode=document.body,this.zIndex=9999,this.revertDuration=0}return e.prototype.start=function(e,t,n){this.sourceEl=e,this.sourceElRect=this.sourceEl.getBoundingClientRect(),this.origScreenX=t-window.pageXOffset,this.origScreenY=n-window.pageYOffset,this.deltaX=0,this.deltaY=0,this.updateElPosition()},e.prototype.handleMove=function(e,t){this.deltaX=e-window.pageXOffset-this.origScreenX,this.deltaY=t-window.pageYOffset-this.origScreenY,this.updateElPosition()},e.prototype.setIsVisible=function(e){e?this.isVisible||(this.mirrorEl&&(this.mirrorEl.style.display=""),this.isVisible=e,this.updateElPosition()):this.isVisible&&(this.mirrorEl&&(this.mirrorEl.style.display="none"),this.isVisible=e)},e.prototype.stop=function(e,t){var n=this,r=function(){n.cleanup(),t()};e&&this.mirrorEl&&this.isVisible&&this.revertDuration&&(this.deltaX||this.deltaY)?this.doRevertAnimation(r,this.revertDuration):setTimeout(r,0)},e.prototype.doRevertAnimation=function(e,t){var n=this.mirrorEl,o=this.sourceEl.getBoundingClientRect();n.style.transition="top "+t+"ms,left "+t+"ms",Object(r.L)(n,{left:o.left,top:o.top}),Object(r.Tc)(n,(function(){n.style.transition="",e()}))},e.prototype.cleanup=function(){this.mirrorEl&&(Object(r.Gc)(this.mirrorEl),this.mirrorEl=null),this.sourceEl=null},e.prototype.updateElPosition=function(){this.sourceEl&&this.isVisible&&Object(r.L)(this.getMirrorEl(),{left:this.sourceElRect.left+this.deltaX,top:this.sourceElRect.top+this.deltaY})},e.prototype.getMirrorEl=function(){var e=this.sourceElRect,t=this.mirrorEl;return t||((t=this.mirrorEl=this.sourceEl.cloneNode(!0)).classList.add("fc-unselectable"),t.classList.add("fc-event-dragging"),Object(r.L)(t,{position:"fixed",zIndex:this.zIndex,visibility:"",boxSizing:"border-box",width:e.right-e.left,height:e.bottom-e.top,right:"auto",bottom:"auto",margin:0}),this.parentNode.appendChild(t)),t},e}(),p=function(e){function t(t,n){var r=e.call(this)||this;return r.handleScroll=function(){r.scrollTop=r.scrollController.getScrollTop(),r.scrollLeft=r.scrollController.getScrollLeft(),r.handleScrollChange()},r.scrollController=t,r.doesListening=n,r.scrollTop=r.origScrollTop=t.getScrollTop(),r.scrollLeft=r.origScrollLeft=t.getScrollLeft(),r.scrollWidth=t.getScrollWidth(),r.scrollHeight=t.getScrollHeight(),r.clientWidth=t.getClientWidth(),r.clientHeight=t.getClientHeight(),r.clientRect=r.computeClientRect(),r.doesListening&&r.getEventTarget().addEventListener("scroll",r.handleScroll),r}return i(t,e),t.prototype.destroy=function(){this.doesListening&&this.getEventTarget().removeEventListener("scroll",this.handleScroll)},t.prototype.getScrollTop=function(){return this.scrollTop},t.prototype.getScrollLeft=function(){return this.scrollLeft},t.prototype.setScrollTop=function(e){this.scrollController.setScrollTop(e),this.doesListening||(this.scrollTop=Math.max(Math.min(e,this.getMaxScrollTop()),0),this.handleScrollChange())},t.prototype.setScrollLeft=function(e){this.scrollController.setScrollLeft(e),this.doesListening||(this.scrollLeft=Math.max(Math.min(e,this.getMaxScrollLeft()),0),this.handleScrollChange())},t.prototype.getClientWidth=function(){return this.clientWidth},t.prototype.getClientHeight=function(){return this.clientHeight},t.prototype.getScrollWidth=function(){return this.scrollWidth},t.prototype.getScrollHeight=function(){return this.scrollHeight},t.prototype.handleScrollChange=function(){},t}(r.w),h=function(e){function t(t,n){return e.call(this,new r.l(t),n)||this}return i(t,e),t.prototype.getEventTarget=function(){return this.scrollController.el},t.prototype.computeClientRect=function(){return Object(r.bb)(this.scrollController.el)},t}(p),v=function(e){function t(t){return e.call(this,new r.F,t)||this}return i(t,e),t.prototype.getEventTarget=function(){return window},t.prototype.computeClientRect=function(){return{left:this.scrollLeft,right:this.scrollLeft+this.clientWidth,top:this.scrollTop,bottom:this.scrollTop+this.clientHeight}},t.prototype.handleScrollChange=function(){this.clientRect=this.computeClientRect()},t}(p),g="function"===typeof performance?performance.now:Date.now,m=function(){function e(){var e=this;this.isEnabled=!0,this.scrollQuery=[window,".fc-scroller"],this.edgeThreshold=50,this.maxVelocity=300,this.pointerScreenX=null,this.pointerScreenY=null,this.isAnimating=!1,this.scrollCaches=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.animate=function(){if(e.isAnimating){var t=e.computeBestEdge(e.pointerScreenX+window.pageXOffset,e.pointerScreenY+window.pageYOffset);if(t){var n=g();e.handleSide(t,(n-e.msSinceRequest)/1e3),e.requestAnimation(n)}else e.isAnimating=!1}}}return e.prototype.start=function(e,t){this.isEnabled&&(this.scrollCaches=this.buildCaches(),this.pointerScreenX=null,this.pointerScreenY=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.handleMove(e,t))},e.prototype.handleMove=function(e,t){if(this.isEnabled){var n=e-window.pageXOffset,r=t-window.pageYOffset,o=null===this.pointerScreenY?0:r-this.pointerScreenY,i=null===this.pointerScreenX?0:n-this.pointerScreenX;o<0?this.everMovedUp=!0:o>0&&(this.everMovedDown=!0),i<0?this.everMovedLeft=!0:i>0&&(this.everMovedRight=!0),this.pointerScreenX=n,this.pointerScreenY=r,this.isAnimating||(this.isAnimating=!0,this.requestAnimation(g()))}},e.prototype.stop=function(){if(this.isEnabled){this.isAnimating=!1;for(var e=0,t=this.scrollCaches;e<t.length;e++){t[e].destroy()}this.scrollCaches=null}},e.prototype.requestAnimation=function(e){this.msSinceRequest=e,requestAnimationFrame(this.animate)},e.prototype.handleSide=function(e,t){var n=e.scrollCache,r=this.edgeThreshold,o=r-e.distance,i=o*o/(r*r)*this.maxVelocity*t,a=1;switch(e.name){case"left":a=-1;case"right":n.setScrollLeft(n.getScrollLeft()+i*a);break;case"top":a=-1;case"bottom":n.setScrollTop(n.getScrollTop()+i*a)}},e.prototype.computeBestEdge=function(e,t){for(var n=this.edgeThreshold,r=null,o=0,i=this.scrollCaches;o<i.length;o++){var a=i[o],l=a.clientRect,s=e-l.left,u=l.right-e,c=t-l.top,d=l.bottom-t;s>=0&&u>=0&&c>=0&&d>=0&&(c<=n&&this.everMovedUp&&a.canScrollUp()&&(!r||r.distance>c)&&(r={scrollCache:a,name:"top",distance:c}),d<=n&&this.everMovedDown&&a.canScrollDown()&&(!r||r.distance>d)&&(r={scrollCache:a,name:"bottom",distance:d}),s<=n&&this.everMovedLeft&&a.canScrollLeft()&&(!r||r.distance>s)&&(r={scrollCache:a,name:"left",distance:s}),u<=n&&this.everMovedRight&&a.canScrollRight()&&(!r||r.distance>u)&&(r={scrollCache:a,name:"right",distance:u}))}return r},e.prototype.buildCaches=function(){return this.queryScrollEls().map((function(e){return e===window?new v(!1):new h(e,!1)}))},e.prototype.queryScrollEls=function(){for(var e=[],t=0,n=this.scrollQuery;t<n.length;t++){var r=n[t];"object"===typeof r?e.push(r):e.push.apply(e,Array.prototype.slice.call(document.querySelectorAll(r)))}return e},e}(),y=function(e){function t(t,n){var o=e.call(this,t)||this;o.delay=null,o.minDistance=0,o.touchScrollAllowed=!0,o.mirrorNeedsRevert=!1,o.isInteracting=!1,o.isDragging=!1,o.isDelayEnded=!1,o.isDistanceSurpassed=!1,o.delayTimeoutId=null,o.onPointerDown=function(e){o.isDragging||(o.isInteracting=!0,o.isDelayEnded=!1,o.isDistanceSurpassed=!1,Object(r.zc)(document.body),Object(r.yc)(document.body),e.isTouch||e.origEvent.preventDefault(),o.emitter.trigger("pointerdown",e),o.isInteracting&&!o.pointer.shouldIgnoreMove&&(o.mirror.setIsVisible(!1),o.mirror.start(e.subjectEl,e.pageX,e.pageY),o.startDelay(e),o.minDistance||o.handleDistanceSurpassed(e)))},o.onPointerMove=function(e){if(o.isInteracting){if(o.emitter.trigger("pointermove",e),!o.isDistanceSurpassed){var t=o.minDistance,n=e.deltaX,r=e.deltaY;n*n+r*r>=t*t&&o.handleDistanceSurpassed(e)}o.isDragging&&("scroll"!==e.origEvent.type&&(o.mirror.handleMove(e.pageX,e.pageY),o.autoScroller.handleMove(e.pageX,e.pageY)),o.emitter.trigger("dragmove",e))}},o.onPointerUp=function(e){o.isInteracting&&(o.isInteracting=!1,Object(r.J)(document.body),Object(r.I)(document.body),o.emitter.trigger("pointerup",e),o.isDragging&&(o.autoScroller.stop(),o.tryStopDrag(e)),o.delayTimeoutId&&(clearTimeout(o.delayTimeoutId),o.delayTimeoutId=null))};var i=o.pointer=new c(t);return i.emitter.on("pointerdown",o.onPointerDown),i.emitter.on("pointermove",o.onPointerMove),i.emitter.on("pointerup",o.onPointerUp),n&&(i.selector=n),o.mirror=new f,o.autoScroller=new m,o}return i(t,e),t.prototype.destroy=function(){this.pointer.destroy(),this.onPointerUp({})},t.prototype.startDelay=function(e){var t=this;"number"===typeof this.delay?this.delayTimeoutId=setTimeout((function(){t.delayTimeoutId=null,t.handleDelayEnd(e)}),this.delay):this.handleDelayEnd(e)},t.prototype.handleDelayEnd=function(e){this.isDelayEnded=!0,this.tryStartDrag(e)},t.prototype.handleDistanceSurpassed=function(e){this.isDistanceSurpassed=!0,this.tryStartDrag(e)},t.prototype.tryStartDrag=function(e){this.isDelayEnded&&this.isDistanceSurpassed&&(this.pointer.wasTouchScroll&&!this.touchScrollAllowed||(this.isDragging=!0,this.mirrorNeedsRevert=!1,this.autoScroller.start(e.pageX,e.pageY),this.emitter.trigger("dragstart",e),!1===this.touchScrollAllowed&&this.pointer.cancelTouchScroll()))},t.prototype.tryStopDrag=function(e){this.mirror.stop(this.mirrorNeedsRevert,this.stopDrag.bind(this,e))},t.prototype.stopDrag=function(e){this.isDragging=!1,this.emitter.trigger("dragend",e)},t.prototype.setIgnoreMove=function(e){this.pointer.shouldIgnoreMove=e},t.prototype.setMirrorIsVisible=function(e){this.mirror.setIsVisible(e)},t.prototype.setMirrorNeedsRevert=function(e){this.mirrorNeedsRevert=e},t.prototype.setAutoScrollEnabled=function(e){this.autoScroller.isEnabled=e},t}(r.k),b=function(){function e(e){this.origRect=Object(r.cb)(e),this.scrollCaches=Object(r.Eb)(e).map((function(e){return new h(e,!0)}))}return e.prototype.destroy=function(){for(var e=0,t=this.scrollCaches;e<t.length;e++){t[e].destroy()}},e.prototype.computeLeft=function(){for(var e=this.origRect.left,t=0,n=this.scrollCaches;t<n.length;t++){var r=n[t];e+=r.origScrollLeft-r.getScrollLeft()}return e},e.prototype.computeTop=function(){for(var e=this.origRect.top,t=0,n=this.scrollCaches;t<n.length;t++){var r=n[t];e+=r.origScrollTop-r.getScrollTop()}return e},e.prototype.isWithinClipping=function(e,t){for(var n={left:e,top:t},o=0,i=this.scrollCaches;o<i.length;o++){var a=i[o];if(!S(a.getEventTarget())&&!Object(r.xc)(n,a.clientRect))return!1}return!0},e}();function S(e){var t=e.tagName;return"HTML"===t||"BODY"===t}var E=function(){function e(e,t){var n=this;this.useSubjectCenter=!1,this.requireInitial=!0,this.initialHit=null,this.movingHit=null,this.finalHit=null,this.handlePointerDown=function(e){var t=n.dragging;n.initialHit=null,n.movingHit=null,n.finalHit=null,n.prepareHits(),n.processFirstCoord(e),n.initialHit||!n.requireInitial?(t.setIgnoreMove(!1),n.emitter.trigger("pointerdown",e)):t.setIgnoreMove(!0)},this.handleDragStart=function(e){n.emitter.trigger("dragstart",e),n.handleMove(e,!0)},this.handleDragMove=function(e){n.emitter.trigger("dragmove",e),n.handleMove(e)},this.handlePointerUp=function(e){n.releaseHits(),n.emitter.trigger("pointerup",e)},this.handleDragEnd=function(e){n.movingHit&&n.emitter.trigger("hitupdate",null,!0,e),n.finalHit=n.movingHit,n.movingHit=null,n.emitter.trigger("dragend",e)},this.droppableStore=t,e.emitter.on("pointerdown",this.handlePointerDown),e.emitter.on("dragstart",this.handleDragStart),e.emitter.on("dragmove",this.handleDragMove),e.emitter.on("pointerup",this.handlePointerUp),e.emitter.on("dragend",this.handleDragEnd),this.dragging=e,this.emitter=new r.m}return e.prototype.processFirstCoord=function(e){var t,n={left:e.pageX,top:e.pageY},o=n,i=e.subjectEl;i!==document&&(t=Object(r.cb)(i),o=Object(r.gb)(o,t));var a=this.initialHit=this.queryHitForOffset(o.left,o.top);if(a){if(this.useSubjectCenter&&t){var l=Object(r.bc)(t,a.rect);l&&(o=Object(r.Kb)(l))}this.coordAdjust=Object(r.qb)(o,n)}else this.coordAdjust={left:0,top:0}},e.prototype.handleMove=function(e,t){var n=this.queryHitForOffset(e.pageX+this.coordAdjust.left,e.pageY+this.coordAdjust.top);!t&&w(this.movingHit,n)||(this.movingHit=n,this.emitter.trigger("hitupdate",n,!1,e))},e.prototype.prepareHits=function(){this.offsetTrackers=Object(r.kc)(this.droppableStore,(function(e){return e.component.prepareHits(),new b(e.el)}))},e.prototype.releaseHits=function(){var e=this.offsetTrackers;for(var t in e)e[t].destroy();this.offsetTrackers={}},e.prototype.queryHitForOffset=function(e,t){var n=this.droppableStore,o=this.offsetTrackers,i=null;for(var a in n){var l=n[a].component,s=o[a];if(s&&s.isWithinClipping(e,t)){var u=s.computeLeft(),c=s.computeTop(),d=e-u,f=t-c,p=s.origRect,h=p.right-p.left,v=p.bottom-p.top;if(d>=0&&d<h&&f>=0&&f<v){var g=l.queryHit(d,f,h,v),m=l.context.getCurrentData().dateProfile;g&&Object(r.Bc)(m.activeRange,g.dateSpan.range)&&(!i||g.layer>i.layer)&&(g.rect.left+=u,g.rect.right+=u,g.rect.top+=c,g.rect.bottom+=c,i=g)}}}return i},e}();function w(e,t){return!e&&!t||Boolean(e)===Boolean(t)&&Object(r.ec)(e.dateSpan,t.dateSpan)}function C(e,t){for(var n,r,o={},i=0,l=t.pluginHooks.datePointTransforms;i<l.length;i++){var s=l[i];a(o,s(e,t))}return a(o,(n=e,{date:(r=t.dateEnv).toDate(n.range.start),dateStr:r.formatIso(n.range.start,{omitTime:n.allDay}),allDay:n.allDay})),o}var D=function(e){function t(t){var n=e.call(this,t)||this;n.handlePointerDown=function(e){var t=n.dragging,r=e.origEvent.target;t.setIgnoreMove(!n.component.isValidDateDownEl(r))},n.handleDragEnd=function(e){var t=n.component;if(!n.dragging.pointer.wasTouchScroll){var r=n.hitDragging,o=r.initialHit,i=r.finalHit;if(o&&i&&w(o,i)){var l=t.context,s=a(a({},C(o.dateSpan,l)),{dayEl:o.dayEl,jsEvent:e.origEvent,view:l.viewApi||l.calendarApi.view});l.emitter.trigger("dateClick",s)}}},n.dragging=new y(t.el),n.dragging.autoScroller.isEnabled=!1;var o=n.hitDragging=new E(n.dragging,Object(r.Zb)(t));return o.emitter.on("pointerdown",n.handlePointerDown),o.emitter.on("dragend",n.handleDragEnd),n}return i(t,e),t.prototype.destroy=function(){this.dragging.destroy()},t}(r.p),R=function(e){function t(t){var n=e.call(this,t)||this;n.dragSelection=null,n.handlePointerDown=function(e){var t=n,r=t.component,o=t.dragging,i=r.context.options.selectable&&r.isValidDateDownEl(e.origEvent.target);o.setIgnoreMove(!i),o.delay=e.isTouch?function(e){var t=e.context.options,n=t.selectLongPressDelay;null==n&&(n=t.longPressDelay);return n}(r):null},n.handleDragStart=function(e){n.component.context.calendarApi.unselect(e)},n.handleHitUpdate=function(e,t){var o=n.component.context,i=null,l=!1;e&&((i=function(e,t,n){var o=e.dateSpan,i=t.dateSpan,l=[o.range.start,o.range.end,i.range.start,i.range.end];l.sort(r.X);for(var s={},u=0,c=n;u<c.length;u++){var d=(0,c[u])(e,t);if(!1===d)return null;d&&a(s,d)}return s.range={start:l[0],end:l[3]},s.allDay=o.allDay,s}(n.hitDragging.initialHit,e,o.pluginHooks.dateSelectionTransformers))&&n.component.isDateSelectionValid(i)||(l=!0,i=null)),i?o.dispatch({type:"SELECT_DATES",selection:i}):t||o.dispatch({type:"UNSELECT_DATES"}),l?Object(r.sb)():Object(r.vb)(),t||(n.dragSelection=i)},n.handlePointerUp=function(e){n.dragSelection&&(Object(r.Rc)(n.dragSelection,e,n.component.context),n.dragSelection=null)};var o=t.component.context.options,i=n.dragging=new y(t.el);i.touchScrollAllowed=!1,i.minDistance=o.selectMinDistance||0,i.autoScroller.isEnabled=o.dragScroll;var l=n.hitDragging=new E(n.dragging,Object(r.Zb)(t));return l.emitter.on("pointerdown",n.handlePointerDown),l.emitter.on("dragstart",n.handleDragStart),l.emitter.on("hitupdate",n.handleHitUpdate),l.emitter.on("pointerup",n.handlePointerUp),n}return i(t,e),t.prototype.destroy=function(){this.dragging.destroy()},t}(r.p);var k=function(e){function t(n){var o=e.call(this,n)||this;o.subjectEl=null,o.subjectSeg=null,o.isDragging=!1,o.eventRange=null,o.relevantEvents=null,o.receivingContext=null,o.validMutation=null,o.mutatedRelevantEvents=null,o.handlePointerDown=function(e){var t=e.origEvent.target,n=o,i=n.component,a=n.dragging,l=a.mirror,s=i.context.options,u=i.context;o.subjectEl=e.subjectEl;var c=o.subjectSeg=Object(r.Ib)(e.subjectEl),d=(o.eventRange=c.eventRange).instance.instanceId;o.relevantEvents=Object(r.Lb)(u.getCurrentData().eventStore,d),a.minDistance=e.isTouch?0:s.eventDragMinDistance,a.delay=e.isTouch&&d!==i.props.eventSelection?function(e){var t=e.context.options,n=t.eventLongPressDelay;null==n&&(n=t.longPressDelay);return n}(i):null,s.fixedMirrorParent?l.parentNode=s.fixedMirrorParent:l.parentNode=Object(r.tb)(t,".fc"),l.revertDuration=s.dragRevertDuration;var f=i.isValidSegDownEl(t)&&!Object(r.tb)(t,".fc-event-resizer");a.setIgnoreMove(!f),o.isDragging=f&&e.subjectEl.classList.contains("fc-event-draggable")},o.handleDragStart=function(e){var t=o.component.context,n=o.eventRange,i=n.instance.instanceId;e.isTouch?i!==o.component.props.eventSelection&&t.dispatch({type:"SELECT_EVENT",eventInstanceId:i}):t.dispatch({type:"UNSELECT_EVENT"}),o.isDragging&&(t.calendarApi.unselect(e),t.emitter.trigger("eventDragStart",{el:o.subjectEl,event:new r.n(t,n.def,n.instance),jsEvent:e.origEvent,view:t.viewApi}))},o.handleHitUpdate=function(e,t){if(o.isDragging){var n=o.relevantEvents,i=o.hitDragging.initialHit,a=o.component.context,l=null,s=null,u=null,c=!1,d={affectedEvents:n,mutatedEvents:Object(r.jb)(),isEvent:!0};if(e){var f=e.component,p=(l=f.context).options;a===l||p.editable&&p.droppable?(s=function(e,t,n){var o=e.dateSpan,i=t.dateSpan,a=o.range.start,l=i.range.start,s={};o.allDay!==i.allDay&&(s.allDay=i.allDay,s.hasEnd=t.component.context.options.allDayMaintainDuration,i.allDay&&(a=Object(r.Pc)(a)));var u=Object(r.pb)(a,l,e.component.context.dateEnv,e.component===t.component?e.component.largeUnit:null);u.milliseconds&&(s.allDay=!1);for(var c={datesDelta:u,standardProps:s},d=0,f=n;d<f.length;d++){(0,f[d])(c,e,t)}return c}(i,e,l.getCurrentData().pluginHooks.eventDragMutationMassagers))&&(u=Object(r.K)(n,l.getCurrentData().eventUiBases,s,l),d.mutatedEvents=u,f.isInteractionValid(d)||(c=!0,s=null,u=null,d.mutatedEvents=Object(r.jb)())):l=null}o.displayDrag(l,d),c?Object(r.sb)():Object(r.vb)(),t||(a===l&&w(i,e)&&(s=null),o.dragging.setMirrorNeedsRevert(!s),o.dragging.setMirrorIsVisible(!e||!document.querySelector(".fc-event-mirror")),o.receivingContext=l,o.validMutation=s,o.mutatedRelevantEvents=u)}},o.handlePointerUp=function(){o.isDragging||o.cleanup()},o.handleDragEnd=function(e){if(o.isDragging){var t=o.component.context,n=t.viewApi,i=o,l=i.receivingContext,s=i.validMutation,u=o.eventRange.def,c=o.eventRange.instance,d=new r.n(t,u,c),f=o.relevantEvents,p=o.mutatedRelevantEvents,h=o.hitDragging.finalHit;if(o.clearDrag(),t.emitter.trigger("eventDragStop",{el:o.subjectEl,event:d,jsEvent:e.origEvent,view:n}),s){if(l===t){var v=new r.n(t,p.defs[u.defId],c?p.instances[c.instanceId]:null);t.dispatch({type:"MERGE_EVENTS",eventStore:p});for(var g={oldEvent:d,event:v,relatedEvents:Object(r.R)(p,t,c),revert:function(){t.dispatch({type:"MERGE_EVENTS",eventStore:f})}},m={},y=0,b=t.getCurrentData().pluginHooks.eventDropTransformers;y<b.length;y++){var S=b[y];a(m,S(s,t))}t.emitter.trigger("eventDrop",a(a(a({},g),m),{el:e.subjectEl,delta:s.datesDelta,jsEvent:e.origEvent,view:n})),t.emitter.trigger("eventChange",g)}else if(l){var E={event:d,relatedEvents:Object(r.R)(f,t,c),revert:function(){t.dispatch({type:"MERGE_EVENTS",eventStore:f})}};t.emitter.trigger("eventLeave",a(a({},E),{draggedEl:e.subjectEl,view:n})),t.dispatch({type:"REMOVE_EVENTS",eventStore:f}),t.emitter.trigger("eventRemove",E);var w=p.defs[u.defId],D=p.instances[c.instanceId],R=new r.n(l,w,D);l.dispatch({type:"MERGE_EVENTS",eventStore:p});var k={event:R,relatedEvents:Object(r.R)(p,l,D),revert:function(){l.dispatch({type:"REMOVE_EVENTS",eventStore:p})}};l.emitter.trigger("eventAdd",k),e.isTouch&&l.dispatch({type:"SELECT_EVENT",eventInstanceId:c.instanceId}),l.emitter.trigger("drop",a(a({},C(h.dateSpan,l)),{draggedEl:e.subjectEl,jsEvent:e.origEvent,view:h.component.context.viewApi})),l.emitter.trigger("eventReceive",a(a({},k),{draggedEl:e.subjectEl,view:h.component.context.viewApi}))}}else t.emitter.trigger("_noEventDrop")}o.cleanup()};var i=o.component.context.options,l=o.dragging=new y(n.el);l.pointer.selector=t.SELECTOR,l.touchScrollAllowed=!1,l.autoScroller.isEnabled=i.dragScroll;var s=o.hitDragging=new E(o.dragging,r.Yb);return s.useSubjectCenter=n.useEventCenter,s.emitter.on("pointerdown",o.handlePointerDown),s.emitter.on("dragstart",o.handleDragStart),s.emitter.on("hitupdate",o.handleHitUpdate),s.emitter.on("pointerup",o.handlePointerUp),s.emitter.on("dragend",o.handleDragEnd),o}return i(t,e),t.prototype.destroy=function(){this.dragging.destroy()},t.prototype.displayDrag=function(e,t){var n=this.component.context,o=this.receivingContext;o&&o!==e&&(o===n?o.dispatch({type:"SET_EVENT_DRAG",state:{affectedEvents:t.affectedEvents,mutatedEvents:Object(r.jb)(),isEvent:!0}}):o.dispatch({type:"UNSET_EVENT_DRAG"})),e&&e.dispatch({type:"SET_EVENT_DRAG",state:t})},t.prototype.clearDrag=function(){var e=this.component.context,t=this.receivingContext;t&&t.dispatch({type:"UNSET_EVENT_DRAG"}),e!==t&&e.dispatch({type:"UNSET_EVENT_DRAG"})},t.prototype.cleanup=function(){this.subjectSeg=null,this.isDragging=!1,this.eventRange=null,this.relevantEvents=null,this.receivingContext=null,this.validMutation=null,this.mutatedRelevantEvents=null},t.SELECTOR=".fc-event-draggable, .fc-event-resizable",t}(r.p);var O=function(e){function t(t){var n=e.call(this,t)||this;n.draggingSegEl=null,n.draggingSeg=null,n.eventRange=null,n.relevantEvents=null,n.validMutation=null,n.mutatedRelevantEvents=null,n.handlePointerDown=function(e){var t=n.component,o=n.querySegEl(e),i=Object(r.Ib)(o),a=n.eventRange=i.eventRange;n.dragging.minDistance=t.context.options.eventDragMinDistance,n.dragging.setIgnoreMove(!n.component.isValidSegDownEl(e.origEvent.target)||e.isTouch&&n.component.props.eventSelection!==a.instance.instanceId)},n.handleDragStart=function(e){var t=n.component.context,o=n.eventRange;n.relevantEvents=Object(r.Lb)(t.getCurrentData().eventStore,n.eventRange.instance.instanceId);var i=n.querySegEl(e);n.draggingSegEl=i,n.draggingSeg=Object(r.Ib)(i),t.calendarApi.unselect(),t.emitter.trigger("eventResizeStart",{el:i,event:new r.n(t,o.def,o.instance),jsEvent:e.origEvent,view:t.viewApi})},n.handleHitUpdate=function(e,t,o){var i=n.component.context,l=n.relevantEvents,s=n.hitDragging.initialHit,u=n.eventRange.instance,c=null,d=null,f=!1,p={affectedEvents:l,mutatedEvents:Object(r.jb)(),isEvent:!0};e&&(c=function(e,t,n,o,i){for(var l=e.component.context.dateEnv,s=e.dateSpan.range.start,u=t.dateSpan.range.start,c=Object(r.pb)(s,u,l,e.component.largeUnit),d={},f=0,p=i;f<p.length;f++){var h=(0,p[f])(e,t);if(!1===h)return null;h&&a(d,h)}if(n){if(l.add(o.start,c)<o.end)return d.startDelta=c,d}else if(l.add(o.end,c)>o.start)return d.endDelta=c,d;return null}(s,e,o.subjectEl.classList.contains("fc-event-resizer-start"),u.range,i.pluginHooks.eventResizeJoinTransforms)),c&&(d=Object(r.K)(l,i.getCurrentData().eventUiBases,c,i),p.mutatedEvents=d,n.component.isInteractionValid(p)||(f=!0,c=null,d=null,p.mutatedEvents=null)),d?i.dispatch({type:"SET_EVENT_RESIZE",state:p}):i.dispatch({type:"UNSET_EVENT_RESIZE"}),f?Object(r.sb)():Object(r.vb)(),t||(c&&w(s,e)&&(c=null),n.validMutation=c,n.mutatedRelevantEvents=d)},n.handleDragEnd=function(e){var t=n.component.context,o=n.eventRange.def,i=n.eventRange.instance,l=new r.n(t,o,i),s=n.relevantEvents,u=n.mutatedRelevantEvents;if(t.emitter.trigger("eventResizeStop",{el:n.draggingSegEl,event:l,jsEvent:e.origEvent,view:t.viewApi}),n.validMutation){var c=new r.n(t,u.defs[o.defId],i?u.instances[i.instanceId]:null);t.dispatch({type:"MERGE_EVENTS",eventStore:u});var d={oldEvent:l,event:c,relatedEvents:Object(r.R)(u,t,i),revert:function(){t.dispatch({type:"MERGE_EVENTS",eventStore:s})}};t.emitter.trigger("eventResize",a(a({},d),{el:n.draggingSegEl,startDelta:n.validMutation.startDelta||Object(r.hb)(0),endDelta:n.validMutation.endDelta||Object(r.hb)(0),jsEvent:e.origEvent,view:t.viewApi})),t.emitter.trigger("eventChange",d)}else t.emitter.trigger("_noEventResize");n.draggingSeg=null,n.relevantEvents=null,n.validMutation=null};var o=t.component,i=n.dragging=new y(t.el);i.pointer.selector=".fc-event-resizer",i.touchScrollAllowed=!1,i.autoScroller.isEnabled=o.context.options.dragScroll;var l=n.hitDragging=new E(n.dragging,Object(r.Zb)(t));return l.emitter.on("pointerdown",n.handlePointerDown),l.emitter.on("dragstart",n.handleDragStart),l.emitter.on("hitupdate",n.handleHitUpdate),l.emitter.on("dragend",n.handleDragEnd),n}return i(t,e),t.prototype.destroy=function(){this.dragging.destroy()},t.prototype.querySegEl=function(e){return Object(r.tb)(e.subjectEl,".fc-event")},t}(r.p);var T=function(){function e(e){var t=this;this.context=e,this.isRecentPointerDateSelect=!1,this.matchesCancel=!1,this.matchesEvent=!1,this.onSelect=function(e){e.jsEvent&&(t.isRecentPointerDateSelect=!0)},this.onDocumentPointerDown=function(e){var n=t.context.options.unselectCancel,o=e.origEvent.target;t.matchesCancel=!!Object(r.tb)(o,n),t.matchesEvent=!!Object(r.tb)(o,k.SELECTOR)},this.onDocumentPointerUp=function(e){var n=t.context,r=t.documentPointer,o=n.getCurrentData();if(!r.wasTouchScroll){if(o.dateSelection&&!t.isRecentPointerDateSelect){var i=n.options.unselectAuto;!i||i&&t.matchesCancel||n.calendarApi.unselect(e)}o.eventSelection&&!t.matchesEvent&&n.dispatch({type:"UNSELECT_EVENT"})}t.isRecentPointerDateSelect=!1};var n=this.documentPointer=new c(document);n.shouldIgnoreMove=!0,n.shouldWatchScroll=!1,n.emitter.on("pointerdown",this.onDocumentPointerDown),n.emitter.on("pointerup",this.onDocumentPointerUp),e.emitter.on("select",this.onSelect)}return e.prototype.destroy=function(){this.context.emitter.off("select",this.onSelect),this.documentPointer.destroy()},e}(),x={fixedMirrorParent:r.Xb},M={dateClick:r.Xb,eventDragStart:r.Xb,eventDragStop:r.Xb,eventDrop:r.Xb,eventResizeStart:r.Xb,eventResizeStop:r.Xb,eventResize:r.Xb,drop:r.Xb,eventReceive:r.Xb,eventLeave:r.Xb},P=function(){function e(e,t){var n=this;this.receivingContext=null,this.droppableEvent=null,this.suppliedDragMeta=null,this.dragMeta=null,this.handleDragStart=function(e){n.dragMeta=n.buildDragMeta(e.subjectEl)},this.handleHitUpdate=function(e,t,o){var i=n.hitDragging.dragging,l=null,s=null,u=!1,c={affectedEvents:Object(r.jb)(),mutatedEvents:Object(r.jb)(),isEvent:n.dragMeta.create};e&&(l=e.component.context,n.canDropElOnCalendar(o.subjectEl,l)&&(s=function(e,t,n){for(var o=a({},t.leftoverProps),i=0,l=n.pluginHooks.externalDefTransforms;i<l.length;i++){var s=l[i];a(o,s(e,t))}var u=Object(r.Ec)(o,n),c=u.refined,d=u.extra,f=Object(r.vc)(c,d,t.sourceId,e.allDay,n.options.forceEventDuration||Boolean(t.duration),n),p=e.range.start;e.allDay&&t.startTime&&(p=n.dateEnv.add(p,t.startTime));var h=t.duration?n.dateEnv.add(p,t.duration):Object(r.Hb)(e.allDay,p,n),v=Object(r.kb)(f.defId,{start:p,end:h});return{def:f,instance:v}}(e.dateSpan,n.dragMeta,l),c.mutatedEvents=Object(r.wb)(s),(u=!Object(r.gc)(c,l))&&(c.mutatedEvents=Object(r.jb)(),s=null))),n.displayDrag(l,c),i.setMirrorIsVisible(t||!s||!document.querySelector(".fc-event-mirror")),u?Object(r.sb)():Object(r.vb)(),t||(i.setMirrorNeedsRevert(!s),n.receivingContext=l,n.droppableEvent=s)},this.handleDragEnd=function(e){var t=n,o=t.receivingContext,i=t.droppableEvent;if(n.clearDrag(),o&&i){var l=n.hitDragging.finalHit,s=l.component.context.viewApi,u=n.dragMeta;if(o.emitter.trigger("drop",a(a({},C(l.dateSpan,o)),{draggedEl:e.subjectEl,jsEvent:e.origEvent,view:s})),u.create){var c=Object(r.wb)(i);o.dispatch({type:"MERGE_EVENTS",eventStore:c}),e.isTouch&&o.dispatch({type:"SELECT_EVENT",eventInstanceId:i.instance.instanceId}),o.emitter.trigger("eventReceive",{event:new r.n(o,i.def,i.instance),relatedEvents:[],revert:function(){o.dispatch({type:"REMOVE_EVENTS",eventStore:c})},draggedEl:e.subjectEl,view:s})}}n.receivingContext=null,n.droppableEvent=null};var o=this.hitDragging=new E(e,r.Yb);o.requireInitial=!1,o.emitter.on("dragstart",this.handleDragStart),o.emitter.on("hitupdate",this.handleHitUpdate),o.emitter.on("dragend",this.handleDragEnd),this.suppliedDragMeta=t}return e.prototype.buildDragMeta=function(e){return"object"===typeof this.suppliedDragMeta?Object(r.uc)(this.suppliedDragMeta):"function"===typeof this.suppliedDragMeta?Object(r.uc)(this.suppliedDragMeta(e)):function(e){var t=function(e,t){var n=r.fb.dataAttrPrefix,o=(n?n+"-":"")+t;return e.getAttribute("data-"+o)||""}(e,"event"),n=t?JSON.parse(t):{create:!1};return Object(r.uc)(n)}(e)},e.prototype.displayDrag=function(e,t){var n=this.receivingContext;n&&n!==e&&n.dispatch({type:"UNSET_EVENT_DRAG"}),e&&e.dispatch({type:"SET_EVENT_DRAG",state:t})},e.prototype.clearDrag=function(){this.receivingContext&&this.receivingContext.dispatch({type:"UNSET_EVENT_DRAG"})},e.prototype.canDropElOnCalendar=function(e,t){var n=t.options.dropAccept;return"function"===typeof n?n.call(t.calendarApi,e):"string"!==typeof n||!n||Boolean(Object(r.ub)(e,n))},e}();r.fb.dataAttrPrefix="";!function(){function e(e,t){var n=this;void 0===t&&(t={}),this.handlePointerDown=function(e){var t=n.dragging,o=n.settings,i=o.minDistance,a=o.longPressDelay;t.minDistance=null!=i?i:e.isTouch?0:r.a.eventDragMinDistance,t.delay=e.isTouch?null!=a?a:r.a.longPressDelay:0},this.handleDragStart=function(e){e.isTouch&&n.dragging.delay&&e.subjectEl.classList.contains("fc-event")&&n.dragging.mirror.getMirrorEl().classList.add("fc-event-selected")},this.settings=t;var o=this.dragging=new y(e);o.touchScrollAllowed=!1,null!=t.itemSelector&&(o.pointer.selector=t.itemSelector),null!=t.appendTo&&(o.mirror.parentNode=t.appendTo),o.emitter.on("pointerdown",this.handlePointerDown),o.emitter.on("dragstart",this.handleDragStart),new P(o,t.eventData)}e.prototype.destroy=function(){this.dragging.destroy()}}();var I=function(e){function t(t){var n=e.call(this,t)||this;n.shouldIgnoreMove=!1,n.mirrorSelector="",n.currentMirrorEl=null,n.handlePointerDown=function(e){n.emitter.trigger("pointerdown",e),n.shouldIgnoreMove||n.emitter.trigger("dragstart",e)},n.handlePointerMove=function(e){n.shouldIgnoreMove||n.emitter.trigger("dragmove",e)},n.handlePointerUp=function(e){n.emitter.trigger("pointerup",e),n.shouldIgnoreMove||n.emitter.trigger("dragend",e)};var r=n.pointer=new c(t);return r.emitter.on("pointerdown",n.handlePointerDown),r.emitter.on("pointermove",n.handlePointerMove),r.emitter.on("pointerup",n.handlePointerUp),n}return i(t,e),t.prototype.destroy=function(){this.pointer.destroy()},t.prototype.setIgnoreMove=function(e){this.shouldIgnoreMove=e},t.prototype.setMirrorIsVisible=function(e){if(e)this.currentMirrorEl&&(this.currentMirrorEl.style.visibility="",this.currentMirrorEl=null);else{var t=this.mirrorSelector?document.querySelector(this.mirrorSelector):null;t&&(this.currentMirrorEl=t,t.style.visibility="hidden")}},t}(r.k),j=(function(){function e(e,t){var n=document;e===document||e instanceof Element?(n=e,t=t||{}):t=e||{};var r=this.dragging=new I(n);"string"===typeof t.itemSelector?r.pointer.selector=t.itemSelector:n===document&&(r.pointer.selector="[data-event]"),"string"===typeof t.mirrorSelector&&(r.mirrorSelector=t.mirrorSelector),new P(r,t.eventData)}e.prototype.destroy=function(){this.dragging.destroy()}}(),Object(r.nb)({componentInteractions:[D,R,k,O],calendarInteractions:[T],elementDraggingImpl:y,optionRefiners:x,listenerRefiners:M}));t.a=j},function(e,t,n){"use strict";n(4);var r=n(1),o=60103;if(t.Fragment=60107,"function"===typeof Symbol&&Symbol.for){var i=Symbol.for;o=i("react.element"),t.Fragment=i("react.fragment")}var a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l=Object.prototype.hasOwnProperty,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,i={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)l.call(t,r)&&!s.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:o,type:e,key:u,ref:c,props:i,_owner:a.current}}t.jsx=u,t.jsxs=u},function(e,t,n){"use strict";var r=n(4),o=60103,i=60106;t.Fragment=60107,t.StrictMode=60108,t.Profiler=60114;var a=60109,l=60110,s=60112;t.Suspense=60113;var u=60115,c=60116;if("function"===typeof Symbol&&Symbol.for){var d=Symbol.for;o=d("react.element"),i=d("react.portal"),t.Fragment=d("react.fragment"),t.StrictMode=d("react.strict_mode"),t.Profiler=d("react.profiler"),a=d("react.provider"),l=d("react.context"),s=d("react.forward_ref"),t.Suspense=d("react.suspense"),u=d("react.memo"),c=d("react.lazy")}var f="function"===typeof Symbol&&Symbol.iterator;function p(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},v={};function g(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}function m(){}function y(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}g.prototype.isReactComponent={},g.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error(p(85));this.updater.enqueueSetState(this,e,t,"setState")},g.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},m.prototype=g.prototype;var b=y.prototype=new m;b.constructor=y,r(b,g.prototype),b.isPureReactComponent=!0;var S={current:null},E=Object.prototype.hasOwnProperty,w={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,n){var r,i={},a=null,l=null;if(null!=t)for(r in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(a=""+t.key),t)E.call(t,r)&&!w.hasOwnProperty(r)&&(i[r]=t[r]);var s=arguments.length-2;if(1===s)i.children=n;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];i.children=u}if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===i[r]&&(i[r]=s[r]);return{$$typeof:o,type:e,key:a,ref:l,props:i,_owner:S.current}}function D(e){return"object"===typeof e&&null!==e&&e.$$typeof===o}var R=/\/+/g;function k(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function O(e,t,n,r,a){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case o:case i:s=!0}}if(s)return a=a(s=e),e=""===r?"."+k(s,0):r,Array.isArray(a)?(n="",null!=e&&(n=e.replace(R,"$&/")+"/"),O(a,t,n,"",(function(e){return e}))):null!=a&&(D(a)&&(a=function(e,t){return{$$typeof:o,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,n+(!a.key||s&&s.key===a.key?"":(""+a.key).replace(R,"$&/")+"/")+e)),t.push(a)),1;if(s=0,r=""===r?".":r+":",Array.isArray(e))for(var u=0;u<e.length;u++){var c=r+k(l=e[u],u);s+=O(l,t,n,c,a)}else if("function"===typeof(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=f&&e[f]||e["@@iterator"])?e:null}(e)))for(e=c.call(e),u=0;!(l=e.next()).done;)s+=O(l=l.value,t,n,c=r+k(l,u++),a);else if("object"===l)throw t=""+e,Error(p(31,"[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t));return s}function T(e,t,n){if(null==e)return e;var r=[],o=0;return O(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function x(e){if(-1===e._status){var t=e._result;t=t(),e._status=0,e._result=t,t.then((function(t){0===e._status&&(t=t.default,e._status=1,e._result=t)}),(function(t){0===e._status&&(e._status=2,e._result=t)}))}if(1===e._status)return e._result;throw e._result}var M={current:null};function P(){var e=M.current;if(null===e)throw Error(p(321));return e}var I={ReactCurrentDispatcher:M,ReactCurrentBatchConfig:{transition:0},ReactCurrentOwner:S,IsSomeRendererActing:{current:!1},assign:r};t.Children={map:T,forEach:function(e,t,n){T(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return T(e,(function(){t++})),t},toArray:function(e){return T(e,(function(e){return e}))||[]},only:function(e){if(!D(e))throw Error(p(143));return e}},t.Component=g,t.PureComponent=y,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=I,t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error(p(267,e));var i=r({},e.props),a=e.key,l=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,s=S.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(c in t)E.call(t,c)&&!w.hasOwnProperty(c)&&(i[c]=void 0===t[c]&&void 0!==u?u[c]:t[c])}var c=arguments.length-2;if(1===c)i.children=n;else if(1<c){u=Array(c);for(var d=0;d<c;d++)u[d]=arguments[d+2];i.children=u}return{$$typeof:o,type:e.type,key:a,ref:l,props:i,_owner:s}},t.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:l,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:a,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:s,render:e}},t.isValidElement=D,t.lazy=function(e){return{$$typeof:c,_payload:{_status:-1,_result:e},_init:x}},t.memo=function(e,t){return{$$typeof:u,type:e,compare:void 0===t?null:t}},t.useCallback=function(e,t){return P().useCallback(e,t)},t.useContext=function(e,t){return P().useContext(e,t)},t.useDebugValue=function(){},t.useEffect=function(e,t){return P().useEffect(e,t)},t.useImperativeHandle=function(e,t,n){return P().useImperativeHandle(e,t,n)},t.useLayoutEffect=function(e,t){return P().useLayoutEffect(e,t)},t.useMemo=function(e,t){return P().useMemo(e,t)},t.useReducer=function(e,t,n){return P().useReducer(e,t,n)},t.useRef=function(e){return P().useRef(e)},t.useState=function(e){return P().useState(e)},t.version="17.0.1"},function(e,t,n){"use strict";var r=n(1),o=n(4),i=n(11);function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}if(!r)throw Error(a(227));var l=new Set,s={};function u(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(s[e]=t,e=0;e<t.length;e++)l.add(t[e])}var d=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p=Object.prototype.hasOwnProperty,h={},v={};function g(e,t,n,r,o,i,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var m={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){m[e]=new g(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];m[t]=new g(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){m[e]=new g(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){m[e]=new g(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){m[e]=new g(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){m[e]=new g(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){m[e]=new g(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){m[e]=new g(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){m[e]=new g(e,5,!1,e.toLowerCase(),null,!1,!1)}));var y=/[\-:]([a-z])/g;function b(e){return e[1].toUpperCase()}function S(e,t,n,r){var o=m.hasOwnProperty(t)?m[t]:null;(null!==o?0===o.type:!r&&(2<t.length&&("o"===t[0]||"O"===t[0])&&("n"===t[1]||"N"===t[1])))||(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,o,r)&&(n=null),r||null===o?function(e){return!!p.call(v,e)||!p.call(h,e)&&(f.test(e)?v[e]=!0:(h[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=null===n?3!==o.type&&"":n:(t=o.attributeName,r=o.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(o=o.type)||4===o&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(y,b);m[t]=new g(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(y,b);m[t]=new g(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(y,b);m[t]=new g(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){m[e]=new g(e,1,!1,e.toLowerCase(),null,!1,!1)})),m.xlinkHref=new g("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){m[e]=new g(e,1,!1,e.toLowerCase(),null,!0,!0)}));var E=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=60103,C=60106,D=60107,R=60108,k=60114,O=60109,T=60110,x=60112,M=60113,P=60120,I=60115,j=60116,N=60121,_=60128,H=60129,L=60130,A=60131;if("function"===typeof Symbol&&Symbol.for){var z=Symbol.for;w=z("react.element"),C=z("react.portal"),D=z("react.fragment"),R=z("react.strict_mode"),k=z("react.profiler"),O=z("react.provider"),T=z("react.context"),x=z("react.forward_ref"),M=z("react.suspense"),P=z("react.suspense_list"),I=z("react.memo"),j=z("react.lazy"),N=z("react.block"),z("react.scope"),_=z("react.opaque.id"),H=z("react.debug_trace_mode"),L=z("react.offscreen"),A=z("react.legacy_hidden")}var U,W="function"===typeof Symbol&&Symbol.iterator;function B(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=W&&e[W]||e["@@iterator"])?e:null}function F(e){if(void 0===U)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);U=t&&t[1]||""}return"\n"+U+e}var V=!1;function q(e,t){if(!e||V)return"";V=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(s){var r=s}Reflect.construct(e,[],t)}else{try{t.call()}catch(s){r=s}e.call(t.prototype)}else{try{throw Error()}catch(s){r=s}e()}}catch(s){if(s&&r&&"string"===typeof s.stack){for(var o=s.stack.split("\n"),i=r.stack.split("\n"),a=o.length-1,l=i.length-1;1<=a&&0<=l&&o[a]!==i[l];)l--;for(;1<=a&&0<=l;a--,l--)if(o[a]!==i[l]){if(1!==a||1!==l)do{if(a--,0>--l||o[a]!==i[l])return"\n"+o[a].replace(" at new "," at ")}while(1<=a&&0<=l);break}}}finally{V=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?F(e):""}function G(e){switch(e.tag){case 5:return F(e.type);case 16:return F("Lazy");case 13:return F("Suspense");case 19:return F("SuspenseList");case 0:case 2:case 15:return e=q(e.type,!1);case 11:return e=q(e.type.render,!1);case 22:return e=q(e.type._render,!1);case 1:return e=q(e.type,!0);default:return""}}function Y(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case D:return"Fragment";case C:return"Portal";case k:return"Profiler";case R:return"StrictMode";case M:return"Suspense";case P:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case T:return(e.displayName||"Context")+".Consumer";case O:return(e._context.displayName||"Context")+".Provider";case x:var t=e.render;return t=t.displayName||t.name||"",e.displayName||(""!==t?"ForwardRef("+t+")":"ForwardRef");case I:return Y(e.type);case N:return Y(e._render);case j:t=e._payload,e=e._init;try{return Y(e(t))}catch(n){}}return null}function X(e){switch(typeof e){case"boolean":case"number":case"object":case"string":case"undefined":return e;default:return""}}function Z(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function $(e){e._valueTracker||(e._valueTracker=function(e){var t=Z(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Z(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function J(e,t){var n=t.checked;return o({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function ee(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=X(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function te(e,t){null!=(t=t.checked)&&S(e,"checked",t,!1)}function ne(e,t){te(e,t);var n=X(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?oe(e,t.type,n):t.hasOwnProperty("defaultValue")&&oe(e,t.type,X(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function re(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function oe(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}function ie(e,t){return e=o({children:void 0},t),(t=function(e){var t="";return r.Children.forEach(e,(function(e){null!=e&&(t+=e)})),t}(t.children))&&(e.children=t),e}function ae(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+X(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function le(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(a(91));return o({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function se(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(a(92));if(Array.isArray(n)){if(!(1>=n.length))throw Error(a(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:X(n)}}function ue(e,t){var n=X(t.value),r=X(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ce(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}var de="http://www.w3.org/1999/xhtml",fe="http://www.w3.org/2000/svg";function pe(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function he(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?pe(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ve,ge,me=(ge=function(e,t){if(e.namespaceURI!==fe||"innerHTML"in e)e.innerHTML=t;else{for((ve=ve||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ve.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ge(e,t)}))}:ge);function ye(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var be={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Se=["Webkit","ms","Moz","O"];function Ee(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||be.hasOwnProperty(e)&&be[e]?(""+t).trim():t+"px"}function we(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=Ee(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}Object.keys(be).forEach((function(e){Se.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),be[t]=be[e]}))}));var Ce=o({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function De(e,t){if(t){if(Ce[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(a(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(a(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(a(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(a(62))}}function Re(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}function ke(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Oe=null,Te=null,xe=null;function Me(e){if(e=eo(e)){if("function"!==typeof Oe)throw Error(a(280));var t=e.stateNode;t&&(t=no(t),Oe(e.stateNode,e.type,t))}}function Pe(e){Te?xe?xe.push(e):xe=[e]:Te=e}function Ie(){if(Te){var e=Te,t=xe;if(xe=Te=null,Me(e),t)for(e=0;e<t.length;e++)Me(t[e])}}function je(e,t){return e(t)}function Ne(e,t,n,r,o){return e(t,n,r,o)}function _e(){}var He=je,Le=!1,Ae=!1;function ze(){null===Te&&null===xe||(_e(),Ie())}function Ue(e,t){var n=e.stateNode;if(null===n)return null;var r=no(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(a(231,t,typeof n));return n}var We=!1;if(d)try{var Be={};Object.defineProperty(Be,"passive",{get:function(){We=!0}}),window.addEventListener("test",Be,Be),window.removeEventListener("test",Be,Be)}catch(ge){We=!1}function Fe(e,t,n,r,o,i,a,l,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Ve=!1,qe=null,Ge=!1,Ye=null,Xe={onError:function(e){Ve=!0,qe=e}};function Ze(e,t,n,r,o,i,a,l,s){Ve=!1,qe=null,Fe.apply(Xe,arguments)}function $e(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(1026&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Qe(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ke(e){if($e(e)!==e)throw Error(a(188))}function Je(e){if(!(e=function(e){var t=e.alternate;if(!t){if(null===(t=$e(e)))throw Error(a(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var i=o.alternate;if(null===i){if(null!==(r=o.return)){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Ke(o),e;if(i===r)return Ke(o),t;i=i.sibling}throw Error(a(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(a(189))}}if(n.alternate!==r)throw Error(a(190))}if(3!==n.tag)throw Error(a(188));return n.stateNode.current===n?e:t}(e)))return null;for(var t=e;;){if(5===t.tag||6===t.tag)return t;if(t.child)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null}function et(e,t){for(var n=e.alternate;null!==t;){if(t===e||t===n)return!0;t=t.return}return!1}var tt,nt,rt,ot,it=!1,at=[],lt=null,st=null,ut=null,ct=new Map,dt=new Map,ft=[],pt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ht(e,t,n,r,o){return{blockedOn:e,domEventName:t,eventSystemFlags:16|n,nativeEvent:o,targetContainers:[r]}}function vt(e,t){switch(e){case"focusin":case"focusout":lt=null;break;case"dragenter":case"dragleave":st=null;break;case"mouseover":case"mouseout":ut=null;break;case"pointerover":case"pointerout":ct.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":dt.delete(t.pointerId)}}function gt(e,t,n,r,o,i){return null===e||e.nativeEvent!==i?(e=ht(t,n,r,o,i),null!==t&&(null!==(t=eo(t))&&nt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function mt(e){var t=Jr(e.target);if(null!==t){var n=$e(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Qe(n)))return e.blockedOn=t,void ot(e.lanePriority,(function(){i.unstable_runWithPriority(e.priority,(function(){rt(n)}))}))}else if(3===t&&n.stateNode.hydrate)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function yt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Jt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=eo(n))&&nt(t),e.blockedOn=n,!1;t.shift()}return!0}function bt(e,t,n){yt(e)&&n.delete(t)}function St(){for(it=!1;0<at.length;){var e=at[0];if(null!==e.blockedOn){null!==(e=eo(e.blockedOn))&&tt(e);break}for(var t=e.targetContainers;0<t.length;){var n=Jt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n){e.blockedOn=n;break}t.shift()}null===e.blockedOn&&at.shift()}null!==lt&&yt(lt)&&(lt=null),null!==st&&yt(st)&&(st=null),null!==ut&&yt(ut)&&(ut=null),ct.forEach(bt),dt.forEach(bt)}function Et(e,t){e.blockedOn===t&&(e.blockedOn=null,it||(it=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,St)))}function wt(e){function t(t){return Et(t,e)}if(0<at.length){Et(at[0],e);for(var n=1;n<at.length;n++){var r=at[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==lt&&Et(lt,e),null!==st&&Et(st,e),null!==ut&&Et(ut,e),ct.forEach(t),dt.forEach(t),n=0;n<ft.length;n++)(r=ft[n]).blockedOn===e&&(r.blockedOn=null);for(;0<ft.length&&null===(n=ft[0]).blockedOn;)mt(n),null===n.blockedOn&&ft.shift()}function Ct(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Dt={animationend:Ct("Animation","AnimationEnd"),animationiteration:Ct("Animation","AnimationIteration"),animationstart:Ct("Animation","AnimationStart"),transitionend:Ct("Transition","TransitionEnd")},Rt={},kt={};function Ot(e){if(Rt[e])return Rt[e];if(!Dt[e])return e;var t,n=Dt[e];for(t in n)if(n.hasOwnProperty(t)&&t in kt)return Rt[e]=n[t];return e}d&&(kt=document.createElement("div").style,"AnimationEvent"in window||(delete Dt.animationend.animation,delete Dt.animationiteration.animation,delete Dt.animationstart.animation),"TransitionEvent"in window||delete Dt.transitionend.transition);var Tt=Ot("animationend"),xt=Ot("animationiteration"),Mt=Ot("animationstart"),Pt=Ot("transitionend"),It=new Map,jt=new Map,Nt=["abort","abort",Tt,"animationEnd",xt,"animationIteration",Mt,"animationStart","canplay","canPlay","canplaythrough","canPlayThrough","durationchange","durationChange","emptied","emptied","encrypted","encrypted","ended","ended","error","error","gotpointercapture","gotPointerCapture","load","load","loadeddata","loadedData","loadedmetadata","loadedMetadata","loadstart","loadStart","lostpointercapture","lostPointerCapture","playing","playing","progress","progress","seeking","seeking","stalled","stalled","suspend","suspend","timeupdate","timeUpdate",Pt,"transitionEnd","waiting","waiting"];function _t(e,t){for(var n=0;n<e.length;n+=2){var r=e[n],o=e[n+1];o="on"+(o[0].toUpperCase()+o.slice(1)),jt.set(r,t),It.set(r,o),u(o,[r])}}(0,i.unstable_now)();var Ht=8;function Lt(e){if(0!==(1&e))return Ht=15,1;if(0!==(2&e))return Ht=14,2;if(0!==(4&e))return Ht=13,4;var t=24&e;return 0!==t?(Ht=12,t):0!==(32&e)?(Ht=11,32):0!==(t=192&e)?(Ht=10,t):0!==(256&e)?(Ht=9,256):0!==(t=3584&e)?(Ht=8,t):0!==(4096&e)?(Ht=7,4096):0!==(t=4186112&e)?(Ht=6,t):0!==(t=62914560&e)?(Ht=5,t):67108864&e?(Ht=4,67108864):0!==(134217728&e)?(Ht=3,134217728):0!==(t=805306368&e)?(Ht=2,t):0!==(1073741824&e)?(Ht=1,1073741824):(Ht=8,e)}function At(e,t){var n=e.pendingLanes;if(0===n)return Ht=0;var r=0,o=0,i=e.expiredLanes,a=e.suspendedLanes,l=e.pingedLanes;if(0!==i)r=i,o=Ht=15;else if(0!==(i=134217727&n)){var s=i&~a;0!==s?(r=Lt(s),o=Ht):0!==(l&=i)&&(r=Lt(l),o=Ht)}else 0!==(i=n&~a)?(r=Lt(i),o=Ht):0!==l&&(r=Lt(l),o=Ht);if(0===r)return 0;if(r=n&((0>(r=31-Vt(r))?0:1<<r)<<1)-1,0!==t&&t!==r&&0===(t&a)){if(Lt(t),o<=Ht)return t;Ht=o}if(0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)o=1<<(n=31-Vt(t)),r|=e[n],t&=~o;return r}function zt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function Ut(e,t){switch(e){case 15:return 1;case 14:return 2;case 12:return 0===(e=Wt(24&~t))?Ut(10,t):e;case 10:return 0===(e=Wt(192&~t))?Ut(8,t):e;case 8:return 0===(e=Wt(3584&~t))&&(0===(e=Wt(4186112&~t))&&(e=512)),e;case 2:return 0===(t=Wt(805306368&~t))&&(t=268435456),t}throw Error(a(358,e))}function Wt(e){return e&-e}function Bt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ft(e,t,n){e.pendingLanes|=t;var r=t-1;e.suspendedLanes&=r,e.pingedLanes&=r,(e=e.eventTimes)[t=31-Vt(t)]=n}var Vt=Math.clz32?Math.clz32:function(e){return 0===e?32:31-(qt(e)/Gt|0)|0},qt=Math.log,Gt=Math.LN2;var Yt=i.unstable_UserBlockingPriority,Xt=i.unstable_runWithPriority,Zt=!0;function $t(e,t,n,r){Le||_e();var o=Kt,i=Le;Le=!0;try{Ne(o,e,t,n,r)}finally{(Le=i)||ze()}}function Qt(e,t,n,r){Xt(Yt,Kt.bind(null,e,t,n,r))}function Kt(e,t,n,r){var o;if(Zt)if((o=0===(4&t))&&0<at.length&&-1<pt.indexOf(e))e=ht(null,e,t,n,r),at.push(e);else{var i=Jt(e,t,n,r);if(null===i)o&&vt(e,r);else{if(o){if(-1<pt.indexOf(e))return e=ht(i,e,t,n,r),void at.push(e);if(function(e,t,n,r,o){switch(t){case"focusin":return lt=gt(lt,e,t,n,r,o),!0;case"dragenter":return st=gt(st,e,t,n,r,o),!0;case"mouseover":return ut=gt(ut,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return ct.set(i,gt(ct.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,dt.set(i,gt(dt.get(i)||null,e,t,n,r,o)),!0}return!1}(i,e,t,n,r))return;vt(e,r)}Ir(e,t,r,null,n)}}}function Jt(e,t,n,r){var o=ke(r);if(null!==(o=Jr(o))){var i=$e(o);if(null===i)o=null;else{var a=i.tag;if(13===a){if(null!==(o=Qe(i)))return o;o=null}else if(3===a){if(i.stateNode.hydrate)return 3===i.tag?i.stateNode.containerInfo:null;o=null}else i!==o&&(o=null)}}return Ir(e,t,r,o,n),null}var en=null,tn=null,nn=null;function rn(){if(nn)return nn;var e,t,n=tn,r=n.length,o="value"in en?en.value:en.textContent,i=o.length;for(e=0;e<r&&n[e]===o[e];e++);var a=r-e;for(t=1;t<=a&&n[r-t]===o[i-t];t++);return nn=o.slice(e,1<t?1-t:void 0)}function on(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function an(){return!0}function ln(){return!1}function sn(e){function t(t,n,r,o,i){for(var a in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(a)&&(t=e[a],this[a]=t?t(o):o[a]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?an:ln,this.isPropagationStopped=ln,this}return o(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=an)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=an)},persist:function(){},isPersistent:an}),t}var un,cn,dn,fn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},pn=sn(fn),hn=o({},fn,{view:0,detail:0}),vn=sn(hn),gn=o({},hn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:On,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==dn&&(dn&&"mousemove"===e.type?(un=e.screenX-dn.screenX,cn=e.screenY-dn.screenY):cn=un=0,dn=e),un)},movementY:function(e){return"movementY"in e?e.movementY:cn}}),mn=sn(gn),yn=sn(o({},gn,{dataTransfer:0})),bn=sn(o({},hn,{relatedTarget:0})),Sn=sn(o({},fn,{animationName:0,elapsedTime:0,pseudoElement:0})),En=sn(o({},fn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),wn=sn(o({},fn,{data:0})),Cn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Rn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function kn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Rn[e])&&!!t[e]}function On(){return kn}var Tn=sn(o({},hn,{key:function(e){if(e.key){var t=Cn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=on(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:On,charCode:function(e){return"keypress"===e.type?on(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?on(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),xn=sn(o({},gn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Mn=sn(o({},hn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:On})),Pn=sn(o({},fn,{propertyName:0,elapsedTime:0,pseudoElement:0})),In=sn(o({},gn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),jn=[9,13,27,32],Nn=d&&"CompositionEvent"in window,_n=null;d&&"documentMode"in document&&(_n=document.documentMode);var Hn=d&&"TextEvent"in window&&!_n,Ln=d&&(!Nn||_n&&8<_n&&11>=_n),An=String.fromCharCode(32),zn=!1;function Un(e,t){switch(e){case"keyup":return-1!==jn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Wn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Bn=!1;var Fn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Vn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Fn[e.type]:"textarea"===t}function qn(e,t,n,r){Pe(r),0<(t=Nr(t,"onChange")).length&&(n=new pn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Gn=null,Yn=null;function Xn(e){kr(e,0)}function Zn(e){if(Q(to(e)))return e}function $n(e,t){if("change"===e)return t}var Qn=!1;if(d){var Kn;if(d){var Jn="oninput"in document;if(!Jn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Jn="function"===typeof er.oninput}Kn=Jn}else Kn=!1;Qn=Kn&&(!document.documentMode||9<document.documentMode)}function tr(){Gn&&(Gn.detachEvent("onpropertychange",nr),Yn=Gn=null)}function nr(e){if("value"===e.propertyName&&Zn(Yn)){var t=[];if(qn(t,Yn,e,ke(e)),e=Xn,Le)e(t);else{Le=!0;try{je(e,t)}finally{Le=!1,ze()}}}}function rr(e,t,n){"focusin"===e?(tr(),Yn=n,(Gn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function or(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Zn(Yn)}function ir(e,t){if("click"===e)return Zn(t)}function ar(e,t){if("input"===e||"change"===e)return Zn(t)}var lr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},sr=Object.prototype.hasOwnProperty;function ur(e,t){if(lr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++)if(!sr.call(t,n[r])||!lr(e[n[r]],t[n[r]]))return!1;return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function dr(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function fr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?fr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function pr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var vr=d&&"documentMode"in document&&11>=document.documentMode,gr=null,mr=null,yr=null,br=!1;function Sr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==K(r)||("selectionStart"in(r=gr)&&hr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&ur(yr,r)||(yr=r,0<(r=Nr(mr,"onSelect")).length&&(t=new pn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}_t("cancel cancel click click close close contextmenu contextMenu copy copy cut cut auxclick auxClick dblclick doubleClick dragend dragEnd dragstart dragStart drop drop focusin focus focusout blur input input invalid invalid keydown keyDown keypress keyPress keyup keyUp mousedown mouseDown mouseup mouseUp paste paste pause pause play play pointercancel pointerCancel pointerdown pointerDown pointerup pointerUp ratechange rateChange reset reset seeked seeked submit submit touchcancel touchCancel touchend touchEnd touchstart touchStart volumechange volumeChange".split(" "),0),_t("drag drag dragenter dragEnter dragexit dragExit dragleave dragLeave dragover dragOver mousemove mouseMove mouseout mouseOut mouseover mouseOver pointermove pointerMove pointerout pointerOut pointerover pointerOver scroll scroll toggle toggle touchmove touchMove wheel wheel".split(" "),1),_t(Nt,2);for(var Er="change selectionchange textInput compositionstart compositionend compositionupdate".split(" "),wr=0;wr<Er.length;wr++)jt.set(Er[wr],0);c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),u("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),u("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),u("onBeforeInput",["compositionend","keypress","textInput","paste"]),u("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Cr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Cr));function Rr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,o,i,l,s,u){if(Ze.apply(this,arguments),Ve){if(!Ve)throw Error(a(198));var c=qe;Ve=!1,qe=null,Ge||(Ge=!0,Ye=c)}}(r,t,void 0,e),e.currentTarget=null}function kr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var a=r.length-1;0<=a;a--){var l=r[a],s=l.instance,u=l.currentTarget;if(l=l.listener,s!==i&&o.isPropagationStopped())break e;Rr(o,l,u),i=s}else for(a=0;a<r.length;a++){if(s=(l=r[a]).instance,u=l.currentTarget,l=l.listener,s!==i&&o.isPropagationStopped())break e;Rr(o,l,u),i=s}}}if(Ge)throw e=Ye,Ge=!1,Ye=null,e}function Or(e,t){var n=ro(t),r=e+"__bubble";n.has(r)||(Pr(t,e,2,!1),n.add(r))}var Tr="_reactListening"+Math.random().toString(36).slice(2);function xr(e){e[Tr]||(e[Tr]=!0,l.forEach((function(t){Dr.has(t)||Mr(t,!1,e,null),Mr(t,!0,e,null)})))}function Mr(e,t,n,r){var o=4<arguments.length&&void 0!==arguments[4]?arguments[4]:0,i=n;if("selectionchange"===e&&9!==n.nodeType&&(i=n.ownerDocument),null!==r&&!t&&Dr.has(e)){if("scroll"!==e)return;o|=2,i=r}var a=ro(i),l=e+"__"+(t?"capture":"bubble");a.has(l)||(t&&(o|=4),Pr(i,e,o,t),a.add(l))}function Pr(e,t,n,r){var o=jt.get(t);switch(void 0===o?2:o){case 0:o=$t;break;case 1:o=Qt;break;default:o=Kt}n=o.bind(null,t,n,e),o=void 0,!We||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ir(e,t,n,r,o){var i=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var a=r.tag;if(3===a||4===a){var l=r.stateNode.containerInfo;if(l===o||8===l.nodeType&&l.parentNode===o)break;if(4===a)for(a=r.return;null!==a;){var s=a.tag;if((3===s||4===s)&&((s=a.stateNode.containerInfo)===o||8===s.nodeType&&s.parentNode===o))return;a=a.return}for(;null!==l;){if(null===(a=Jr(l)))return;if(5===(s=a.tag)||6===s){r=i=a;continue e}l=l.parentNode}}r=r.return}!function(e,t,n){if(Ae)return e(t,n);Ae=!0;try{He(e,t,n)}finally{Ae=!1,ze()}}((function(){var r=i,o=ke(n),a=[];e:{var l=It.get(e);if(void 0!==l){var s=pn,u=e;switch(e){case"keypress":if(0===on(n))break e;case"keydown":case"keyup":s=Tn;break;case"focusin":u="focus",s=bn;break;case"focusout":u="blur",s=bn;break;case"beforeblur":case"afterblur":s=bn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=mn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=yn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Mn;break;case Tt:case xt:case Mt:s=Sn;break;case Pt:s=Pn;break;case"scroll":s=vn;break;case"wheel":s=In;break;case"copy":case"cut":case"paste":s=En;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=xn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==l?l+"Capture":null:l;c=[];for(var p,h=r;null!==h;){var v=(p=h).stateNode;if(5===p.tag&&null!==v&&(p=v,null!==f&&(null!=(v=Ue(h,f))&&c.push(jr(h,v,p)))),d)break;h=h.return}0<c.length&&(l=new s(l,u,null,n,o),a.push({event:l,listeners:c}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||0!==(16&t)||!(u=n.relatedTarget||n.fromElement)||!Jr(u)&&!u[Qr])&&(s||l)&&(l=o.window===o?o:(l=o.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?Jr(u):null)&&(u!==(d=$e(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=mn,v="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=xn,v="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?l:to(s),p=null==u?l:to(u),(l=new c(v,h+"leave",s,n,o)).target=d,l.relatedTarget=p,v=null,Jr(o)===r&&((c=new c(f,h+"enter",u,n,o)).target=p,c.relatedTarget=d,v=c),d=v,s&&u)e:{for(f=u,h=0,p=c=s;p;p=_r(p))h++;for(p=0,v=f;v;v=_r(v))p++;for(;0<h-p;)c=_r(c),h--;for(;0<p-h;)f=_r(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=_r(c),f=_r(f)}c=null}else c=null;null!==s&&Hr(a,l,s,c,!1),null!==u&&null!==d&&Hr(a,d,u,c,!0)}if("select"===(s=(l=r?to(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var g=$n;else if(Vn(l))if(Qn)g=ar;else{g=or;var m=rr}else(s=l.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(g=ir);switch(g&&(g=g(e,r))?qn(a,g,n,o):(m&&m(e,l,r),"focusout"===e&&(m=l._wrapperState)&&m.controlled&&"number"===l.type&&oe(l,"number",l.value)),m=r?to(r):window,e){case"focusin":(Vn(m)||"true"===m.contentEditable)&&(gr=m,mr=r,yr=null);break;case"focusout":yr=mr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,Sr(a,n,o);break;case"selectionchange":if(vr)break;case"keydown":case"keyup":Sr(a,n,o)}var y;if(Nn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Bn?Un(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Ln&&"ko"!==n.locale&&(Bn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Bn&&(y=rn()):(tn="value"in(en=o)?en.value:en.textContent,Bn=!0)),0<(m=Nr(r,b)).length&&(b=new wn(b,e,null,n,o),a.push({event:b,listeners:m}),y?b.data=y:null!==(y=Wn(n))&&(b.data=y))),(y=Hn?function(e,t){switch(e){case"compositionend":return Wn(t);case"keypress":return 32!==t.which?null:(zn=!0,An);case"textInput":return(e=t.data)===An&&zn?null:e;default:return null}}(e,n):function(e,t){if(Bn)return"compositionend"===e||!Nn&&Un(e,t)?(e=rn(),nn=tn=en=null,Bn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ln&&"ko"!==t.locale?null:t.data;default:return null}}(e,n))&&(0<(r=Nr(r,"onBeforeInput")).length&&(o=new wn("onBeforeInput","beforeinput",null,n,o),a.push({event:o,listeners:r}),o.data=y))}kr(a,t)}))}function jr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Nr(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,i=o.stateNode;5===o.tag&&null!==i&&(o=i,null!=(i=Ue(e,n))&&r.unshift(jr(e,i,o)),null!=(i=Ue(e,t))&&r.push(jr(e,i,o))),e=e.return}return r}function _r(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Hr(e,t,n,r,o){for(var i=t._reactName,a=[];null!==n&&n!==r;){var l=n,s=l.alternate,u=l.stateNode;if(null!==s&&s===r)break;5===l.tag&&null!==u&&(l=u,o?null!=(s=Ue(n,i))&&a.unshift(jr(n,s,l)):o||null!=(s=Ue(n,i))&&a.push(jr(n,s,l))),n=n.return}0!==a.length&&e.push({event:t,listeners:a})}function Lr(){}var Ar=null,zr=null;function Ur(e,t){switch(e){case"button":case"input":case"select":case"textarea":return!!t.autoFocus}return!1}function Wr(e,t){return"textarea"===e||"option"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var Br="function"===typeof setTimeout?setTimeout:void 0,Fr="function"===typeof clearTimeout?clearTimeout:void 0;function Vr(e){1===e.nodeType?e.textContent="":9===e.nodeType&&(null!=(e=e.body)&&(e.textContent=""))}function qr(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break}return e}function Gr(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var Yr=0;var Xr=Math.random().toString(36).slice(2),Zr="__reactFiber$"+Xr,$r="__reactProps$"+Xr,Qr="__reactContainer$"+Xr,Kr="__reactEvents$"+Xr;function Jr(e){var t=e[Zr];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Qr]||n[Zr]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=Gr(e);null!==e;){if(n=e[Zr])return n;e=Gr(e)}return t}n=(e=n).parentNode}return null}function eo(e){return!(e=e[Zr]||e[Qr])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function to(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(a(33))}function no(e){return e[$r]||null}function ro(e){var t=e[Kr];return void 0===t&&(t=e[Kr]=new Set),t}var oo=[],io=-1;function ao(e){return{current:e}}function lo(e){0>io||(e.current=oo[io],oo[io]=null,io--)}function so(e,t){io++,oo[io]=e.current,e.current=t}var uo={},co=ao(uo),fo=ao(!1),po=uo;function ho(e,t){var n=e.type.contextTypes;if(!n)return uo;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o,i={};for(o in n)i[o]=t[o];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function vo(e){return null!==(e=e.childContextTypes)&&void 0!==e}function go(){lo(fo),lo(co)}function mo(e,t,n){if(co.current!==uo)throw Error(a(168));so(co,t),so(fo,n)}function yo(e,t,n){var r=e.stateNode;if(e=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var i in r=r.getChildContext())if(!(i in e))throw Error(a(108,Y(t)||"Unknown",i));return o({},n,r)}function bo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||uo,po=co.current,so(co,e),so(fo,fo.current),!0}function So(e,t,n){var r=e.stateNode;if(!r)throw Error(a(169));n?(e=yo(e,t,po),r.__reactInternalMemoizedMergedChildContext=e,lo(fo),lo(co),so(co,e)):lo(fo),so(fo,n)}var Eo=null,wo=null,Co=i.unstable_runWithPriority,Do=i.unstable_scheduleCallback,Ro=i.unstable_cancelCallback,ko=i.unstable_shouldYield,Oo=i.unstable_requestPaint,To=i.unstable_now,xo=i.unstable_getCurrentPriorityLevel,Mo=i.unstable_ImmediatePriority,Po=i.unstable_UserBlockingPriority,Io=i.unstable_NormalPriority,jo=i.unstable_LowPriority,No=i.unstable_IdlePriority,_o={},Ho=void 0!==Oo?Oo:function(){},Lo=null,Ao=null,zo=!1,Uo=To(),Wo=1e4>Uo?To:function(){return To()-Uo};function Bo(){switch(xo()){case Mo:return 99;case Po:return 98;case Io:return 97;case jo:return 96;case No:return 95;default:throw Error(a(332))}}function Fo(e){switch(e){case 99:return Mo;case 98:return Po;case 97:return Io;case 96:return jo;case 95:return No;default:throw Error(a(332))}}function Vo(e,t){return e=Fo(e),Co(e,t)}function qo(e,t,n){return e=Fo(e),Do(e,t,n)}function Go(){if(null!==Ao){var e=Ao;Ao=null,Ro(e)}Yo()}function Yo(){if(!zo&&null!==Lo){zo=!0;var e=0;try{var t=Lo;Vo(99,(function(){for(;e<t.length;e++){var n=t[e];do{n=n(!0)}while(null!==n)}})),Lo=null}catch(n){throw null!==Lo&&(Lo=Lo.slice(e+1)),Do(Mo,Go),n}finally{zo=!1}}}var Xo=E.ReactCurrentBatchConfig;function Zo(e,t){if(e&&e.defaultProps){for(var n in t=o({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}var $o=ao(null),Qo=null,Ko=null,Jo=null;function ei(){Jo=Ko=Qo=null}function ti(e){var t=$o.current;lo($o),e.type._context._currentValue=t}function ni(e,t){for(;null!==e;){var n=e.alternate;if((e.childLanes&t)===t){if(null===n||(n.childLanes&t)===t)break;n.childLanes|=t}else e.childLanes|=t,null!==n&&(n.childLanes|=t);e=e.return}}function ri(e,t){Qo=e,Jo=Ko=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(Na=!0),e.firstContext=null)}function oi(e,t){if(Jo!==e&&!1!==t&&0!==t)if("number"===typeof t&&1073741823!==t||(Jo=e,t=1073741823),t={context:e,observedBits:t,next:null},null===Ko){if(null===Qo)throw Error(a(308));Ko=t,Qo.dependencies={lanes:0,firstContext:t,responders:null}}else Ko=Ko.next=t;return e._currentValue}var ii=!1;function ai(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null},effects:null}}function li(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function si(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ui(e,t){if(null!==(e=e.updateQueue)){var n=(e=e.shared).pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}}function ci(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===i?o=i=a:i=i.next=a,n=n.next}while(null!==n);null===i?o=i=t:i=i.next=t}else o=i=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function di(e,t,n,r){var i=e.updateQueue;ii=!1;var a=i.firstBaseUpdate,l=i.lastBaseUpdate,s=i.shared.pending;if(null!==s){i.shared.pending=null;var u=s,c=u.next;u.next=null,null===l?a=c:l.next=c,l=u;var d=e.alternate;if(null!==d){var f=(d=d.updateQueue).lastBaseUpdate;f!==l&&(null===f?d.firstBaseUpdate=c:f.next=c,d.lastBaseUpdate=u)}}if(null!==a){for(f=i.baseState,l=0,d=c=u=null;;){s=a.lane;var p=a.eventTime;if((r&s)===s){null!==d&&(d=d.next={eventTime:p,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var h=e,v=a;switch(s=t,p=n,v.tag){case 1:if("function"===typeof(h=v.payload)){f=h.call(p,f,s);break e}f=h;break e;case 3:h.flags=-4097&h.flags|64;case 0:if(null===(s="function"===typeof(h=v.payload)?h.call(p,f,s):h)||void 0===s)break e;f=o({},f,s);break e;case 2:ii=!0}}null!==a.callback&&(e.flags|=32,null===(s=i.effects)?i.effects=[a]:s.push(a))}else p={eventTime:p,lane:s,tag:a.tag,payload:a.payload,callback:a.callback,next:null},null===d?(c=d=p,u=f):d=d.next=p,l|=s;if(null===(a=a.next)){if(null===(s=i.shared.pending))break;a=s.next,s.next=null,i.lastBaseUpdate=s,i.shared.pending=null}}null===d&&(u=f),i.baseState=u,i.firstBaseUpdate=c,i.lastBaseUpdate=d,Al|=l,e.lanes=l,e.memoizedState=f}}function fi(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(null!==o){if(r.callback=null,r=n,"function"!==typeof o)throw Error(a(191,o));o.call(r)}}}var pi=(new r.Component).refs;function hi(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:o({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var vi={isMounted:function(e){return!!(e=e._reactInternals)&&$e(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=us(),o=cs(e),i=si(r,o);i.payload=t,void 0!==n&&null!==n&&(i.callback=n),ui(e,i),ds(e,o,r)},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=us(),o=cs(e),i=si(r,o);i.tag=1,i.payload=t,void 0!==n&&null!==n&&(i.callback=n),ui(e,i),ds(e,o,r)},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=us(),r=cs(e),o=si(n,r);o.tag=2,void 0!==t&&null!==t&&(o.callback=t),ui(e,o),ds(e,r,n)}};function gi(e,t,n,r,o,i,a){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,i,a):!t.prototype||!t.prototype.isPureReactComponent||(!ur(n,r)||!ur(o,i))}function mi(e,t,n){var r=!1,o=uo,i=t.contextType;return"object"===typeof i&&null!==i?i=oi(i):(o=vo(t)?po:co.current,i=(r=null!==(r=t.contextTypes)&&void 0!==r)?ho(e,o):uo),t=new t(n,i),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=vi,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function yi(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&vi.enqueueReplaceState(t,t.state,null)}function bi(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=pi,ai(e);var i=t.contextType;"object"===typeof i&&null!==i?o.context=oi(i):(i=vo(t)?po:co.current,o.context=ho(e,i)),di(e,n,o,r),o.state=e.memoizedState,"function"===typeof(i=t.getDerivedStateFromProps)&&(hi(e,t,i,n),o.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(t=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),t!==o.state&&vi.enqueueReplaceState(o,o.state,null),di(e,n,o,r),o.state=e.memoizedState),"function"===typeof o.componentDidMount&&(e.flags|=4)}var Si=Array.isArray;function Ei(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(a(309));var r=n.stateNode}if(!r)throw Error(a(147,e));var o=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===o?t.ref:((t=function(e){var t=r.refs;t===pi&&(t=r.refs={}),null===e?delete t[o]:t[o]=e})._stringRef=o,t)}if("string"!==typeof e)throw Error(a(284));if(!n._owner)throw Error(a(290,e))}return e}function wi(e,t){if("textarea"!==e.type)throw Error(a(31,"[object Object]"===Object.prototype.toString.call(t)?"object with keys {"+Object.keys(t).join(", ")+"}":t))}function Ci(e){function t(t,n){if(e){var r=t.lastEffect;null!==r?(r.nextEffect=n,t.lastEffect=n):t.firstEffect=t.lastEffect=n,n.nextEffect=null,n.flags=8}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=Fs(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags=2,n):r:(t.flags=2,n):n}function l(t){return e&&null===t.alternate&&(t.flags=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Ys(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function u(e,t,n,r){return null!==t&&t.elementType===n.type?((r=o(t,n.props)).ref=Ei(e,t,n),r.return=e,r):((r=Vs(n.type,n.key,n.props,null,e.mode,r)).ref=Ei(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Xs(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,i){return null===t||7!==t.tag?((t=qs(n,e.mode,r,i)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t||"number"===typeof t)return(t=Ys(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Vs(t.type,t.key,t.props,null,e.mode,n)).ref=Ei(e,null,t),n.return=e,n;case C:return(t=Xs(t,e.mode,n)).return=e,t}if(Si(t)||B(t))return(t=qs(t,e.mode,n,null)).return=e,t;wi(e,t)}return null}function p(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n||"number"===typeof n)return null!==o?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===o?n.type===D?d(e,t,n.props.children,r,o):u(e,t,n,r):null;case C:return n.key===o?c(e,t,n,r):null}if(Si(n)||B(n))return null!==o?null:d(e,t,n,r,null);wi(e,n)}return null}function h(e,t,n,r,o){if("string"===typeof r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return e=e.get(null===r.key?n:r.key)||null,r.type===D?d(t,e,r.props.children,o,r.key):u(t,e,r,o);case C:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o)}if(Si(r)||B(r))return d(t,e=e.get(n)||null,r,o,null);wi(t,r)}return null}function v(o,a,l,s){for(var u=null,c=null,d=a,v=a=0,g=null;null!==d&&v<l.length;v++){d.index>v?(g=d,d=null):g=d.sibling;var m=p(o,d,l[v],s);if(null===m){null===d&&(d=g);break}e&&d&&null===m.alternate&&t(o,d),a=i(m,a,v),null===c?u=m:c.sibling=m,c=m,d=g}if(v===l.length)return n(o,d),u;if(null===d){for(;v<l.length;v++)null!==(d=f(o,l[v],s))&&(a=i(d,a,v),null===c?u=d:c.sibling=d,c=d);return u}for(d=r(o,d);v<l.length;v++)null!==(g=h(d,o,v,l[v],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?v:g.key),a=i(g,a,v),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach((function(e){return t(o,e)})),u}function g(o,l,s,u){var c=B(s);if("function"!==typeof c)throw Error(a(150));if(null==(s=c.call(s)))throw Error(a(151));for(var d=c=null,v=l,g=l=0,m=null,y=s.next();null!==v&&!y.done;g++,y=s.next()){v.index>g?(m=v,v=null):m=v.sibling;var b=p(o,v,y.value,u);if(null===b){null===v&&(v=m);break}e&&v&&null===b.alternate&&t(o,v),l=i(b,l,g),null===d?c=b:d.sibling=b,d=b,v=m}if(y.done)return n(o,v),c;if(null===v){for(;!y.done;g++,y=s.next())null!==(y=f(o,y.value,u))&&(l=i(y,l,g),null===d?c=y:d.sibling=y,d=y);return c}for(v=r(o,v);!y.done;g++,y=s.next())null!==(y=h(v,o,g,y.value,u))&&(e&&null!==y.alternate&&v.delete(null===y.key?g:y.key),l=i(y,l,g),null===d?c=y:d.sibling=y,d=y);return e&&v.forEach((function(e){return t(o,e)})),c}return function(e,r,i,s){var u="object"===typeof i&&null!==i&&i.type===D&&null===i.key;u&&(i=i.props.children);var c="object"===typeof i&&null!==i;if(c)switch(i.$$typeof){case w:e:{for(c=i.key,u=r;null!==u;){if(u.key===c){switch(u.tag){case 7:if(i.type===D){n(e,u.sibling),(r=o(u,i.props.children)).return=e,e=r;break e}break;default:if(u.elementType===i.type){n(e,u.sibling),(r=o(u,i.props)).ref=Ei(e,u,i),r.return=e,e=r;break e}}n(e,u);break}t(e,u),u=u.sibling}i.type===D?((r=qs(i.props.children,e.mode,s,i.key)).return=e,e=r):((s=Vs(i.type,i.key,i.props,null,e.mode,s)).ref=Ei(e,r,i),s.return=e,e=s)}return l(e);case C:e:{for(u=i.key;null!==r;){if(r.key===u){if(4===r.tag&&r.stateNode.containerInfo===i.containerInfo&&r.stateNode.implementation===i.implementation){n(e,r.sibling),(r=o(r,i.children||[])).return=e,e=r;break e}n(e,r);break}t(e,r),r=r.sibling}(r=Xs(i,e.mode,s)).return=e,e=r}return l(e)}if("string"===typeof i||"number"===typeof i)return i=""+i,null!==r&&6===r.tag?(n(e,r.sibling),(r=o(r,i)).return=e,e=r):(n(e,r),(r=Ys(i,e.mode,s)).return=e,e=r),l(e);if(Si(i))return v(e,r,i,s);if(B(i))return g(e,r,i,s);if(c&&wi(e,i),"undefined"===typeof i&&!u)switch(e.tag){case 1:case 22:case 0:case 11:case 15:throw Error(a(152,Y(e.type)||"Component"))}return n(e,r)}}var Di=Ci(!0),Ri=Ci(!1),ki={},Oi=ao(ki),Ti=ao(ki),xi=ao(ki);function Mi(e){if(e===ki)throw Error(a(174));return e}function Pi(e,t){switch(so(xi,t),so(Ti,e),so(Oi,ki),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:he(null,"");break;default:t=he(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}lo(Oi),so(Oi,t)}function Ii(){lo(Oi),lo(Ti),lo(xi)}function ji(e){Mi(xi.current);var t=Mi(Oi.current),n=he(t,e.type);t!==n&&(so(Ti,e),so(Oi,n))}function Ni(e){Ti.current===e&&(lo(Oi),lo(Ti))}var _i=ao(0);function Hi(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(64&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Li=null,Ai=null,zi=!1;function Ui(e,t){var n=Ws(5,null,null,0);n.elementType="DELETED",n.type="DELETED",n.stateNode=t,n.return=e,n.flags=8,null!==e.lastEffect?(e.lastEffect.nextEffect=n,e.lastEffect=n):e.firstEffect=e.lastEffect=n}function Wi(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,!0);case 13:default:return!1}}function Bi(e){if(zi){var t=Ai;if(t){var n=t;if(!Wi(e,t)){if(!(t=qr(n.nextSibling))||!Wi(e,t))return e.flags=-1025&e.flags|2,zi=!1,void(Li=e);Ui(Li,n)}Li=e,Ai=qr(t.firstChild)}else e.flags=-1025&e.flags|2,zi=!1,Li=e}}function Fi(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;Li=e}function Vi(e){if(e!==Li)return!1;if(!zi)return Fi(e),zi=!0,!1;var t=e.type;if(5!==e.tag||"head"!==t&&"body"!==t&&!Wr(t,e.memoizedProps))for(t=Ai;t;)Ui(e,t),t=qr(t.nextSibling);if(Fi(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(a(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){Ai=qr(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}Ai=null}}else Ai=Li?qr(e.stateNode.nextSibling):null;return!0}function qi(){Ai=Li=null,zi=!1}var Gi=[];function Yi(){for(var e=0;e<Gi.length;e++)Gi[e]._workInProgressVersionPrimary=null;Gi.length=0}var Xi=E.ReactCurrentDispatcher,Zi=E.ReactCurrentBatchConfig,$i=0,Qi=null,Ki=null,Ji=null,ea=!1,ta=!1;function na(){throw Error(a(321))}function ra(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!lr(e[n],t[n]))return!1;return!0}function oa(e,t,n,r,o,i){if($i=i,Qi=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Xi.current=null===e||null===e.memoizedState?Ma:Pa,e=n(r,o),ta){i=0;do{if(ta=!1,!(25>i))throw Error(a(301));i+=1,Ji=Ki=null,t.updateQueue=null,Xi.current=Ia,e=n(r,o)}while(ta)}if(Xi.current=xa,t=null!==Ki&&null!==Ki.next,$i=0,Ji=Ki=Qi=null,ea=!1,t)throw Error(a(300));return e}function ia(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Ji?Qi.memoizedState=Ji=e:Ji=Ji.next=e,Ji}function aa(){if(null===Ki){var e=Qi.alternate;e=null!==e?e.memoizedState:null}else e=Ki.next;var t=null===Ji?Qi.memoizedState:Ji.next;if(null!==t)Ji=t,Ki=e;else{if(null===e)throw Error(a(310));e={memoizedState:(Ki=e).memoizedState,baseState:Ki.baseState,baseQueue:Ki.baseQueue,queue:Ki.queue,next:null},null===Ji?Qi.memoizedState=Ji=e:Ji=Ji.next=e}return Ji}function la(e,t){return"function"===typeof t?t(e):t}function sa(e){var t=aa(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=Ki,o=r.baseQueue,i=n.pending;if(null!==i){if(null!==o){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(null!==o){o=o.next,r=r.baseState;var s=l=i=null,u=o;do{var c=u.lane;if(($i&c)===c)null!==s&&(s=s.next={lane:0,action:u.action,eagerReducer:u.eagerReducer,eagerState:u.eagerState,next:null}),r=u.eagerReducer===e?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,eagerReducer:u.eagerReducer,eagerState:u.eagerState,next:null};null===s?(l=s=d,i=r):s=s.next=d,Qi.lanes|=c,Al|=c}u=u.next}while(null!==u&&u!==o);null===s?i=r:s.next=l,lr(r,t.memoizedState)||(Na=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=s,n.lastRenderedState=r}return[t.memoizedState,n.dispatch]}function ua(e){var t=aa(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(null!==o){n.pending=null;var l=o=o.next;do{i=e(i,l.action),l=l.next}while(l!==o);lr(i,t.memoizedState)||(Na=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function ca(e,t,n){var r=t._getVersion;r=r(t._source);var o=t._workInProgressVersionPrimary;if(null!==o?e=o===r:(e=e.mutableReadLanes,(e=($i&e)===e)&&(t._workInProgressVersionPrimary=r,Gi.push(t))),e)return n(t._source);throw Gi.push(t),Error(a(350))}function da(e,t,n,r){var o=Ml;if(null===o)throw Error(a(349));var i=t._getVersion,l=i(t._source),s=Xi.current,u=s.useState((function(){return ca(o,t,n)})),c=u[1],d=u[0];u=Ji;var f=e.memoizedState,p=f.refs,h=p.getSnapshot,v=f.source;f=f.subscribe;var g=Qi;return e.memoizedState={refs:p,source:t,subscribe:r},s.useEffect((function(){p.getSnapshot=n,p.setSnapshot=c;var e=i(t._source);if(!lr(l,e)){e=n(t._source),lr(d,e)||(c(e),e=cs(g),o.mutableReadLanes|=e&o.pendingLanes),e=o.mutableReadLanes,o.entangledLanes|=e;for(var r=o.entanglements,a=e;0<a;){var s=31-Vt(a),u=1<<s;r[s]|=e,a&=~u}}}),[n,t,r]),s.useEffect((function(){return r(t._source,(function(){var e=p.getSnapshot,n=p.setSnapshot;try{n(e(t._source));var r=cs(g);o.mutableReadLanes|=r&o.pendingLanes}catch(i){n((function(){throw i}))}}))}),[t,r]),lr(h,n)&&lr(v,t)&&lr(f,r)||((e={pending:null,dispatch:null,lastRenderedReducer:la,lastRenderedState:d}).dispatch=c=Ta.bind(null,Qi,e),u.queue=e,u.baseQueue=null,d=ca(o,t,n),u.memoizedState=u.baseState=d),d}function fa(e,t,n){return da(aa(),e,t,n)}function pa(e){var t=ia();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e=(e=t.queue={pending:null,dispatch:null,lastRenderedReducer:la,lastRenderedState:e}).dispatch=Ta.bind(null,Qi,e),[t.memoizedState,e]}function ha(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=Qi.updateQueue)?(t={lastEffect:null},Qi.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function va(e){return e={current:e},ia().memoizedState=e}function ga(){return aa().memoizedState}function ma(e,t,n,r){var o=ia();Qi.flags|=e,o.memoizedState=ha(1|t,n,void 0,void 0===r?null:r)}function ya(e,t,n,r){var o=aa();r=void 0===r?null:r;var i=void 0;if(null!==Ki){var a=Ki.memoizedState;if(i=a.destroy,null!==r&&ra(r,a.deps))return void ha(t,n,i,r)}Qi.flags|=e,o.memoizedState=ha(1|t,n,i,r)}function ba(e,t){return ma(516,4,e,t)}function Sa(e,t){return ya(516,4,e,t)}function Ea(e,t){return ya(4,2,e,t)}function wa(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ca(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,ya(4,2,wa.bind(null,t,e),n)}function Da(){}function Ra(e,t){var n=aa();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ra(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function ka(e,t){var n=aa();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ra(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Oa(e,t){var n=Bo();Vo(98>n?98:n,(function(){e(!0)})),Vo(97<n?97:n,(function(){var n=Zi.transition;Zi.transition=1;try{e(!1),t()}finally{Zi.transition=n}}))}function Ta(e,t,n){var r=us(),o=cs(e),i={lane:o,action:n,eagerReducer:null,eagerState:null,next:null},a=t.pending;if(null===a?i.next=i:(i.next=a.next,a.next=i),t.pending=i,a=e.alternate,e===Qi||null!==a&&a===Qi)ta=ea=!0;else{if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var l=t.lastRenderedState,s=a(l,n);if(i.eagerReducer=a,i.eagerState=s,lr(s,l))return}catch(u){}ds(e,o,r)}}var xa={readContext:oi,useCallback:na,useContext:na,useEffect:na,useImperativeHandle:na,useLayoutEffect:na,useMemo:na,useReducer:na,useRef:na,useState:na,useDebugValue:na,useDeferredValue:na,useTransition:na,useMutableSource:na,useOpaqueIdentifier:na,unstable_isNewReconciler:!1},Ma={readContext:oi,useCallback:function(e,t){return ia().memoizedState=[e,void 0===t?null:t],e},useContext:oi,useEffect:ba,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,ma(4,2,wa.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ma(4,2,e,t)},useMemo:function(e,t){var n=ia();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ia();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e=(e=r.queue={pending:null,dispatch:null,lastRenderedReducer:e,lastRenderedState:t}).dispatch=Ta.bind(null,Qi,e),[r.memoizedState,e]},useRef:va,useState:pa,useDebugValue:Da,useDeferredValue:function(e){var t=pa(e),n=t[0],r=t[1];return ba((function(){var t=Zi.transition;Zi.transition=1;try{r(e)}finally{Zi.transition=t}}),[e]),n},useTransition:function(){var e=pa(!1),t=e[0];return va(e=Oa.bind(null,e[1])),[e,t]},useMutableSource:function(e,t,n){var r=ia();return r.memoizedState={refs:{getSnapshot:t,setSnapshot:null},source:e,subscribe:n},da(r,e,t,n)},useOpaqueIdentifier:function(){if(zi){var e=!1,t=function(e){return{$$typeof:_,toString:e,valueOf:e}}((function(){throw e||(e=!0,n("r:"+(Yr++).toString(36))),Error(a(355))})),n=pa(t)[1];return 0===(2&Qi.mode)&&(Qi.flags|=516,ha(5,(function(){n("r:"+(Yr++).toString(36))}),void 0,null)),t}return pa(t="r:"+(Yr++).toString(36)),t},unstable_isNewReconciler:!1},Pa={readContext:oi,useCallback:Ra,useContext:oi,useEffect:Sa,useImperativeHandle:Ca,useLayoutEffect:Ea,useMemo:ka,useReducer:sa,useRef:ga,useState:function(){return sa(la)},useDebugValue:Da,useDeferredValue:function(e){var t=sa(la),n=t[0],r=t[1];return Sa((function(){var t=Zi.transition;Zi.transition=1;try{r(e)}finally{Zi.transition=t}}),[e]),n},useTransition:function(){var e=sa(la)[0];return[ga().current,e]},useMutableSource:fa,useOpaqueIdentifier:function(){return sa(la)[0]},unstable_isNewReconciler:!1},Ia={readContext:oi,useCallback:Ra,useContext:oi,useEffect:Sa,useImperativeHandle:Ca,useLayoutEffect:Ea,useMemo:ka,useReducer:ua,useRef:ga,useState:function(){return ua(la)},useDebugValue:Da,useDeferredValue:function(e){var t=ua(la),n=t[0],r=t[1];return Sa((function(){var t=Zi.transition;Zi.transition=1;try{r(e)}finally{Zi.transition=t}}),[e]),n},useTransition:function(){var e=ua(la)[0];return[ga().current,e]},useMutableSource:fa,useOpaqueIdentifier:function(){return ua(la)[0]},unstable_isNewReconciler:!1},ja=E.ReactCurrentOwner,Na=!1;function _a(e,t,n,r){t.child=null===e?Ri(t,null,n,r):Di(t,e.child,n,r)}function Ha(e,t,n,r,o){n=n.render;var i=t.ref;return ri(t,o),r=oa(e,t,n,r,i,o),null===e||Na?(t.flags|=1,_a(e,t,r,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-517,e.lanes&=~o,nl(e,t,o))}function La(e,t,n,r,o,i){if(null===e){var a=n.type;return"function"!==typeof a||Bs(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Vs(n.type,null,r,t,t.mode,i)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,Aa(e,t,a,r,o,i))}return a=e.child,0===(o&i)&&(o=a.memoizedProps,(n=null!==(n=n.compare)?n:ur)(o,r)&&e.ref===t.ref)?nl(e,t,i):(t.flags|=1,(e=Fs(a,r)).ref=t.ref,e.return=t,t.child=e)}function Aa(e,t,n,r,o,i){if(null!==e&&ur(e.memoizedProps,r)&&e.ref===t.ref){if(Na=!1,0===(i&o))return t.lanes=e.lanes,nl(e,t,i);0!==(16384&e.flags)&&(Na=!0)}return Wa(e,t,n,r,i)}function za(e,t,n){var r=t.pendingProps,o=r.children,i=null!==e?e.memoizedState:null;if("hidden"===r.mode||"unstable-defer-without-hiding"===r.mode)if(0===(4&t.mode))t.memoizedState={baseLanes:0},bs(t,n);else{if(0===(1073741824&n))return e=null!==i?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e},bs(t,e),null;t.memoizedState={baseLanes:0},bs(t,null!==i?i.baseLanes:n)}else null!==i?(r=i.baseLanes|n,t.memoizedState=null):r=n,bs(t,r);return _a(e,t,o,n),t.child}function Ua(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=128)}function Wa(e,t,n,r,o){var i=vo(n)?po:co.current;return i=ho(t,i),ri(t,o),n=oa(e,t,n,r,i,o),null===e||Na?(t.flags|=1,_a(e,t,n,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-517,e.lanes&=~o,nl(e,t,o))}function Ba(e,t,n,r,o){if(vo(n)){var i=!0;bo(t)}else i=!1;if(ri(t,o),null===t.stateNode)null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),mi(t,n,r),bi(t,n,r,o),r=!0;else if(null===e){var a=t.stateNode,l=t.memoizedProps;a.props=l;var s=a.context,u=n.contextType;"object"===typeof u&&null!==u?u=oi(u):u=ho(t,u=vo(n)?po:co.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof a.getSnapshotBeforeUpdate;d||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(l!==r||s!==u)&&yi(t,a,r,u),ii=!1;var f=t.memoizedState;a.state=f,di(t,r,a,o),s=t.memoizedState,l!==r||f!==s||fo.current||ii?("function"===typeof c&&(hi(t,n,c,r),s=t.memoizedState),(l=ii||gi(t,n,l,r,f,s,u))?(d||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||("function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"===typeof a.componentDidMount&&(t.flags|=4)):("function"===typeof a.componentDidMount&&(t.flags|=4),t.memoizedProps=r,t.memoizedState=s),a.props=r,a.state=s,a.context=u,r=l):("function"===typeof a.componentDidMount&&(t.flags|=4),r=!1)}else{a=t.stateNode,li(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Zo(t.type,l),a.props=u,d=t.pendingProps,f=a.context,"object"===typeof(s=n.contextType)&&null!==s?s=oi(s):s=ho(t,s=vo(n)?po:co.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof a.getSnapshotBeforeUpdate)||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(l!==d||f!==s)&&yi(t,a,r,s),ii=!1,f=t.memoizedState,a.state=f,di(t,r,a,o);var h=t.memoizedState;l!==d||f!==h||fo.current||ii?("function"===typeof p&&(hi(t,n,p,r),h=t.memoizedState),(u=ii||gi(t,n,u,r,f,h,s))?(c||"function"!==typeof a.UNSAFE_componentWillUpdate&&"function"!==typeof a.componentWillUpdate||("function"===typeof a.componentWillUpdate&&a.componentWillUpdate(r,h,s),"function"===typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,h,s)),"function"===typeof a.componentDidUpdate&&(t.flags|=4),"function"===typeof a.getSnapshotBeforeUpdate&&(t.flags|=256)):("function"!==typeof a.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=256),t.memoizedProps=r,t.memoizedState=h),a.props=r,a.state=h,a.context=s,r=u):("function"!==typeof a.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=256),r=!1)}return Fa(e,t,n,r,i,o)}function Fa(e,t,n,r,o,i){Ua(e,t);var a=0!==(64&t.flags);if(!r&&!a)return o&&So(t,n,!1),nl(e,t,i);r=t.stateNode,ja.current=t;var l=a&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&a?(t.child=Di(t,e.child,null,i),t.child=Di(t,null,l,i)):_a(e,t,l,i),t.memoizedState=r.state,o&&So(t,n,!0),t.child}function Va(e){var t=e.stateNode;t.pendingContext?mo(0,t.pendingContext,t.pendingContext!==t.context):t.context&&mo(0,t.context,!1),Pi(e,t.containerInfo)}var qa,Ga,Ya,Xa={dehydrated:null,retryLane:0};function Za(e,t,n){var r,o=t.pendingProps,i=_i.current,a=!1;return(r=0!==(64&t.flags))||(r=(null===e||null!==e.memoizedState)&&0!==(2&i)),r?(a=!0,t.flags&=-65):null!==e&&null===e.memoizedState||void 0===o.fallback||!0===o.unstable_avoidThisFallback||(i|=1),so(_i,1&i),null===e?(void 0!==o.fallback&&Bi(t),e=o.children,i=o.fallback,a?(e=$a(t,e,i,n),t.child.memoizedState={baseLanes:n},t.memoizedState=Xa,e):"number"===typeof o.unstable_expectedLoadTime?(e=$a(t,e,i,n),t.child.memoizedState={baseLanes:n},t.memoizedState=Xa,t.lanes=33554432,e):((n=Gs({mode:"visible",children:e},t.mode,n,null)).return=t,t.child=n)):(e.memoizedState,a?(o=Ka(e,t,o.children,o.fallback,n),a=t.child,i=e.child.memoizedState,a.memoizedState=null===i?{baseLanes:n}:{baseLanes:i.baseLanes|n},a.childLanes=e.childLanes&~n,t.memoizedState=Xa,o):(n=Qa(e,t,o.children,n),t.memoizedState=null,n))}function $a(e,t,n,r){var o=e.mode,i=e.child;return t={mode:"hidden",children:t},0===(2&o)&&null!==i?(i.childLanes=0,i.pendingProps=t):i=Gs(t,o,0,null),n=qs(n,o,r,null),i.return=e,n.return=e,i.sibling=n,e.child=i,n}function Qa(e,t,n,r){var o=e.child;return e=o.sibling,n=Fs(o,{mode:"visible",children:n}),0===(2&t.mode)&&(n.lanes=r),n.return=t,n.sibling=null,null!==e&&(e.nextEffect=null,e.flags=8,t.firstEffect=t.lastEffect=e),t.child=n}function Ka(e,t,n,r,o){var i=t.mode,a=e.child;e=a.sibling;var l={mode:"hidden",children:n};return 0===(2&i)&&t.child!==a?((n=t.child).childLanes=0,n.pendingProps=l,null!==(a=n.lastEffect)?(t.firstEffect=n.firstEffect,t.lastEffect=a,a.nextEffect=null):t.firstEffect=t.lastEffect=null):n=Fs(a,l),null!==e?r=Fs(e,r):(r=qs(r,i,o,null)).flags|=2,r.return=t,n.return=t,n.sibling=r,t.child=n,r}function Ja(e,t){e.lanes|=t;var n=e.alternate;null!==n&&(n.lanes|=t),ni(e.return,t)}function el(e,t,n,r,o,i){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o,lastEffect:i}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o,a.lastEffect=i)}function tl(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(_a(e,t,r.children,n),0!==(2&(r=_i.current)))r=1&r|2,t.flags|=64;else{if(null!==e&&0!==(64&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ja(e,n);else if(19===e.tag)Ja(e,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(so(_i,r),0===(2&t.mode))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===Hi(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),el(t,!1,o,n,i,t.lastEffect);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===Hi(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}el(t,!0,n,null,i,t.lastEffect);break;case"together":el(t,!1,null,null,void 0,t.lastEffect);break;default:t.memoizedState=null}return t.child}function nl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Al|=t.lanes,0!==(n&t.childLanes)){if(null!==e&&t.child!==e.child)throw Error(a(153));if(null!==t.child){for(n=Fs(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Fs(e,e.pendingProps)).return=t;n.sibling=null}return t.child}return null}function rl(e,t){if(!zi)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ol(e,t,n){var r=t.pendingProps;switch(t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return null;case 1:return vo(t.type)&&go(),null;case 3:return Ii(),lo(fo),lo(co),Yi(),(r=t.stateNode).pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(Vi(t)?t.flags|=4:r.hydrate||(t.flags|=256)),null;case 5:Ni(t);var i=Mi(xi.current);if(n=t.type,null!==e&&null!=t.stateNode)Ga(e,t,n,r),e.ref!==t.ref&&(t.flags|=128);else{if(!r){if(null===t.stateNode)throw Error(a(166));return null}if(e=Mi(Oi.current),Vi(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[Zr]=t,r[$r]=l,n){case"dialog":Or("cancel",r),Or("close",r);break;case"iframe":case"object":case"embed":Or("load",r);break;case"video":case"audio":for(e=0;e<Cr.length;e++)Or(Cr[e],r);break;case"source":Or("error",r);break;case"img":case"image":case"link":Or("error",r),Or("load",r);break;case"details":Or("toggle",r);break;case"input":ee(r,l),Or("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},Or("invalid",r);break;case"textarea":se(r,l),Or("invalid",r)}for(var u in De(n,l),e=null,l)l.hasOwnProperty(u)&&(i=l[u],"children"===u?"string"===typeof i?r.textContent!==i&&(e=["children",i]):"number"===typeof i&&r.textContent!==""+i&&(e=["children",""+i]):s.hasOwnProperty(u)&&null!=i&&"onScroll"===u&&Or("scroll",r));switch(n){case"input":$(r),re(r,l,!0);break;case"textarea":$(r),ce(r);break;case"select":case"option":break;default:"function"===typeof l.onClick&&(r.onclick=Lr)}r=e,t.updateQueue=r,null!==r&&(t.flags|=4)}else{switch(u=9===i.nodeType?i:i.ownerDocument,e===de&&(e=pe(n)),e===de?"script"===n?((e=u.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=u.createElement(n,{is:r.is}):(e=u.createElement(n),"select"===n&&(u=e,r.multiple?u.multiple=!0:r.size&&(u.size=r.size))):e=u.createElementNS(e,n),e[Zr]=t,e[$r]=r,qa(e,t),t.stateNode=e,u=Re(n,r),n){case"dialog":Or("cancel",e),Or("close",e),i=r;break;case"iframe":case"object":case"embed":Or("load",e),i=r;break;case"video":case"audio":for(i=0;i<Cr.length;i++)Or(Cr[i],e);i=r;break;case"source":Or("error",e),i=r;break;case"img":case"image":case"link":Or("error",e),Or("load",e),i=r;break;case"details":Or("toggle",e),i=r;break;case"input":ee(e,r),i=J(e,r),Or("invalid",e);break;case"option":i=ie(e,r);break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=o({},r,{value:void 0}),Or("invalid",e);break;case"textarea":se(e,r),i=le(e,r),Or("invalid",e);break;default:i=r}De(n,i);var c=i;for(l in c)if(c.hasOwnProperty(l)){var d=c[l];"style"===l?we(e,d):"dangerouslySetInnerHTML"===l?null!=(d=d?d.__html:void 0)&&me(e,d):"children"===l?"string"===typeof d?("textarea"!==n||""!==d)&&ye(e,d):"number"===typeof d&&ye(e,""+d):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(s.hasOwnProperty(l)?null!=d&&"onScroll"===l&&Or("scroll",e):null!=d&&S(e,l,d,u))}switch(n){case"input":$(e),re(e,r,!1);break;case"textarea":$(e),ce(e);break;case"option":null!=r.value&&e.setAttribute("value",""+X(r.value));break;case"select":e.multiple=!!r.multiple,null!=(l=r.value)?ae(e,!!r.multiple,l,!1):null!=r.defaultValue&&ae(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof i.onClick&&(e.onclick=Lr)}Ur(n,r)&&(t.flags|=4)}null!==t.ref&&(t.flags|=128)}return null;case 6:if(e&&null!=t.stateNode)Ya(0,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(a(166));n=Mi(xi.current),Mi(Oi.current),Vi(t)?(r=t.stateNode,n=t.memoizedProps,r[Zr]=t,r.nodeValue!==n&&(t.flags|=4)):((r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[Zr]=t,t.stateNode=r)}return null;case 13:return lo(_i),r=t.memoizedState,0!==(64&t.flags)?(t.lanes=n,t):(r=null!==r,n=!1,null===e?void 0!==t.memoizedProps.fallback&&Vi(t):n=null!==e.memoizedState,r&&!n&&0!==(2&t.mode)&&(null===e&&!0!==t.memoizedProps.unstable_avoidThisFallback||0!==(1&_i.current)?0===_l&&(_l=3):(0!==_l&&3!==_l||(_l=4),null===Ml||0===(134217727&Al)&&0===(134217727&zl)||vs(Ml,Il))),(r||n)&&(t.flags|=4),null);case 4:return Ii(),null===e&&xr(t.stateNode.containerInfo),null;case 10:return ti(t),null;case 17:return vo(t.type)&&go(),null;case 19:if(lo(_i),null===(r=t.memoizedState))return null;if(l=0!==(64&t.flags),null===(u=r.rendering))if(l)rl(r,!1);else{if(0!==_l||null!==e&&0!==(64&e.flags))for(e=t.child;null!==e;){if(null!==(u=Hi(e))){for(t.flags|=64,rl(r,!1),null!==(l=u.updateQueue)&&(t.updateQueue=l,t.flags|=4),null===r.lastEffect&&(t.firstEffect=null),t.lastEffect=r.lastEffect,r=n,n=t.child;null!==n;)e=r,(l=n).flags&=2,l.nextEffect=null,l.firstEffect=null,l.lastEffect=null,null===(u=l.alternate)?(l.childLanes=0,l.lanes=e,l.child=null,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=u.childLanes,l.lanes=u.lanes,l.child=u.child,l.memoizedProps=u.memoizedProps,l.memoizedState=u.memoizedState,l.updateQueue=u.updateQueue,l.type=u.type,e=u.dependencies,l.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return so(_i,1&_i.current|2),t.child}e=e.sibling}null!==r.tail&&Wo()>Fl&&(t.flags|=64,l=!0,rl(r,!1),t.lanes=33554432)}else{if(!l)if(null!==(e=Hi(u))){if(t.flags|=64,l=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),rl(r,!0),null===r.tail&&"hidden"===r.tailMode&&!u.alternate&&!zi)return null!==(t=t.lastEffect=r.lastEffect)&&(t.nextEffect=null),null}else 2*Wo()-r.renderingStartTime>Fl&&1073741824!==n&&(t.flags|=64,l=!0,rl(r,!1),t.lanes=33554432);r.isBackwards?(u.sibling=t.child,t.child=u):(null!==(n=r.last)?n.sibling=u:t.child=u,r.last=u)}return null!==r.tail?(n=r.tail,r.rendering=n,r.tail=n.sibling,r.lastEffect=t.lastEffect,r.renderingStartTime=Wo(),n.sibling=null,t=_i.current,so(_i,l?1&t|2:1&t),n):null;case 23:case 24:return Ss(),null!==e&&null!==e.memoizedState!==(null!==t.memoizedState)&&"unstable-defer-without-hiding"!==r.mode&&(t.flags|=4),null}throw Error(a(156,t.tag))}function il(e){switch(e.tag){case 1:vo(e.type)&&go();var t=e.flags;return 4096&t?(e.flags=-4097&t|64,e):null;case 3:if(Ii(),lo(fo),lo(co),Yi(),0!==(64&(t=e.flags)))throw Error(a(285));return e.flags=-4097&t|64,e;case 5:return Ni(e),null;case 13:return lo(_i),4096&(t=e.flags)?(e.flags=-4097&t|64,e):null;case 19:return lo(_i),null;case 4:return Ii(),null;case 10:return ti(e),null;case 23:case 24:return Ss(),null;default:return null}}function al(e,t){try{var n="",r=t;do{n+=G(r),r=r.return}while(r);var o=n}catch(i){o="\nError generating stack: "+i.message+"\n"+i.stack}return{value:e,source:t,stack:o}}function ll(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}qa=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ga=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Mi(Oi.current);var a,l=null;switch(n){case"input":i=J(e,i),r=J(e,r),l=[];break;case"option":i=ie(e,i),r=ie(e,r),l=[];break;case"select":i=o({},i,{value:void 0}),r=o({},r,{value:void 0}),l=[];break;case"textarea":i=le(e,i),r=le(e,r),l=[];break;default:"function"!==typeof i.onClick&&"function"===typeof r.onClick&&(e.onclick=Lr)}for(d in De(n,r),n=null,i)if(!r.hasOwnProperty(d)&&i.hasOwnProperty(d)&&null!=i[d])if("style"===d){var u=i[d];for(a in u)u.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==d&&"children"!==d&&"suppressContentEditableWarning"!==d&&"suppressHydrationWarning"!==d&&"autoFocus"!==d&&(s.hasOwnProperty(d)?l||(l=[]):(l=l||[]).push(d,null));for(d in r){var c=r[d];if(u=null!=i?i[d]:void 0,r.hasOwnProperty(d)&&c!==u&&(null!=c||null!=u))if("style"===d)if(u){for(a in u)!u.hasOwnProperty(a)||c&&c.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in c)c.hasOwnProperty(a)&&u[a]!==c[a]&&(n||(n={}),n[a]=c[a])}else n||(l||(l=[]),l.push(d,n)),n=c;else"dangerouslySetInnerHTML"===d?(c=c?c.__html:void 0,u=u?u.__html:void 0,null!=c&&u!==c&&(l=l||[]).push(d,c)):"children"===d?"string"!==typeof c&&"number"!==typeof c||(l=l||[]).push(d,""+c):"suppressContentEditableWarning"!==d&&"suppressHydrationWarning"!==d&&(s.hasOwnProperty(d)?(null!=c&&"onScroll"===d&&Or("scroll",e),l||u===c||(l=[])):"object"===typeof c&&null!==c&&c.$$typeof===_?c.toString():(l=l||[]).push(d,c))}n&&(l=l||[]).push("style",n);var d=l;(t.updateQueue=d)&&(t.flags|=4)}},Ya=function(e,t,n,r){n!==r&&(t.flags|=4)};var sl="function"===typeof WeakMap?WeakMap:Map;function ul(e,t,n){(n=si(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Yl||(Yl=!0,Xl=r),ll(0,t)},n}function cl(e,t,n){(n=si(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var o=t.value;n.payload=function(){return ll(0,t),r(o)}}var i=e.stateNode;return null!==i&&"function"===typeof i.componentDidCatch&&(n.callback=function(){"function"!==typeof r&&(null===Zl?Zl=new Set([this]):Zl.add(this),ll(0,t));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}var dl="function"===typeof WeakSet?WeakSet:Set;function fl(e){var t=e.ref;if(null!==t)if("function"===typeof t)try{t(null)}catch(n){Ls(e,n)}else t.current=null}function pl(e,t){switch(t.tag){case 0:case 11:case 15:case 22:return;case 1:if(256&t.flags&&null!==e){var n=e.memoizedProps,r=e.memoizedState;t=(e=t.stateNode).getSnapshotBeforeUpdate(t.elementType===t.type?n:Zo(t.type,n),r),e.__reactInternalSnapshotBeforeUpdate=t}return;case 3:return void(256&t.flags&&Vr(t.stateNode.containerInfo));case 5:case 6:case 4:case 17:return}throw Error(a(163))}function hl(e,t,n){switch(n.tag){case 0:case 11:case 15:case 22:if(null!==(t=null!==(t=n.updateQueue)?t.lastEffect:null)){e=t=t.next;do{if(3===(3&e.tag)){var r=e.create;e.destroy=r()}e=e.next}while(e!==t)}if(null!==(t=null!==(t=n.updateQueue)?t.lastEffect:null)){e=t=t.next;do{var o=e;r=o.next,0!==(4&(o=o.tag))&&0!==(1&o)&&(Ns(n,e),js(n,e)),e=r}while(e!==t)}return;case 1:return e=n.stateNode,4&n.flags&&(null===t?e.componentDidMount():(r=n.elementType===n.type?t.memoizedProps:Zo(n.type,t.memoizedProps),e.componentDidUpdate(r,t.memoizedState,e.__reactInternalSnapshotBeforeUpdate))),void(null!==(t=n.updateQueue)&&fi(n,t,e));case 3:if(null!==(t=n.updateQueue)){if(e=null,null!==n.child)switch(n.child.tag){case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}fi(n,t,e)}return;case 5:return e=n.stateNode,void(null===t&&4&n.flags&&Ur(n.type,n.memoizedProps)&&e.focus());case 6:case 4:case 12:return;case 13:return void(null===n.memoizedState&&(n=n.alternate,null!==n&&(n=n.memoizedState,null!==n&&(n=n.dehydrated,null!==n&&wt(n)))));case 19:case 17:case 20:case 21:case 23:case 24:return}throw Error(a(163))}function vl(e,t){for(var n=e;;){if(5===n.tag){var r=n.stateNode;if(t)"function"===typeof(r=r.style).setProperty?r.setProperty("display","none","important"):r.display="none";else{r=n.stateNode;var o=n.memoizedProps.style;o=void 0!==o&&null!==o&&o.hasOwnProperty("display")?o.display:null,r.style.display=Ee("display",o)}}else if(6===n.tag)n.stateNode.nodeValue=t?"":n.memoizedProps;else if((23!==n.tag&&24!==n.tag||null===n.memoizedState||n===e)&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}}function gl(e,t){if(wo&&"function"===typeof wo.onCommitFiberUnmount)try{wo.onCommitFiberUnmount(Eo,t)}catch(i){}switch(t.tag){case 0:case 11:case 14:case 15:case 22:if(null!==(e=t.updateQueue)&&null!==(e=e.lastEffect)){var n=e=e.next;do{var r=n,o=r.destroy;if(r=r.tag,void 0!==o)if(0!==(4&r))Ns(t,n);else{r=t;try{o()}catch(i){Ls(r,i)}}n=n.next}while(n!==e)}break;case 1:if(fl(t),"function"===typeof(e=t.stateNode).componentWillUnmount)try{e.props=t.memoizedProps,e.state=t.memoizedState,e.componentWillUnmount()}catch(i){Ls(t,i)}break;case 5:fl(t);break;case 4:wl(e,t)}}function ml(e){e.alternate=null,e.child=null,e.dependencies=null,e.firstEffect=null,e.lastEffect=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.return=null,e.updateQueue=null}function yl(e){return 5===e.tag||3===e.tag||4===e.tag}function bl(e){e:{for(var t=e.return;null!==t;){if(yl(t))break e;t=t.return}throw Error(a(160))}var n=t;switch(t=n.stateNode,n.tag){case 5:var r=!1;break;case 3:case 4:t=t.containerInfo,r=!0;break;default:throw Error(a(161))}16&n.flags&&(ye(t,""),n.flags&=-17);e:t:for(n=e;;){for(;null===n.sibling;){if(null===n.return||yl(n.return)){n=null;break e}n=n.return}for(n.sibling.return=n.return,n=n.sibling;5!==n.tag&&6!==n.tag&&18!==n.tag;){if(2&n.flags)continue t;if(null===n.child||4===n.tag)continue t;n.child.return=n,n=n.child}if(!(2&n.flags)){n=n.stateNode;break e}}r?Sl(e,n,t):El(e,n,t)}function Sl(e,t,n){var r=e.tag,o=5===r||6===r;if(o)e=o?e.stateNode:e.stateNode.instance,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Lr));else if(4!==r&&null!==(e=e.child))for(Sl(e,t,n),e=e.sibling;null!==e;)Sl(e,t,n),e=e.sibling}function El(e,t,n){var r=e.tag,o=5===r||6===r;if(o)e=o?e.stateNode:e.stateNode.instance,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(El(e,t,n),e=e.sibling;null!==e;)El(e,t,n),e=e.sibling}function wl(e,t){for(var n,r,o=t,i=!1;;){if(!i){i=o.return;e:for(;;){if(null===i)throw Error(a(160));switch(n=i.stateNode,i.tag){case 5:r=!1;break e;case 3:case 4:n=n.containerInfo,r=!0;break e}i=i.return}i=!0}if(5===o.tag||6===o.tag){e:for(var l=e,s=o,u=s;;)if(gl(l,u),null!==u.child&&4!==u.tag)u.child.return=u,u=u.child;else{if(u===s)break e;for(;null===u.sibling;){if(null===u.return||u.return===s)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}r?(l=n,s=o.stateNode,8===l.nodeType?l.parentNode.removeChild(s):l.removeChild(s)):n.removeChild(o.stateNode)}else if(4===o.tag){if(null!==o.child){n=o.stateNode.containerInfo,r=!0,o.child.return=o,o=o.child;continue}}else if(gl(e,o),null!==o.child){o.child.return=o,o=o.child;continue}if(o===t)break;for(;null===o.sibling;){if(null===o.return||o.return===t)return;4===(o=o.return).tag&&(i=!1)}o.sibling.return=o.return,o=o.sibling}}function Cl(e,t){switch(t.tag){case 0:case 11:case 14:case 15:case 22:var n=t.updateQueue;if(null!==(n=null!==n?n.lastEffect:null)){var r=n=n.next;do{3===(3&r.tag)&&(e=r.destroy,r.destroy=void 0,void 0!==e&&e()),r=r.next}while(r!==n)}return;case 1:return;case 5:if(null!=(n=t.stateNode)){r=t.memoizedProps;var o=null!==e?e.memoizedProps:r;e=t.type;var i=t.updateQueue;if(t.updateQueue=null,null!==i){for(n[$r]=r,"input"===e&&"radio"===r.type&&null!=r.name&&te(n,r),Re(e,o),t=Re(e,r),o=0;o<i.length;o+=2){var l=i[o],s=i[o+1];"style"===l?we(n,s):"dangerouslySetInnerHTML"===l?me(n,s):"children"===l?ye(n,s):S(n,l,s,t)}switch(e){case"input":ne(n,r);break;case"textarea":ue(n,r);break;case"select":e=n._wrapperState.wasMultiple,n._wrapperState.wasMultiple=!!r.multiple,null!=(i=r.value)?ae(n,!!r.multiple,i,!1):e!==!!r.multiple&&(null!=r.defaultValue?ae(n,!!r.multiple,r.defaultValue,!0):ae(n,!!r.multiple,r.multiple?[]:"",!1))}}}return;case 6:if(null===t.stateNode)throw Error(a(162));return void(t.stateNode.nodeValue=t.memoizedProps);case 3:return void((n=t.stateNode).hydrate&&(n.hydrate=!1,wt(n.containerInfo)));case 12:return;case 13:return null!==t.memoizedState&&(Bl=Wo(),vl(t.child,!0)),void Dl(t);case 19:return void Dl(t);case 17:return;case 23:case 24:return void vl(t,null!==t.memoizedState)}throw Error(a(163))}function Dl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new dl),t.forEach((function(t){var r=zs.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function Rl(e,t){return null!==e&&(null===(e=e.memoizedState)||null!==e.dehydrated)&&(null!==(t=t.memoizedState)&&null===t.dehydrated)}var kl=Math.ceil,Ol=E.ReactCurrentDispatcher,Tl=E.ReactCurrentOwner,xl=0,Ml=null,Pl=null,Il=0,jl=0,Nl=ao(0),_l=0,Hl=null,Ll=0,Al=0,zl=0,Ul=0,Wl=null,Bl=0,Fl=1/0;function Vl(){Fl=Wo()+500}var ql,Gl=null,Yl=!1,Xl=null,Zl=null,$l=!1,Ql=null,Kl=90,Jl=[],es=[],ts=null,ns=0,rs=null,os=-1,is=0,as=0,ls=null,ss=!1;function us(){return 0!==(48&xl)?Wo():-1!==os?os:os=Wo()}function cs(e){if(0===(2&(e=e.mode)))return 1;if(0===(4&e))return 99===Bo()?1:2;if(0===is&&(is=Ll),0!==Xo.transition){0!==as&&(as=null!==Wl?Wl.pendingLanes:0),e=is;var t=4186112&~as;return 0===(t&=-t)&&(0===(t=(e=4186112&~e)&-e)&&(t=8192)),t}return e=Bo(),0!==(4&xl)&&98===e?e=Ut(12,is):e=Ut(e=function(e){switch(e){case 99:return 15;case 98:return 10;case 97:case 96:return 8;case 95:return 2;default:return 0}}(e),is),e}function ds(e,t,n){if(50<ns)throw ns=0,rs=null,Error(a(185));if(null===(e=fs(e,t)))return null;Ft(e,t,n),e===Ml&&(zl|=t,4===_l&&vs(e,Il));var r=Bo();1===t?0!==(8&xl)&&0===(48&xl)?gs(e):(ps(e,n),0===xl&&(Vl(),Go())):(0===(4&xl)||98!==r&&99!==r||(null===ts?ts=new Set([e]):ts.add(e)),ps(e,n)),Wl=e}function fs(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}function ps(e,t){for(var n=e.callbackNode,r=e.suspendedLanes,o=e.pingedLanes,i=e.expirationTimes,l=e.pendingLanes;0<l;){var s=31-Vt(l),u=1<<s,c=i[s];if(-1===c){if(0===(u&r)||0!==(u&o)){c=t,Lt(u);var d=Ht;i[s]=10<=d?c+250:6<=d?c+5e3:-1}}else c<=t&&(e.expiredLanes|=u);l&=~u}if(r=At(e,e===Ml?Il:0),t=Ht,0===r)null!==n&&(n!==_o&&Ro(n),e.callbackNode=null,e.callbackPriority=0);else{if(null!==n){if(e.callbackPriority===t)return;n!==_o&&Ro(n)}15===t?(n=gs.bind(null,e),null===Lo?(Lo=[n],Ao=Do(Mo,Yo)):Lo.push(n),n=_o):14===t?n=qo(99,gs.bind(null,e)):n=qo(n=function(e){switch(e){case 15:case 14:return 99;case 13:case 12:case 11:case 10:return 98;case 9:case 8:case 7:case 6:case 4:case 5:return 97;case 3:case 2:case 1:return 95;case 0:return 90;default:throw Error(a(358,e))}}(t),hs.bind(null,e)),e.callbackPriority=t,e.callbackNode=n}}function hs(e){if(os=-1,as=is=0,0!==(48&xl))throw Error(a(327));var t=e.callbackNode;if(Is()&&e.callbackNode!==t)return null;var n=At(e,e===Ml?Il:0);if(0===n)return null;var r=n,o=xl;xl|=16;var i=Cs();for(Ml===e&&Il===r||(Vl(),Es(e,r));;)try{ks();break}catch(s){ws(e,s)}if(ei(),Ol.current=i,xl=o,null!==Pl?r=0:(Ml=null,Il=0,r=_l),0!==(Ll&zl))Es(e,0);else if(0!==r){if(2===r&&(xl|=64,e.hydrate&&(e.hydrate=!1,Vr(e.containerInfo)),0!==(n=zt(e))&&(r=Ds(e,n))),1===r)throw t=Hl,Es(e,0),vs(e,n),ps(e,Wo()),t;switch(e.finishedWork=e.current.alternate,e.finishedLanes=n,r){case 0:case 1:throw Error(a(345));case 2:xs(e);break;case 3:if(vs(e,n),(62914560&n)===n&&10<(r=Bl+500-Wo())){if(0!==At(e,0))break;if(((o=e.suspendedLanes)&n)!==n){us(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Br(xs.bind(null,e),r);break}xs(e);break;case 4:if(vs(e,n),(4186112&n)===n)break;for(r=e.eventTimes,o=-1;0<n;){var l=31-Vt(n);i=1<<l,(l=r[l])>o&&(o=l),n&=~i}if(n=o,10<(n=(120>(n=Wo()-n)?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*kl(n/1960))-n)){e.timeoutHandle=Br(xs.bind(null,e),n);break}xs(e);break;case 5:xs(e);break;default:throw Error(a(329))}}return ps(e,Wo()),e.callbackNode===t?hs.bind(null,e):null}function vs(e,t){for(t&=~Ul,t&=~zl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Vt(t),r=1<<n;e[n]=-1,t&=~r}}function gs(e){if(0!==(48&xl))throw Error(a(327));if(Is(),e===Ml&&0!==(e.expiredLanes&Il)){var t=Il,n=Ds(e,t);0!==(Ll&zl)&&(n=Ds(e,t=At(e,t)))}else n=Ds(e,t=At(e,0));if(0!==e.tag&&2===n&&(xl|=64,e.hydrate&&(e.hydrate=!1,Vr(e.containerInfo)),0!==(t=zt(e))&&(n=Ds(e,t))),1===n)throw n=Hl,Es(e,0),vs(e,t),ps(e,Wo()),n;return e.finishedWork=e.current.alternate,e.finishedLanes=t,xs(e),ps(e,Wo()),null}function ms(e,t){var n=xl;xl|=1;try{return e(t)}finally{0===(xl=n)&&(Vl(),Go())}}function ys(e,t){var n=xl;xl&=-2,xl|=8;try{return e(t)}finally{0===(xl=n)&&(Vl(),Go())}}function bs(e,t){so(Nl,jl),jl|=t,Ll|=t}function Ss(){jl=Nl.current,lo(Nl)}function Es(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,Fr(n)),null!==Pl)for(n=Pl.return;null!==n;){var r=n;switch(r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&go();break;case 3:Ii(),lo(fo),lo(co),Yi();break;case 5:Ni(r);break;case 4:Ii();break;case 13:case 19:lo(_i);break;case 10:ti(r);break;case 23:case 24:Ss()}n=n.return}Ml=e,Pl=Fs(e.current,null),Il=jl=Ll=t,_l=0,Hl=null,Ul=zl=Al=0}function ws(e,t){for(;;){var n=Pl;try{if(ei(),Xi.current=xa,ea){for(var r=Qi.memoizedState;null!==r;){var o=r.queue;null!==o&&(o.pending=null),r=r.next}ea=!1}if($i=0,Ji=Ki=Qi=null,ta=!1,Tl.current=null,null===n||null===n.return){_l=1,Hl=t,Pl=null;break}e:{var i=e,a=n.return,l=n,s=t;if(t=Il,l.flags|=2048,l.firstEffect=l.lastEffect=null,null!==s&&"object"===typeof s&&"function"===typeof s.then){var u=s;if(0===(2&l.mode)){var c=l.alternate;c?(l.updateQueue=c.updateQueue,l.memoizedState=c.memoizedState,l.lanes=c.lanes):(l.updateQueue=null,l.memoizedState=null)}var d=0!==(1&_i.current),f=a;do{var p;if(p=13===f.tag){var h=f.memoizedState;if(null!==h)p=null!==h.dehydrated;else{var v=f.memoizedProps;p=void 0!==v.fallback&&(!0!==v.unstable_avoidThisFallback||!d)}}if(p){var g=f.updateQueue;if(null===g){var m=new Set;m.add(u),f.updateQueue=m}else g.add(u);if(0===(2&f.mode)){if(f.flags|=64,l.flags|=16384,l.flags&=-2981,1===l.tag)if(null===l.alternate)l.tag=17;else{var y=si(-1,1);y.tag=2,ui(l,y)}l.lanes|=1;break e}s=void 0,l=t;var b=i.pingCache;if(null===b?(b=i.pingCache=new sl,s=new Set,b.set(u,s)):void 0===(s=b.get(u))&&(s=new Set,b.set(u,s)),!s.has(l)){s.add(l);var S=As.bind(null,i,u,l);u.then(S,S)}f.flags|=4096,f.lanes=t;break e}f=f.return}while(null!==f);s=Error((Y(l.type)||"A React component")+" suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display.")}5!==_l&&(_l=2),s=al(s,l),f=a;do{switch(f.tag){case 3:i=s,f.flags|=4096,t&=-t,f.lanes|=t,ci(f,ul(0,i,t));break e;case 1:i=s;var E=f.type,w=f.stateNode;if(0===(64&f.flags)&&("function"===typeof E.getDerivedStateFromError||null!==w&&"function"===typeof w.componentDidCatch&&(null===Zl||!Zl.has(w)))){f.flags|=4096,t&=-t,f.lanes|=t,ci(f,cl(f,i,t));break e}}f=f.return}while(null!==f)}Ts(n)}catch(C){t=C,Pl===n&&null!==n&&(Pl=n=n.return);continue}break}}function Cs(){var e=Ol.current;return Ol.current=xa,null===e?xa:e}function Ds(e,t){var n=xl;xl|=16;var r=Cs();for(Ml===e&&Il===t||Es(e,t);;)try{Rs();break}catch(o){ws(e,o)}if(ei(),xl=n,Ol.current=r,null!==Pl)throw Error(a(261));return Ml=null,Il=0,_l}function Rs(){for(;null!==Pl;)Os(Pl)}function ks(){for(;null!==Pl&&!ko();)Os(Pl)}function Os(e){var t=ql(e.alternate,e,jl);e.memoizedProps=e.pendingProps,null===t?Ts(e):Pl=t,Tl.current=null}function Ts(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(2048&t.flags)){if(null!==(n=ol(n,t,jl)))return void(Pl=n);if(24!==(n=t).tag&&23!==n.tag||null===n.memoizedState||0!==(1073741824&jl)||0===(4&n.mode)){for(var r=0,o=n.child;null!==o;)r|=o.lanes|o.childLanes,o=o.sibling;n.childLanes=r}null!==e&&0===(2048&e.flags)&&(null===e.firstEffect&&(e.firstEffect=t.firstEffect),null!==t.lastEffect&&(null!==e.lastEffect&&(e.lastEffect.nextEffect=t.firstEffect),e.lastEffect=t.lastEffect),1<t.flags&&(null!==e.lastEffect?e.lastEffect.nextEffect=t:e.firstEffect=t,e.lastEffect=t))}else{if(null!==(n=il(t)))return n.flags&=2047,void(Pl=n);null!==e&&(e.firstEffect=e.lastEffect=null,e.flags|=2048)}if(null!==(t=t.sibling))return void(Pl=t);Pl=t=e}while(null!==t);0===_l&&(_l=5)}function xs(e){var t=Bo();return Vo(99,Ms.bind(null,e,t)),null}function Ms(e,t){do{Is()}while(null!==Ql);if(0!==(48&xl))throw Error(a(327));var n=e.finishedWork;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(a(177));e.callbackNode=null;var r=n.lanes|n.childLanes,o=r,i=e.pendingLanes&~o;e.pendingLanes=o,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=o,e.mutableReadLanes&=o,e.entangledLanes&=o,o=e.entanglements;for(var l=e.eventTimes,s=e.expirationTimes;0<i;){var u=31-Vt(i),c=1<<u;o[u]=0,l[u]=-1,s[u]=-1,i&=~c}if(null!==ts&&0===(24&r)&&ts.has(e)&&ts.delete(e),e===Ml&&(Pl=Ml=null,Il=0),1<n.flags?null!==n.lastEffect?(n.lastEffect.nextEffect=n,r=n.firstEffect):r=n:r=n.firstEffect,null!==r){if(o=xl,xl|=32,Tl.current=null,Ar=Zt,hr(l=pr())){if("selectionStart"in l)s={start:l.selectionStart,end:l.selectionEnd};else e:if(s=(s=l.ownerDocument)&&s.defaultView||window,(c=s.getSelection&&s.getSelection())&&0!==c.rangeCount){s=c.anchorNode,i=c.anchorOffset,u=c.focusNode,c=c.focusOffset;try{s.nodeType,u.nodeType}catch(k){s=null;break e}var d=0,f=-1,p=-1,h=0,v=0,g=l,m=null;t:for(;;){for(var y;g!==s||0!==i&&3!==g.nodeType||(f=d+i),g!==u||0!==c&&3!==g.nodeType||(p=d+c),3===g.nodeType&&(d+=g.nodeValue.length),null!==(y=g.firstChild);)m=g,g=y;for(;;){if(g===l)break t;if(m===s&&++h===i&&(f=d),m===u&&++v===c&&(p=d),null!==(y=g.nextSibling))break;m=(g=m).parentNode}g=y}s=-1===f||-1===p?null:{start:f,end:p}}else s=null;s=s||{start:0,end:0}}else s=null;zr={focusedElem:l,selectionRange:s},Zt=!1,ls=null,ss=!1,Gl=r;do{try{Ps()}catch(k){if(null===Gl)throw Error(a(330));Ls(Gl,k),Gl=Gl.nextEffect}}while(null!==Gl);ls=null,Gl=r;do{try{for(l=e;null!==Gl;){var b=Gl.flags;if(16&b&&ye(Gl.stateNode,""),128&b){var S=Gl.alternate;if(null!==S){var E=S.ref;null!==E&&("function"===typeof E?E(null):E.current=null)}}switch(1038&b){case 2:bl(Gl),Gl.flags&=-3;break;case 6:bl(Gl),Gl.flags&=-3,Cl(Gl.alternate,Gl);break;case 1024:Gl.flags&=-1025;break;case 1028:Gl.flags&=-1025,Cl(Gl.alternate,Gl);break;case 4:Cl(Gl.alternate,Gl);break;case 8:wl(l,s=Gl);var w=s.alternate;ml(s),null!==w&&ml(w)}Gl=Gl.nextEffect}}catch(k){if(null===Gl)throw Error(a(330));Ls(Gl,k),Gl=Gl.nextEffect}}while(null!==Gl);if(E=zr,S=pr(),b=E.focusedElem,l=E.selectionRange,S!==b&&b&&b.ownerDocument&&fr(b.ownerDocument.documentElement,b)){null!==l&&hr(b)&&(S=l.start,void 0===(E=l.end)&&(E=S),"selectionStart"in b?(b.selectionStart=S,b.selectionEnd=Math.min(E,b.value.length)):(E=(S=b.ownerDocument||document)&&S.defaultView||window).getSelection&&(E=E.getSelection(),s=b.textContent.length,w=Math.min(l.start,s),l=void 0===l.end?w:Math.min(l.end,s),!E.extend&&w>l&&(s=l,l=w,w=s),s=dr(b,w),i=dr(b,l),s&&i&&(1!==E.rangeCount||E.anchorNode!==s.node||E.anchorOffset!==s.offset||E.focusNode!==i.node||E.focusOffset!==i.offset)&&((S=S.createRange()).setStart(s.node,s.offset),E.removeAllRanges(),w>l?(E.addRange(S),E.extend(i.node,i.offset)):(S.setEnd(i.node,i.offset),E.addRange(S))))),S=[];for(E=b;E=E.parentNode;)1===E.nodeType&&S.push({element:E,left:E.scrollLeft,top:E.scrollTop});for("function"===typeof b.focus&&b.focus(),b=0;b<S.length;b++)(E=S[b]).element.scrollLeft=E.left,E.element.scrollTop=E.top}Zt=!!Ar,zr=Ar=null,e.current=n,Gl=r;do{try{for(b=e;null!==Gl;){var C=Gl.flags;if(36&C&&hl(b,Gl.alternate,Gl),128&C){S=void 0;var D=Gl.ref;if(null!==D){var R=Gl.stateNode;switch(Gl.tag){case 5:S=R;break;default:S=R}"function"===typeof D?D(S):D.current=S}}Gl=Gl.nextEffect}}catch(k){if(null===Gl)throw Error(a(330));Ls(Gl,k),Gl=Gl.nextEffect}}while(null!==Gl);Gl=null,Ho(),xl=o}else e.current=n;if($l)$l=!1,Ql=e,Kl=t;else for(Gl=r;null!==Gl;)t=Gl.nextEffect,Gl.nextEffect=null,8&Gl.flags&&((C=Gl).sibling=null,C.stateNode=null),Gl=t;if(0===(r=e.pendingLanes)&&(Zl=null),1===r?e===rs?ns++:(ns=0,rs=e):ns=0,n=n.stateNode,wo&&"function"===typeof wo.onCommitFiberRoot)try{wo.onCommitFiberRoot(Eo,n,void 0,64===(64&n.current.flags))}catch(k){}if(ps(e,Wo()),Yl)throw Yl=!1,e=Xl,Xl=null,e;return 0!==(8&xl)||Go(),null}function Ps(){for(;null!==Gl;){var e=Gl.alternate;ss||null===ls||(0!==(8&Gl.flags)?et(Gl,ls)&&(ss=!0):13===Gl.tag&&Rl(e,Gl)&&et(Gl,ls)&&(ss=!0));var t=Gl.flags;0!==(256&t)&&pl(e,Gl),0===(512&t)||$l||($l=!0,qo(97,(function(){return Is(),null}))),Gl=Gl.nextEffect}}function Is(){if(90!==Kl){var e=97<Kl?97:Kl;return Kl=90,Vo(e,_s)}return!1}function js(e,t){Jl.push(t,e),$l||($l=!0,qo(97,(function(){return Is(),null})))}function Ns(e,t){es.push(t,e),$l||($l=!0,qo(97,(function(){return Is(),null})))}function _s(){if(null===Ql)return!1;var e=Ql;if(Ql=null,0!==(48&xl))throw Error(a(331));var t=xl;xl|=32;var n=es;es=[];for(var r=0;r<n.length;r+=2){var o=n[r],i=n[r+1],l=o.destroy;if(o.destroy=void 0,"function"===typeof l)try{l()}catch(u){if(null===i)throw Error(a(330));Ls(i,u)}}for(n=Jl,Jl=[],r=0;r<n.length;r+=2){o=n[r],i=n[r+1];try{var s=o.create;o.destroy=s()}catch(u){if(null===i)throw Error(a(330));Ls(i,u)}}for(s=e.current.firstEffect;null!==s;)e=s.nextEffect,s.nextEffect=null,8&s.flags&&(s.sibling=null,s.stateNode=null),s=e;return xl=t,Go(),!0}function Hs(e,t,n){ui(e,t=ul(0,t=al(n,t),1)),t=us(),null!==(e=fs(e,1))&&(Ft(e,1,t),ps(e,t))}function Ls(e,t){if(3===e.tag)Hs(e,e,t);else for(var n=e.return;null!==n;){if(3===n.tag){Hs(n,e,t);break}if(1===n.tag){var r=n.stateNode;if("function"===typeof n.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Zl||!Zl.has(r))){var o=cl(n,e=al(t,e),1);if(ui(n,o),o=us(),null!==(n=fs(n,1)))Ft(n,1,o),ps(n,o);else if("function"===typeof r.componentDidCatch&&(null===Zl||!Zl.has(r)))try{r.componentDidCatch(t,e)}catch(i){}break}}n=n.return}}function As(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=us(),e.pingedLanes|=e.suspendedLanes&n,Ml===e&&(Il&n)===n&&(4===_l||3===_l&&(62914560&Il)===Il&&500>Wo()-Bl?Es(e,0):Ul|=n),ps(e,t)}function zs(e,t){var n=e.stateNode;null!==n&&n.delete(t),0===(t=0)&&(0===(2&(t=e.mode))?t=1:0===(4&t)?t=99===Bo()?1:2:(0===is&&(is=Ll),0===(t=Wt(62914560&~is))&&(t=4194304))),n=us(),null!==(e=fs(e,t))&&(Ft(e,t,n),ps(e,n))}function Us(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.flags=0,this.lastEffect=this.firstEffect=this.nextEffect=null,this.childLanes=this.lanes=0,this.alternate=null}function Ws(e,t,n,r){return new Us(e,t,n,r)}function Bs(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Fs(e,t){var n=e.alternate;return null===n?((n=Ws(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.nextEffect=null,n.firstEffect=null,n.lastEffect=null),n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Vs(e,t,n,r,o,i){var l=2;if(r=e,"function"===typeof e)Bs(e)&&(l=1);else if("string"===typeof e)l=5;else e:switch(e){case D:return qs(n.children,o,i,t);case H:l=8,o|=16;break;case R:l=8,o|=1;break;case k:return(e=Ws(12,n,t,8|o)).elementType=k,e.type=k,e.lanes=i,e;case M:return(e=Ws(13,n,t,o)).type=M,e.elementType=M,e.lanes=i,e;case P:return(e=Ws(19,n,t,o)).elementType=P,e.lanes=i,e;case L:return Gs(n,o,i,t);case A:return(e=Ws(24,n,t,o)).elementType=A,e.lanes=i,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case O:l=10;break e;case T:l=9;break e;case x:l=11;break e;case I:l=14;break e;case j:l=16,r=null;break e;case N:l=22;break e}throw Error(a(130,null==e?e:typeof e,""))}return(t=Ws(l,n,t,o)).elementType=e,t.type=r,t.lanes=i,t}function qs(e,t,n,r){return(e=Ws(7,e,r,t)).lanes=n,e}function Gs(e,t,n,r){return(e=Ws(23,e,r,t)).elementType=L,e.lanes=n,e}function Ys(e,t,n){return(e=Ws(6,e,null,t)).lanes=n,e}function Xs(e,t,n){return(t=Ws(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Zs(e,t,n){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.pendingContext=this.context=null,this.hydrate=n,this.callbackNode=null,this.callbackPriority=0,this.eventTimes=Bt(0),this.expirationTimes=Bt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Bt(0),this.mutableSourceEagerHydrationData=null}function $s(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:C,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}function Qs(e,t,n,r){var o=t.current,i=us(),l=cs(o);e:if(n){t:{if($e(n=n._reactInternals)!==n||1!==n.tag)throw Error(a(170));var s=n;do{switch(s.tag){case 3:s=s.stateNode.context;break t;case 1:if(vo(s.type)){s=s.stateNode.__reactInternalMemoizedMergedChildContext;break t}}s=s.return}while(null!==s);throw Error(a(171))}if(1===n.tag){var u=n.type;if(vo(u)){n=yo(n,u,s);break e}}n=s}else n=uo;return null===t.context?t.context=n:t.pendingContext=n,(t=si(i,l)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),ui(o,t),ds(o,l,i),l}function Ks(e){if(!(e=e.current).child)return null;switch(e.child.tag){case 5:default:return e.child.stateNode}}function Js(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function eu(e,t){Js(e,t),(e=e.alternate)&&Js(e,t)}function tu(e,t,n){var r=null!=n&&null!=n.hydrationOptions&&n.hydrationOptions.mutableSources||null;if(n=new Zs(e,t,null!=n&&!0===n.hydrate),t=Ws(3,null,null,2===t?7:1===t?3:0),n.current=t,t.stateNode=n,ai(t),e[Qr]=n.current,xr(8===e.nodeType?e.parentNode:e),r)for(e=0;e<r.length;e++){var o=(t=r[e])._getVersion;o=o(t._source),null==n.mutableSourceEagerHydrationData?n.mutableSourceEagerHydrationData=[t,o]:n.mutableSourceEagerHydrationData.push(t,o)}this._internalRoot=n}function nu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function ru(e,t,n,r,o){var i=n._reactRootContainer;if(i){var a=i._internalRoot;if("function"===typeof o){var l=o;o=function(){var e=Ks(a);l.call(e)}}Qs(t,a,e,o)}else{if(i=n._reactRootContainer=function(e,t){if(t||(t=!(!(t=e?9===e.nodeType?e.documentElement:e.firstChild:null)||1!==t.nodeType||!t.hasAttribute("data-reactroot"))),!t)for(var n;n=e.lastChild;)e.removeChild(n);return new tu(e,0,t?{hydrate:!0}:void 0)}(n,r),a=i._internalRoot,"function"===typeof o){var s=o;o=function(){var e=Ks(a);s.call(e)}}ys((function(){Qs(t,a,e,o)}))}return Ks(a)}function ou(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!nu(t))throw Error(a(200));return $s(e,t,null,n)}ql=function(e,t,n){var r=t.lanes;if(null!==e)if(e.memoizedProps!==t.pendingProps||fo.current)Na=!0;else{if(0===(n&r)){switch(Na=!1,t.tag){case 3:Va(t),qi();break;case 5:ji(t);break;case 1:vo(t.type)&&bo(t);break;case 4:Pi(t,t.stateNode.containerInfo);break;case 10:r=t.memoizedProps.value;var o=t.type._context;so($o,o._currentValue),o._currentValue=r;break;case 13:if(null!==t.memoizedState)return 0!==(n&t.child.childLanes)?Za(e,t,n):(so(_i,1&_i.current),null!==(t=nl(e,t,n))?t.sibling:null);so(_i,1&_i.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(64&e.flags)){if(r)return tl(e,t,n);t.flags|=64}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),so(_i,_i.current),r)break;return null;case 23:case 24:return t.lanes=0,za(e,t,n)}return nl(e,t,n)}Na=0!==(16384&e.flags)}else Na=!1;switch(t.lanes=0,t.tag){case 2:if(r=t.type,null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),e=t.pendingProps,o=ho(t,co.current),ri(t,n),o=oa(null,t,r,e,o,n),t.flags|=1,"object"===typeof o&&null!==o&&"function"===typeof o.render&&void 0===o.$$typeof){if(t.tag=1,t.memoizedState=null,t.updateQueue=null,vo(r)){var i=!0;bo(t)}else i=!1;t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,ai(t);var l=r.getDerivedStateFromProps;"function"===typeof l&&hi(t,r,l,e),o.updater=vi,t.stateNode=o,o._reactInternals=t,bi(t,r,e,n),t=Fa(null,t,r,!0,i,n)}else t.tag=0,_a(null,t,o,n),t=t.child;return t;case 16:o=t.elementType;e:{switch(null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),e=t.pendingProps,o=(i=o._init)(o._payload),t.type=o,i=t.tag=function(e){if("function"===typeof e)return Bs(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===x)return 11;if(e===I)return 14}return 2}(o),e=Zo(o,e),i){case 0:t=Wa(null,t,o,e,n);break e;case 1:t=Ba(null,t,o,e,n);break e;case 11:t=Ha(null,t,o,e,n);break e;case 14:t=La(null,t,o,Zo(o.type,e),r,n);break e}throw Error(a(306,o,""))}return t;case 0:return r=t.type,o=t.pendingProps,Wa(e,t,r,o=t.elementType===r?o:Zo(r,o),n);case 1:return r=t.type,o=t.pendingProps,Ba(e,t,r,o=t.elementType===r?o:Zo(r,o),n);case 3:if(Va(t),r=t.updateQueue,null===e||null===r)throw Error(a(282));if(r=t.pendingProps,o=null!==(o=t.memoizedState)?o.element:null,li(e,t),di(t,r,null,n),(r=t.memoizedState.element)===o)qi(),t=nl(e,t,n);else{if((i=(o=t.stateNode).hydrate)&&(Ai=qr(t.stateNode.containerInfo.firstChild),Li=t,i=zi=!0),i){if(null!=(e=o.mutableSourceEagerHydrationData))for(o=0;o<e.length;o+=2)(i=e[o])._workInProgressVersionPrimary=e[o+1],Gi.push(i);for(n=Ri(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|1024,n=n.sibling}else _a(e,t,r,n),qi();t=t.child}return t;case 5:return ji(t),null===e&&Bi(t),r=t.type,o=t.pendingProps,i=null!==e?e.memoizedProps:null,l=o.children,Wr(r,o)?l=null:null!==i&&Wr(r,i)&&(t.flags|=16),Ua(e,t),_a(e,t,l,n),t.child;case 6:return null===e&&Bi(t),null;case 13:return Za(e,t,n);case 4:return Pi(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Di(t,null,r,n):_a(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,Ha(e,t,r,o=t.elementType===r?o:Zo(r,o),n);case 7:return _a(e,t,t.pendingProps,n),t.child;case 8:case 12:return _a(e,t,t.pendingProps.children,n),t.child;case 10:e:{r=t.type._context,o=t.pendingProps,l=t.memoizedProps,i=o.value;var s=t.type._context;if(so($o,s._currentValue),s._currentValue=i,null!==l)if(s=l.value,0===(i=lr(s,i)?0:0|("function"===typeof r._calculateChangedBits?r._calculateChangedBits(s,i):1073741823))){if(l.children===o.children&&!fo.current){t=nl(e,t,n);break e}}else for(null!==(s=t.child)&&(s.return=t);null!==s;){var u=s.dependencies;if(null!==u){l=s.child;for(var c=u.firstContext;null!==c;){if(c.context===r&&0!==(c.observedBits&i)){1===s.tag&&((c=si(-1,n&-n)).tag=2,ui(s,c)),s.lanes|=n,null!==(c=s.alternate)&&(c.lanes|=n),ni(s.return,n),u.lanes|=n;break}c=c.next}}else l=10===s.tag&&s.type===t.type?null:s.child;if(null!==l)l.return=s;else for(l=s;null!==l;){if(l===t){l=null;break}if(null!==(s=l.sibling)){s.return=l.return,l=s;break}l=l.return}s=l}_a(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=(i=t.pendingProps).children,ri(t,n),r=r(o=oi(o,i.unstable_observedBits)),t.flags|=1,_a(e,t,r,n),t.child;case 14:return i=Zo(o=t.type,t.pendingProps),La(e,t,o,i=Zo(o.type,i),r,n);case 15:return Aa(e,t,t.type,t.pendingProps,r,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Zo(r,o),null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),t.tag=1,vo(r)?(e=!0,bo(t)):e=!1,ri(t,n),mi(t,r,o),bi(t,r,o,n),Fa(null,t,r,!0,e,n);case 19:return tl(e,t,n);case 23:case 24:return za(e,t,n)}throw Error(a(156,t.tag))},tu.prototype.render=function(e){Qs(e,this._internalRoot,null,null)},tu.prototype.unmount=function(){var e=this._internalRoot,t=e.containerInfo;Qs(null,e,null,(function(){t[Qr]=null}))},tt=function(e){13===e.tag&&(ds(e,4,us()),eu(e,4))},nt=function(e){13===e.tag&&(ds(e,67108864,us()),eu(e,67108864))},rt=function(e){if(13===e.tag){var t=us(),n=cs(e);ds(e,n,t),eu(e,n)}},ot=function(e,t){return t()},Oe=function(e,t,n){switch(t){case"input":if(ne(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=no(r);if(!o)throw Error(a(90));Q(r),ne(r,o)}}}break;case"textarea":ue(e,n);break;case"select":null!=(t=n.value)&&ae(e,!!n.multiple,t,!1)}},je=ms,Ne=function(e,t,n,r,o){var i=xl;xl|=4;try{return Vo(98,e.bind(null,t,n,r,o))}finally{0===(xl=i)&&(Vl(),Go())}},_e=function(){0===(49&xl)&&(function(){if(null!==ts){var e=ts;ts=null,e.forEach((function(e){e.expiredLanes|=24&e.pendingLanes,ps(e,Wo())}))}Go()}(),Is())},He=function(e,t){var n=xl;xl|=2;try{return e(t)}finally{0===(xl=n)&&(Vl(),Go())}};var iu={Events:[eo,to,no,Pe,Ie,Is,{current:!1}]},au={findFiberByHostInstance:Jr,bundleType:0,version:"17.0.1",rendererPackageName:"react-dom"},lu={bundleType:au.bundleType,version:au.version,rendererPackageName:au.rendererPackageName,rendererConfig:au.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:E.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Je(e))?null:e.stateNode},findFiberByHostInstance:au.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var su=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!su.isDisabled&&su.supportsFiber)try{Eo=su.inject(lu),wo=su}catch(ge){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=iu,t.createPortal=ou,t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(a(188));throw Error(a(268,Object.keys(e)))}return e=null===(e=Je(t))?null:e.stateNode},t.flushSync=function(e,t){var n=xl;if(0!==(48&n))return e(t);xl|=1;try{if(e)return Vo(99,e.bind(null,t))}finally{xl=n,Go()}},t.hydrate=function(e,t,n){if(!nu(t))throw Error(a(200));return ru(null,e,t,!0,n)},t.render=function(e,t,n){if(!nu(t))throw Error(a(200));return ru(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!nu(e))throw Error(a(40));return!!e._reactRootContainer&&(ys((function(){ru(null,null,e,!1,(function(){e._reactRootContainer=null,e[Qr]=null}))})),!0)},t.unstable_batchedUpdates=ms,t.unstable_createPortal=function(e,t){return ou(e,t,2<arguments.length&&void 0!==arguments[2]?arguments[2]:null)},t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!nu(n))throw Error(a(200));if(null==e||void 0===e._reactInternals)throw Error(a(38));return ru(e,t,n,!1,r)},t.version="17.0.1"},function(e,t,n){"use strict";e.exports=n(12)},function(e,t,n){"use strict";var r,o,i,a;if("object"===typeof performance&&"function"===typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var s=Date,u=s.now();t.unstable_now=function(){return s.now()-u}}if("undefined"===typeof window||"function"!==typeof MessageChannel){var c=null,d=null,f=function e(){if(null!==c)try{var n=t.unstable_now();c(!0,n),c=null}catch(r){throw setTimeout(e,0),r}};r=function(e){null!==c?setTimeout(r,0,e):(c=e,setTimeout(f,0))},o=function(e,t){d=setTimeout(e,t)},i=function(){clearTimeout(d)},t.unstable_shouldYield=function(){return!1},a=t.unstable_forceFrameRate=function(){}}else{var p=window.setTimeout,h=window.clearTimeout;if("undefined"!==typeof console){var v=window.cancelAnimationFrame;"function"!==typeof window.requestAnimationFrame&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills"),"function"!==typeof v&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills")}var g=!1,m=null,y=-1,b=5,S=0;t.unstable_shouldYield=function(){return t.unstable_now()>=S},a=function(){},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):b=0<e?Math.floor(1e3/e):5};var E=new MessageChannel,w=E.port2;E.port1.onmessage=function(){if(null!==m){var e=t.unstable_now();S=e+b;try{m(!0,e)?w.postMessage(null):(g=!1,m=null)}catch(n){throw w.postMessage(null),n}}else g=!1},r=function(e){m=e,g||(g=!0,w.postMessage(null))},o=function(e,n){y=p((function(){e(t.unstable_now())}),n)},i=function(){h(y),y=-1}}function C(e,t){var n=e.length;e.push(t);e:for(;;){var r=n-1>>>1,o=e[r];if(!(void 0!==o&&0<k(o,t)))break e;e[r]=t,e[n]=o,n=r}}function D(e){return void 0===(e=e[0])?null:e}function R(e){var t=e[0];if(void 0!==t){var n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length;r<o;){var i=2*(r+1)-1,a=e[i],l=i+1,s=e[l];if(void 0!==a&&0>k(a,n))void 0!==s&&0>k(s,a)?(e[r]=s,e[l]=n,r=l):(e[r]=a,e[i]=n,r=i);else{if(!(void 0!==s&&0>k(s,n)))break e;e[r]=s,e[l]=n,r=l}}}return t}return null}function k(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}var O=[],T=[],x=1,M=null,P=3,I=!1,j=!1,N=!1;function _(e){for(var t=D(T);null!==t;){if(null===t.callback)R(T);else{if(!(t.startTime<=e))break;R(T),t.sortIndex=t.expirationTime,C(O,t)}t=D(T)}}function H(e){if(N=!1,_(e),!j)if(null!==D(O))j=!0,r(L);else{var t=D(T);null!==t&&o(H,t.startTime-e)}}function L(e,n){j=!1,N&&(N=!1,i()),I=!0;var r=P;try{for(_(n),M=D(O);null!==M&&(!(M.expirationTime>n)||e&&!t.unstable_shouldYield());){var a=M.callback;if("function"===typeof a){M.callback=null,P=M.priorityLevel;var l=a(M.expirationTime<=n);n=t.unstable_now(),"function"===typeof l?M.callback=l:M===D(O)&&R(O),_(n)}else R(O);M=D(O)}if(null!==M)var s=!0;else{var u=D(T);null!==u&&o(H,u.startTime-n),s=!1}return s}finally{M=null,P=r,I=!1}}var A=a;t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){j||I||(j=!0,r(L))},t.unstable_getCurrentPriorityLevel=function(){return P},t.unstable_getFirstCallbackNode=function(){return D(O)},t.unstable_next=function(e){switch(P){case 1:case 2:case 3:var t=3;break;default:t=P}var n=P;P=t;try{return e()}finally{P=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=A,t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=P;P=e;try{return t()}finally{P=n}},t.unstable_scheduleCallback=function(e,n,a){var l=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?l+a:l:a=l,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:x++,callback:n,priorityLevel:e,startTime:a,expirationTime:s=a+s,sortIndex:-1},a>l?(e.sortIndex=a,C(T,e),null===D(O)&&e===D(T)&&(N?i():N=!0,o(H,a-l))):(e.sortIndex=s,C(O,e),j||I||(j=!0,r(L))),e},t.unstable_wrapCallback=function(e){var t=P;return function(){var n=P;P=t;try{return e.apply(this,arguments)}finally{P=n}}}},,,function(e,t,n){},function(e,t,n){},function(e,t,n){}]]);
//# sourceMappingURL=2.f0d2e34d.chunk.js.map