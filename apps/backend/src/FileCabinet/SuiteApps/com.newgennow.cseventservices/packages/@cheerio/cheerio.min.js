var parse=require("./parse"),isHtml=require("./utils").isHtml,_={extend:require("lodash.assignin"),bind:require("lodash.bind"),forEach:require("lodash.foreach"),defaults:require("lodash.defaults")},api=[require("./api/attributes"),require("./api/traversing"),require("./api/manipulation"),require("./api/css"),require("./api/forms")],Cheerio=module.exports=function(e,i,t,r){return this instanceof Cheerio?(this.options=_.defaults(r||{},this.options),e?(t&&("string"==typeof t&&(t=parse(t,this.options)),this._root=Cheerio.call(this,t)),e.cheerio?e:(isNode(e)&&(e=[e]),Array.isArray(e)?(_.forEach(e,_.bind(function(e,i){this[i]=e},this)),this.length=e.length,this):"string"==typeof e&&isHtml(e)?Cheerio.call(this,parse(e,this.options).children):(i?"string"==typeof i?i=isHtml(i)?(i=parse(i,this.options),Cheerio.call(this,i)):(e=[i,e].join(" "),this._root):i.cheerio||(i=Cheerio.call(this,i)):i=this._root,i?i.find(e):this))):this):new Cheerio(e,i,t,r)};_.extend(Cheerio,require("./static")),Cheerio.prototype.cheerio="[cheerio object]",Cheerio.prototype.options={withDomLvl1:!0,normalizeWhitespace:!1,xmlMode:!1,decodeEntities:!0},Cheerio.prototype.length=0,Cheerio.prototype.splice=Array.prototype.splice,Cheerio.prototype._make=function(e,i){var t=new this.constructor(e,i,this._root,this.options);return t.prevObject=this,t},Cheerio.prototype.toArray=function(){return this.get()},api.forEach(function(e){_.extend(Cheerio.prototype,e)});var isNode=function(e){return e.name||"text"===e.type||"comment"===e.type};