/**
 * @NApiVersion 2.1
 * @NModuleScope SameAccount
 */
define([
  "N/query",
  "N/record",
  "N/runtime",
  "N/search",
  "N/url",
  "N/encode",
  "N/file",
  "N/compress",
], /**
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{url} url
 * @param{encode} encode
 * @param{file} file
 * @param{compress} compress
 */ function (query, record, runtime, search, url, encode, file, compress) {
  // Keys taken from the N/format Module - format.Type
  // type mapping decided manually by comparing to the closest html input type
  // webDescriptions for display
  // validations are any additional validations a field may need
  // https://tstdrv2274238.app.netsuite.com/app/help/helpcenter.nl?fid=section_4388844232.html
  const NETSUITE_FIELD_MAPPING = {
    ADDRESS: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    CCEXPDATE: {
      type: "date",
      validations: ["date"],
      defaultValue: "",
    },
    CCNUMBER: {
      type: "text",
      validations: ["creditCard"],
      defaultValue: "",
    },
    CCVALIDFROM: {
      type: "date",
      validations: ["date"],
      defaultValue: "",
    },
    CHECKBOX: {
      type: "checkbox",
      validations: ["boolean"],
      defaultValue: false,
    },
    CLOBTEXT: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    COLOR: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    CURRENCY: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    CURRENCY2: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    DATE: {
      type: "date",
      validations: ["date"],
      defaultValue: "",
    },
    DATETIME: {
      type: "datetime",
      validations: ["datetime"],
      defaultValue: "",
    },
    DATETIMETZ: {
      type: "datetime",
      validations: ["datetime"],
      defaultValue: "",
    },
    DOCUMENT: {
      type: "file",
      validations: [],
      defaultValue: null,
    },
    DYNAMICPRECISION: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    EMAIL: {
      type: "email",
      validations: ["email"],
      defaultValue: "",
    },
    EMAILS: {
      type: "email",
      validations: ["email"],
      defaultValue: "",
    },
    FLOAT: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    FULLPHONE: {
      type: "text",
      validations: ["phone"],
      defaultValue: "",
    },
    FUNCTION: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    FURIGANA: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    IDENTIFIER: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    IDENTIFIERANYCASE: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    INTEGER: {
      type: "number",
      validations: ["integer"],
      defaultValue: "",
    },
    MULTISELECT: {
      type: "select",
      validations: [],
      defaultValue: [],
    },
    MMYYDATE: {
      type: "text",
      validations: ["date"],
      defaultValue: "",
    },
    NONNEGCURRENCY: {
      type: "number",
      validations: ["positive"],
      defaultValue: "",
    },
    NONNEGFLOAT: {
      type: "number",
      validations: ["positive"],
      defaultValue: "",
    },
    PACKAGE: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    PERCENT: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    PHONE: {
      type: "phone",
      validations: ["phone"],
      defaultValue: "",
    },
    POSCURRENCY: {
      type: "number",
      validations: ["positive"],
      defaultValue: "",
    },
    POSFLOAT: {
      type: "number",
      validations: ["positive"],
      defaultValue: "",
    },
    POSINTEGER: {
      type: "number",
      validations: ["positive"],
      defaultValue: "",
    },
    QUOTEDFUNCTION: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    RICHTEXT: {
      type: "richText",
      validations: [],
      defaultValue: "",
    },
    RADIO: {
      type: "radio",
      validations: [],
      defaultValue: "",
    },
    RATE: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    RATEHIGHPRECISION: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    SELECT: {
      type: "select",
      validations: [],
      defaultValue: "",
    },
    TEXT: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    TEXTAREA: {
      type: "textarea",
      validations: [],
      defaultValue: "",
    },
    TIME: {
      type: "time",
      validations: ["time"],
      defaultValue: "",
    },
    TIMEOFDAY: {
      type: "time",
      validations: ["time"],
      defaultValue: "",
    },
    TIMETRACK: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    URL: {
      type: "url",
      validations: ["url"],
      defaultValue: "",
    },
  };

  /**
   * Processes a paged search and applies a callback to each result
   * @param {search.Result[]} searchObj - The search object to process
   * @param {Function} callback - Function to call for each result
   */
  const getAllResultsFor = (searchObj, callback) => {
    const myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach((pageRange) => {
      const myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(callback);
    });
  };

  /**
   * Formats a date string to a timestamp in seconds
   * @param {string} dateString - Date string to format
   * @returns {number|null} Timestamp in seconds or null if invalid
   */
  const formatDateToTimestamp = (dateString) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.getTime() ? Math.floor(date.getTime() / 1000) : null;
  };

  /**
   * Gets the current domain URL
   * @returns {string} The application domain URL
   */
  const getDomainUrl = () => {
    return url.resolveDomain({ hostType: url.HostType.APPLICATION });
  };

  /**
   * Generates a print URL for a transaction
   * @param {string} baseUrl - The base domain URL
   * @param {string} templateId - The print template ID
   * @param {string} transactionId - The transaction ID
   * @param {string} type - The transaction type (e.g., 'salesord', 'custinvc')
   * @param {string} label - The label for the printout
   * @returns {string} The generated print URL
   */
  const generatePrintUrl = (
    baseUrl,
    templateId,
    transactionId,
    type,
    label
  ) => {
    return (
      `https://${baseUrl}/app/accounting/print/hotprint.nl?` +
      `regular=T&sethotprinter=T&formnumber=${templateId}&` +
      `trantype=${type}&id=${transactionId}&label=${encodeURIComponent(label)}`
    );
  };

  /**
   * Safely escapes a string for use in SQL queries
   * @param {string} str - The string to escape
   * @returns {string} The escaped string
   */
  const escapeSqlString = (str) => {
    if (!str) return "";
    return str.replace(/'/g, "''");
  };


  /**
   * Processes a search result to a simplified object
   * @param {search.Result} result - The search result to process
   * @param {Object} fieldMap - Mapping of field names to result properties
   * @returns {Object} Processed result object
   */
  const processSearchResult = (result, fieldMap) => {
    const processed = { id: result.id };

    Object.entries(fieldMap).forEach(([key, fieldDef]) => {
      if (typeof fieldDef === "string") {
        processed[key] = result.getValue(fieldDef);
      } else if (fieldDef.getText) {
        processed[key] = result.getText(fieldDef.getText);
      } else if (fieldDef.getValue) {
        processed[key] = result.getValue(fieldDef.getValue);
      } else if (fieldDef.join) {
        processed[key] = result.getValue({
          name: fieldDef.field,
          join: fieldDef.join,
        });
      }
    });

    return processed;
  };

  /**
   * Gets a state record by name or short name
   * @param {string} stateInput - The state name or short name
   * @returns {Object} The state record
   */
  const getState = (stateInput) => {
    return query
      .runSuiteQL({
        query: `
              SELECT
                id,
                shortname,
                fullname
              FROM
                state
              WHERE
                shortname = '${stateInput}'
                OR fullname like '%${stateInput}%'
            `,
      })
      .asMappedResults()[0];
  };

  /*  *
   * Gets a country record by name or id
   * @param {string} countryInput - The country name or id
   * @returns {Object} The country record
   */
  const getCountry = (countryInput) => {
    return query
      .runSuiteQL({
        query: `
              SELECT
                uniquekey,
                id,
                name
              FROM
                country
              WHERE
                id = '${countryInput}'
                OR name like '%${countryInput}%'
            `,
      })
      .asMappedResults()[0];
  };

  /**
   * Transforms a value back into a format that Netsuite will accept
   * @param {string} netsuiteFieldType
   * @param {boolean|number|string|null} value
   * @returns {number|string|null|{onRequest: onRequest}|*|Date|Date|boolean}
   */
  const transformCustomFieldValue = (netsuiteFieldType, value) => {
    const fieldType =
      NETSUITE_FIELD_MAPPING[netsuiteFieldType.toUpperCase()].type;
    switch (fieldType) {
      case "email":
      case "phone":
      case "radio":
      case "richText":
      case "text":
      case "textarea":
      case "url":
        return String(value || "");
      case "checkbox":
        if (typeof value === "string") {
          if (value === "F" || value === "false") return false;
          return value === "T" || value === "true";
        }
        return Boolean(value || false);
      case "date":
      case "datetime":
      case "time":
        // Handle empty/null/undefined values for date fields
        if (
          !value ||
          value === "" ||
          value === "undefined" ||
          value === "null"
        ) {
          return null;
        }
        // If already a Date object, return as-is
        if (value instanceof Date) {
          return value;
        }
        // Try to parse the value as a date
        try {
          const parsedDate = new Date(value);
          // Check if the parsed date is valid
          if (isNaN(parsedDate.getTime())) {
            return null;
          }
          return parsedDate;
        } catch (error) {
          // If parsing fails, return null
          return null;
        }
      case "file":
        return value || null;
      case "number":
        return Number(value || 0);
      case "select":
        return Array.isArray(value) ? value : String(value || "");
      default:
        return value;
    }
  };

  /**
   * Takes a field selection saved in a PortalGen Form record jsonData and converts
   * it to formik field props to render in a formik form
   *
   * NOTE on select fields: This fn does not add in options for select or radio fields
   * @param {Object} field
   * @param {string} field.id
   * @param {string} field.fieldHelp
   * @param {number} field.group
   * @param {string} field.label
   * @param {boolean} field.required
   * @param {string} field.type
   * @returns {Object} formikFieldProps - Field props to render in a formik field
   */
  const fieldToFormikFieldProps = (field) => {
    const netsuiteFieldType = field.file
      ? "DOCUMENT"
      : field.type.toUpperCase();
    const fieldDefaults = NETSUITE_FIELD_MAPPING[netsuiteFieldType];

    const validations = fieldDefaults.validations.slice();
    if (field.required) {
      validations.push("required");
    }

    return {
      // Certain field help may have html in it. We want to encode this to prevent
      // request injections
      fieldHelp: encode.convert({
        string: field.fieldHelp,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
      // Coerce empty string to null if no group specified, since the group key
      // tells us how to group fields in the front end and Object.groupBy
      // will sort nulls at the end which is what we want
      group: field.group || null,
      label: field.label,
      name: field.id,
      type: fieldDefaults.type,
      validations,
      value: fieldDefaults.defaultValue,
      ...(netsuiteFieldType === "MULTISELECT" && { multiple: true }),
    };
  };

  /**
   * Retrieves the custom form for the specified formId
   * Also retrieves the values for the fields from the salesRecord if one is specified
   * @param {number} formId
   * @param {record} record
   * @param {number} entity
   * @param {Record} projectRecord
   * @return {object} customFieldsObject
   */
  const getCustomFormFields = (
    formId,
    record,
    entity,
    projectRecord = null
  ) => {
    const portalGenForm = record.load({
      type: "customrecord_ng_eh_cs_project_form",
      id: formId,
    });

    const formJson = portalGenForm.getValue({
      fieldId: "custrecord_project_form_json",
    });

    const {
      customForm,
      formFields: rawFormFields,
      formGroups,
      formLayout,
    } = JSON.parse(formJson);

    const projectForReference = record.create({
      type: record.Type.JOB,
      isDynamic: true,
      defaultValues: { parent: entity },
    });

    log.debug("ARG: Project Record Loaded:", projectRecord);

    const fieldsRequiringOptions = ["RADIO", "MULTISELECT", "SELECT"];

    const formFields = rawFormFields.map((formField) => {
      const needsOptions = fieldsRequiringOptions.includes(
        formField.type.toUpperCase()
      );

      let fieldValue = null;
      let fileUrl = null;
      // Special handling for different field types to ensure proper formatting
      const fieldType = formField.type.toUpperCase();

      // Get the raw value from the project record if it exists
      if (projectRecord) {
        if (formField.file) {
          // For file fields, get both the filename (text) and file ID (value)
          fieldValue = projectRecord.getText({
            fieldId: formField.id,
          });

          const fileId = projectRecord.getValue({
            fieldId: formField.id,
          });

          // Get the file URL if we have a file ID
          if (fileId) {
            try {
              const domainUrl = getDomainUrl();
              const fileInfo = search.lookupFields({
                type: "file",
                id: fileId,
                columns: ["url"],
              });

              fileUrl = `https://${domainUrl}${fileInfo.url}`;

              log.debug("File URL generated", {
                fieldId: formField.id,
                fileId: fileId,
                fileName: fieldValue,
                fileUrl: fileUrl,
              });
            } catch (error) {
              log.error("Error getting file URL", {
                fieldId: formField.id,
                fileId: fileId,
                error: error.toString(),
              });
            }
          }
        } else {
          fieldValue = projectRecord.getValue({
            fieldId: formField.id,
          });
        }

        if (
          fieldValue !== null &&
          fieldValue !== undefined &&
          fieldValue !== ""
        ) {
          log.debug("Processing field value", {
            fieldId: formField.id,
            fieldType: fieldType,
            originalValue: fieldValue,
            valueType: typeof fieldValue,
          });

          switch (fieldType) {
            case "TIME":
            case "TIMEOFDAY":
              // Ensure time fields return a valid Date object or ISO string
              if (fieldValue instanceof Date) {
                // If it's already a Date, convert to ISO string for consistent frontend handling
                fieldValue = fieldValue.toISOString();
                log.debug("Time field converted from Date to ISO", {
                  fieldId: formField.id,
                  value: fieldValue,
                });
              } else if (typeof fieldValue === "string") {
                // If it's a string, try to parse it as a date and convert to ISO
                try {
                  const parsedDate = new Date(fieldValue);
                  if (!isNaN(parsedDate.getTime())) {
                    fieldValue = parsedDate.toISOString();
                    log.debug("Time field converted from string to ISO", {
                      fieldId: formField.id,
                      originalValue: fieldValue,
                      convertedValue: fieldValue,
                    });
                  }
                } catch (e) {
                  log.debug("Time field parsing error", {
                    fieldId: formField.id,
                    value: fieldValue,
                    error: e,
                  });
                }
              }
              break;

            case "SELECT":
              // For single select, ensure we have a string value
              if (typeof fieldValue !== "string") {
                fieldValue = String(fieldValue);
              }
              log.debug("Select field processed", {
                fieldId: formField.id,
                value: fieldValue,
              });
              break;

            case "MULTISELECT":
              // For multi-select, ensure we have an array of string values
              const originalMultiValue = fieldValue;
              if (typeof fieldValue === "string") {
                // Split comma-separated values into array
                fieldValue = fieldValue.includes(",")
                  ? fieldValue.split(",").map((v) => v.trim())
                  : [fieldValue];
              } else if (Array.isArray(fieldValue)) {
                // Ensure all values are strings
                fieldValue = fieldValue.map((v) => String(v));
              } else {
                // Convert single value to array
                fieldValue = [String(fieldValue)];
              }
              log.debug("Multi-select field processed", {
                fieldId: formField.id,
                originalValue: originalMultiValue,
                processedValue: fieldValue,
              });
              break;
          }
        }
      }

      const fieldProps = {
        ...fieldToFormikFieldProps(formField),
        ...(needsOptions && {
          options: projectForReference
            .getField({ fieldId: formField.id })
            .getSelectOptions(),
        }),
        ...(projectRecord &&
          fieldValue !== null &&
          fieldValue !== undefined &&
          fieldValue !== "" && {
            value: fieldValue,
          }),
      };

      // Add file URL for file type fields
      if (formField.file && fileUrl) {
        fieldProps.fileUrl = fileUrl;
      }

      return fieldProps;
    });

    return {
      customForm,
      formFields,
      formGroups,
      formLayout,
      formId,
    };
  };

  /* ------------------------
   * SEVER EXCLUSIVE FUNCTIONS
   * ------------------------ */

  /**
   * Gets the URL of a file
   * @param {number} fileId - The internal ID of the file
   * @returns {string} The URL of the file
   */
  const getFileInfoUrl = (fileId) => {
    let fileInfo = file.load({
      id: fileId,
    });
    return fileInfo.url;
  };

  /**
   * Searches for a folder by name
   * @param {string} folderName - The name of the folder to search for
   * @returns {Object|null} The folder search result or null if not found
   */
  const folderSearch = (folderName) => {
    try {
      const results = search
        .create({
          type: "folder",
          filters: [["name", "startswith", folderName]],
        })
        .run()
        .getRange(0, 1);

      return results.length > 0 ? results[0] : null;
    } catch (error) {
      log.error("Error searching for folder", {
        folderName,
        error: error.toString(),
      });
      return null;
    }
  };

  /**
   * Ensures a folder structure exists, creating it if necessary
   * @param {string} projectId - The project ID
   * @param {string} projectName - The project name
   * @returns {Object} Object containing the folder ID and creation status
   */
  const ensureProjectFolderStructure = (projectId, projectName) => {
    const folderName = `${projectId} - ${projectName}`.slice(0, 98);

    log.debug("📁 Ensuring folder structure", { projectId, projectName });

    // Check for root "Project Files" folder
    let projectFilesFolder = folderSearch("Project Files");
    let rootFolderId;

    if (!projectFilesFolder) {
      log.audit('Creating root "Project Files" folder');

      const newRootFolder = record.create({
        type: record.Type.FOLDER,
        isDynamic: true,
      });
      newRootFolder.setValue({
        fieldId: "name",
        value: "Project Files",
      });

      rootFolderId = newRootFolder.save({
        enableSourcing: true,
        ignoreMandatoryFields: true,
      });

      log.audit("Root folder created", { rootFolderId });
    } else {
      rootFolderId = projectFilesFolder.id;
    }

    // Check for project-specific subfolder
    let projectSubfolder = folderSearch(folderName);
    let projectFolderId;

    if (!projectSubfolder) {
      log.audit("Creating project subfolder", { folderName });

      const newProjectFolder = record.create({
        type: record.Type.FOLDER,
        isDynamic: true,
      });
      newProjectFolder.setValue({
        fieldId: "name",
        value: folderName,
      });
      newProjectFolder.setValue({
        fieldId: "parent",
        value: rootFolderId,
      });

      projectFolderId = newProjectFolder.save({
        enableSourcing: true,
        ignoreMandatoryFields: true,
      });

      log.audit("Project folder created", { projectFolderId, folderName });
    } else {
      projectFolderId = projectSubfolder.id;
    }

    return {
      folderId: projectFolderId,
      rootFolderId: rootFolderId,
      wasCreated: !projectSubfolder,
    };
  };

  /**
   * Creates a file with automatic compression fallback on failure
   * @param {Object} fileConfig - Configuration for file creation
   * @param {string} fileConfig.name - File name
   * @param {string} fileConfig.fileType - File type
   * @param {string} fileConfig.contents - File contents
   * @param {number} fileConfig.folderId - Folder ID where file will be saved
   * @returns {Object} Object with fileId and compression status
   */
  const createFileWithCompressionFallback = (fileConfig) => {
    const { name: fileName, fileType, contents, folderId } = fileConfig;

    try {
      // Attempt to create the file normally
      log.debug("Creating file", { fileName, fileType, folderId });

      const fileObj = file.create({
        name: fileName,
        fileType: fileType,
        contents: contents,
        folder: folderId,
        isOnline: true,
      });

      const fileId = fileObj.save();

      log.debug("File created successfully", { fileId, fileName });

      return {
        fileId: fileId,
        compressed: false,
        error: null,
      };
    } catch (originalError) {
      log.error("Failed to create file, attempting compression", {
        fileName,
        error: originalError.toString(),
      });

      try {
        // Create a compressed version of the file
        const compressedFileName = `${fileName}.gz`;

        // Compress the contents
        const compressedContents = compress.gzip({
          input: contents,
          name: fileName,
        });

        // Create the compressed file
        const compressedFileObj = file.create({
          name: compressedFileName,
          fileType: file.Type.GZIP,
          contents: compressedContents,
          folder: folderId,
          isOnline: true,
        });

        const compressedFileId = compressedFileObj.save();

        log.audit("File compressed and saved successfully", {
          originalName: fileName,
          compressedName: compressedFileName,
          fileId: compressedFileId,
        });

        return {
          fileId: compressedFileId,
          compressed: true,
          originalName: fileName,
          compressedName: compressedFileName,
          error: null,
        };
      } catch (compressionError) {
        log.error("Failed to create compressed file", {
          fileName,
          originalError: originalError.toString(),
          compressionError: compressionError.toString(),
        });

        return {
          fileId: null,
          compressed: false,
          error: {
            original: originalError.toString(),
            compression: compressionError.toString(),
          },
        };
      }
    }
  };

  /**
   * Processes a single file for attachment to a project
   * @param {Object} inputFileObj - The file object from the request
   * @param {number} folderId - The folder ID where the file should be saved
   * @param {boolean} includeFileNameInOutput - Whether to include file name in the output
   * @returns {Object|number|null} File info object, file ID, or null on failure
   */
  const processFileForProject = (
    inputFileObj,
    folderId,
    includeFileNameInOutput = false
  ) => {
    log.debug("Processing file", { fileName: inputFileObj.name });

    const filename = inputFileObj.name;
    let file_type = inputFileObj.fileType;
    const extension =
      filename.substring(filename.lastIndexOf(".") + 1, filename.length) ||
      filename;

    // Special handling for GLTF files
    if (inputFileObj.fileType === "MISCBINARY" && extension === "gltf") {
      file_type = file.Type.JSON;
    }

    const fileResult = createFileWithCompressionFallback({
      name: filename,
      fileType: file_type,
      contents: inputFileObj.getContents(),
      folderId: folderId,
    });

    if (fileResult.fileId) {
      if (fileResult.compressed) {
        log.audit("File was compressed", {
          originalName: fileResult.originalName,
          compressedName: fileResult.compressedName,
        });
      }

      if (includeFileNameInOutput) {
        return {
          name: fileResult.compressed ? fileResult.compressedName : filename,
          id: fileResult.fileId,
          compressed: fileResult.compressed,
        };
      }
      return fileResult.fileId;
    }
    log.error("Failed to process file", {
      fileName: filename,
      error: fileResult.error,
    });
    return null;
  };

  const addAndAttachFilesToProject = (
    projectRecord,
    files,
    includeFileNameInOutput = false,
    fieldName = null
  ) => {
    // Validate inputs
    if (!projectRecord) {
      throw new Error("Project record is required");
    }

    if (!files || Object.keys(files).length === 0) {
      log.audit("No files to attach", { projectId: projectRecord.id });
      return [];
    }

    log.audit("📎 Starting file attachment process", {
      projectId: projectRecord.id,
      fileCount: Object.keys(files).length,
      fieldName: fieldName || "N/A",
    });

    try {
      const projectName = projectRecord.getValue("companyname");
      const projectId = projectRecord.id;

      // Ensure folder structure exists
      const folderInfo = ensureProjectFolderStructure(projectId, projectName);

      log.audit("📁 Folder structure ready", {
        folderId: folderInfo.folderId,
        wasCreated: folderInfo.wasCreated,
      });

      // Process and attach files
      const fileResults = [];
      const failedFiles = [];

      Object.values(files).forEach((inputFileObj) => {
        try {
          const fileResult = processFileForProject(
            inputFileObj,
            folderInfo.folderId,
            includeFileNameInOutput
          );

          if (fileResult) {
            fileResults.push(fileResult);
          } else {
            failedFiles.push({
              name: inputFileObj.name,
              reason: "Failed to create file",
            });
          }
        } catch (error) {
          log.error("Error processing file", {
            fileName: inputFileObj.name,
            error: error.toString(),
          });
          failedFiles.push({
            name: inputFileObj.name,
            reason: error.toString(),
          });
        }
      });

      // Attach successful files to the project
      const attachedFiles = [];
      fileResults.forEach((fileInfo) => {
        try {
          const fileId = includeFileNameInOutput ? fileInfo.id : fileInfo;

          record.attach({
            record: {
              type: "file",
              id: fileId,
            },
            to: {
              type: "job",
              id: projectId,
            },
          });

          attachedFiles.push(fileInfo);

          log.debug("File attached to project", {
            fileId: fileId,
            projectId: projectId,
          });
        } catch (error) {
          log.error("Failed to attach file to project", {
            fileId: includeFileNameInOutput ? fileInfo.id : fileInfo,
            projectId: projectId,
            error: error.toString(),
          });
          failedFiles.push({
            name: includeFileNameInOutput ? fileInfo.name : "Unknown",
            reason: `Attachment failed: ${error.toString()}`,
          });
        }
      });

      // Log summary
      log.audit("✅ File attachment process completed", {
        projectId: projectId,
        totalFiles: Object.keys(files).length,
        successfulFiles: attachedFiles.length,
        failedFiles: failedFiles.length,
        failedFileDetails: failedFiles,
      });

      // Return attached files array
      return attachedFiles;
    } catch (error) {
      log.error("Critical error in file attachment process", {
        projectId: projectRecord.id,
        error: error.toString(),
        stack: error.stack,
      });
      throw error;
    }
  };

  return {
    getAllResultsFor,
    formatDateToTimestamp,
    getDomainUrl,
    generatePrintUrl,
    processSearchResult,
    transformCustomFieldValue,
    fieldToFormikFieldProps,
    getCustomFormFields,
    getState,
    getCountry,
    escapeSqlString,
    NETSUITE_FIELD_MAPPING,
    // Server Exclusive
    getFileInfoUrl,
    addAndAttachFilesToProject,
    // New file handling utilities
    folderSearch,
    ensureProjectFolderStructure,
    createFileWithCompressionFallback,
    processFileForProject,
  };
});
