/**
 * @NApiVersion 2.1
 * @NModuleScope SameAccount
 */
define(["N/query", "N/record", "N/runtime", "N/search", "N/url"]
/**
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{url} url
 */, function (query, record, runtime, search, url) {
  // Keys taken from the N/format Module - format.Type
  // type mapping decided manually by comparing to the closest html input type
  // webDescriptions for display
  // validations are any additional validations a field may need
  // https://tstdrv2274238.app.netsuite.com/app/help/helpcenter.nl?fid=section_4388844232.html
  const NETSUITE_FIELD_MAPPING = {
    ADDRESS: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    CCEXPDATE: {
      type: "date",
      validations: ["date"],
      defaultValue: "",
    },
    CCNUMBER: {
      type: "text",
      validations: ["creditCard"],
      defaultValue: "",
    },
    CCVALIDFROM: {
      type: "date",
      validations: ["date"],
      defaultValue: "",
    },
    CHECKBOX: {
      type: "checkbox",
      validations: ["boolean"],
      defaultValue: false,
    },
    CLOBTEXT: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    COLOR: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    CURRENCY: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    CURRENCY2: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    DATE: {
      type: "date",
      validations: ["date"],
      defaultValue: "",
    },
    DATETIME: {
      type: "datetime",
      validations: ["datetime"],
      defaultValue: "",
    },
    DATETIMETZ: {
      type: "datetime",
      validations: ["datetime"],
      defaultValue: "",
    },
    DOCUMENT: {
      type: "file",
      validations: [],
      defaultValue: null,
    },
    DYNAMICPRECISION: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    EMAIL: {
      type: "email",
      validations: ["email"],
      defaultValue: "",
    },
    EMAILS: {
      type: "email",
      validations: ["email"],
      defaultValue: "",
    },
    FLOAT: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    FULLPHONE: {
      type: "text",
      validations: ["phone"],
      defaultValue: "",
    },
    FUNCTION: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    FURIGANA: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    IDENTIFIER: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    IDENTIFIERANYCASE: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    INTEGER: {
      type: "number",
      validations: ["integer"],
      defaultValue: "",
    },
    MULTISELECT: {
      type: "select",
      validations: [],
      defaultValue: [],
    },
    MMYYDATE: {
      type: "text",
      validations: ["date"],
      defaultValue: "",
    },
    NONNEGCURRENCY: {
      type: "number",
      validations: ["positive"],
      defaultValue: "",
    },
    NONNEGFLOAT: {
      type: "number",
      validations: ["positive"],
      defaultValue: "",
    },
    PACKAGE: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    PERCENT: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    PHONE: {
      type: "phone",
      validations: ["phone"],
      defaultValue: "",
    },
    POSCURRENCY: {
      type: "number",
      validations: ["positive"],
      defaultValue: "",
    },
    POSFLOAT: {
      type: "number",
      validations: ["positive"],
      defaultValue: "",
    },
    POSINTEGER: {
      type: "number",
      validations: ["positive"],
      defaultValue: "",
    },
    QUOTEDFUNCTION: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    RICHTEXT: {
      type: "richText",
      validations: [],
      defaultValue: "",
    },
    RADIO: {
      type: "radio",
      validations: [],
      defaultValue: "",
    },
    RATE: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    RATEHIGHPRECISION: {
      type: "number",
      validations: ["number"],
      defaultValue: "",
    },
    SELECT: {
      type: "select",
      validations: [],
      defaultValue: "",
    },
    TEXT: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    TEXTAREA: {
      type: "textarea",
      validations: [],
      defaultValue: "",
    },
    TIME: {
      type: "time",
      validations: ["time"],
      defaultValue: "",
    },
    TIMEOFDAY: {
      type: "time",
      validations: ["time"],
      defaultValue: "",
    },
    TIMETRACK: {
      type: "text",
      validations: [],
      defaultValue: "",
    },
    URL: {
      type: "url",
      validations: ["url"],
      defaultValue: "",
    },
  };

  /**
   * Processes a paged search and applies a callback to each result
   * @param {search.Result[]} searchObj - The search object to process
   * @param {Function} callback - Function to call for each result
   */
  const getAllResultsFor = (searchObj, callback) => {
    const myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach((pageRange) => {
      const myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(callback);
    });
  };

  /**
   * Formats a date string to a timestamp in seconds
   * @param {string} dateString - Date string to format
   * @returns {number|null} Timestamp in seconds or null if invalid
   */
  const formatDateToTimestamp = (dateString) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.getTime() ? Math.floor(date.getTime() / 1000) : null;
  };

  /**
   * Gets the current domain URL
   * @returns {string} The application domain URL
   */
  const getDomainUrl = () => {
    return url.resolveDomain({ hostType: url.HostType.APPLICATION });
  };

  /**
   * Generates a print URL for a transaction
   * @param {string} baseUrl - The base domain URL
   * @param {string} templateId - The print template ID
   * @param {string} transactionId - The transaction ID
   * @param {string} type - The transaction type (e.g., 'salesord', 'custinvc')
   * @param {string} label - The label for the printout
   * @returns {string} The generated print URL
   */
  const generatePrintUrl = (
    baseUrl,
    templateId,
    transactionId,
    type,
    label
  ) => {
    return (
      `https://${baseUrl}/app/accounting/print/hotprint.nl?` +
      `regular=T&sethotprinter=T&formnumber=${templateId}&` +
      `trantype=${type}&id=${transactionId}&label=${encodeURIComponent(label)}`
    );
  };

  /**
   * Safely escapes a string for use in SQL queries
   * @param {string} str - The string to escape
   * @returns {string} The escaped string
   */
  const escapeSqlString = (str) => {
    if (!str) return "";
    return str.replace(/'/g, "''");
  };

  /**
   * Gets user permission levels from contact
   * @param {number} contactId - The contact internal ID
   * @returns {string[]} Array of permission level IDs
   */
  const getUserPermissionLevels = (contactId) => {
    if (!contactId) return [];

    try {
      const result = query
        .runSuiteQL({
          query: `
                    SELECT custentity_ng_eh_portal_perm_level 
                    FROM CONTACT 
                    WHERE ID = ?
                `,
          params: [contactId],
        })
        .asMappedResults()[0];

      const permissionLevel = result?.custentity_ng_eh_portal_perm_level;
      return permissionLevel
        ? permissionLevel.split(",").map((l) => l.trim())
        : [];
    } catch (e) {
      log.error({ title: "Error getting user permissions", details: e });
      return [];
    }
  };

  /**
   * Processes a search result to a simplified object
   * @param {search.Result} result - The search result to process
   * @param {Object} fieldMap - Mapping of field names to result properties
   * @returns {Object} Processed result object
   */
  const processSearchResult = (result, fieldMap) => {
    const processed = { id: result.id };

    Object.entries(fieldMap).forEach(([key, fieldDef]) => {
      if (typeof fieldDef === "string") {
        processed[key] = result.getValue(fieldDef);
      } else if (fieldDef.getText) {
        processed[key] = result.getText(fieldDef.getText);
      } else if (fieldDef.getValue) {
        processed[key] = result.getValue(fieldDef.getValue);
      } else if (fieldDef.join) {
        processed[key] = result.getValue({
          name: fieldDef.field,
          join: fieldDef.join,
        });
      }
    });

    return processed;
  };

  /**
   * Gets a state record by name or short name
   * @param {string} stateInput - The state name or short name
   * @returns {Object} The state record
   */
  const getState = (stateInput) => {
    return query
      .runSuiteQL({
        query: `
              SELECT
                id,
                shortname,
                fullname
              FROM
                state
              WHERE
                shortname = '${stateInput}'
                OR fullname like '%${stateInput}%'
            `,
      })
      .asMappedResults()[0];
  };

  /*  *
   * Gets a country record by name or id
   * @param {string} countryInput - The country name or id
   * @returns {Object} The country record
   */
  const getCountry = (countryInput) => {
    return query
      .runSuiteQL({
        query: `
              SELECT
                uniquekey,
                id,
                name
              FROM
                country
              WHERE
                id = '${countryInput}'
                OR name like '%${countryInput}%'
            `,
      })
      .asMappedResults()[0];
  };

  /**
   * Transforms a value back into a format that Netsuite will accept
   * @param {string} netsuiteFieldType
   * @param {boolean|number|string|null} value
   * @returns {number|string|null|{onRequest: onRequest}|*|Date|Date|boolean}
   */
  const transformCustomFieldValue = (netsuiteFieldType, value) => {
    const fieldType =
      NETSUITE_FIELD_MAPPING[netsuiteFieldType.toUpperCase()].type;
    switch (fieldType) {
      case "email":
      case "phone":
      case "radio":
      case "richText":
      case "text":
      case "textarea":
      case "url":
        return String(value || "");
      case "checkbox":
        if (typeof value === "string") {
          if (value === "F" || value === "false") return false;
          return value === "T" || value === "true";
        }
        return Boolean(value || false);
      case "date":
      case "datetime":
      case "time":
        // Handle empty/null/undefined values for date fields
        if (
          !value ||
          value === "" ||
          value === "undefined" ||
          value === "null"
        ) {
          return null;
        }
        // If already a Date object, return as-is
        if (value instanceof Date) {
          return value;
        }
        // Try to parse the value as a date
        try {
          const parsedDate = new Date(value);
          // Check if the parsed date is valid
          if (isNaN(parsedDate.getTime())) {
            return null;
          }
          return parsedDate;
        } catch (error) {
          // If parsing fails, return null
          return null;
        }
      case "file":
        return value || null;
      case "number":
        return Number(value || 0);
      case "select":
        return Array.isArray(value) ? value : String(value || "");
      default:
        return value;
    }
  };

  return {
    getAllResultsFor,
    formatDateToTimestamp,
    getDomainUrl,
    generatePrintUrl,
    getUserPermissionLevels,
    processSearchResult,
    transformCustomFieldValue,
    getState,
    getCountry,
    escapeSqlString,
    NETSUITE_FIELD_MAPPING,
  };
});
