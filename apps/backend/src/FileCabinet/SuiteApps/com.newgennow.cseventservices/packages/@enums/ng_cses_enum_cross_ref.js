/**
 * @NApiVersion 2.1
 */
define(["N/url"], /**
 * @param{url} url
 */ (url) => {
  /**
   * @typedef {Object} Item
   * @property {Object} Type
   * @property {string} Type.FREIGHT
   * @property {string} Type.SQFT
   * @property {string} Type.DAYS_CALC
   * @property {string} Type.SHOW_DURATION
   * @property {string} Type.LABOR
   * @property {string} Type.COLOR
   * @property {string} Type.SIZE
   * @property {string} Type.ORIENTATION
   * @property {string} Type.VARIANT
   * @property {string} Type.GRAPHICS
   * @property {string} Type.ESTIMATED
   * @property {string} Type.MEMO
   * @property {string} Type.UPLOAD
   * @property {Object} Field
   * @property {string} Field.COLOR
   * @property {string} Field.SIZE
   * @property {string} Field.ORIENTATION
   * @property {string} Field.VARIANT
   * @property {string} Field.GRAPHIC
   * @property {Object} Options
   * @property {string} Options.SUPERVISON
   * @property {string} Options.LABORDATE
   * @property {string} Options.LABORENDTIME
   * @property {string} Options.LABORESTHOURS
   * @property {string} Options.LABORWORKERS
   * */

  const item = {
    Type: {
      FREIGHT: "custitem_is_freight",
      SQFT: "custitem_is_sqft",
      DAYS_CALC: "custitem_is_days",
      SHOW_DURATION: "custitem_show_duration",
      LABOR: "custitem_labor_item",
      COLOR: "custitem_has_color_options",
      SIZE: "custitem_has_size_options",
      /**
       * @deprecated Use VARIANT instead
       * */
      ORIENTATION: "custitem_has_orient_options",
      VARIANT: "custitem_has_orient_options",
      GRAPHICS: "custitem_ng_cs_has_graphic_options",
      ESTIMATED: "custitem_cost_is_estimated",
      MEMO: "custitem_cost_is_memo",
      UPLOAD: "custitem_cost_is_upload",
    },
    Field: {
      COLOR: "custitem27",
      SIZE: "custitem28",
      /**
       * @deprecated Use VARIANT instead
       * */
      ORIENTATION: "custitem_orientation",
      VARIANT: "custitem_orientation",
      GRAPHIC: "custitem42",
    },
    Options: {
      SUPERVISON: "custcol_labor_sup_required",
      LABORDATE: "custcol_labor_date",
      LABORENDTIME: "custcol_labor_end_time",
      LABORESTHOURS: "custcol_labor_est_hours",
      LABORWORKERS: "custcol_labor_workers",
      LABORTIME: "custcol_labor_time",
      VARIANT: "custcol_orientation",
      /**
       * @deprecated Use VARIANT instead
       * */
      ORIENTATION: "custcol_orientation",
      SIZE: "custcol_sizeopt",
      GRAPHIC: "custcol_graphmat_substrate",
      LABORITEM: "custcol_ng_cs_labor_item",
      COLOR: "custcol_coloropt",
      CANCELLED: "custcol_canc_descr",
      ATTACHSUPPORTFILE: "custcol_attach_document",
    },
    Relationship: {
      custitem28: "custitem_has_size_options", // Size
      custitem27: "custitem_has_color_options", // Color
      custitem_orientation: "custitem_has_orient_options", // Orientation
      custitem42: "custitem_ng_cs_has_graphic_options", // Graphic
    },
  };

  /**
   * Mappings that assist with venue operations
   *
   * @typedef {Object} VenueOpsMappings
   * @property {Object} BOOKING_NAME_FIELDS
   * @property {string} BOOKING_NAME_FIELDS.custrecord_ng_cs_eb_space_ui
   * @property {string} BOOKING_NAME_FIELDS.custrecord_ng_cs_eb_status
   * @property {string} BOOKING_NAME_FIELDS.custrecord_ng_cs_eb_start_date
   * @property {string} BOOKING_NAME_FIELDS.custrecord_ng_cs_eb_start_time
   * @property {string} BOOKING_NAME_FIELDS.custrecord_ng_cs_eb_end_date
   * @property {string} BOOKING_NAME_FIELDS.custrecord_ng_cs_eb_end_time
   * @property {string} BOOKING_NAME_FIELDS.custrecord_ng_cs_eb_event
   * @property {Object} FUNCTION_NAME_FIELDS
   * @property {string} FUNCTION_NAME_FIELDS.custrecord_nges_fun_venue_space
   * @property {string} FUNCTION_NAME_FIELDS.custrecord_nges_fun_title
   * @property {string} FUNCTION_NAME_FIELDS.custrecord_nges_fun_start_date
   * @property {string} FUNCTION_NAME_FIELDS.custrecord_nges_fun_start_time
   * @property {string} FUNCTION_NAME_FIELDS.custrecord_nges_fun_end_date
   * @property {string} FUNCTION_NAME_FIELDS.custrecord_nges_fun_end_time
   * @property {Object} BOOKING_FUNCTION_MAPPING
   * @property {string} BOOKING_FUNCTION_MAPPING.custrecord_ng_cs_eb_event
   * @property {string} BOOKING_FUNCTION_MAPPING.custrecord_ng_cs_eb_venue
   * @property {string} BOOKING_FUNCTION_MAPPING.custrecord_ng_cs_eb_space_ui
   * @property {string} BOOKING_FUNCTION_MAPPING.custrecord_ng_cs_eb_start_date
   * @property {string} BOOKING_FUNCTION_MAPPING.custrecord_ng_cs_eb_start_time
   * @property {string} BOOKING_FUNCTION_MAPPING.custrecord_ng_cs_eb_end_date
   * @property {string} BOOKING_FUNCTION_MAPPING.custrecord_ng_cs_eb_end_time
   * */
  const venueOpsMappings = {
    // fields that require name update with mapping to default value if blank
    BOOKING_NAME_FIELDS: {
      custrecord_ng_cs_eb_space_ui: "Space",
      custrecord_ng_cs_eb_status: "Status",
      custrecord_ng_cs_eb_start_date: "Start Date",
      custrecord_ng_cs_eb_start_time: "Time",
      custrecord_ng_cs_eb_end_date: "End Date",
      custrecord_ng_cs_eb_end_time: "Time",
      custrecord_ng_cs_eb_event: "Event",
    },
    // fields that require name update with mapping to default value if blank
    FUNCTION_NAME_FIELDS: {
      custrecord_nges_fun_venue_space: "Space",
      custrecord_nges_fun_title: "Title",
      custrecord_nges_fun_start_date: "Start Date",
      custrecord_nges_fun_start_time: "Time",
      custrecord_nges_fun_end_date: "End Date",
      custrecord_nges_fun_end_time: "Time",
    },
    // keys are the field id for the booking
    // values are the equivalent field id for the function
    BOOKING_FUNCTION_MAPPING: {
      custrecord_ng_cs_eb_event: "custrecord_nges_fun_event",
      custrecord_ng_cs_eb_venue: "custrecord_nges_fun_venue",
      custrecord_ng_cs_eb_space_ui: "custrecord_nges_fun_venue_space",
      custrecord_ng_cs_eb_start_date: "custrecord_nges_fun_start_date",
      custrecord_ng_cs_eb_start_time: "custrecord_nges_fun_start_time",
      custrecord_ng_cs_eb_end_date: "custrecord_nges_fun_end_date",
      custrecord_ng_cs_eb_end_time: "custrecord_nges_fun_end_time",
    },
  };

  /**
   * @typedef {Object} ES_Enums
   * @property {Item} item
   * @property {VenueOpsMappings} venueOpsMappings
   * */

  return { item, venueOpsMappings };
});
