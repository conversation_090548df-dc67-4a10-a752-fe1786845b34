/**
 * ng_server_cm_price_hooks.js
 * @Author: <PERSON>
 * @NModuleScope Public
 * @NApiVersion 2.1
 */
define([
  "N/query",
  "N/record",
  "N/runtime",
  "N/search",
  "N/url",
  "../@moment/moment.min",
  "N/cache",
  "../@event/ng_cm_event_helpers",
], (query, record, runtime, search, url, moment, cache, eventHelpers) => {
  const CACHE_NAME = "ITEM_PRICE_CACHE";
  const CACHE_TTL = 900; // 15 minutes
  const SETTINGS_CACHE_NAME = "CS_SETTINGS";
  const SETTINGS_CACHE_TTL = 300; // 5 minutes
  const TAX_CACHE_NAME = "TAX_GROUP_CACHE";
  const TAX_CACHE_TTL = 3600; // 1 hour cache

  let SETTINGS_CACHE_OBJ = null;
  let CS_SETTINGS = null;

  /**
   * Determines the current item price level based on the Event ID.
   * @param {string|number} eid - The ID of the event.
   * @returns {string|number} The ID of the current price level.
   */
  function useItemPriceLevel(eid) {
    if (!eid) {
      throw new Error("An event id arg is missing for useItemPriceLevel()");
    }

    log.audit("🟢 Server | Getting Price Level For Event:", eid);

    const { showDates, eventData } = eventHelpers.getEventData(eid);
    const settings = getSettings();
    const today = moment().endOf("day");

    const eventAdvancedDate = moment(eventData.advancedOrderDate).endOf("day");
    const firstShowDate = eventHelpers.getFirstShowDate(showDates, settings);

    log.debug("🪝 Server | First Show Date:", firstShowDate);

    if (today.isSameOrBefore(eventAdvancedDate)) {
      log.debug("🪝 Server | Advanced Price Level: ", eventData.advancedPriceLevel.value);
      return eventData.advancedPriceLevel.value;
    } else if (today.isBetween(eventAdvancedDate, firstShowDate)) {
      log.debug("🪝 Server | Today: ", today);
      return eventData.standardPriceLevel.value;
    } else if (today.isSameOrAfter(firstShowDate)) {
      log.debug("🪝 Server | Show Price Level: (site) ", eventData.sitePriceLevel.value);
      log.debug("🪝 Server | Today: ", today);
      return eventData.sitePriceLevel.value;
    }
  }

  /**
   * An object representing the output of the pricing information.
   *
   * @typedef {Object} PriceOutput
   *
   * @property {(String|Number)} comparePrice - The compare price in a strikethrough format.
   * @property {(String|Number)} price - The listing price.
   */

  /**
   * Retrieves the current event and item price.
   * @param {string|number} eid - The ID of the event.
   * @param {string|number} pid - The Product ID of the item.
   * @returns {Object} An object containing the pricing information.
   */
  const useItemPrice = (eid, pid) => {
    if (!eid || !pid) {
      throw new Error(
        "Event ID and Product ID are required for useItemPrice()",
      );
    }

    const cacheKey = `${eid}_${pid}`;
    const itemPriceCache = cache.getCache({ name: CACHE_NAME });
    let cachedResult = itemPriceCache.get({ key: cacheKey });

    log.debug("🪝 Server | Cache result:", cachedResult ? "Hit" : "Miss");

    if (cachedResult) {
      return JSON.parse(cachedResult);
    }

    let priceOutput = { comparePrice: null, price: null };

    try {
      const settings = getSettings();
      const materialHandlingBetaEnabled =
        settings.custrecord_enable_material_handling_beta;

      log.debug("🪝 Server | Settings:", { materialHandlingBetaEnabled });

      const itemPriceResult = getItemPriceResult(pid);
      if (!itemPriceResult) return null;

      log.debug("🪝 Server | Item price result:", itemPriceResult);

      const { eventData, showDates } = eventHelpers.getEventData(eid);
      log.debug("🪝 Server | Event data:", eventData);
      log.debug("🪝 Server | Show dates:", showDates);
      const firstShowDate = eventHelpers.getFirstShowDate(showDates, settings).endOf("day");
      const freightTable = getFreightTable(eid);
      log.debug("🪝 Server | Freight table:", freightTable);

      const today = moment().endOf("day");
      const eventAdvancedDate = moment(eventData.advancedOrderDate).endOf(
        "day",
      );

      log.debug("🪝 Server | Calculating price with params:", {
        itemPriceResult,
        eventData,
        freightTable,
        today: today.format(),
        eventAdvancedDate: eventAdvancedDate.format(),
        firstShowDate: firstShowDate.format(),
        materialHandlingBetaEnabled,
      });

      priceOutput = calculatePrice(
        itemPriceResult,
        eventData,
        freightTable,
        today,
        eventAdvancedDate,
        firstShowDate,
        materialHandlingBetaEnabled,
      );

      log.debug("🪝 Server | Calculated price output:", priceOutput);

      itemPriceCache.put({
        key: cacheKey,
        value: JSON.stringify(priceOutput),
        ttl: CACHE_TTL,
      });
    } catch (err) {
      log.error({ title: "🪝 Server | Error in useItemPrice", details: err });
    }

    log.debug("🪝 Server | Final price output:", priceOutput);
    return priceOutput;
  };

  /**
   * Retrieves item price result from a search.
   * @param {string} pid - Product ID
   * @returns {Object|null} Item price result or null if not found
   */
  function getItemPriceResult(pid) {
    const itemSearch = search.create({
      type: "item",
      filters: [["internalid", "anyof", pid]],
      columns: [
        "custitem_has_color_options",
        "custitem_has_size_options",
        "custitem_has_orient_options",
        "custitem_ng_cs_has_graphic_options",
        "custitem_is_sqft",
        "custitem_is_freight",
        "custitem_is_days",
        "custitem_show_duration",
        "custitem_labor_item",
        "custitem_cost_is_estimated",
        "itemid",
        "displayname",
        "custitem_ng_mat_handling_sched",
        search.createColumn({ name: "internalid", join: "pricing" }),
        search.createColumn({ name: "pricelevel", join: "pricing" }),
        search.createColumn({ name: "saleunit", join: "pricing" }),
        search.createColumn({ name: "unitprice", join: "pricing" }),
      ],
    });

    let itemResult = null;
    let priceLevels = [];

    itemSearch.run().each(function (result) {
      if (!itemResult) {
        itemResult = {
          id: result.id,
          name: result.getValue("itemid"),
          isFreight: result.getValue("custitem_is_freight"),
          materialHandlingSchedule: result.getValue(
            "custitem_ng_mat_handling_sched",
          ),
          priceLevels: [],
        };
      }

      priceLevels.push({
        internalId: result.getValue({ name: "internalid", join: "pricing" }),
        priceLevel: {
          value: result.getValue({ name: "pricelevel", join: "pricing" }),
          text: result.getText({ name: "pricelevel", join: "pricing" }),
        },
        unitPrice: result.getValue({ name: "unitprice", join: "pricing" }),
      });

      return true;
    });

    if (itemResult) {
      itemResult.priceLevels = priceLevels;
    }

    log.debug("🪝 Server | Item Result", itemResult);
    return itemResult;
  }

  /**
   * Retrieves freight table from a search.
   * @param {string} eid - Event ID
   * @returns {Array} Freight table results
   */
  function getFreightTable(eid) {
    const freightSearch = search.create({
      type: "customrecord_freight_table",
      filters: [["custrecord_show_freight", "anyof", eid]],
      columns: [
        "custrecord_freight_rate",
        "custrecord_pre_show_rate",
        "custrecord_inbetween_rate",
        "custrecord_on_site_rate",
        "custrecord_freight_item",
      ],
    });

    let freightResults = [];
    freightSearch.run().each(function (result) {
      freightResults.push({
        id: result.id,
        standardFreightRate: result.getValue("custrecord_freight_rate"),
        preShowFreightRate: result.getValue("custrecord_pre_show_rate"),
        inBetweenFreightRate: result.getValue("custrecord_inbetween_rate"),
        onSiteFreightRate: result.getValue("custrecord_on_site_rate"),
        freightItem: {
          value: result.getValue("custrecord_freight_item"),
          text: result.getText("custrecord_freight_item"),
        },
      });
      return true;
    });

    return freightResults;
  }

  /**
   * Calculates the price based on the provided data.
   * @param {Object} itemPriceResult - Item price result
   * @param {Object} eventData - Event data
   * @param {Array} freightTable - Freight table
   * @param {moment.Moment} today - Current date
   * @param {moment.Moment} eventAdvancedDate - Event advanced date
   * @param {moment.Moment} firstShowDate - First show date
   * @param {boolean} materialHandlingBetaEnabled - Material handling beta flag
   * @returns {Object} Calculated price output
   */
  function calculatePrice(
    itemPriceResult,
    eventData,
    freightTable,
    today,
    eventAdvancedDate,
    firstShowDate,
    materialHandlingBetaEnabled,
  ) {
    log.debug("🪝 Server | calculatePrice input", {
      itemPriceResult,
      eventData,
      freightTable,
      materialHandlingBetaEnabled,
    });

    log.debug("🪝 Server | Today:", today);
    log.debug("🪝 Server | Event Advanced Date:", eventAdvancedDate);
    log.debug("🪝 Server | First Show Date:", firstShowDate);

    let priceOutput = { comparePrice: null, price: null };

    if (itemPriceResult.isFreight && !materialHandlingBetaEnabled) {
      const freightItem = freightTable.find(
        (item) => item.freightItem.value === itemPriceResult.id,
      );
      if (freightItem) {
        if (today.isSameOrBefore(eventAdvancedDate)) {
          priceOutput.comparePrice = freightItem.inBetweenFreightRate;
          priceOutput.price = freightItem.preShowFreightRate;
        } else if (today.isBetween(eventAdvancedDate, firstShowDate)) {
          priceOutput.price = freightItem.inBetweenFreightRate;
        } else if (today.isSameOrAfter(firstShowDate)) {
          priceOutput.price = freightItem.onSiteFreightRate;
        }
      }
      log.debug("🪝 Server | Freight item calculation result", {
        isFreight: itemPriceResult.isFreight,
        freightItem,
        priceOutput,
      });
    } else {
      let price;
      if (today.isSameOrBefore(eventAdvancedDate)) {
        log.debug("🪝 Server | Advanced Price Level:", eventData.advancedPriceLevel.value);
        price = getPriceForLevel(
          itemPriceResult,
          eventData.advancedPriceLevel.value,
        );
        priceOutput.comparePrice = getPriceForLevel(
          itemPriceResult,
          eventData.standardPriceLevel.value,
        );
        priceOutput.price = price;
      } else if (today.isBetween(eventAdvancedDate, firstShowDate)) {
        log.debug("🪝 Server | Standard Price Level:", eventData.standardPriceLevel.value);
        price = getPriceForLevel(
          itemPriceResult,
          eventData.standardPriceLevel.value,
        );
        priceOutput.price = price;
      } else if (today.isSameOrAfter(firstShowDate) && today.isAfter(eventAdvancedDate)) {
        log.debug("🪝 Server | Site Price Level:", eventData.sitePriceLevel.value);
        price = getPriceForLevel(
          itemPriceResult,
          eventData.sitePriceLevel.value,
        );
        priceOutput.price = price;
      }

      if (
        materialHandlingBetaEnabled &&
        itemPriceResult?.materialHandlingSchedule
      ) {
        priceOutput.materialSchedule = getMaterialHandlingSchedule(
          itemPriceResult.materialHandlingSchedule,
        );
      }
      log.debug("🪝 Server | Non-freight item calculation result", {
        price,
        priceOutput,
        materialHandlingSchedule: itemPriceResult.materialHandlingSchedule,
      });
    }

    if (
      priceOutput.comparePrice &&
      Number(priceOutput.comparePrice) <= Number(priceOutput.price)
    ) {
      log.debug("🪝 Server | Compare price removed", {
        comparePrice: priceOutput.comparePrice,
        price: priceOutput.price,
      });
      priceOutput.comparePrice = null;
    }

    log.debug("🪝 Server | Final price output", priceOutput);
    return priceOutput;
  }

  /**
   * Gets the price for a specific price level.
   * @param {Object} itemPriceResult - Item price result
   * @param {string} priceLevel - Price level to get
   * @returns {string|null} Price for the given level or null if not found
   */
  function getPriceForLevel(itemPriceResult, priceLevel) {
    const matchingPriceLevel = itemPriceResult.priceLevels.find(
      (pl) => pl.priceLevel.value === priceLevel,
    );
    log.debug("🪝 Server | Matching Price Level:", {
      itemPriceResult: itemPriceResult.priceLevels,
      priceLevel,
      matchingPriceLevel,
    });

    return matchingPriceLevel ? matchingPriceLevel.unitPrice : null;
  }

  /**
   * Gets the material handling schedule.
   * @param {string} scheduleId - Material handling schedule ID
   * @returns {Object} Material handling schedule
   */
  function getMaterialHandlingSchedule(scheduleId) {
    // Implement the logic to fetch and return the material handling schedule
    // This is a placeholder and should be replaced with actual implementation

    let materialHandlingScheduleLookup =
      scheduleId &&
      search.lookupFields({
        type: "customrecord_ng_mat_handling_sched",
        id: scheduleId,
        columns: MATERIAL_HANDLING_SCHEDULE_FIELDS,
      });

    let materialHandlingPricing = null;

    if (materialHandlingScheduleLookup) {
      // Format the data to be used in the front end
      materialHandlingPricing = {
        id: scheduleId,
        name: materialHandlingScheduleLookup.name,
        chargeType: getListValueFromLookup(
          materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_charge_type,
        ),
        minWeight:
          materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_min_weight,
        maxWeight:
          materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_max_weight,
        firstPcPrice:
          materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_first_pc,
        additionalPcPrice:
          materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_addtl_pc,
        replaceEstimatedItem:
        materialHandlingScheduleLookup.custrecord_ng_replace_est_item,
        surcharge: {
          type: getListValueFromLookup(
            materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_surchgtype,
          ),
          percentage:
            materialHandlingScheduleLookup.custrecord_surcharge_percentage,
          flatFee: materialHandlingScheduleLookup.custrecord_surcharge_flat_fee,
          surchargeItem:
            materialHandlingScheduleLookup.custrecord_ng_cs_late_surcharge_item,
        },
        overtimeSurcharge: {
          type: getListValueFromLookup(
            materialHandlingScheduleLookup.custrecord_ng_ot_surchgtype,
          ),
          percentage:
            materialHandlingScheduleLookup.custrecord_ot_surcharge_percentage,
          flatFee:
            materialHandlingScheduleLookup.custrecord_ot_surcharge_flat_fee,
          surchargeItem:
            materialHandlingScheduleLookup.custrecord_ng_cs_overtime_surcharge_item,
        },
        doubleTimeSurcharge: {
          type: getListValueFromLookup(
            materialHandlingScheduleLookup.custrecord_ng_dt_surchgtype,
          ),
          percentage:
            materialHandlingScheduleLookup.custrecord_dt_surcharge_percentage,
          flatFee:
            materialHandlingScheduleLookup.custrecord_dt_surcharge_flat_fee,
          surchargeItem:
            materialHandlingScheduleLookup.custrecord_ng_cs_doubletime_srchrge_item,
        },
      };
    }

    return materialHandlingPricing;
  }

  /**
   * This function `useEventPricingDates` retrieves information related to the pricing and schedules of the given event. It calls several searches in sequence, obtaining information such as show dates, price stats, freight table, and assigns these to the `output` object.
   *
   * It requires one parameter `eid`, the event id, which will be used in the internal searches as a filter. This can be a `Number` or `String`.
   *
   * The function returns an `Object` with the following properties:
   * - laborDates: An array containing the labor dates.
   * - showDates: An array containing the show dates.
   * - daysCalcDates: An array holding the calculated dates.
   * - freightTable: An array representing the freight table.
   * - laborSchedule: An array holding the labor schedule.
   *
   * @param {Number|String} eid - The event id (show_table)
   * @example
   * ```js
   * useEventPricingDates(5)
   * ```
   * @returns {
   *  {
   *  laborDates: Object[],
   *  showDates: Object[],
   *  daysCalcDates: Object[],
   *  freightTable: Object[],
   *  laborSchedule: Object[]
   *  }
   * }
   */
  const useEventPricingDates = (eid) => {
    log.debug({ title: "🪝 Server | ITEM: Event Id", details: eid });

    let output = {
      laborDates: [],
      showDates: [],
      daysCalcDates: [],
      freightTable: [],
      laborSchedule: [],
      dates: [],
    };

    try {
      // Get Show Dates
      let showDates = [];
      // Get event price stats
      let eventArr = [];
      // Get freight table
      let freightResults = [];

      let showDatesSearch = search.create({
        type: "customrecord_show_date",
        filters: [["custrecord_show_number_date", "anyof", eid]],
        columns: [
          search.createColumn({
            name: "scriptid",
            sort: search.Sort.ASC,
            label: "Script ID",
          }),
          search.createColumn({
            name: "custrecord_show_number_date",
            label: "CS Event",
          }),
          search.createColumn({
            name: "custrecord_date_type",
            label: "Date Type",
          }),
          search.createColumn({ name: "custrecord_date", label: "Date" }),
          search.createColumn({
            name: "custrecord_start_time",
            label: "Start Time",
          }),
          search.createColumn({
            name: "custrecord_end_time",
            label: "End Time",
          }),
          search.createColumn({
            name: "internalid",
            join: "CUSTRECORD_SHOW_NUMBER_DATE",
            label: "Internal ID",
          }),
        ],
      });

      let showDatesSearchResult = showDatesSearch.runPaged().count;
      log.debug(
        "🪝 Server | customrecord_show_dateSearchObj result count",
        showDatesSearchResult,
      );
      showDatesSearch.run().each(function (result) {
        // .run().each has a limit of 4,000 results

        let showDateObj = {
          id: result.id,
          recordType: result.recordType,
          event: {
            value: result.getValue("custrecord_show_number_date"),
            text: result.getText("custrecord_show_number_date"),
          },
          dateType: {
            value: result.getValue("custrecord_date_type"),
            text: result.getText("custrecord_date_type"),
          },
          date: result.getValue("custrecord_date"),
          startTime: result.getValue("custrecord_start_time"),
          endTime: result.getValue("custrecord_end_time"),
          internalId: result.getValue({
            name: "internalid",
            join: "CUSTRECORD_SHOW_NUMBER_DATE",
          }),
        };

        showDates.push(showDateObj);
        return true;
      });

      let eventSearch = search.create({
        type: "customrecord_show",
        filters: [["internalidnumber", "equalto", eid]],
        columns: [
          search.createColumn({
            name: "name",
            sort: search.Sort.DESC,
            label: "Name",
          }),
          search.createColumn({
            name: "custrecord_tax_rate",
            label: "Tax Rate",
          }),
          search.createColumn({
            name: "custrecord_tax_percent",
            label: "Tax Percent",
          }),
          search.createColumn({
            name: "custrecord_adv_ord_date",
            label: "Advanced Order Date",
          }),
          search.createColumn({
            name: "custrecord_adv_price_level",
            label: "Advance Price Level",
          }),
          search.createColumn({
            name: "custrecord_std_price_level",
            label: "Standard Price Level",
          }),
          search.createColumn({
            name: "custrecord_site_price_level",
            label: "On Site Price Level",
          }),
          search.createColumn({
            name: "custrecord_show_mgmnt_price_lvl",
            label: "Show Management Price Level",
          }),
        ],
      });

      // let eventSearchResultCount = eventSearch.runPaged().count;
      // log.debug(
      //   "customrecord_showSearchObj result count",
      //   eventSearchResultCount
      // );

      eventSearch.run().each(function (result) {
        // .run().each has a limit of 4,000 results
        let eventResultObj = {
          id: result.id,
          recordType: result.recordType,
          name: result.getValue("name"),
          taxRate: result.getValue("custrecord_tax_rate"),
          taxPercent: result.getValue("custrecord_tax_percent"),
          advancedOrderDate: result.getValue("custrecord_adv_ord_date"),
          advancedPriceLevel: {
            value: result.getValue("custrecord_adv_price_level"),
            text: result.getText("custrecord_adv_price_level"),
          },
          standardPriceLevel: {
            value: result.getValue("custrecord_std_price_level"),
            text: result.getText("custrecord_std_price_level"),
          },
          sitePriceLevel: {
            value: result.getValue("custrecord_site_price_level"),
            text: result.getText("custrecord_site_price_level"),
          },
          showManagementPriceLevel: {
            value: result.getValue("custrecord_show_mgmnt_price_lvl"),
            text: result.getText("custrecord_show_mgmnt_price_lvl"),
          },
        };

        eventArr.push(eventResultObj);
        return true;
      });

      log.debug({ title: "🪝 Server | Event added:", details: eventArr });

      let freight_table_search = search.create({
        type: "customrecord_freight_table",
        filters: [["custrecord_show_freight", "anyof", eid]],
        columns: [
          search.createColumn({
            name: "custrecord_show_freight",
            sort: search.Sort.ASC,
            label: "Event",
          }),
          search.createColumn({
            name: "custrecord_freight_rate",
            label: "Standard Freight Rate",
          }),
          search.createColumn({
            name: "custrecord_pre_show_rate",
            label: "Pre-Show Freight Rate",
          }),
          search.createColumn({
            name: "custrecord_inbetween_rate",
            label: "In-Between Freight Rate",
          }),
          search.createColumn({
            name: "custrecord_on_site_rate",
            label: "On-Site Freight Rate",
          }),
          search.createColumn({
            name: "custrecord_freight_item",
            label: "Freight Item",
          }),
        ],
      });

      let freightSearchResultsCount = freight_table_search.runPaged().count;
      log.debug({
        title: "🪝 Server | Freight Table Results Count",
        details: freightSearchResultsCount,
      });

      freight_table_search.run().each((each) => {
        freightResults.push(each);
        return true;
      });

      /*
			   Advanced Price Level: Before advanced price date (deadline)
			   Standard: After the advanced price date but before the first show date.
			   Show Site price: On the first show date. Then after until end of show.
		   * */

      SETTINGS_CACHE_OBJ = cache.getCache({
        name: "CS_SETTINGS",
      });

      CS_SETTINGS = SETTINGS_CACHE_OBJ.get({
        key: "CS_SETTINGS",
        loader: getSettings,
        ttl: 300,
      });

      let settings = JSON.parse(CS_SETTINGS);

      let today = new Date();
      // eslint-disable-next-line no-unused-vars
      let dd = today.getDate();
      // eslint-disable-next-line no-unused-vars
      let mm = today.getMonth() + 1;
      // eslint-disable-next-line no-unused-vars
      let yyyy = today.getFullYear();

      // Event specs
      let eventResultObj = eventArr[0];
      let eventAdvancedDate = new Date(eventResultObj.advancedOrderDate);

      let eventAdvancedPriceLevel = eventResultObj.advancedPriceLevel.value;
      let eventStandardPriceLevel = eventResultObj.standardPriceLevel.value;
      let eventSitePriceLevel = eventResultObj.sitePriceLevel.value;

      let priceDebugObject = {
        eventAdvancedDate,
        eventAdvancedPriceLevel,
        eventStandardPriceLevel,
        eventSitePriceLevel,
      };

      log.debug({
        title: "🟡 Server | Current event price levels set:",
        details: priceDebugObject,
      });

      // Event Show Dates - GRAB SOONEST "Show Date"

      log.debug({
        title: "🟡 Server | Current event price levels set:",
        details: priceDebugObject,
      });

      // Event Show Dates - GRAB SOONEST "Show Date"
      let sortedShowDates = showDates.sort(
        (d1, d2) => new Date(d1.date) - new Date(d2.date),
      );
      let onlyShowDates = sortedShowDates.filter(
        (eventdate) =>
          eventdate.dateType.text ===
          settings.custrecord_ng_cs_default_show_date[0].text,
      );

      let daysCalcDateTypes = settings.custrecord_ng_cs_dflt_d_calc_date_types;
      let daysCalcDates = [];
      daysCalcDateTypes.forEach((type) => {
        let foundDateTypeArr = sortedShowDates.filter(
          (event_date) => event_date.dateType.text === type.text,
        );
        log.debug({
          title: `🪝 Server | Found array ${type.text}`,
          details: foundDateTypeArr,
        });
        // Have to push()..... concat() doesn't work LOL
        foundDateTypeArr.forEach((dateResult) =>
          daysCalcDates.push(dateResult),
        );
        log.debug({ 
          title: "🪝 Server | Concat days calc array",
          details: daysCalcDates 
        });
      });
      log.debug({ 
        title: "🪝 Server | Days calc array",
        details: daysCalcDates 
      });

      let laborDateTypes = settings.custrecord_ng_cs_dflt_labor_date_types;
      let laborDates = [];

      laborDateTypes.forEach((type) => {
        let foundDateTypeArr = sortedShowDates.filter(
          (event_date) => event_date.dateType.text === type.text,
        );
        log.debug({
          title: `🪝 Server | Found in labor array ${type.text}`,
          details: foundDateTypeArr,
        });
        // Have to push()..... concat() doesn't work LOL
        foundDateTypeArr.forEach((dateResult) => laborDates.push(dateResult));
        log.debug({ 
          title: "🪝 Server | Concat labor array",
          details: laborDates 
        });
      });
      log.debug({ 
        title: "🪝 Server | Labor array",
        details: laborDates 
      });

      let laborSchedule = [];

      let cs_event_labor_schedule_search = search.create({
        type: "customrecord_ng_cs_show_labor_schedule",
        filters: [["custrecord_ng_cs_labor_show", "anyof", eid]],
        columns: [
          search.createColumn({
            name: "custrecord_ng_cs_labor_date",
            label: "Labor Date",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_labor_start",
            label: "Labor Start",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_labor_end",
            label: "Labor End",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_labor_type",
            label: "Labor Type",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_labor_multiplier",
            label: "Labor Multiplier",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_supervisor_markup",
            label: "Supervisor Markup",
          }),
        ],
      });

      var searchResultCount = cs_event_labor_schedule_search.runPaged().count;
      log.debug("🪝 Server | cs_event_labor_schedule_search result count", searchResultCount);
      cs_event_labor_schedule_search.run().each(function (result) {
        // .run().each has a limit of 4,000 results
        let laborScheduleObj = {
          id: result.id,
          date: moment(result.getValue("custrecord_ng_cs_labor_date")).format(
            "yyyy-MM-DD",
          ),
          start: moment(
            result.getValue("custrecord_ng_cs_labor_start"),
            "hh:mm a",
          ).format("HH:mm"),
          end: moment(
            result.getValue("custrecord_ng_cs_labor_end"),
            "hh:mm a",
          ).format("HH:mm"),
          multiplier: result.getValue("custrecord_ng_cs_labor_multiplier"),
          supervision: result.getValue("custrecord_ng_cs_supervisor_markup"),
          type: result.getText("custrecord_ng_cs_labor_type"),
        };

        laborSchedule.push(laborScheduleObj);
        return true;
      });

      output.showDates = onlyShowDates;
      output.laborDates = laborDates;
      output.daysCalcDates = daysCalcDates;
      output.freightTable = freightResults;
      output.laborSchedule = laborSchedule;
      output.dates = sortedShowDates;

      return output;
    } catch (err) {
      log.error({
        title: "🪝 Server | Error occurred with getting dates:",
        details: err,
      });
    }
  };

  /**
   * @typedef {Object} TaxGroup
   * @property {string} id - Tax code ID, if no value is assigned, it is set to "Non-Taxable"
   * @property {string} name - Name of the tax code
   * @property {string} rate - The rate of the tax
   * @property {string} unitprice1 - The first unit price
   * @property {string} unitprice2 - The second unit price
   * @property {string} city - The associated city of the tax group
   * @property {string} state - The associated state of the tax group
   * @property {string} country - The associated country of the tax group
   * @property {string} zip - The associated zip code of the tax group
   */

  /**
   * @typedef {Object} Options
   * @property {string} city - City of the location for the associated tax code
   * @property {string} state - State of the location for the associated tax code
   * @property {string} zip - Zip code of the location for the associated tax code
   * @property {(string|Number)} country - An ID of the country where the location for the associated tax code is
   * @property {string} countryShortName - Short name of the country where location for associated tax code is
   */


 /**
   * This function gets associated tax code for a given location with optimized retrieval
   *
   * @function useTaxCode
   * @param {Options} options - options for getting tax code for a location
   * @returns {TaxGroup} - Returns an object containing the tax code ID, name, rate, and unit prices
   */
 const useTaxCode = (options) => {
  const { city, state, zip, country, countryShortName } = options;
  
  // Create unique cache key
  const cacheKey = `TAX_${countryShortName || ""}_${state || ""}_${city || ""}_${zip || ""}`.replace(/\s+/g, "_");
  
  // Initialize tax group cache
  const taxGroupCache = cache.getCache({ name: TAX_CACHE_NAME });
  
  // Try to get cached result first
  let cachedTaxCode = taxGroupCache.get({ key: cacheKey });
  if (cachedTaxCode) {
    return JSON.parse(cachedTaxCode);
  }

  // Initialize with default values
  const taxCode = {
    id: country === "230" ? "-8" : "", // Pre-set default based on country
    name: "",
    rate: "0%",
    unitprice1: "",
    unitprice2: "",
    city: "",
    state: "",
    country: "",
    zip: ""
  };

  try {
    // Get settings once and destructure needed value
    const { custrecord_ng_cs_tax_auto_sel_txt_filter } = CS_SETTINGS || getSettings();
    const countryIsUs = country === "230";

    // Pre-build base filter array with known conditions
    const taxGroupFilter = [
      ["isinactive", "is", "F"],
      "AND",
      ["country", "anyof", countryShortName || "@NONE@"]
    ];

    // Efficiently build filter array
    if (state) taxGroupFilter.push("AND", ["state", "is", state]);
    if (city) {
      const cityFilter = city.includes(" ") 
        ? `${city.split(" ")[0]} ${city.split(" ")[1][0] || ""}`
        : city;
      taxGroupFilter.push("AND", ["city", "contains", cityFilter]);
    }
    if (zip) taxGroupFilter.push("AND", ["zip", "contains", zip]);
    if (custrecord_ng_cs_tax_auto_sel_txt_filter) {
      taxGroupFilter.push("AND", ["itemid", "contains", custrecord_ng_cs_tax_auto_sel_txt_filter]);
    }

    // Pre-define columns array with conditional additions
    const columns = [
      search.createColumn({ name: "itemid", sort: search.Sort.ASC }),
      search.createColumn({ name: "rate" }),
      search.createColumn({ name: "country" }),
      search.createColumn({ name: "city" }),
      search.createColumn({ name: "state" }),
      search.createColumn({ name: "zip" }),
      ...(countryIsUs ? [] : [
        search.createColumn({ name: "taxitem1" }),
        search.createColumn({ name: "unitprice1" }),
        search.createColumn({ name: "taxitem2" }),
        search.createColumn({ name: "unitprice2" })
      ])
    ];

    let bestScore = -1;
    let bestMatch = null;

    // Create and run search
    search.create({
      type: "taxgroup",
      filters: taxGroupFilter,
      columns
    }).run().each(result => {
      const currentTaxGroup = {
        id: result.id,
        country: result.getValue("country"),
        city: result.getValue("city"),
        state: result.getValue("state"),
        name: result.getValue("itemid"),
        rate: result.getValue("rate"),
        zip: result.getValue("zip"),
        ...(countryIsUs ? {} : {
          unitprice1: result.getValue("unitprice1"),
          unitprice2: result.getValue("unitprice2")
        })
      };

      // Calculate score using bitwise operations for performance
      const score = (
        ((currentTaxGroup.country === countryShortName) << 3) |
        ((currentTaxGroup.state === state) << 2) |
        ((currentTaxGroup.city.includes(city)) << 1) |
        (currentTaxGroup.zip === zip)
      );

      // Update best match if better score or same score with higher rate
      if (score > bestScore || (
        score === bestScore && 
        parseFloat(currentTaxGroup.rate) > parseFloat(bestMatch?.rate || "0")
      )) {
        bestScore = score;
        bestMatch = currentTaxGroup;
      }

      return true;
    });

    // Update taxCode if match found and cache the result
    if (bestMatch) {
      Object.assign(taxCode, bestMatch);
      
      // Only cache successful matches
      try {
        taxGroupCache.put({
          key: cacheKey,
          value: JSON.stringify(taxCode),
          ttl: TAX_CACHE_TTL
        });
      } catch (cacheErr) {
        log.error({
          title: "🪝 Server | Cache storage error",
          details: cacheErr
        });
      }
    }

  } catch (err) {
    log.error({
      title: "🪝 Server | Tax group search error",
      details: err
    });
  }

  return taxCode;
};


  /**
   * Retrieves settings from cache or loads them if not cached.
   * @returns {Object} Settings object
   */
  function getSettings() {
    if (!SETTINGS_CACHE_OBJ) {
      SETTINGS_CACHE_OBJ = cache.getCache({ name: SETTINGS_CACHE_NAME });
    }

    if (!CS_SETTINGS) {
      CS_SETTINGS = SETTINGS_CACHE_OBJ.get({
        key: SETTINGS_CACHE_NAME,
        loader: getSettingsLoader,
        ttl: SETTINGS_CACHE_TTL,
      });
    }

    return JSON.parse(CS_SETTINGS);
  }

  /**
   * Loader function for settings.
   * @returns {string} JSON string of settings
   */
  function getSettingsLoader() {
    const settingsFields = _SettingsFields;

    const settings = search.lookupFields({
      type: "customrecord_ng_cs_settings",
      id: "1",
      columns: settingsFields,
    });

    return JSON.stringify(settings);
  }

  function getListValueFromLookup(array) {
    if (Array.isArray(array) && array.length === 1) {
      const element = array[0];
      if (element && element.value && element.text) {
        return {
          value: element.value,
          text: element.text,
        };
      } else {
        log.audit({
          title: "🪝 Server | Invalid element structure. Element must have 'value' and 'text' properties.",
          details: "",
        });
        return false;
      }
    } else {
      log.audit({
        title: "🪝 Server | Input array must contain exactly one element.",
        details: "",
      });
      return null;
    }
  }

  const _SettingsFields = [
    "custrecord_ng_cs_gen_rand_email",
    "custrecord_ng_cs_rand_email_domain",
    "custrecord_ng_cs_rand_email_prefix",
    "custrecord_ng_cs_web_img_folder_id",
    "custrecord_ng_cs_exhb_kit_folder_id",
    "custrecord_ng_cs_use_undep_funds",
    "custrecord_ng_cs_def_dep_account",
    "custrecord_ng_cs_use_show_auditing",
    "custrecord_ng_cs_show_audit_form",
    "custrecord_ng_cs_use_pre_invoicing",
    "custrecord_ng_cs_pre_invoicing_form",
    "custrecord_ng_cs_use_alt_forms",
    "custrecord_ng_cs_prev_adtl_orders",
    "custrecord_ng_cs_allow_mult_billng_part",
    "custrecord_ng_cs_use_show_tax",
    "custrecord_ng_cs_use_cancl_charge",
    "custrecord_ng_cs_cancl_charge_item",
    "custrecord_ng_cs_def_canc_chrg_pct",
    "custrecord_ng_cs_canc_threshold",
    "custrecord_ng_cs_booth_ord_forms",
    "custrecord_ng_cs_add_item_forms",
    "custrecord_ng_cs_do_not_prompt_terms",
    "custrecord_ng_cs_prompt_exclusion_roles",
    "custrecord_ng_cs_import_log_record_id",
    "custrecord_ng_cs_import_log_search_id",
    "custrecord_ng_cs_payment_ar_account",
    "custrecord_ng_cs_cc_auth_item",
    "custrecord_ng_cs_pymnt_fail_eml_template",
    "custrecord_ng_cs_use_job_numbering",
    "custrecord_ng_cs_simple_job_numbering",
    "custrecord_ng_cs_job_num_prefix",
    "custrecord_ng_cs_custom_job_numbering",
    "custrecord_ng_cs_use_multi_cc_proc",
    "custrecord_ng_cs_no_prompt_under_zero",
    "custrecord_ng_cs_prompt_for_new_line",
    "custrecord_ng_cs_retain_last_show",
    "custrecord_ng_cs_retain_last_item_cat",
    "custrecord_ng_cs_default_show_subsidiary",
    "custrecord_ng_cs_no_billed_order_editing",
    "custrecord_ng_cs_billed_ord_edit_users",
    "custrecord_ng_cs_use_scripted_pynt_frm",
    "custrecord_ng_cs_clear_order_cc_details",
    "custrecord_ng_cs_send_invoice_fail_email",
    "custrecord_ng_cs_inv_fail_sender",
    "custrecord_ng_cs_inv_fail_recip",
    "custrecord_ng_cs_inv_fail_cc",
    "custrecord_ng_cs_def_exhb_dept",
    "custrecord_ng_cs_def_exhb_ord_type",
    "custrecord_ng_cs_send_exhib_invoice",
    "custrecord_ng_cs_exhib_invoice_sender",
    "custrecord_ng_cs_exhb_inv_email_template",
    "custrecord_ng_cs_inv_email_conditions",
    "custrecord_ng_cs_give_contacts_access",
    "custrecord_ng_cs_allow_mass_booth_delete",
    "custrecord_ng_cs_mass_booth_delete_roles",
    "custrecord_ng_cs_send_web_pymnt_email",
    "custrecord_ng_cs_web_pymnt_notice_sender",
    "custrecord_ng_cs_web_pymnt_fail_recip",
    "custrecord_ng_cs_web_pymnt_fail_cc",
    "custrecord_ng_cs_csv_import_folder_id",
    "custrecord_ng_cs_allow_show_autopay",
    "custrecord_ng_cs_pymt_rcpt_template",
    "custrecord_ng_cs_dpst_rcpt_template",
    "custrecord_ng_cs_log_time_zone",
    "custrecord_ng_cs_freight_minimum",
    "custrecord_ng_cs_prev_bo_redir_alert",
    "custrecord_ng_cs_dflt_shw_tbl_form",
    "custrecord_ng_cs_dflt_exhibtr_form",
    "custrecord_ng_cs_dflt_booth_order_form",
    "custrecord_ng_cs_activity_log_rec_id",
    "custrecord_ng_cs_activity_log_srch_id",
    "custrecord_ng_cs_auto_charge_web_orders",
    "custrecord_ng_cs_auth_non_web_orders",
    "custrecord_ng_cs_autochrg_cat_excl",
    "custrecord_ng_cs_mastercard",
    "custrecord_ng_cs_visa",
    "custrecord_ng_cs_amex",
    "custrecord_ng_cs_discover",
    "custrecord_ng_cs_default_adv_show_price",
    "custrecord_ng_cs_default_std_show_price",
    "custrecord_ng_cs_default_onst_show_price",
    "custrecord_ng_cs_payment_type",
    "custrecord_ng_cs_dflt_d_calc_date_types",
    "custrecord_ng_cs_dflt_labor_date_types",
    "custrecord_ng_cs_supervisor_item",
    "custrecord_ng_cs_exempt_estimated_items",
    "custrecord_ng_cs_auth_non_web_orders",
    "custrecord_ng_cs_default_show_mgmt_price",
    "custrecord_ng_cs_name_number_ordering",
    "custrecord_ng_cs_name_number_separator",
    "custrecord_ng_cs_use_custom_job",
    "custrecord_ng_cs_def_show_mgmt_dept",
    "custrecord_ng_cs_def_show_mgmt_ord_type",
    "custrecord_ng_cs_default_show_date",
    "custrecord_ng_cs_show_mgt_forms",
    "custrecord_ng_cs_enable_freight_opts_opt",
    "custrecord_ng_cs_enable_graphics_option",
    "custrecord_ng_cs_dflt_show_mgmt_ord_form",
    "custrecord_ng_cs_enable_orientation_opt",
    "custrecord_ng_cs_enable_labor_matrix_opt",
    "custrecord_ng_cs_enforce_item_max_qty",
    "custrecord_ng_cs_enable_paytrace",
    "custrecord_ng_cs_show_calendar_id",
    "custrecord_ng_cs_acct_domain_url",
    "custrecord_ng_cs_algolia_application_id",
    "custrecord_ng_cs_algolia_search_key",
    "custrecord_ng_cs_algolia_api_key",
    "custrecord_ng_cs_algolia_index",
    "custrecord_ng_cs_fclty_addy_template",
    "custrecord_ng_cs_wrhs_addy_template",
    "custrecord_ng_cs_name_from_subsidiary",
    "custrecord_ng_cs_booth_num_line_text",
    "custrecord_ng_cs_wo_img",
    "custrecord_ng_cs_wo_logo_img_url",
    "custrecord_ng_cs_inv_transfer_type",
    "custrecord_ng_cs_transfer_count_markup",
    "custrecord_ng_cs_trnsfr_exmpt_cats",
    "custrecord_ng_cs_default_transfer_from",
    "custrecord_ng_cs_default_to_as_st_loc",
    "custrecord_ng_cs_item_rprts_exluded_cats",
    "custrecord_ng_cs_exhb_wo_exluded_cats",
    "custrecord_ng_cs_hide_bthchklst_cnt_info",
    "custrecord_ng_cs_shade_alt_report_lines",
    "custrecord_ng_cs_report_line_shade_hex",
    "custrecord_ng_cs_report_item_display",
    "custrecord_ng_cs_graphics_item_cat",
    "custrecord_ng_cs_canonical_base_url",
    "custrecord_ng_cs_use_cc_conv_fee",
    "custrecord_ng_cs_cc_conv_fee_rate",
    "custrecord_ng_cs_cc_conv_fee_item",
    "custrecord_ng_cs_cc_conv_fee_order_types",
    "custrecord_ng_cs_csv_import_file",
    "custrecord_ng_cs_default_show_move_in",
    "custrecord_ng_cs_default_exhib_move_in",
    "custrecord_ng_cs_default_show_move_out",
    "custrecord_ng_cs_default_exhib_move_out",
    "custrecord_ng_cs_enable_rentals",
    "custrecord_ng_cs_tax_auto_sel_txt_filter",
    "custrecord_enable_material_handling_beta",
  ];

  const MATERIAL_HANDLING_SCHEDULE_FIELDS = [
    "name",
    "custrecord_ng_mat_handle_sch_charge_type",
    "custrecord_ng_mat_handle_sch_min_weight",
    "custrecord_ng_mat_handle_sch_max_weight",
    "custrecord_ng_mat_handle_sch_first_pc",
    "custrecord_ng_mat_handle_sch_addtl_pc",
    "custrecord_ng_mat_handle_sch_surchgtype",
    "custrecord_surcharge_percentage",
    "custrecord_surcharge_flat_fee",
    "custrecord_ng_ot_surchgtype",
    "custrecord_ot_surcharge_percentage",
    "custrecord_ot_surcharge_flat_fee",
    "custrecord_ng_dt_surchgtype",
    "custrecord_dt_surcharge_percentage",
    "custrecord_dt_surcharge_flat_fee",
    "custrecord_ng_cs_late_surcharge_item",
    "custrecord_ng_cs_overtime_surcharge_item",
    "custrecord_ng_cs_doubletime_srchrge_item",
    "custrecord_ng_replace_est_item",
  ];

  /**
   * Retrieves collection information for a specific item in an event.
   * This function searches for the collection that contains the specified item
   * and is relevant to the specified event.
   * 
   * @param {string|number} eventId - The ID of the event
   * @param {string|number} itemId - The ID of the item
   * @param {string|number} [collectionId] - Optional. The ID of a specific collection to check
   * @returns {Object|null} The collection information including id and name, or null if not found
   */
  const useItemCollection = (eventId, itemId, collectionId) => {
    if (!eventId || !itemId) {
      log.debug("🪝 Server | useItemCollection called without required parameters", { eventId, itemId });
      return null;
    }

    try {
      // Create a cache key combining event and item IDs
      const COLLECTION_CACHE_NAME = "ITEM_COLLECTION_CACHE";
      const COLLECTION_CACHE_TTL = 300; // 5 minutes
      const cacheKey = `${eventId}_${itemId}_${collectionId || ''}`;
      
      // // Try to get from cache first
      const collectionCache = cache.getCache({ name: COLLECTION_CACHE_NAME });
      let cachedCollection = collectionCache.get({ key: cacheKey });
      
      if (cachedCollection) {
        log.debug("🪝 Server | Collection cache hit", { cacheKey });
        return JSON.parse(cachedCollection);
      }
      
      log.debug("🪝 Server | Looking up collection for item", { eventId, itemId, collectionId });
      
      // Build the search filters
      let filters = [
        ["custrecord_ng_cs_itemcoll_event", "anyof", eventId],
        "AND",
        ["custrecord_ng_cs_itemcoll_items", "anyof", itemId]
      ];
      
      // If a specific collection ID is provided, add it to the filters
      if (collectionId) {
        filters.push("AND", ["internalid", "anyof", collectionId]);
      }
      
      // Create and run the search
      const collectionSearch = search.create({
        type: "customrecord_ng_cs_item_collection",
        filters: filters,
        columns: [
          search.createColumn({ name: "internalid" }),
          search.createColumn({ name: "custrecord_ng_cs_itemcoll_display_name" }),
          search.createColumn({ 
            name: "custrecord_ng_cs_itemcoll_event", 
            label: "Event" 
          }),
          search.createColumn({
            name: "custrecord_item_sort_index_read_only",
            label: "Sequence"
          })
        ]
      });
      
      let collection = null;
      
      // Process the first result (we only need one)
      collectionSearch.run().each(function(result) {
        collection = {
          id: result.id,
          name: result.getValue("custrecord_ng_cs_itemcoll_display_name"),
          event: {
            value: result.getValue("custrecord_ng_cs_itemcoll_event"),
            text: result.getText("custrecord_ng_cs_itemcoll_event")
          },
          sequence: result.getValue("custrecord_item_sort_index_read_only")
        };
        
        // Only process the first result
        return false;
      });
      
      // If a collection was found, cache it
      if (collection) {
        log.debug("🪝 Server | Found collection for item", { collection });
        collectionCache.put({
          key: cacheKey,
          value: JSON.stringify(collection),
          ttl: COLLECTION_CACHE_TTL
        });
      } else {
        log.debug("🪝 Server | No collection found for item", { eventId, itemId, collectionId });
      }
      
      return collection;
    } catch (err) {
      log.error({ title: "🪝 Server | Error in useItemCollection", details: err });
      return null;
    }
  };

  return { useItemPriceLevel, useItemPrice, useEventPricingDates, useTaxCode, useItemCollection };
});
