/**
 * ng_server_cm_price_hooks.js
 * @Author: <PERSON>
 * @NModuleScope Public
 * @NApiVersion 2.1
 */
define([
  "N/query",
  "N/record",
  "N/runtime",
  "N/search",
  "N/url",
  "../@moment/moment.min",
], /**
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{url} url
 * @param{moment} moment
 *
 *
 */ (query, record, runtime, search, url, moment, settings) => {
  let CS_SETTINGS = null;

  /**
   * Function to determine the current item price level depending on the Event ID.
   *
   * This function takes as argument the event id and based on data retrieved from the event and the current system
   * date, determines the current price level - Advance, Standard or Show Site. If no event id is passed, it
   * throws an error. The function uses the SuiteScript's search module to obtain data about the event
   * and calculate the proper price level. It performs two searches, one for events and one for show dates.
   *
   * @param {Number | String} eid - The ID of the event (from the CS Event record).
   * @throws {String} Throws an error if the event ID is missing.
   * @returns {Number | String} Returns the ID of the current price level.
   *
   * For example,
   *
   * useItemPriceLevel('123');
   *
   * This will return the current price level id for the event with id '123'.
   */
  function useItemPriceLevel(eid) {
    if (!eid) {
      throw "An event id arg is missing for useItemPriceLevel()";
    }

    log.audit("🟢 Getting Price Level For Event:", eid);

    // Get Show Dates
    let showDates = [];
    let showDatesSearch = search.create({
      type: "customrecord_show_date",
      filters: [["custrecord_show_number_date", "anyof", eid]],
      columns: [
        search.createColumn({
          name: "scriptid",
          sort: search.Sort.ASC,
          label: "Script ID",
        }),
        search.createColumn({
          name: "custrecord_show_number_date",
          label: "CS Event",
        }),
        search.createColumn({
          name: "custrecord_date_type",
          label: "Date Type",
        }),
        search.createColumn({ name: "custrecord_date", label: "Date" }),
        search.createColumn({
          name: "custrecord_start_time",
          label: "Start Time",
        }),
        search.createColumn({ name: "custrecord_end_time", label: "End Time" }),
        search.createColumn({
          name: "internalid",
          join: "CUSTRECORD_SHOW_NUMBER_DATE",
          label: "Internal ID",
        }),
      ],
    });

    showDatesSearch.run().each(function (result) {
      // .run().each has a limit of 4,000 results

      let showDateObj = {
        id: result.id,
        recordType: result.recordType,
        event: {
          value: result.getValue("custrecord_show_number_date"),
          text: result.getText("custrecord_show_number_date"),
        },
        dateType: {
          value: result.getValue("custrecord_date_type"),
          text: result.getText("custrecord_date_type"),
        },
        date: result.getValue("custrecord_date"),
        startTime: result.getValue("custrecord_start_time"),
        endTime: result.getValue("custrecord_end_time"),
        internalId: result.getValue({
          name: "internalid",
          join: "CUSTRECORD_SHOW_NUMBER_DATE",
        }),
      };

      showDates.push(showDateObj);
      return true;
    });

    // Get event price stats
    let eventArr = [];
    let eventSearch = search.create({
      type: "customrecord_show",
      filters: [["internalid", "anyof", eid]],
      columns: [
        search.createColumn({
          name: "name",
          sort: search.Sort.ASC,
          label: "Name",
        }),
        search.createColumn({ name: "custrecord_tax_rate", label: "Tax Rate" }),
        search.createColumn({
          name: "custrecord_tax_percent",
          label: "Tax Percent",
        }),
        search.createColumn({
          name: "custrecord_adv_ord_date",
          label: "Advanced Order Date",
        }),
        search.createColumn({
          name: "custrecord_adv_price_level",
          label: "Advance Price Level",
        }),
        search.createColumn({
          name: "custrecord_std_price_level",
          label: "Standard Price Level",
        }),
        search.createColumn({
          name: "custrecord_site_price_level",
          label: "On Site Price Level",
        }),
        search.createColumn({
          name: "custrecord_show_mgmnt_price_lvl",
          label: "Show Management Price Level",
        }),
      ],
    });

    let eventSearchResultCount = eventSearch.runPaged().count;
    log.debug("eventSearchResultCount result count", eventSearchResultCount);

    eventSearch.run().each(function (result) {
      // .run().each has a limit of 4,000 results
      let eventResultObj = {
        id: result.id,
        recordType: result.recordType,
        name: result.getValue("name"),
        taxRate: result.getValue("custrecord_tax_rate"),
        taxPercent: result.getValue("custrecord_tax_percent"),
        advancedOrderDate: result.getValue("custrecord_adv_ord_date"),
        advancedPriceLevel: {
          value: result.getValue("custrecord_adv_price_level"),
          text: result.getText("custrecord_adv_price_level"),
        },
        standardPriceLevel: {
          value: result.getValue("custrecord_std_price_level"),
          text: result.getText("custrecord_std_price_level"),
        },
        sitePriceLevel: {
          value: result.getValue("custrecord_site_price_level"),
          text: result.getText("custrecord_site_price_level"),
        },
        showManagementPriceLevel: {
          value: result.getValue("custrecord_show_mgmnt_price_lvl"),
          text: result.getText("custrecord_show_mgmnt_price_lvl"),
        },
      };
      eventArr.push(eventResultObj);
      return true;
    });

    /*
		   0:
			   id: "209"
			   recordType: "noninventoryitem"
			   values: {
			   displayname: "4th Side Table Skirt (30\" high)"
			   itemid: "4th Side Table Skirt (30\" high)"
			   pricing.internalid: Array(1)
			   0: {value: "1", text: "1"}
			   length: 1
			   pricing.pricelevel: Array(1)
			   0: {value: "1", text: "Standard Price"}
			   length: 1
			   pricing.saleunit: []
			   pricing.unitprice: "43.00"
		   }

			SHOW Dates
		   id: "84"
			recordType: "customrecord_show_date"
			values:{
			CUSTRECORD_SHOW_NUMBER_DATE.internalid: Array(1)
			0: {value: "29", text: "29"}
			length: 1
			custrecord_date: "8/31/2021"
			custrecord_date_type: Array(1)
			0: {value: "11", text: "Exhibitor Move-In"}
			length: 1
			custrecord_end_time: "5:00 pm"
			custrecord_show_number_date: Array(1)
			0: {value: "29", text: "Atlanta Tech Summit 2021"}
			length: 1
			custrecord_start_time: "8:00 am"
			}

			event values: {
			custrecord_adv_ord_date: "9/1/2021"
			custrecord_adv_ord_date_1: "9/1/2021"
			custrecord_adv_price_level: Array(1)
			0: {value: "2", text: "Advance Price"}
			custrecord_adv_price_level_1: Array(1)
			0: {value: "2", text: "Advance Price"}
			length: 1
			custrecord_site_price_level: Array(1)
			v0: {value: "6", text: "Standard Show Site Price"}
			length: 1
			custrecord_std_price_level: Array(1)
			0: {value: "1", text: "Standard Price"}
			length: 1
			custrecord_std_price_level_1: Array(1)
			0: {value: "1", text: "Standard Price"}
			}

		   Advanced Price Level: Before advanced price date (deadline)
		   Standard: After the advanced price date but before the first show date.
		   Show Site price: On the first show date. Then after until end of show.
	   * */

    CS_SETTINGS = CS_SETTINGS || getSettings();

    let settings = CS_SETTINGS;

    let today = new Date();

    // Event specs
    let eventResultObj = eventArr[0];
    let eventAdvancedDate = new Date(eventResultObj.advancedOrderDate);

    let eventAdvancedPriceLevel = eventResultObj.advancedPriceLevel.value;
    let eventStandardPriceLevel = eventResultObj.standardPriceLevel.value;
    let eventSitePriceLevel = eventResultObj.sitePriceLevel.value;

    let priceDebugObject = {
      eventAdvancedDate,
      eventAdvancedPriceLevel,
      eventStandardPriceLevel,
      eventSitePriceLevel,
    };

    log.debug({
      title: "🟡 Current event price levels set:",
      details: priceDebugObject,
    });

    // Event Show Dates - GRAB SOONEST "Show Date"
    let sortedShowDates = showDates.sort(
      (d1, d2) => new Date(d1.date) - new Date(d2.date),
    );
    let onlyShowDates = sortedShowDates.filter(
      (eventdate) =>
        eventdate.dateType.text ===
        settings.custrecord_ng_cs_default_show_date[0].text,
    );

    /*
	 Advanced Price Level: Before advanced price date (deadline)
	 Standard: After the advanced price date but before the first show date.
	 Show Site price: On the first show date. Then after until end of show.
	*/

    let firstShowDate =
      onlyShowDates.length !== 0 ? new Date(onlyShowDates[0].date) : null;
    log.debug("First Show Date:", firstShowDate);
    // Run date conditionals
    if (eventAdvancedDate >= today) {
      // today is before adv date
      // Check Settings for Advanced Price Level Default
      // Do calculation

      // log.debug('Advanced Price Level Date: ', eventAdvancedDate)
      log.debug("Advanced Price Level: ", eventAdvancedPriceLevel);
      // log.debug('Today: ', today)
      /*log.debug('Item Price Level Index: ', itemPriceIndex)
				log.debug('Item Price: ', allItemPrices[itemPriceIndex])*/

      /*
				Dont return price return the price level value field.
				return allItemPrices[itemPriceIndex]*/
      return eventAdvancedPriceLevel;
    } else if (eventAdvancedDate < today && firstShowDate > today) {
      // today is after adv date AND today is before 1st show
      // Check Settings for Standard Price Level Default
      // Do calculation
      // log.debug('Item Price Level Index: ', itemPriceIndex)

      log.debug("Today: ", today);

      /* log.debug('Item Std Price: ', allItemPrices[itemPriceIndex])
				 log.debug('Standard Price Level: ', eventStandardPriceLevel)*/

      /*
				 Dont return price return the price level value field.
				 return allItemPrices[itemPriceIndex]*/
      return eventStandardPriceLevel;
    } else if (firstShowDate <= today) {
      // today is after 1st show
      // Check Settings for Show Price Level Default
      // Do calculation
      log.debug("Show Price Level: (site) ", eventSitePriceLevel);
      log.debug("Today: ", today);

      /*log.debug('Item Price Level Index: ', itemPriceIndex)
				log.debug('Item Shw Price: ', allItemPrices[itemPriceIndex])*/

      /*
				 Dont return price return the price level value field.
				 return allItemPrices[itemPriceIndex]*/
      return eventSitePriceLevel;
    }
  }

  /**
   * An object representing the output of the pricing information.
   *
   * @typedef {Object} PriceOutput
   *
   * @property {(String|Number)} comparePrice - The compare price in a strikethrough format.
   * @property {(String|Number)} price - The listing price.
   */

  /**
   * This function retrieves the current event and item and returns the price in accordance with the stipulated dates and levels.
   *
   * @param {(Number|String)} eid - The ID of the event from the CS Event record (show_table).
   * @param {(Number|String)} pid - The Product ID of the item for which the price levels are being retrieved.
   *
   * @throws {String} Will throw an error if eid and pid are not defined.
   * @throws {String} Will throw an error if eid is not defined.
   * @throws {String} Will throw an error if pid is not defined.
   *
   * @returns {PriceOutput} An object containing the pricing information.
   *
   * @example
   * // Assuming eid = '1' and pid = '2', the function returns
   * // {
   * //   comparePrice: '100',
   * //   price: '80'
   * // } if these are the prices linked with the event and product.
   * useItemPrice('1', '2');
   */
  const useItemPrice = (eid, pid) => {
    if (!eid && !pid) {
      throw "An event id followed by a item id needs specified in getItemPrice()";
    } else if (!eid) {
      throw "An event id arg is missing for getItemPrice()";
    } else if (!pid) {
      throw "A item id arg is missing for getItemPrice()";
    }

    let priceOutput = {
      comparePrice: null,
      price: null,
    };

    try {
      log.debug({ title: "ITEM: Event Id", details: eid });
      log.debug({ title: "ITEM: product Id", details: pid });

      CS_SETTINGS = CS_SETTINGS || getSettings();

      const materialHandlingBetaEnabled =
        CS_SETTINGS.custrecord_enable_material_handling_beta;

      let itemPriceResultLevels = [];
      let itemSearchPricesObj = search.create({
        type: "item",
        filters: [["internalid", "anyof", pid]],
        columns: [
          search.createColumn({
            name: "custitem_has_color_options",
            label: "Color Options",
          }),
          search.createColumn({
            name: "custitem_has_size_options",
            label: "Size Options",
          }),
          search.createColumn({
            name: "custitem_has_orient_options",
            label: "Orientation Options",
          }),
          search.createColumn({
            name: "custitem_ng_cs_has_graphic_options",
            label: "Graphic Options",
          }),
          search.createColumn({
            name: "custitem_is_sqft",
            label: "Square Foot Item",
          }),
          search.createColumn({
            name: "custitem_is_freight",
            label: "Freight Item",
          }),
          search.createColumn({
            name: "custitem_is_days",
            label: "Days Calc Item",
          }),
          search.createColumn({
            name: "custitem_show_duration",
            label: "Show Duration",
          }),
          search.createColumn({
            name: "custitem_labor_item",
            label: "Labor Item",
          }),
          search.createColumn({
            name: "custitem_cost_is_estimated",
            label: "Estimated",
          }),
          // Pricing Level Attributes
          search.createColumn({
            name: "itemid",
            sort: search.Sort.ASC,
            label: "Name",
          }),
          search.createColumn({ name: "displayname", label: "Display Name" }),
          search.createColumn({
            name: "custitem_ng_mat_handling_sched",
            label: "Material Handling Schedule",
          }),
          search.createColumn({
            name: "internalid",
            join: "pricing",
            label: "Internal ID",
          }),
          search.createColumn({
            name: "pricelevel",
            join: "pricing",
            label: "Price Level",
          }),
          search.createColumn({
            name: "saleunit",
            join: "pricing",
            label: "Sale Unit",
          }),
          search.createColumn({
            name: "unitprice",
            join: "pricing",
            label: "Unit Price",
          }),
        ],
      });
      let searchPricesCount = itemSearchPricesObj.runPaged().count;
      log.debug("item price levels result count", searchPricesCount);

      // If no price levels are on item return null price
      if (searchPricesCount === 0) {
        return null;
      }

      itemSearchPricesObj.run().each(function (result) {
        const materialHandlingSchedule = result.getValue(
          "custitem_ng_mat_handling_sched",
        );

        let materialHandlingScheduleLookup =
          materialHandlingSchedule &&
          search.lookupFields({
            type: "customrecord_ng_mat_handling_sched",
            id: materialHandlingSchedule,
            columns: MATERIAL_HANDLING_SCHEDULE_FIELDS,
          });

        let materialHandlingPricing = null;

        if (materialHandlingScheduleLookup) {
          // Format the data to be used in the front end
          materialHandlingPricing = {
            id: materialHandlingSchedule,
            name: materialHandlingScheduleLookup.name,
            chargeType: getListValueFromLookup(
              materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_charge_type,
            ),
            minWeight:
              materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_min_weight,
            maxWeight:
              materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_max_weight,
            firstPcPrice:
              materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_first_pc,
            additionalPcPrice:
              materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_addtl_pc,
            replaceEstimatedItem:
            materialHandlingScheduleLookup.custrecord_ng_replace_est_item,
            surcharge: {
              type: getListValueFromLookup(
                materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_surchgtype,
              ),
              percentage:
                materialHandlingScheduleLookup.custrecord_surcharge_percentage,
              flatFee:
                materialHandlingScheduleLookup.custrecord_surcharge_flat_fee,
              surchargeItem:
                materialHandlingScheduleLookup.custrecord_ng_cs_late_surcharge_item,
            },
            overtimeSurcharge: {
              type: getListValueFromLookup(
                materialHandlingScheduleLookup.custrecord_ng_ot_surchgtype,
              ),
              percentage:
                materialHandlingScheduleLookup.custrecord_ot_surcharge_percentage,
              flatFee:
                materialHandlingScheduleLookup.custrecord_ot_surcharge_flat_fee,
              surchargeItem:
                materialHandlingScheduleLookup.custrecord_ng_cs_overtime_surcharge_item,
            },
            doubleTimeSurcharge: {
              type: getListValueFromLookup(
                materialHandlingScheduleLookup.custrecord_ng_dt_surchgtype,
              ),
              percentage:
                materialHandlingScheduleLookup.custrecord_dt_surcharge_percentage,
              flatFee:
                materialHandlingScheduleLookup.custrecord_dt_surcharge_flat_fee,
              surchargeItem:
                materialHandlingScheduleLookup.custrecord_ng_cs_doubletime_srchrge_item,
            },
          };
        }

        // .run().each has a limit of 4,000 results
        let itemPriceResult = {
          id: result.id,
          name: result.getValue("itemid"),
          recordType: result.recordType,
          hasColorOptions: result.getValue("custitem_has_color_options"),
          hasSizeOptions: result.getValue("custitem_has_size_options"),
          hasOrientOptions: result.getValue("custitem_has_orient_options"),
          hasGraphicOptions: result.getValue(
            "custitem_ng_cs_has_graphic_options",
          ),
          isSquareFt: result.getValue("custitem_is_sqft"),
          isFreight: result.getValue("custitem_is_freight"),
          isDaysCalc: result.getValue("custitem_is_days"),
          isLabor: result.getValue("custitem_labor_item"),
          isEstimated: result.getValue("custitem_cost_is_estimated"),
          isShowDuration: result.getValue("custitem_show_duration"),
          displayName: result.getValue("displayname"),
          pricing: {
            internalId: result.getValue({
              name: "internalid",
              join: "pricing",
            }),
            priceLevel: {
              value: result.getValue({
                name: "pricelevel",
                join: "pricing",
              }),
              text: result.getText({
                name: "pricelevel",
                join: "pricing",
              }),
            },
            saleUnit: {
              value: result.getValue({
                name: "saleunit",
                join: "pricing",
              }),
              text: result.getText({
                name: "saleunit",
                join: "pricing",
              }),
            },
            unitPrice: result.getValue({
              name: "unitprice",
              join: "pricing",
            }),
            ...(materialHandlingPricing && {
              materialHanding: materialHandlingPricing,
            }),
          },
        };

        itemPriceResultLevels.push(itemPriceResult);
        return true;
      });

      // Get Show Dates
      let showDates = [];
      let showDatesSearch = search.create({
        type: "customrecord_show_date",
        filters: [["custrecord_show_number_date", "anyof", eid]],
        columns: [
          search.createColumn({
            name: "custrecord_show_number_date",
            label: "CS Event",
          }),
          search.createColumn({
            name: "custrecord_date_type",
            label: "Date Type",
          }),
          search.createColumn({ name: "custrecord_date", label: "Date" }),
          search.createColumn({
            name: "custrecord_start_time",
            label: "Start Time",
          }),
          search.createColumn({
            name: "custrecord_end_time",
            label: "End Time",
          }),
          search.createColumn({
            name: "internalid",
            join: "CUSTRECORD_SHOW_NUMBER_DATE",
            label: "Internal ID",
          }),
        ],
      });

      let showDatesSearchResult = showDatesSearch.runPaged().count;
      log.debug(
        "customrecord_show_dateSearchObj result count",
        showDatesSearchResult,
      );
      showDatesSearch.run().each(function (result) {
        // .run().each has a limit of 4,000 results
        let showDateObj = {
          id: result.id,
          recordType: result.recordType,
          event: {
            value: result.getValue("custrecord_show_number_date"),
            text: result.getText("custrecord_show_number_date"),
          },
          dateType: {
            value: result.getValue("custrecord_date_type"),
            text: result.getText("custrecord_date_type"),
          },
          date: result.getValue("custrecord_date"),
          startTime: result.getValue("custrecord_start_time"),
          endTime: result.getValue("custrecord_end_time"),
          internalId: result.getValue({
            name: "internalid",
            join: "CUSTRECORD_SHOW_NUMBER_DATE",
          }),
        };

        showDates.push(showDateObj);
        return true;
      });

      // Get event price stats
      let eventArr = [];
      let eventSearch = search.create({
        type: "customrecord_show",
        filters: [["internalid", "anyof", eid]],
        columns: [
          search.createColumn({
            name: "name",
            sort: search.Sort.ASC,
            label: "Name",
          }),
          search.createColumn({
            name: "custrecord_tax_rate",
            label: "Tax Rate",
          }),
          search.createColumn({
            name: "custrecord_tax_percent",
            label: "Tax Percent",
          }),
          search.createColumn({
            name: "custrecord_adv_ord_date",
            label: "Advanced Order Date",
          }),
          search.createColumn({
            name: "custrecord_adv_price_level",
            label: "Advance Price Level",
          }),
          search.createColumn({
            name: "custrecord_std_price_level",
            label: "Standard Price Level",
          }),
          search.createColumn({
            name: "custrecord_site_price_level",
            label: "On Site Price Level",
          }),
          search.createColumn({
            name: "custrecord_show_mgmnt_price_lvl",
            label: "Show Management Price Level",
          }),
        ],
      });
      let eventSearchResultCount = eventSearch.runPaged().count;
      log.debug(
        "customrecord_showSearchObj result count",
        eventSearchResultCount,
      );

      eventSearch.run().each(function (result) {
        // .run().each has a limit of 4,000 results
        let eventResultObj = {
          id: result.id,
          recordType: result.recordType,
          name: result.getValue("name"),
          taxRate: result.getValue("custrecord_tax_rate"),
          taxPercent: result.getValue("custrecord_tax_percent"),
          advancedOrderDate: result.getValue("custrecord_adv_ord_date"),
          advancedPriceLevel: {
            value: result.getValue("custrecord_adv_price_level"),
            text: result.getText("custrecord_adv_price_level"),
          },
          standardPriceLevel: {
            value: result.getValue("custrecord_std_price_level"),
            text: result.getText("custrecord_std_price_level"),
          },
          sitePriceLevel: {
            value: result.getValue("custrecord_site_price_level"),
            text: result.getText("custrecord_site_price_level"),
          },
          showManagementPriceLevel: {
            value: result.getValue("custrecord_show_mgmnt_price_lvl"),
            text: result.getText("custrecord_show_mgmnt_price_lvl"),
          },
        };

        eventArr.push(eventResultObj);
        return true;
      });

      // Get freight table
      let freightResults = [];
      let freight_table_search = search.create({
        type: "customrecord_freight_table",
        filters: [["custrecord_show_freight", "anyof", eid]],
        columns: [
          search.createColumn({
            name: "custrecord_show_freight",
            sort: search.Sort.ASC,
            label: "Event",
          }),
          search.createColumn({
            name: "custrecord_freight_rate",
            label: "Standard Freight Rate",
          }),
          search.createColumn({
            name: "custrecord_pre_show_rate",
            label: "Pre-Show Freight Rate",
          }),
          search.createColumn({
            name: "custrecord_inbetween_rate",
            label: "In-Between Freight Rate",
          }),
          search.createColumn({
            name: "custrecord_on_site_rate",
            label: "On-Site Freight Rate",
          }),
          search.createColumn({
            name: "custrecord_freight_item",
            label: "Freight Item",
          }),
        ],
      });

      // Keep the search object just don't run it if material handling beta is enabled
      if (!materialHandlingBetaEnabled) {
        let freightSearchResultsCount = freight_table_search.runPaged().count;
        log.debug({
          title: "Freight Table Results Count",
          details: freightSearchResultsCount,
        });

        freight_table_search.run().each((result) => {
          let freightPriceResult = {
            id: result.id,
            recordType: result.recordType,
            event: {
              value: result.getValue("custrecord_show_freight"),
              text: result.getText("custrecord_show_freight"),
            },
            standardFreightRate: result.getValue("custrecord_freight_rate"),
            preShowFreightRate: result.getValue("custrecord_pre_show_rate"),
            inBetweenFreightRate: result.getValue("custrecord_inbetween_rate"),
            onSiteFreightRate: result.getValue("custrecord_on_site_rate"),
            freightItem: {
              value: result.getValue("custrecord_freight_item"),
              text: result.getText("custrecord_freight_item"),
            },
          };

          freightResults.push(freightPriceResult);
          return true;
        });
      } else {
        log.audit({
          title:
            "🟢 Material handling beta enabled showing price of material schedule",
          details: "",
        });
      }
      /*
			   0:
				   id: "209"
				   recordType: "noninventoryitem"
				   values: {
				   displayname: "4th Side Table Skirt (30\" high)"
				   itemid: "4th Side Table Skirt (30\" high)"
				   pricing.internalid: Array(1)
				   0: {value: "1", text: "1"}
				   length: 1
				   pricing.pricelevel: Array(1)
				   0: {value: "1", text: "Standard Price"}
				   length: 1
				   pricing.saleunit: []
				   pricing.unitprice: "43.00"
			   }

				SHOW Dates
			   id: "84"
				recordType: "customrecord_show_date"
				values:{
				CUSTRECORD_SHOW_NUMBER_DATE.internalid: Array(1)
				0: {value: "29", text: "29"}
				length: 1
				custrecord_date: "8/31/2021"
				custrecord_date_type: Array(1)
				0: {value: "11", text: "Exhibitor Move-In"}
				length: 1
				custrecord_end_time: "5:00 pm"
				custrecord_show_number_date: Array(1)
				0: {value: "29", text: "Atlanta Tech Summit 2021"}
				length: 1
				custrecord_start_time: "8:00 am"
				}

				event values: {
				custrecord_adv_ord_date: "9/1/2021"
				custrecord_adv_ord_date_1: "9/1/2021"
				custrecord_adv_price_level: Array(1)
				0: {value: "2", text: "Advance Price"}
				custrecord_adv_price_level_1: Array(1)
				0: {value: "2", text: "Advance Price"}
				length: 1
				custrecord_site_price_level: Array(1)
				v0: {value: "6", text: "Standard Show Site Price"}
				length: 1
				custrecord_std_price_level: Array(1)
				0: {value: "1", text: "Standard Price"}
				length: 1
				custrecord_std_price_level_1: Array(1)
				0: {value: "1", text: "Standard Price"}
				}

			   Advanced Price Level: Before advanced price date (deadline)
			   Standard: After the advanced price date but before the first show date.
			   Show Site price: On the first show date. Then after until end of show.
		   * */

      let settings = CS_SETTINGS;

      let today = new Date();
      // eslint-disable-next-line no-unused-vars
      let dd = today.getDate();
      // eslint-disable-next-line no-unused-vars
      let mm = today.getMonth() + 1;
      // eslint-disable-next-line no-unused-vars
      let yyyy = today.getFullYear();

      // Event specs
      let eventResultObj = eventArr[0];
      let eventAdvancedDate = new Date(eventResultObj.advancedOrderDate);

      let eventAdvancedPriceLevel = eventResultObj.advancedPriceLevel.value;
      let eventStandardPriceLevel = eventResultObj.standardPriceLevel.value;
      let eventSitePriceLevel = eventResultObj.sitePriceLevel.value;

      let priceDebugObject = {
        eventAdvancedDate,
        eventAdvancedPriceLevel,
        eventStandardPriceLevel,
        eventSitePriceLevel,
      };

      log.debug({
        title: "🟡 Current event price levels set:",
        details: priceDebugObject,
      });

      // Event Show Dates - GRAB SOONEST "Show Date"
      let sortedShowDates = showDates.sort(
        (d1, d2) => new Date(d1.date) - new Date(d2.date),
      );
      let onlyShowDates = sortedShowDates.filter(
        (eventdate) =>
          eventdate.dateType.text ===
          settings.custrecord_ng_cs_default_show_date[0].text,
      );

      // Use Var if needed
      // eslint-disable-next-line no-unused-vars
      // let onlyExibitorMoveIn = sortedShowDates.filter(
      //   (eventdate) =>
      //     eventdate.values.custrecord_date_type[0].text ===
      //     settings.custrecord_ng_cs_default_exhib_move_in[0].text
      // );

      // log.debug('Show date sort', sortedShowDates)
      // log.debug('Only Show dates', onlyShowDates)
      // log.debug('Settings', settings)

      /*
					showDuration={item.values.custitem_show_duration}
					isFreight={item.values.custitem_is_freight}
					isLabor={item.values.custitem_labor_item}
					isSquareFt={item.values.custitem_is_sqft}
					isEstimated={item.values.custitem_cost_is_estimated}
					isDays={item.values.custitem_is_days}
					hasColorOptions={item.values.custitem_has_color_options
					hasOrientOptions={item.values.custitem_has_orient_options
					hasGraphicOptions={item.values.custitem_has_graphic_options
					hasSizeOptions={item.values.custitem_has_size_options
				 */

      let itemObjectResult = itemPriceResultLevels[0];
      let itemPriceLevels = itemPriceResultLevels;
      let allItemPriceLevels = itemPriceLevels.map(
        (item) => item.pricing.priceLevel,
      );
      let allItemPrices = itemPriceLevels.map((item) => item.pricing.unitPrice);
      // let isLabor = itemObjectResultJson.values.custitem_labor_item // Use if needed
      let isFreight = itemObjectResult.isFreight;
      // let isDaysCalc = itemObjectResultJson.values.custitem_is_days // Use if needed
      // let isSquareFt = itemObjectResultJson.values.custitem_is_sqft // Use if needed
      // let isEstimated = itemObjectResultJson.values.custitem_cost_is_estimated // Use if needed
      // let showDuration = itemObjectResultJson.values.custitem_show_duration // Use if needed
      // let hasColorOptions = itemObjectResultJson.values.custitem_has_color_options // Use if needed
      // let hasOrientOptions = itemObjectResultJson.values.custitem_has_orient_options // Use if needed
      // let hasGraphicOptions = itemObjectResultJson.values.custitem_has_graphic_options // Use if needed
      // let hasSizeOptions = itemObjectResultJson.values.custitem_has_size_options // Use if needed

      // log.debug('An item variable: isLabor', isLabor)
      log.debug({ title: "An item variable: isFreight", details: isFreight });
      // log.debug('An item variable: isDaysCalc', isDaysCalc)
      // log.debug('An item variable: isSquareFt', isSquareFt)
      // log.debug('An item variable: isEstimated', isEstimated)
      // log.debug('An item variable: showDuration', showDuration)
      // log.debug('An item variable: hasColorOptions', hasColorOptions)
      // log.debug('An item variable: hasOrientOptions', hasOrientOptions)
      // log.debug('An item variable: hasGraphicOptions', hasGraphicOptions)
      // log.debug('An item variable: hasSizeOptions', hasSizeOptions)
      //
      // log.debug('An item variable: allItemPrices', allItemPrices)
      log.debug("An item variable: allItemPriceLevels", allItemPriceLevels);

      /*
				  Advanced Price Level: Before advanced price date (deadline)
				  Standard: After the advanced price date but before the first show date.
				  Show Site price: On the first show date. Then after until end of show.
			   */
      let itemStdPriceIndex = allItemPriceLevels.findIndex((pl) => {
        let itemPriceLevel = Number(pl.value);
        let eventAStdPriceLvl = Number(eventStandardPriceLevel);

        return itemPriceLevel === eventAStdPriceLvl;
      });

      let itemAdvPriceIndex = allItemPriceLevels.findIndex((pl) => {
        let itemPriceLevel = Number(pl.value);
        let eventAdvPriceLvl = Number(eventAdvancedPriceLevel);

        return itemPriceLevel === eventAdvPriceLvl;
      });

      let itemSitePriceIndex = allItemPriceLevels.findIndex((pl) => {
        let itemPriceLevel = Number(pl.value);
        log.debug({ title: "Item price Level:", details: itemPriceLevel });
        log.debug({
          title: "Item to match price level to event:",
          details: eventSitePriceLevel,
        });
        let eventAShowPriceLvl = Number(eventSitePriceLevel);

        return itemPriceLevel === eventAShowPriceLvl;
      });
      let firstShowDate = new Date();

      if (onlyShowDates.length !== 0) {
        firstShowDate = new Date(onlyShowDates[0].date);
      } else {
        log.error({
          title: "NO SHOW DATES ADDED!",
          details:
            "PLEASE ENTER A SHOW DATE FOR ITEM PRICE ACCURACY TO CORRECT LEVEL.",
        });
        return "No Show Dates";
      }

      log.debug("First Show Date:", firstShowDate);
      log.debug({ title: "📊 Freight table results", details: freightResults });

      let freightItemPrices = freightResults.filter(
        (frItem) => frItem.freightItem.value === `${pid}`,
      );

      log.debug({
        title: "📊 Freight price results",
        details: freightItemPrices,
      });

      let freightItemId =
        freightItemPrices.length !== 0 && freightResults[0].freightItem.value;
      let filteredFreightItem =
        freightItemPrices.length !== 0 &&
        freightItemPrices[0].freightItem.value;
      let freightInBetweenRate =
        freightItemPrices.length !== 0 &&
        freightItemPrices[0].inBetweenFreightRate;
      let freightPreShowRate =
        freightItemPrices.length !== 0 &&
        freightItemPrices[0].preShowFreightRate;
      let freightOnSiteRate =
        freightItemPrices.length !== 0 &&
        freightItemPrices[0].onSiteFreightRate;

      // ✨ Beta material handling schedule record.
      let materialHandlingSchedule = itemObjectResult.pricing?.materialHanding;

      today.setHours(0, 0, 0, 0);

      // Run date conditionals

      /** =================================================================
       * Today is before adv date
       * Check Settings for Advanced Price Level Default
       * If beta feature for material handling is enabled AND a material
       * handling schedule is present, add the schedule to the price output
       * ==================================================================*/

      if (eventAdvancedDate >= today) {
        if (
          !materialHandlingBetaEnabled &&
          freightItemPrices.length !== 0 &&
          isFreight &&
          freightItemId
        ) {
          // Freight table functionality

          log.debug({
            title: "🔧 Item is freight setting:",
            details: filteredFreightItem,
          });
          priceOutput.comparePrice = freightInBetweenRate;
          priceOutput.price = freightPreShowRate;

          return priceOutput;
        } else if (materialHandlingBetaEnabled && materialHandlingSchedule) {
          log.debug({
            title:
              "🟢 Material handling beta enabled showing price of material schedule",
            details: materialHandlingSchedule,
          });

          // If a material handling schedule charge type is set to per-piece return pricing with per-piece added
          priceOutput.materialSchedule = materialHandlingSchedule;
        }
        // Do calculation

        // log.debug('Advanced Price Level Date: ', eventAdvancedDate)
        log.debug("Advanced Price Level: ", eventAdvancedPriceLevel);
        // log.debug('Today: ', today)
        log.debug("Item Price Level Index: ", itemAdvPriceIndex);
        log.debug("Item Price: ", allItemPrices[itemAdvPriceIndex]);
        // Importing outside var

        // Check if price level is valid if not insert null value
        let comparePriceOutput = !isNaN(
          Number(allItemPrices[itemStdPriceIndex]),
        )
          ? parseFloat(allItemPrices[itemStdPriceIndex])
          : null;

        // Check if price level is valid if not insert null value
        priceOutput.price = !isNaN(Number(allItemPrices[itemAdvPriceIndex]))
          ? parseFloat(allItemPrices[itemAdvPriceIndex])
          : null;

        priceOutput.comparePrice = comparePriceOutput;
        return priceOutput;

        /* ======================================================
         * Today is after adv date AND today is before 1st show
         * Check Settings for Standard Price Level Default
         * Do calculation
         * =======================================================*/
      } else if (today > eventAdvancedDate && today < firstShowDate) {
        log.debug("Item Price Level Index: ", itemStdPriceIndex);

        log.debug("Today: ", today);
        if (
          !materialHandlingBetaEnabled &&
          freightItemPrices.length !== 0 &&
          isFreight &&
          freightItemId
        ) {
          log.debug({
            title: "🔧 Item is freight setting:",
            details: freightItemId,
          });

          priceOutput.price = freightInBetweenRate;

          return priceOutput;
        } else if (materialHandlingBetaEnabled && materialHandlingSchedule) {
          log.debug({
            title:
              "🟢 Material handling beta enabled showing price of material schedule",
            details: materialHandlingSchedule,
          });

          // If a material handling schedule charge type is set to per piece return pricing with per piece added
          priceOutput.materialSchedule = materialHandlingSchedule;
        }

        log.debug("Item Std Price: ", allItemPrices[itemStdPriceIndex]);
        log.debug("Standard Price Level: ", eventStandardPriceLevel);

        // Check if price level is valid if not insert null value
        priceOutput.price = !isNaN(Number(allItemPrices[itemStdPriceIndex]))
          ? parseFloat(allItemPrices[itemStdPriceIndex])
          : null;

        return priceOutput;

        /* =================================================================
         * Today is after 1st show
         * Check Settings for Show Price Level Default
         * Do calculation
         * ================================================================*/
      } else if (firstShowDate <= today) {
        log.debug("Show Price Level: (site) ", eventSitePriceLevel);
        log.debug("Today: ", today);

        log.debug({
          title: `🔧 Is item matching PID for ${pid}`,
          details: freightItemId,
        });

        if (
          !materialHandlingBetaEnabled &&
          freightItemPrices.length !== 0 &&
          isFreight &&
          freightItemId
        ) {
          log.debug({
            title: "🔧 Item is freight setting:",
            details: freightItemId,
          });
          priceOutput.price = freightOnSiteRate;

          return priceOutput;
        } else if (materialHandlingBetaEnabled && materialHandlingSchedule) {
          log.debug({
            title:
              "🟢 Material handling beta enabled showing price of material schedule",
            details: materialHandlingSchedule,
          });

          // If a material handling schedule charge type is set to per piece return pricing with per piece added
          priceOutput.materialSchedule = materialHandlingSchedule;
        }

        log.debug("Item Price Level Index: ", itemSitePriceIndex);
        log.debug("Item Shw Price: ", allItemPrices[itemSitePriceIndex]);

        // Check if price level is valid if not insert null value
        priceOutput.price = !isNaN(Number(allItemPrices[itemSitePriceIndex]))
          ? parseFloat(allItemPrices[itemSitePriceIndex])
          : null;

        return priceOutput;
      }

      return priceOutput;
    } catch (err) {
      log.error({
        title: "❌ An error occurred with item pricing:",
        details: err,
      });
    }
  };

  /**
   * This function `useEventPricingDates` retrieves information related to the pricing and schedules of the given event. It calls several searches in sequence, obtaining information such as show dates, price stats, freight table, and assigns these to the `output` object.
   *
   * It requires one parameter `eid`, the event id, which will be used in the internal searches as a filter. This can be a `Number` or `String`.
   *
   * The function returns an `Object` with the following properties:
   * - laborDates: An array containing the labor dates.
   * - showDates: An array containing the show dates.
   * - daysCalcDates: An array holding the calculated dates.
   * - freightTable: An array representing the freight table.
   * - laborSchedule: An array holding the labor schedule.
   *
   * @param {Number|String} eid - The event id (show_table)
   * @example
   * ```js
   * useEventPricingDates(5)
   * ```
   * @returns {
   *  {
   *  laborDates: Object[],
   *  showDates: Object[],
   *  daysCalcDates: Object[],
   *  freightTable: Object[],
   *  laborSchedule: Object[]
   *  }
   * }
   */
  const useEventPricingDates = (eid) => {
    log.debug({ title: "ITEM: Event Id", details: eid });

    let output = {
      laborDates: [],
      showDates: [],
      daysCalcDates: [],
      freightTable: [],
      laborSchedule: [],
      dates: [],
    };

    try {
      // Get Show Dates
      let showDates = [];
      // Get event price stats
      let eventArr = [];
      // Get freight table
      let freightResults = [];

      let showDatesSearch = search.create({
        type: "customrecord_show_date",
        filters: [["custrecord_show_number_date", "anyof", eid]],
        columns: [
          search.createColumn({
            name: "scriptid",
            sort: search.Sort.ASC,
            label: "Script ID",
          }),
          search.createColumn({
            name: "custrecord_show_number_date",
            label: "CS Event",
          }),
          search.createColumn({
            name: "custrecord_date_type",
            label: "Date Type",
          }),
          search.createColumn({ name: "custrecord_date", label: "Date" }),
          search.createColumn({
            name: "custrecord_start_time",
            label: "Start Time",
          }),
          search.createColumn({
            name: "custrecord_end_time",
            label: "End Time",
          }),
          search.createColumn({
            name: "internalid",
            join: "CUSTRECORD_SHOW_NUMBER_DATE",
            label: "Internal ID",
          }),
        ],
      });

      let showDatesSearchResult = showDatesSearch.runPaged().count;
      log.debug(
        "customrecord_show_dateSearchObj result count",
        showDatesSearchResult,
      );
      showDatesSearch.run().each(function (result) {
        // .run().each has a limit of 4,000 results

        let showDateObj = {
          id: result.id,
          recordType: result.recordType,
          event: {
            value: result.getValue("custrecord_show_number_date"),
            text: result.getText("custrecord_show_number_date"),
          },
          dateType: {
            value: result.getValue("custrecord_date_type"),
            text: result.getText("custrecord_date_type"),
          },
          date: result.getValue("custrecord_date"),
          startTime: result.getValue("custrecord_start_time"),
          endTime: result.getValue("custrecord_end_time"),
          internalId: result.getValue({
            name: "internalid",
            join: "CUSTRECORD_SHOW_NUMBER_DATE",
          }),
        };

        showDates.push(showDateObj);
        return true;
      });

      let eventSearch = search.create({
        type: "customrecord_show",
        filters: [["internalidnumber", "equalto", eid]],
        columns: [
          search.createColumn({
            name: "name",
            sort: search.Sort.DESC,
            label: "Name",
          }),
          search.createColumn({
            name: "custrecord_tax_rate",
            label: "Tax Rate",
          }),
          search.createColumn({
            name: "custrecord_tax_percent",
            label: "Tax Percent",
          }),
          search.createColumn({
            name: "custrecord_adv_ord_date",
            label: "Advanced Order Date",
          }),
          search.createColumn({
            name: "custrecord_adv_price_level",
            label: "Advance Price Level",
          }),
          search.createColumn({
            name: "custrecord_std_price_level",
            label: "Standard Price Level",
          }),
          search.createColumn({
            name: "custrecord_site_price_level",
            label: "On Site Price Level",
          }),
          search.createColumn({
            name: "custrecord_show_mgmnt_price_lvl",
            label: "Show Management Price Level",
          }),
        ],
      });

      // let eventSearchResultCount = eventSearch.runPaged().count;
      // log.debug(
      //   "customrecord_showSearchObj result count",
      //   eventSearchResultCount
      // );

      eventSearch.run().each(function (result) {
        // .run().each has a limit of 4,000 results
        let eventResultObj = {
          id: result.id,
          recordType: result.recordType,
          name: result.getValue("name"),
          taxRate: result.getValue("custrecord_tax_rate"),
          taxPercent: result.getValue("custrecord_tax_percent"),
          advancedOrderDate: result.getValue("custrecord_adv_ord_date"),
          advancedPriceLevel: {
            value: result.getValue("custrecord_adv_price_level"),
            text: result.getText("custrecord_adv_price_level"),
          },
          standardPriceLevel: {
            value: result.getValue("custrecord_std_price_level"),
            text: result.getText("custrecord_std_price_level"),
          },
          sitePriceLevel: {
            value: result.getValue("custrecord_site_price_level"),
            text: result.getText("custrecord_site_price_level"),
          },
          showManagementPriceLevel: {
            value: result.getValue("custrecord_show_mgmnt_price_lvl"),
            text: result.getText("custrecord_show_mgmnt_price_lvl"),
          },
        };

        eventArr.push(eventResultObj);
        return true;
      });

      log.debug({ title: "Event added:", details: eventArr });

      let freight_table_search = search.create({
        type: "customrecord_freight_table",
        filters: [["custrecord_show_freight", "anyof", eid]],
        columns: [
          search.createColumn({
            name: "custrecord_show_freight",
            sort: search.Sort.ASC,
            label: "Event",
          }),
          search.createColumn({
            name: "custrecord_freight_rate",
            label: "Standard Freight Rate",
          }),
          search.createColumn({
            name: "custrecord_pre_show_rate",
            label: "Pre-Show Freight Rate",
          }),
          search.createColumn({
            name: "custrecord_inbetween_rate",
            label: "In-Between Freight Rate",
          }),
          search.createColumn({
            name: "custrecord_on_site_rate",
            label: "On-Site Freight Rate",
          }),
          search.createColumn({
            name: "custrecord_freight_item",
            label: "Freight Item",
          }),
        ],
      });

      let freightSearchResultsCount = freight_table_search.runPaged().count;
      log.debug({
        title: "Freight Table Results Count",
        details: freightSearchResultsCount,
      });

      freight_table_search.run().each((each) => {
        freightResults.push(each);
        return true;
      });

      /*
			   0:
				   id: "209"
				   recordType: "noninventoryitem"
				   values: {
				   displayname: "4th Side Table Skirt (30\" high)"
				   itemid: "4th Side Table Skirt (30\" high)"
				   pricing.internalid: Array(1)
				   0: {value: "1", text: "1"}
				   length: 1
				   pricing.pricelevel: Array(1)
				   0: {value: "1", text: "Standard Price"}
				   length: 1
				   pricing.saleunit: []
				   pricing.unitprice: "43.00"
			   }

				SHOW Dates
			   id: "84"
				recordType: "customrecord_show_date"
				values:{
				CUSTRECORD_SHOW_NUMBER_DATE.internalid: Array(1)
				0: {value: "29", text: "29"}
				length: 1
				custrecord_date: "8/31/2021"
				custrecord_date_type: Array(1)
				0: {value: "11", text: "Exhibitor Move-In"}
				length: 1
				custrecord_end_time: "5:00 pm"
				custrecord_show_number_date: Array(1)
				0: {value: "29", text: "Atlanta Tech Summit 2021"}
				length: 1
				custrecord_start_time: "8:00 am"
				}

				event values: {
				custrecord_adv_ord_date: "9/1/2021"
				custrecord_adv_ord_date_1: "9/1/2021"
				custrecord_adv_price_level: Array(1)
				0: {value: "2", text: "Advance Price"}
				custrecord_adv_price_level_1: Array(1)
				0: {value: "2", text: "Advance Price"}
				length: 1
				custrecord_site_price_level: Array(1)
				v0: {value: "6", text: "Standard Show Site Price"}
				length: 1
				custrecord_std_price_level: Array(1)
				0: {value: "1", text: "Standard Price"}
				length: 1
				custrecord_std_price_level_1: Array(1)
				0: {value: "1", text: "Standard Price"}
				}

			   Advanced Price Level: Before advanced price date (deadline)
			   Standard: After the advanced price date but before the first show date.
			   Show Site price: On the first show date. Then after until end of show.
		   * */

      CS_SETTINGS = CS_SETTINGS || getSettings();

      let settings = CS_SETTINGS;

      let today = new Date();
      // eslint-disable-next-line no-unused-vars
      let dd = today.getDate();
      // eslint-disable-next-line no-unused-vars
      let mm = today.getMonth() + 1;
      // eslint-disable-next-line no-unused-vars
      let yyyy = today.getFullYear();

      // Event specs
      let eventResultObj = eventArr[0];
      let eventAdvancedDate = new Date(eventResultObj.advancedOrderDate);

      let eventAdvancedPriceLevel = eventResultObj.advancedPriceLevel.value;
      let eventStandardPriceLevel = eventResultObj.standardPriceLevel.value;
      let eventSitePriceLevel = eventResultObj.sitePriceLevel.value;

      let priceDebugObject = {
        eventAdvancedDate,
        eventAdvancedPriceLevel,
        eventStandardPriceLevel,
        eventSitePriceLevel,
      };

      log.debug({
        title: "🟡 Current event price levels set:",
        details: priceDebugObject,
      });

      // Event Show Dates - GRAB SOONEST "Show Date"

      log.debug({
        title: "🟡 Current event price levels set:",
        details: priceDebugObject,
      });

      // Event Show Dates - GRAB SOONEST "Show Date"
      let sortedShowDates = showDates.sort(
        (d1, d2) => new Date(d1.date) - new Date(d2.date),
      );
      let onlyShowDates = sortedShowDates.filter(
        (eventdate) =>
          eventdate.dateType.text ===
          settings.custrecord_ng_cs_default_show_date[0].text,
      );

      let daysCalcDateTypes = settings.custrecord_ng_cs_dflt_d_calc_date_types;
      let daysCalcDates = [];
      daysCalcDateTypes.forEach((type) => {
        let foundDateTypeArr = sortedShowDates.filter(
          (event_date) => event_date.dateType.text === type.text,
        );
        log.debug({
          title: `Found array ${type.text}`,
          details: foundDateTypeArr,
        });
        // Have to push()..... concat() doesn't work LOL
        foundDateTypeArr.forEach((dateResult) =>
          daysCalcDates.push(dateResult),
        );
        log.debug({ title: "Concat days calc array", details: daysCalcDates });
      });
      log.debug({ title: "Days calc array", details: daysCalcDates });

      let laborDateTypes = settings.custrecord_ng_cs_dflt_labor_date_types;
      let laborDates = [];

      laborDateTypes.forEach((type) => {
        let foundDateTypeArr = sortedShowDates.filter(
          (event_date) => event_date.dateType.text === type.text,
        );
        log.debug({
          title: `Found in labor array ${type.text}`,
          details: foundDateTypeArr,
        });
        // Have to push()..... concat() doesn't work LOL
        foundDateTypeArr.forEach((dateResult) => laborDates.push(dateResult));
        log.debug({ title: "Concat labor array", details: laborDates });
      });
      log.debug({ title: "Labor array", details: laborDates });

      let laborSchedule = [];

      let cs_event_labor_schedule_search = search.create({
        type: "customrecord_ng_cs_show_labor_schedule",
        filters: [["custrecord_ng_cs_labor_show", "anyof", eid]],
        columns: [
          search.createColumn({
            name: "custrecord_ng_cs_labor_date",
            label: "Labor Date",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_labor_start",
            label: "Labor Start",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_labor_end",
            label: "Labor End",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_labor_type",
            label: "Labor Type",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_labor_multiplier",
            label: "Labor Multiplier",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_supervisor_markup",
            label: "Supervisor Markup",
          }),
        ],
      });

      var searchResultCount = cs_event_labor_schedule_search.runPaged().count;
      log.debug(
        "cs_event_labor_schedule_search result count",
        searchResultCount,
      );
      cs_event_labor_schedule_search.run().each(function (result) {
        // .run().each has a limit of 4,000 results
        let laborScheduleObj = {
          id: result.id,
          date: moment(result.getValue("custrecord_ng_cs_labor_date")).format(
            "yyyy-MM-DD",
          ),
          start: moment(
            result.getValue("custrecord_ng_cs_labor_start"),
            "hh:mm a",
          ).format("HH:mm"),
          end: moment(
            result.getValue("custrecord_ng_cs_labor_end"),
            "hh:mm a",
          ).format("HH:mm"),
          multiplier: result.getValue("custrecord_ng_cs_labor_multiplier"),
          supervision: result.getValue("custrecord_ng_cs_supervisor_markup"),
          type: result.getText("custrecord_ng_cs_labor_type"),
        };

        laborSchedule.push(laborScheduleObj);
        return true;
      });

      output.showDates = onlyShowDates;
      output.laborDates = laborDates;
      output.daysCalcDates = daysCalcDates;
      output.freightTable = freightResults;
      output.laborSchedule = laborSchedule;
      output.dates = sortedShowDates;

      return output;
    } catch (err) {
      log.error({
        title: "❌ Error occurred with getting dates:",
        details: err,
      });
    }
  };

  /**
   * @typedef {Object} TaxGroup
   * @property {string} id - Tax code ID, if no value is assigned, it is set to "Non-Taxable"
   * @property {string} name - Name of the tax code
   * @property {string} rate - The rate of the tax
   * @property {string} unitprice1 - The first unit price
   * @property {string} unitprice2 - The second unit price
   * @property {string} city - The associated city of the tax group
   * @property {string} state - The associated state of the tax group
   * @property {string} country - The associated country of the tax group
   * @property {string} zip - The associated zip code of the tax group
   */

  /**
   * @typedef {Object} Options
   * @property {string} city - City of the location for the associated tax code
   * @property {string} state - State of the location for the associated tax code
   * @property {string} zip - Zip code of the location for the associated tax code
   * @property {(string|Number)} country - An ID of the country where the location for the associated tax code is, these are values obtained from SuiteQL/Country field (e.g. 230, 38, etc.)
   * @property {string} countryShortName - Short name of the country where location for associated tax code is, these are short names obtained from SuiteQL (e.g. US, CA, etc.)
   */

  /**
   * @typedef {Object} TaxGroup
   * @property {string} id - Tax code ID, if no value is assigned, it is set to "Non-Taxable"
   * @property {string} name - Name of the tax code
   * @property {string} rate - The rate of the tax
   * @property {string} unitprice1 - The first unit price
   * @property {string} unitprice2 - The second unit price
   * @property {string} city - The associated city of the tax group
   * @property {string} state - The associated state of the tax group
   * @property {string} country - The associated country of the tax group
   * @property {string} zip - The associated zip code of the tax group
   */

  /**
   * @typedef {Object} Options
   * @property {string} city - City of the location for the associated tax code
   * @property {string} state - State of the location for the associated tax code using the shortname
   * @property {string} zip - Zip code of the location for the associated tax code
   * @property {(string|Number)} country - An ID of the country where the location for the associated tax code is
   * @property {string} countryShortName - Short name of the country where location for associated tax code is
   */

  /**
   * This function gets associated tax code for a given location with optimized retrieval
   *
   * @function useTaxCode
   * @param {Options} options - options for getting tax code for a location
   * @returns {TaxGroup} - Returns an object containing the tax code ID, name, rate, and unit prices
   */
  const useTaxCode = (options) => {
    let { city, state, zip, country, countryShortName } = options;
    let logger = useLogger();
    logger.log("⚡️ useTaxCode() called with options:", options);

    let taxCode = {
      id: "", // Default to "Non-Taxable"
      name: "",
      rate: "",
      unitprice1: "",
      unitprice2: "",
    };

    CS_SETTINGS = CS_SETTINGS || getSettings();
    let settings = CS_SETTINGS;
    let countryIsUs = country === "230";

    logger.log("Country is US?", countryIsUs);
    logger.log("CS_SETTINGS:", settings);

    try {
      // Build search filter dynamically as values come in
      let taxGroupFilter = [["isinactive", "is", "F"]];

      if (countryShortName) {
        taxGroupFilter.push("AND", ["country", "anyof", countryShortName]);
      }

      if (state) {
        taxGroupFilter.push("AND", ["state", "is", state]);
      }

      if (city) {
        const cityPieces = city.split(" ");
        const cityFilter =
          cityPieces.length > 1
            ? `${cityPieces[0]} ${cityPieces[1].substring(0, 1)}`
            : city;
        taxGroupFilter.push("AND", ["city", "contains", cityFilter]);
      }

      if (zip) {
        taxGroupFilter.push("AND", ["zip", "contains", zip]);
      }

      logger.log("Tax group filter:", taxGroupFilter);

      // Add text filter if configured
      if (settings?.custrecord_ng_cs_tax_auto_sel_txt_filter) {
        taxGroupFilter.push("AND", [
          "itemid",
          "contains",
          settings.custrecord_ng_cs_tax_auto_sel_txt_filter,
        ]);
      }

      const cols = [
        search.createColumn({ name: "itemid", sort: search.Sort.ASC }),
        search.createColumn({ name: "rate" }),
        search.createColumn({ name: "country" }),
        search.createColumn({ name: "city" }),
        search.createColumn({ name: "state" }),
        search.createColumn({ name: "zip" }),
      ];

      // Add additional columns for non-US tax groups
      if (!countryIsUs) {
        cols.push(
          search.createColumn({ name: "taxitem1" }),
          search.createColumn({ name: "unitprice1" }),
          search.createColumn({ name: "taxitem2" }),
          search.createColumn({ name: "unitprice2" }),
        );
      }

      logger.log("Search columns:", cols);

      const taxGroupSearch = search.create({
        type: "taxgroup",
        filters: taxGroupFilter,
        columns: cols,
      });

      logger.log("Tax group search created");

      let resultCount = 0;
      // Process results as they come in
      taxGroupSearch.run().each(function (result) {
        resultCount++;
        const currentTaxGroup = {
          id: result.id,
          country: result.getValue("country"),
          city: result.getValue("city"),
          state: result.getValue("state"),
          name: result.getValue("itemid"),
          rate: result.getValue("rate"),
          zip: result.getValue("zip"),
          unitprice1: result.getValue("unitprice1"),
          unitprice2: result.getValue("unitprice2"),
        };

        logger.log("Processing tax group:", currentTaxGroup);

        // Scoring system for best match
        let currentScore = 0;
        let existingScore = 0;

        if (currentTaxGroup.country === countryShortName) currentScore += 1;
        if (taxCode.country === countryShortName) existingScore += 1;

        if (currentTaxGroup.state === state) currentScore += 2;
        if (taxCode.state === state) existingScore += 2;

        if (currentTaxGroup.city.includes(city)) currentScore += 4;
        if (taxCode.city === city) existingScore += 4;

        if (currentTaxGroup.zip === zip) currentScore += 8;
        if (taxCode.zip === zip) existingScore += 8;

        logger.log(
          "Current score:",
          currentScore,
          "Existing score:",
          existingScore,
        );

        // Update tax code if current result is a better match
        if (
          currentScore > existingScore ||
          (currentScore === existingScore &&
            parseFloat(currentTaxGroup.rate.replace("%", "")) >
              parseFloat(taxCode.rate.replace("%", "")))
        ) {
          taxCode = currentTaxGroup;
          logger.log("Updated tax code to:", taxCode);
        }

        return true;
      });

      logger.log("Total results processed:", resultCount);

      // Set default non-taxable code if no match found
      if (taxCode.id === "") {
        taxCode.id = countryIsUs ? "-8" : "";
        logger.log("No match found, setting default tax code:", taxCode.id);
      }
    } catch (err) {
      logger.error(
        "Error encountered searching for applicable tax groups",
        err,
      );
    }

    logger.log("Final tax code:", taxCode);
    return taxCode;
  };

  function getTaxGroups(countryShortName, state, city, zip, settings) {
    const taxGroupFilter = buildSearchFilter(
      countryShortName,
      state,
      city,
      zip,
    );
    const cols = getTaxGroupColumns(countryShortName === "230");

    if (settings?.custrecord_ng_cs_tax_auto_sel_txt_filter) {
      taxGroupFilter.push("and", [
        "itemid",
        "contains",
        settings.custrecord_ng_cs_tax_auto_sel_txt_filter,
      ]);
    }

    const taxGroupSearch = search.create({
      type: "taxgroup",
      filters: taxGroupFilter,
      columns: cols,
    });

    const taxGroups = [];
    getAllResultsFor(taxGroupSearch, (result) => {
      taxGroups.push(formatTaxGroupResult(result));
    });

    return taxGroups;
  }

  function buildSearchFilter(countryShortName, state, city, zip) {
    let taxGroupFilter = [["isinactive", "is", "F"]];
    if (countryShortName)
      taxGroupFilter.push("AND", ["country", "anyof", countryShortName]);
    if (state) taxGroupFilter.push("AND", ["state", "is", state]);
    if (city) {
      const cityPieces = city.split(" ");
      const cityFilter =
        cityPieces.length > 1
          ? `${cityPieces[0]} ${cityPieces[1].substring(0, 1)}`
          : city;
      taxGroupFilter.push("AND", ["city", "contains", cityFilter]);
    }
    if (zip) taxGroupFilter.push("AND", ["zip", "contains", zip]);
    return taxGroupFilter;
  }

  function getTaxGroupColumns(isUS) {
    const cols = [
      search.createColumn({
        name: "itemid",
        sort: search.Sort.ASC,
        label: "Item ID",
      }),
      search.createColumn({ name: "rate", label: "Rate" }),
      search.createColumn({ name: "country", label: "Country" }),
      search.createColumn({ name: "city", label: "City" }),
      search.createColumn({ name: "state", label: "State/Province/County" }),
      search.createColumn({ name: "zip", label: "Zip" }),
    ];

    if (!isUS) {
      cols.push(
        search.createColumn({ name: "taxitem1", label: "GST/HST" }),
        search.createColumn({ name: "unitprice1", label: "GST/HST Rate" }),
        search.createColumn({ name: "taxitem2", label: "PST" }),
        search.createColumn({ name: "unitprice2", label: "PST Rate" }),
      );
    }

    return cols;
  }

  function formatTaxGroupResult(result) {
    return {
      id: result.id,
      country: result.getValue({ name: "country" }),
      city: result.getValue({ name: "city" }),
      state: result.getValue({ name: "state" }),
      name: result.getValue({ name: "itemid" }),
      rate: result.getValue({ name: "rate" }),
      zip: result.getValue({ name: "zip" }),
      unitprice1: result.getValue({ name: "unitprice1" }),
      unitprice2: result.getValue({ name: "unitprice2" }),
    };
  }

  function selectBestTaxGroup(taxGroups, countryShortName, state, city, zip) {
    let filteredTaxGroups = taxGroups.filter((taxGroup) => {
      if (countryShortName && state && city && zip) {
        return (
          taxGroup.country === countryShortName &&
          ((state && taxGroup.state === state) ||
            (zip && taxGroup.zip && zip === taxGroup.zip) ||
            (city && taxGroup.city && city === taxGroup.city))
        );
      } else if (countryShortName && state && city) {
        return (
          taxGroup.country === countryShortName &&
          ((state && taxGroup.state && taxGroup.state === state) ||
            (city && taxGroup.city && city === taxGroup.city))
        );
      } else if (countryShortName && state) {
        return (
          taxGroup.country === countryShortName && taxGroup.state === state
        );
      } else if (countryShortName) {
        return taxGroup.country === countryShortName;
      }
    });

    if (filteredTaxGroups.length > 0) {
      return filteredTaxGroups.reduce((max, taxGroup) => {
        const rate = parseFloat(taxGroup.rate.replace("%", ""));
        return rate > parseFloat(max.rate.replace("%", "")) ? taxGroup : max;
      });
    }

    return taxGroups[0];
  }

  function getAllResultsFor(searchObj, callback) {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  }

  /**
   * Function to return either a standard client console object or a SuiteScript log object based on
   * the environment, determined by the `isEnvType` function. When the environment is "client",
   * the standard console object is used for logging. Otherwise, a SuiteScript log object is expected
   * which includes the `debug`, `log`, `audit`, `emergency`, and `error` methods for various levels of logging.
   *
   * @returns {Object} Either the console object with standard methods (like `debug`, `info`, `warn`, etc.) when the
   * environment is client, or a SuiteScript log object when the environment isn't client.
   *
   * @example
   * const logger = useLogger();
   * logger.debug('This is a debug message'); // Logs a debug message
   */
  function useLogger() {
    const envType = isEnvType();

    if (envType === "client") {
      // eslint-disable-next-line no-undef
      return console; // Default console for the client side
    }

    // If not client environment, then it should be SuiteScript
    return {
      debug: log.debug,
      log: log.debug,
      audit: log.audit,
      emergency: log.emergency,
      error: log.error,
    };
  }

  /**
   * Returns the environment type on which the application is running.
   * It executes a `console.log` to infer whether the script is run on a client's machine or a server.
   * If an error is thrown (implying that `console.log` is not available, a common case for servers), it is assumed that the script is run on a server.
   *
   * @returns {string} - Returns 'client' if the function successfully logs to the console, otherwise 'server'.
   */
  function isEnvType() {
    let envType = "";
    try {
      // eslint-disable-next-line no-undef
      console.log("Cross Env Logger: Running on client");
      envType = "client";
    } catch (err) {
      envType = "server";
    }

    return envType;
  }

  /**
   * Function to retrieve settings from the custom record.
   * @param {string} id - The internal ID of the settings record to look up.
   * @returns {Object|null} The settings object or null if not found.
   */
  const getSettings = (id = "1") => {
    try {
      const result = search.lookupFields({
        type: "customrecord_ng_cs_settings",
        id,
        columns: _SettingsFields,
      });
      return result || null; // return null if result is undefined
    } catch (error) {
      console.error(`Error fetching settings: ${error.message}`);
      return null; // handles any lookup errors
    }
  };

  const _SettingsFields = [
    "custrecord_ng_cs_gen_rand_email",
    "custrecord_ng_cs_rand_email_domain",
    "custrecord_ng_cs_rand_email_prefix",
    "custrecord_ng_cs_web_img_folder_id",
    "custrecord_ng_cs_exhb_kit_folder_id",
    "custrecord_ng_cs_use_undep_funds",
    "custrecord_ng_cs_def_dep_account",
    "custrecord_ng_cs_use_show_auditing",
    "custrecord_ng_cs_show_audit_form",
    "custrecord_ng_cs_use_pre_invoicing",
    "custrecord_ng_cs_pre_invoicing_form",
    "custrecord_ng_cs_use_alt_forms",
    "custrecord_ng_cs_prev_adtl_orders",
    "custrecord_ng_cs_allow_mult_billng_part",
    "custrecord_ng_cs_use_show_tax",
    "custrecord_ng_cs_use_cancl_charge",
    "custrecord_ng_cs_cancl_charge_item",
    "custrecord_ng_cs_def_canc_chrg_pct",
    "custrecord_ng_cs_canc_threshold",
    "custrecord_ng_cs_booth_ord_forms",
    "custrecord_ng_cs_add_item_forms",
    "custrecord_ng_cs_do_not_prompt_terms",
    "custrecord_ng_cs_prompt_exclusion_roles",
    "custrecord_ng_cs_import_log_record_id",
    "custrecord_ng_cs_import_log_search_id",
    "custrecord_ng_cs_payment_ar_account",
    "custrecord_ng_cs_cc_auth_item",
    "custrecord_ng_cs_pymnt_fail_eml_template",
    "custrecord_ng_cs_use_job_numbering",
    "custrecord_ng_cs_simple_job_numbering",
    "custrecord_ng_cs_job_num_prefix",
    "custrecord_ng_cs_custom_job_numbering",
    "custrecord_ng_cs_use_multi_cc_proc",
    "custrecord_ng_cs_no_prompt_under_zero",
    "custrecord_ng_cs_prompt_for_new_line",
    "custrecord_ng_cs_retain_last_show",
    "custrecord_ng_cs_retain_last_item_cat",
    "custrecord_ng_cs_default_show_subsidiary",
    "custrecord_ng_cs_no_billed_order_editing",
    "custrecord_ng_cs_billed_ord_edit_users",
    "custrecord_ng_cs_use_scripted_pynt_frm",
    "custrecord_ng_cs_clear_order_cc_details",
    "custrecord_ng_cs_send_invoice_fail_email",
    "custrecord_ng_cs_inv_fail_sender",
    "custrecord_ng_cs_inv_fail_recip",
    "custrecord_ng_cs_inv_fail_cc",
    "custrecord_ng_cs_def_exhb_dept",
    "custrecord_ng_cs_def_exhb_ord_type",
    "custrecord_ng_cs_send_exhib_invoice",
    "custrecord_ng_cs_exhib_invoice_sender",
    "custrecord_ng_cs_exhb_inv_email_template",
    "custrecord_ng_cs_inv_email_conditions",
    "custrecord_ng_cs_give_contacts_access",
    "custrecord_ng_cs_allow_mass_booth_delete",
    "custrecord_ng_cs_mass_booth_delete_roles",
    "custrecord_ng_cs_send_web_pymnt_email",
    "custrecord_ng_cs_web_pymnt_notice_sender",
    "custrecord_ng_cs_web_pymnt_fail_recip",
    "custrecord_ng_cs_web_pymnt_fail_cc",
    "custrecord_ng_cs_csv_import_folder_id",
    "custrecord_ng_cs_allow_show_autopay",
    "custrecord_ng_cs_pymt_rcpt_template",
    "custrecord_ng_cs_dpst_rcpt_template",
    "custrecord_ng_cs_log_time_zone",
    "custrecord_ng_cs_freight_minimum",
    "custrecord_ng_cs_prev_bo_redir_alert",
    "custrecord_ng_cs_dflt_shw_tbl_form",
    "custrecord_ng_cs_dflt_exhibtr_form",
    "custrecord_ng_cs_dflt_booth_order_form",
    "custrecord_ng_cs_activity_log_rec_id",
    "custrecord_ng_cs_activity_log_srch_id",
    "custrecord_ng_cs_auto_charge_web_orders",
    "custrecord_ng_cs_auth_non_web_orders",
    "custrecord_ng_cs_autochrg_cat_excl",
    "custrecord_ng_cs_mastercard",
    "custrecord_ng_cs_visa",
    "custrecord_ng_cs_amex",
    "custrecord_ng_cs_discover",
    "custrecord_ng_cs_default_adv_show_price",
    "custrecord_ng_cs_default_std_show_price",
    "custrecord_ng_cs_default_onst_show_price",
    "custrecord_ng_cs_payment_type",
    "custrecord_ng_cs_dflt_d_calc_date_types",
    "custrecord_ng_cs_dflt_labor_date_types",
    "custrecord_ng_cs_supervisor_item",
    "custrecord_ng_cs_exempt_estimated_items",
    "custrecord_ng_cs_auth_non_web_orders",
    "custrecord_ng_cs_default_show_mgmt_price",
    "custrecord_ng_cs_name_number_ordering",
    "custrecord_ng_cs_name_number_separator",
    "custrecord_ng_cs_use_custom_job",
    "custrecord_ng_cs_def_show_mgmt_dept",
    "custrecord_ng_cs_def_show_mgmt_ord_type",
    "custrecord_ng_cs_default_show_date",
    "custrecord_ng_cs_show_mgt_forms",
    "custrecord_ng_cs_enable_freight_opts_opt",
    "custrecord_ng_cs_enable_graphics_option",
    "custrecord_ng_cs_dflt_show_mgmt_ord_form",
    "custrecord_ng_cs_enable_orientation_opt",
    "custrecord_ng_cs_enable_labor_matrix_opt",
    "custrecord_ng_cs_enforce_item_max_qty",
    "custrecord_ng_cs_enable_paytrace",
    "custrecord_ng_cs_show_calendar_id",
    "custrecord_ng_cs_acct_domain_url",
    "custrecord_ng_cs_algolia_application_id",
    "custrecord_ng_cs_algolia_search_key",
    "custrecord_ng_cs_algolia_api_key",
    "custrecord_ng_cs_algolia_index",
    "custrecord_ng_cs_fclty_addy_template",
    "custrecord_ng_cs_wrhs_addy_template",
    "custrecord_ng_cs_name_from_subsidiary",
    "custrecord_ng_cs_booth_num_line_text",
    "custrecord_ng_cs_wo_img",
    "custrecord_ng_cs_wo_logo_img_url",
    "custrecord_ng_cs_inv_transfer_type",
    "custrecord_ng_cs_transfer_count_markup",
    "custrecord_ng_cs_trnsfr_exmpt_cats",
    "custrecord_ng_cs_default_transfer_from",
    "custrecord_ng_cs_default_to_as_st_loc",
    "custrecord_ng_cs_item_rprts_exluded_cats",
    "custrecord_ng_cs_exhb_wo_exluded_cats",
    "custrecord_ng_cs_hide_bthchklst_cnt_info",
    "custrecord_ng_cs_shade_alt_report_lines",
    "custrecord_ng_cs_report_line_shade_hex",
    "custrecord_ng_cs_report_item_display",
    "custrecord_ng_cs_graphics_item_cat",
    "custrecord_ng_cs_canonical_base_url",
    "custrecord_ng_cs_use_cc_conv_fee",
    "custrecord_ng_cs_cc_conv_fee_rate",
    "custrecord_ng_cs_cc_conv_fee_item",
    "custrecord_ng_cs_cc_conv_fee_order_types",
    "custrecord_ng_cs_csv_import_file",
    "custrecord_ng_cs_default_show_move_in",
    "custrecord_ng_cs_default_exhib_move_in",
    "custrecord_ng_cs_default_show_move_out",
    "custrecord_ng_cs_default_exhib_move_out",
    "custrecord_ng_cs_enable_rentals",
    "custrecord_ng_cs_tax_auto_sel_txt_filter",
    "custrecord_enable_material_handling_beta",
  ];

  const MATERIAL_HANDLING_SCHEDULE_FIELDS = [
    "name",
    "custrecord_ng_mat_handle_sch_charge_type",
    "custrecord_ng_mat_handle_sch_min_weight",
    "custrecord_ng_mat_handle_sch_max_weight",
    "custrecord_ng_mat_handle_sch_first_pc",
    "custrecord_ng_mat_handle_sch_addtl_pc",
    "custrecord_ng_mat_handle_sch_surchgtype",
    "custrecord_surcharge_percentage",
    "custrecord_surcharge_flat_fee",
    "custrecord_ng_ot_surchgtype",
    "custrecord_ot_surcharge_percentage",
    "custrecord_ot_surcharge_flat_fee",
    "custrecord_ng_dt_surchgtype",
    "custrecord_dt_surcharge_percentage",
    "custrecord_dt_surcharge_flat_fee",
    "custrecord_ng_cs_late_surcharge_item",
    "custrecord_ng_cs_overtime_surcharge_item",
    "custrecord_ng_cs_doubletime_srchrge_item",
    "custrecord_ng_replace_est_item",
  ];

  function getListValueFromLookup(array) {
    if (Array.isArray(array) && array.length === 1) {
      const element = array[0];
      if (element && element.value && element.text) {
        return {
          value: element.value,
          text: element.text,
        };
      } else {
        log.audit({
          title:
            "❗Invalid element structure. Element must have 'value' and 'text' properties.",
          details: "",
        });
        return false;
      }
    } else {
      log.audit({
        title: "❗Input array must contain exactly one element.",
        details: "",
      });
      return null;
    }
  }

  // Create a function that accepts parameters and returns a search object.
  function createSearchObject(type, filters, columns) {
    return search.create({
      type: type,
      filters: filters,
      columns: columns,
    });
  }

  return { useItemPriceLevel, useItemPrice, useEventPricingDates, useTaxCode };
});
