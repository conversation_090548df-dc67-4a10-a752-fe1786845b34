/**
 * ng_cm_event_helpers.js
 * @NApiVersion 2.1
 * @NModuleScope Public
 */
define(["N/search", "../@moment/moment.min"], function (search, moment) {
  /**
   * Retrieves event data including show dates and event details.
   * @param {string|number} eid - The ID of the event.
   * @returns {Object} An object containing show dates and event data.
   */
  function getEventData(eid) {
    const showDates = getShowDates(eid);
    const eventData = getEventDetails(eid);
    return { showDates, eventData };
  }

  /**
   * Retrieves show dates for a given event.
   * @param {string|number} eid - The ID of the event.
   * @returns {Array} An array of show date objects.
   */
  function getShowDates(eid) {
    let showDates = [];
    let showDatesSearch = search.create({
      type: "customrecord_show_date",
      filters: [["custrecord_show_number_date", "anyof", eid]],
      columns: [
        search.createColumn({
          name: "scriptid",
          sort: search.Sort.ASC,
          label: "Script ID",
        }),
        search.createColumn({
          name: "custrecord_show_number_date",
          label: "CS Event",
        }),
        search.createColumn({
          name: "custrecord_date_type",
          label: "Date Type",
        }),
        search.createColumn({ name: "custrecord_date", label: "Date" }),
        search.createColumn({
          name: "custrecord_start_time",
          label: "Start Time",
        }),
        search.createColumn({ name: "custrecord_end_time", label: "End Time" }),
        search.createColumn({
          name: "internalid",
          join: "CUSTRECORD_SHOW_NUMBER_DATE",
          label: "Internal ID",
        }),
      ],
    });

    showDatesSearch.run().each(function (result) {
      let showDateObj = {
        id: result.id,
        recordType: result.recordType,
        event: {
          value: result.getValue("custrecord_show_number_date"),
          text: result.getText("custrecord_show_number_date"),
        },
        dateType: {
          value: result.getValue("custrecord_date_type"),
          text: result.getText("custrecord_date_type"),
        },
        date: result.getValue("custrecord_date"),
        startTime: result.getValue("custrecord_start_time"),
        endTime: result.getValue("custrecord_end_time"),
        internalId: result.getValue({
          name: "internalid",
          join: "CUSTRECORD_SHOW_NUMBER_DATE",
        }),
      };
      showDates.push(showDateObj);
      return true;
    });

    return showDates;
  }

  /**
   * Retrieves event details for a given event.
   * @param {string|number} eid - The ID of the event.
   * @returns {Object} An object containing event details.
   */
  function getEventDetails(eid) {
    let eventDetails = null;
    let eventSearch = search.create({
      type: "customrecord_show",
      filters: [["internalid", "anyof", eid]],
      columns: [
        search.createColumn({
          name: "name",
          sort: search.Sort.ASC,
          label: "Name",
        }),
        search.createColumn({ name: "custrecord_tax_rate", label: "Tax Rate" }),
        search.createColumn({
          name: "custrecord_tax_percent",
          label: "Tax Percent",
        }),
        search.createColumn({
          name: "custrecord_adv_ord_date",
          label: "Advanced Order Date",
        }),
        search.createColumn({
          name: "custrecord_adv_price_level",
          label: "Advance Price Level",
        }),
        search.createColumn({
          name: "custrecord_std_price_level",
          label: "Standard Price Level",
        }),
        search.createColumn({
          name: "custrecord_site_price_level",
          label: "On Site Price Level",
        }),
        search.createColumn({
          name: "custrecord_show_mgmnt_price_lvl",
          label: "Show Management Price Level",
        }),
      ],
    });

    eventSearch.run().each(function (result) {
      eventDetails = {
        id: result.id,
        recordType: result.recordType,
        name: result.getValue("name"),
        taxRate: result.getValue("custrecord_tax_rate"),
        taxPercent: result.getValue("custrecord_tax_percent"),
        advancedOrderDate: result.getValue("custrecord_adv_ord_date"),
        advancedPriceLevel: {
          value: result.getValue("custrecord_adv_price_level"),
          text: result.getText("custrecord_adv_price_level"),
        },
        standardPriceLevel: {
          value: result.getValue("custrecord_std_price_level"),
          text: result.getText("custrecord_std_price_level"),
        },
        sitePriceLevel: {
          value: result.getValue("custrecord_site_price_level"),
          text: result.getText("custrecord_site_price_level"),
        },
        showManagementPriceLevel: {
          value: result.getValue("custrecord_show_mgmnt_price_lvl"),
          text: result.getText("custrecord_show_mgmnt_price_lvl"),
        },
      };
      return false; // Stop after first result
    });

    return eventDetails;
  }

  /**
   * Gets the first show date based on settings.
   * @param {Array} showDates - Array of show dates.
   * @param {Object} settings - Settings object.
   * @returns {moment.Moment} The first show date as a moment object.
   */
  function getFirstShowDate(showDates, settings) {
    const onlyShowDates = showDates.filter(
      (eventdate) =>
        eventdate.dateType.text ===
        settings.custrecord_ng_cs_default_show_date[0].text,
    );
    return onlyShowDates.length !== 0 ? moment(onlyShowDates[0].date) : null;
  }

  return {
    getEventData: getEventData,
    getShowDates: getShowDates,
    getEventDetails: getEventDetails,
    getFirstShowDate: getFirstShowDate,
  };
});
