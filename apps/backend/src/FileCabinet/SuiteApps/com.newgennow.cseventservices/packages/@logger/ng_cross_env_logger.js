/**
 * ng_cross_env_logger.js
 * Cross environment logger for NetSuite
 * @NApiVersion 2.1
 * @Author: <PERSON>
 *
 */
define(["N/log"], (log) => {
  /**
   * @typedef {Object} Logger
   * @property {function} log Simulates a console.log or log.debug
   * @property {function} warn Simulates a console.warn
   * @property {function} error Simulates a console.error or log.error
   * @property {function} dir Simulates a console.dir
   * @property {function} info Simulates a console.info
   * @property {function} audit Simulates a log.audit
   * @property {function} emergency Simulates a log.emergency
   * */

  /**
   * Logs console for client env or execution logs for server env
   *
   *
   * @example ```javascript
   * const logger = useCrossLog()
   *
   * // Will run on both client and server
   * logger.log('Hi', {foo: 'bar'})
   *
   * // Specific to client
   * var clientName = 'Lorem'
   * logger.warn('Warning', clientName )
   *
   * // Server specific
   * var clientName = 'lorem'
   * logger.audit('Person', clientName)
   * ```
   * */
  function useLogger() {
    const envType = isEnvType();
    if (envType === "client") {
      return {
        ...console,
        audit: console.log,
      };
    }

    return {
      ...log,
      log: (...args) => {
        log.debug(...args);
      },
    };
  }

  function isEnvType() {
    let envType = "";
    try {
      console.log("Cross Env Logger: Running on client");
      envType = "client";
    } catch (err) {
      envType = "server";
    }

    return envType;
  }

  return useLogger;
});
