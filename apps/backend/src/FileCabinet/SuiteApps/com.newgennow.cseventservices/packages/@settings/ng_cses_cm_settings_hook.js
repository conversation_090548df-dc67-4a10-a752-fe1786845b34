/**
 *  ng_cses_cm_settings_hook.js
 *
 * @NApiVersion 2.1
 * Author: 	<PERSON>
 */

/** The CS settings record fields and type definitions
 * @typedef {Object} Settings
 * @property {number} custrecord_ng_cs_supervisor_item - The supervisor item ID. 🆔
 * @property {?number} custrecord_ng_cs_job_num_prefix - The job number prefix. 🏷️
 * @property {?string} custrecord_ng_cs_inv_fail_cc - The invoice fail credit card. 💳
 * @property {string} custrecord_ng_cs_use_cancl_charge - Indicates whether to use cancellation charges. Possible values: "T" (true) or "F" (false). 💵
 * @property {number} custrecord_ng_cs_dflt_shw_tbl_form - The default show table form ID. 🗂️
 * @property {number} custrecord_ng_cs_exhb_kit_folder_id - The exhibition kit folder ID. 📁
 * @property {string} custrecord_ng_cs_clear_order_cc_details - Indicates whether to clear order credit card details. Possible values: "T" (true) or "F" (false). ❌
 * @property {number} custrecord_ng_cs_cc_auth_item - The credit card authorization item ID. 💳
 * @property {string} custrecord_ng_cs_retain_last_show - Indicates whether to retain the last show. Possible values: "T" (true) or "F" (false). 🔒
 * @property {string} custrecord_ng_cs_cc_conv_fee_order_types - The order types for credit card convenience fee. Possible values: "1". 📦
 * @property {?number} custrecord_ng_cs_mb_service_fee_tier - The service fee tier for master billing. 📊
 * @property {number} custrecord_ng_cs_mastercard - The Mastercard item ID. 💳
 * @property {string} custrecord_ng_cs_wo_logo_img_url - The URL of the work order logo image. 🖼️
 * @property {string} custrecord_ng_cs_gen_rand_email - Indicates whether to generate random email. Possible values: "T" (true) or "F" (false). 📧
 * @property {number} custrecord_ng_cs_import_log_record_id - The import log record ID. 📝
 * @property {?string} custrecord_ng_cs_billed_ord_edit_users - The users allowed to edit billed orders. 👥
 * @property {number} custrecord_ng_cs_freight_minimum - The minimum freight charge. 💰
 * @property {string} custrecord_ng_cs_send_invoice_fail_email - Indicates whether to send email for invoice failure. Possible values: "T" (true) or "F" (false). 📧
 * @property {number} custrecord_ng_cs_dflt_booth_order_form - The default booth order form ID. 🗂️
 * @property {string} custrecord_ng_cs_header_logo_url - The URL of the header logo image. 🖼️
 * @property {number} custrecord_ng_cs_activity_log_srch_id - The activity log search ID. 🔍
 * @property {number} recordid - The record ID. 🆔
 * @property {?number} custrecord_ng_cs_pymt_rcpt_template - The payment receipt template ID. 📄
 * @property {string} custrecord_ng_cs_exempt_estimated_items - Indicates whether to exempt estimated items. Possible values: "T" (true) or "F" (false). ❌
 * @property {string} custrecord_ng_cs_trnsfr_exmpt_cats - The transfer exempt categories. 🐱
 * @property {string} custrecord_ng_cs_accent_color - The accent color. 🎨
 * @property {string} custrecord_ng_cs_algolia_search_key - The Algolia search key. 🔑
 * @property {number} custrecord_ng_cs_default_onst_show_price - The default on-site show price. 💲
 * @property {number} custrecord_ng_cs_def_exhb_ord_type - The default exhibition order type. 📦
 * @property {string} custrecord_ng_cses_web_primary_color - The primary color for web. 🌈
 * @property {string} custrecord_ng_cs_give_contacts_access - Indicates whether to give contacts access. Possible values: "T" (true) or "F" (false). 👥
 * @property {string} custrecord_ng_cs_dflt_d_calc_date_types - The default date calculation types. 🗓️
 * @property {string} custrecord_ng_cs_conv_fee_web_displayed - Indicates whether to display credit card convenience fee on the web. Possible values: "T" (true) or "F" (false). 💳
 * @property {string} custrecord_ng_cs_algolia_index - The Algolia index (dev_cs_inventory_items)
 * @property {number} custrecord_ng_cs_name_number_separator - The name-number separator (🔢)
 * @property {number} custrecord_ng_cs_report_item_display - The item display report (🔢)
 *
 * @property {number} custrecord_ng_cs_def_show_mgmt_ord_type - The default show management order type.
 * @property {string} custrecord_ng_cs_item_rprts_exluded_cats - The excluded categories for item reports. 📋
 * @property {string} custrecord_ng_cs_prev_bo_redir_alert - Flag indicating whether to show a redirect alert for previous booth orders. ✅🚀
 * @property {string} custrecord_ng_cs_no_prompt_under_zero - Flag indicating whether to prompt under zero. ✅📋
 * @property {string} custrecord_ng_cs_contact_us_url - The URL for the contact us page. 🔗📞
 * @property {string} custrecord_ng_cs_allow_show_autopay - Flag indicating whether to allow show autopay. ✅💳
 * @property {number} id - The ID of the CS Settings object. 🆔
 * @property {string} custrecord_ng_cs_exhb_kit_path - The path for the exhibition kit folder. 📂
 * @property {string} custrecord_ng_cs_use_scripted_pynt_frm - Flag indicating whether to use scripted payment form. 🚫💳
 * @property {string|null} custrecord_ng_cs_add_item_forms - The additional item forms. Can be null. 📝
 * @property {number} custrecord_ng_cs_pymnt_fail_eml_template - The payment fail email template. 📝📧
 * @property {string} custrecord_ng_cs_use_canadian_sales_tax - Flag indicating whether to use Canadian sales tax. 🚫📋
 * @property {number} custrecord_ng_cs_payment_type - The payment type. 💳
 * @property {string} custrecord_ng_cs_algolia_application_id - The Algolia application ID. 🔑🔍
 * @property {number} custrecord_ng_cs_exhb_inv_email_template - The exhibition invoice email template. 📝📧
 * @property {number} custrecord_ng_cs_default_show_date - The default show date. 📆
 * @property {string|null} custrecord_ng_cs_dpst_rcpt_template - The deposit receipt template. Can be null. 📝
 * @property {string} custrecord_ng_cs_fclty_addy_template - The facility address template. 📝📍
 * @property {number} custrecord_ng_cs_amex - The ID of the American Express item. 🆔💳
 * @property {string} custrecord_ng_cs_exhibitor_serv_phone - The exhibitor service phone number. ☎️
 * @property {number} custrecord_ng_cs_default_exhib_move_out - The default exhibition move-out. 📆
 * @property {string} custrecord_ng_cs_shade_alt_report_lines - Flag indicating whether to shade alternate report lines. ✅📋
 * @property {number} custrecord_ng_cs_activity_log_rec_id - The ID of the activity log record. 🆔📋
 * @property {string|null} abbreviation - The abbreviation. Can be null. ✍️
 * @property {number} custrecord_bol_avery_form - The Avery form for the bill of lading. 📝
 * @property {string} custrecord_ng_cs_acct_domain_url - The account domain URL. 🔗🌐
 * @property {string} custrecord_ng_cs_name_from_subsidiary - Flag indicating whether to use the subsidiary name. 🚫🏢
 * @property {string|null} custrecord_ng_cs_direct_bill_terms - The terms for direct billing. Can be null. 📋
 * @property {string} custrecord_enable_auto_job_inheritance - Flag indicating whether to enable auto job inheritance. ✅📋
 * @property {string|null} custrecord_ng_cs_cancl_charge_item - The cancellation charge item. Can be null. 💰
 * @property {string|null} custrecord_ng_cs_def_exhb_dept - The default exhibition department. Can be null. 📋
 * @property {number} custrecord_ng_cs_dflt_exhibtr_form - The default exhibitor form. 📝
 * @property {string|null} custrecord_ng_cs_mass_booth_delete_roles - The roles allowed to mass delete booths. Can be null. 👥🚫
 * @property {string|null} custrecord_ng_cs_master_bill_terms - The terms for master billing
 * @property {string} custrecord_ng_cs_use_pre_invoicing - Flag indicating whether to use pre-invoicing. 🚫📋
 * @property {number} custrecord_ng_cs_default_sprvisor_markup - The default supervisor markup. 💰
 * @property {string} custrecord_ng_cs_rand_email_domain - The domain for random emails. 📧
 * @property {number|null} custrecord_ng_cs_pre_invoicing_form - The pre-invoicing form. Can be null. 📝
 * @property {string|null} custrecord_ng_cs_conv_fee_zero_tax - The zero tax for conversion fee. Can be null. 💳
 * @property {number} custrecord_ng_cs_show_audit_form - The show audit form. 📝
 * @property {string} custrecord_ng_eh_enable_international_or - Flag indicating whether to enable international orders. ✅🌍
 * @property {string} custrecord_ng_cs_dflt_labor_date_types - The default labor date types. 📅
 * @property {string} custrecord_ng_cs_use_show_tax - Flag indicating whether to use show tax. ✅📋
 * @property {number} custrecord_ng_cs_import_log_search_id - The ID of the import log search. 🆔🔎
 * @property {number} custrecord_ng_cs_transfer_count_markup - The transfer count markup. 💰
 * @property {string} custrecord_ng_cs_allow_mult_billng_part - Flag indicating whether to allow multiple billing parties. 🚫👥
 * @property {number} custrecord_ng_cs_prefrd_pymt_processor - The preferred payment processor. 💳
 * @property {number} custrecord_ng_cs_dflt_show_mgmt_ord_form - The default show management order form. 📝
 * @property {string} custrecord_ng_cs_use_avalara_tax_message - Flag indicating whether to use Avalara tax message. 🚫📋
 * @property {string} custrecord_ng_cs_prev_adtl_orders - Flag indicating whether to allow previous additional orders. ✅📋
 * @property {number} custrecord_ng_cs_default_exhib_move_in - The default exhibition move-in. 📆
 * @property {string} custrecord_ng_cs_enable_paytrace - Flag indicating whether to enable Paytrace payment. ✅💳
 * @property {number} custrecord_ng_cs_csv_import_folder_id - The ID of the CSV import folder. 📂
 * @property {string} custrecord_ng_cs_sales_tax_is_on_lines - Flag indicating whether sales tax is applied on lines. ✅📋
 * @property {string} custrecord_ng_cs_use_job_numbering - Flag indicating whether to use job numbering. 🚫📋
 * @property {number} custrecord_ng_cs_default_std_show_price - The default standard show price. 💰
 * @property {number} custrecord_ng_cs_event_selection_info - The event selection information. 📝
 * @property {number} custrecord_ng_cs_default_adv_show_price - The default advanced show price. 💰
 * @property {number} owner - The owner ID. 🆔
 * @property {string} custrecord_ng_cs_enable_graphics_option - Flag indicating whether to enable graphics option. ✅🎨
 * @property {string} custrecord_ng_cs_auto_charge_web_orders - Flag indicating whether to automatically charge web orders. ✅💳
 * @property {string} custrecord_ng_cs_default_to_as_st_loc - Flag indicating whether to default to the assigned show location. ✅📋
 * @property {number} custrecord_ng_cs_canc_threshold - The cancellation threshold. 💰
 * @property {number} custrecord_ng_cs_file_upload_folder_id - The ID of the file upload folder. 📂
 * @property {number} custrecord_ng_cs_default_show_move_in - The default show move-in. 📆
 * @property {string} custrecord_ng_cs_show_calendar_id - The ID of the show calendar. 🆔📆
 * @property {string} custrecord_ng_cs_use_multi_cc_proc - Flag indicating whether to use multiple credit card processors. ✅💳
 * @property {string} custrecord_convenience_fee_checkout_labe - The label for the convenience fee at checkout. 💰
 * @property {number|null} custrecord_ng_cs_bol_terms - The ID of the bill of lading terms. Can be null. 📝
 * @property {string} custrecord_ng_cs_auth_non_web_orders - Flag indicating whether to authorize non-web orders. ✅
 * @property {String} custrecord_ng_cs_web_welcome_blurb
 * @property {String} custrecord_ng_cs_def_canc_chrg_pct
 * @property {'T'|'F'} custrecord_ng_bol_enable
 * @property {Number} custrecord_ng_cs_web_pymnt_notice_sender
 * @property {String|Array<Number>} custrecord_ng_cs_rental_forms
 * @property {Number} custrecord_ng_cs_autochrg_cat_excl
 * @property {Number} custrecord_ng_cs_prompt_exclusion_roles
 * @property {'T'|'F'} custrecord_ng_cs_no_billed_order_editing
 * @property {'T'|'F'} custrecord_ng_cs_enforce_item_max_qty
 * @property {'T'|'F'} custrecord_ng_cs_enable_rentals_by_form
 * @property {'T'|'F'} custrecord_ng_cs_enable_labor_matrix_opt
 * @property {Number} custrecord_ng_cs_inv_fail_sender
 * @property {Number} custrecord_ng_cs_report_xml_folder_id
 * @property {'T'|'F'} custrecord_ng_cs_send_exhib_invoice
 * @property {Number} custrecord_ng_cs_log_time_zone
 * @property {Number} custrecord_ng_cs_payment_ar_account
 * @property {String} custrecord_ng_cs_bol_instructions
 * @property {'T'|'F'} custrecord_ng_cs_prompt_for_new_line
 * @property {Number} custrecord_ng_cs_inv_transfer_type
 * @property {'T'|'F'} custrecord_ng_cs_enable_master_billing
 * @property {String} custrecord_ng_cs_wrhs_addy_template
 * @property {String} custrecord_ng_cs_def_show_mgmt_dept
 * @property {'T'|'F'} custrecord_ng_cs_use_cc_conv_fee
 * @property {Number} custrecord_ng_cs_evt_mm_attch_folder_id
 * @property {String|'#000} custrecord_ng_cs_report_line_shade_hex
 * @property {Number} custrecord_ng_cs_cust_web_access_role
 * @property {'T'|'F'} custrecord_ng_cs_allow_mass_booth_delete
 * @property {'T'|'F'} custrecord_ng_cs_use_undep_funds
 * @property {String} custrecord_ng_cs_graphics_item_cat
 * @property {'T'|'F'} custrecord_ng_cs_send_web_pymnt_email
 * @property {Number} custrecord_ng_cs_web_pymnt_fail_recip
 * @property {'T'|'F'} custrecord_ng_cs_hide_bthchklst_cnt_info
 * @property {Number} custrecord_ng_cs_evt_mm_temp_folder_id
 * @property {Number} custrecord_ng_cs_default_billing_type
 * @property {String} custrecord_ng_cs_def_dep_account
 * @property {'T'|'F'} custrecord_ng_cs_custom_job_numbering
 * @property {'T'|'F'} custrecord_ng_cs_do_not_prompt_terms
 * @property {Number} custrecord_ng_cs_web_img_folder_id
 * @property {Number} custrecord_ng_cs_inv_fail_recip
 * @property {Number} custrecord_ng_cs_discover
 * @property {'T'|'F'} custrecord_ng_cs_use_show_auditing
 * @property {Number} custrecord_ng_cs_cc_conv_fee_rate
 * @property {'T'|'F'} custrecord_ng_cs_enable_rentals
 * @property {String} custrecord_ng_cs_canonical_base_url
 * @property {String} custrecord_ng_cs_web_pymnt_fail_cc
 * @property {Number} custrecord_ng_cs_name_number_ordering Works in tandem with **custrecord_ng_cs_use_job_numbering** - [Field Ref](https://tstdrv1516212.app.netsuite.com/app/common/custom/custreccustfield.nl?rectype=62&id=961&e=F)
 * @property {'T'|'F'} isinactive
 * @property {Number} custrecord_ng_cs_exhib_invoice_sender
 * @property {String} custrecord_ng_cs_tax_auto_sel_txt_filter
 * @property {String} externalid
 * @property {Number} custrecord_ng_cs_show_mgt_forms
 * @property {Number} lastmodifiedby
 * @property {Number} custrecord_ng_cs_cc_conv_fee_item
 * @property {Number} custrecord_ng_cs_default_transfer_from
 * @property {String|Array<Number|String>} custrecord_ng_cs_booth_ord_forms
 * @property {'T'|'F'} custrecord_ng_cs_enable_orientation_opt
 * @property {String} scriptid
 * @property {'T'|'F'} custrecord_ng_cs_simple_job_numbering
 * @property {String} custrecord_ng_cs_bill_back_terms
 * @property {Number} custrecord_ng_cs_default_show_move_out
 * @property {String} custrecord_ng_cs_rand_email_prefix
 * @property {String} custrecord_ng_cs_default_show_subsidiary
 * @property {String} custrecord_ng_cs_settings_access
 * @property {Number} custrecord_ng_cs_visa
 * @property {'T'|'F'} custrecord_ng_cs_use_custom_job
 * @property {String|'#000'} custrecord_ng_cs_navbar_bckgrnd_color
 * @property {Number} custrecord_ng_cs_show_mgmt_ord_types
 * @property {String} custrecord_ng_cs_booth_num_line_text
 * @property {Number} custrecord_ng_cs_master_billing_form
 * @property {'T'|'F'} custrecord_enable_auto_job_inheritance
 * @property {'T'|'F'} custrecord_enable_material_handling_beta
 *
 * */
define(["N/log", "N/query"], /**
 * @param{log} log
 * @param{query} query
 */ (log, query) => {
  /**
   * Returns settings record
   * @returns {Settings}
   * @governance 10 units
   * @example
   * const settings = useSettings();
   * */
  const useSettings = () => {
    let settings = {};

    settings = query
      .runSuiteQL({
        query: `SELECT * FROM customrecord_ng_cs_settings WHERE ID = '1'`,
      })
      .asMappedResults()[0];

    return settings;
  };

  return { useSettings: useSettings };
});
