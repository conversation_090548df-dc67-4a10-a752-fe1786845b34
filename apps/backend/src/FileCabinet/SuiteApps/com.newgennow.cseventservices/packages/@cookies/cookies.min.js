/**
 * Minified by jsDelivr using Terser v5.19.2.
 * Original file: /npm/cookies@0.9.1/index.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
/*!
 * cookies
 * Copyright(c) 2014 <PERSON>, http://jed.is/
 * Copyright(c) 2015-2016 <PERSON>
 * MIT Licensed
 */
"use strict";var deprecate=require("depd")("cookies"),Keygrip=require("keygrip"),http=require("http"),fieldContentRegExp=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,PRIORITY_REGEXP=/^(?:low|medium|high)$/i,REGEXP_CACHE=Object.create(null),REGEXP_ESCAPE_CHARS_REGEXP=/[\^$\\.*+?()[\]{}|]/g,RESTRICTED_NAME_CHARS_REGEXP=/[;=]/,RESTRICTED_VALUE_CHARS_REGEXP=/[;]/,SAME_SITE_REGEXP=/^(?:lax|none|strict)$/i;function Cookies(e,t,i){if(!(this instanceof Cookies))return new Cookies(e,t,i);this.secure=void 0,this.request=e,this.response=t,i&&(Array.isArray(i)?(deprecate('"keys" argument; provide using options {"keys": [...]}'),this.keys=new Keygrip(i)):i.constructor&&"Keygrip"===i.constructor.name?(deprecate('"keys" argument; provide using options {"keys": keygrip}'),this.keys=i):(this.keys=Array.isArray(i.keys)?new Keygrip(i.keys):i.keys,this.secure=i.secure))}function Cookie(e,t,i){if(!fieldContentRegExp.test(e)||RESTRICTED_NAME_CHARS_REGEXP.test(e))throw new TypeError("argument name is invalid");if(t&&(!fieldContentRegExp.test(t)||RESTRICTED_VALUE_CHARS_REGEXP.test(t)))throw new TypeError("argument value is invalid");for(var e in this.name=e,this.value=t||"",i)this[e]=i[e];if(this.value||(this.expires=new Date(0),this.maxAge=null),this.path&&!fieldContentRegExp.test(this.path))throw new TypeError("option path is invalid");if(this.domain&&!fieldContentRegExp.test(this.domain))throw new TypeError("option domain is invalid");if("number"==typeof this.maxAge?isNaN(this.maxAge)||!isFinite(this.maxAge):this.maxAge)throw new TypeError("option maxAge is invalid");if(this.priority&&!PRIORITY_REGEXP.test(this.priority))throw new TypeError("option priority is invalid");if(this.sameSite&&!0!==this.sameSite&&!SAME_SITE_REGEXP.test(this.sameSite))throw new TypeError("option sameSite is invalid")}function getPattern(e){return REGEXP_CACHE[e]||(REGEXP_CACHE[e]=new RegExp("(?:^|;) *"+e.replace(REGEXP_ESCAPE_CHARS_REGEXP,"\\$&")+"=([^;]*)")),REGEXP_CACHE[e]}function isRequestEncrypted(e){return e.socket?e.socket.encrypted:e.connection.encrypted}function pushCookie(e,t){if(t.overwrite)for(var i=e.length-1;i>=0;i--)0===e[i].indexOf(t.name+"=")&&e.splice(i,1);e.push(t.toHeader())}Cookies.prototype.get=function(e,t){var i,o,r,s,n,p,a=e+".sig",h=t&&void 0!==t.signed?t.signed:!!this.keys;if((i=this.request.headers.cookie)&&(o=i.match(getPattern(e)))){if('"'===(r=o[1])[0]&&(r=r.slice(1,-1)),!t||!h)return r;if(s=this.get(a)){if(n=e+"="+r,!this.keys)throw new Error(".keys required for signed cookies");if(!((p=this.keys.index(n,s))<0))return p&&this.set(a,this.keys.sign(n),{signed:!1}),r;this.set(a,null,{path:"/",signed:!1})}}},Cookies.prototype.set=function(e,t,i){var o=this.response,r=this.request,s=o.getHeader("Set-Cookie")||[],n=new Cookie(e,t,i),p=i&&void 0!==i.signed?i.signed:!!this.keys,a=void 0===this.secure?"https"===r.protocol||isRequestEncrypted(r):Boolean(this.secure);if("string"==typeof s&&(s=[s]),!a&&i&&i.secure)throw new Error("Cannot send secure cookie over unencrypted connection");if(n.secure=i&&void 0!==i.secure?i.secure:a,i&&"secureProxy"in i&&(deprecate('"secureProxy" option; use "secure" option, provide "secure" to constructor if needed'),n.secure=i.secureProxy),pushCookie(s,n),i&&p){if(!this.keys)throw new Error(".keys required for signed cookies");n.value=this.keys.sign(n.toString()),n.name+=".sig",pushCookie(s,n)}return(o.set?http.OutgoingMessage.prototype.setHeader:o.setHeader).call(o,"Set-Cookie",s),this},Cookie.prototype.path="/",Cookie.prototype.expires=void 0,Cookie.prototype.domain=void 0,Cookie.prototype.httpOnly=!0,Cookie.prototype.partitioned=!1,Cookie.prototype.priority=void 0,Cookie.prototype.sameSite=!1,Cookie.prototype.secure=!1,Cookie.prototype.overwrite=!1,Cookie.prototype.toString=function(){return this.name+"="+this.value},Cookie.prototype.toHeader=function(){var e=this.toString();return this.maxAge&&(this.expires=new Date(Date.now()+this.maxAge)),this.path&&(e+="; path="+this.path),this.expires&&(e+="; expires="+this.expires.toUTCString()),this.domain&&(e+="; domain="+this.domain),this.priority&&(e+="; priority="+this.priority.toLowerCase()),this.sameSite&&(e+="; samesite="+(!0===this.sameSite?"strict":this.sameSite.toLowerCase())),this.secure&&(e+="; secure"),this.httpOnly&&(e+="; httponly"),this.partitioned&&(e+="; partitioned"),e},Object.defineProperty(Cookie.prototype,"maxage",{configurable:!0,enumerable:!0,get:function(){return this.maxAge},set:function(e){return this.maxAge=e}}),deprecate.property(Cookie.prototype,"maxage",'"maxage"; use "maxAge" instead'),Cookies.connect=Cookies.express=function(e){return function(t,i,o){t.cookies=i.cookies=new Cookies(t,i,{keys:e}),o()}},Cookies.Cookie=Cookie,module.exports=Cookies;
//# sourceMappingURL=/sm/7262fcff65717d5430481fb08eebd4588e25c73fe6787a93fb9d9437da2f9283.map