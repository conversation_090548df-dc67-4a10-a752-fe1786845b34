/**
 *@NApiVersion 2.1
 *@NScriptType Suitelet
 */

// example events json endpoint from fullcalendar.com
// https://fullcalendar.io/demo-events.json?single-day&for-resource-timeline&start=2021-01-04T00%3A00%3A00Z&end=2021-01-05T00%3A00%3A00Z&timeZone=UTC

define(["N/search", "N/log", "N/format", "N/record", "N/util"], function (
  s,
  log,
  format,
  record,
  util
) {
  function onRequest(context) {
    {
      if (context.request.method === "POST") {
        // Do POST Code Here (send updated events back to NetSuite)
        const data = JSON.parse(context.request.body);
        log.audit({
          title: "FullCalendar Updated Events POST",
          details: data,
        });
        // loop through data from bookings app and update each event
        util.each(data, function (eventLine) {
          log.audit({
            title: "Event Line Data",
            details: JSON.stringify(eventLine),
          });
          const startDate = format.format({
            type: format.Type.DATE,
            value: new Date(eventLine.eventDetails.start),
          });
          const startTimeofDay = format.format({
            type: format.Type.TIMEOFDAY,
            value: new Date(eventLine.eventDetails.start),
          });
          const endDate = format.format({
            type: format.Type.DATE,
            value: new Date(eventLine.eventDetails.end),
          });
          const endTimeofDay = format.format({
            type: format.Type.TIMEOFDAY,
            value: new Date(eventLine.eventDetails.end),
          });
          if (eventLine.eventDetails.id == null) {
            // this means the booking is new so we create it
            // create project record
            const createBooking = record.create({
              type: "customrecord_ng_cs_event_booking",
              isDynamic: true,
            });
            createBooking.setValue({
              fieldId: "name",
              value: eventLine.eventDetails.title,
            });
            createBooking.setValue({
              fieldId: "custrecord_ng_cs_eb_event",
              value: eventLine.eventDetails.extendedProps.eventId,
            });
            createBooking.setValue({
              fieldId: "custrecord_ng_cs_eb_venue",
              value: eventLine.venueId,
            });
            createBooking.setValue({
              fieldId: "custrecord_ng_cs_eb_space",
              value: eventLine.resourceId,
            });
            createBooking.setValue({
              fieldId: "custrecord_ng_cs_eb_start_date",
              value: new Date(startDate),
            });
            createBooking.setValue({
              fieldId: "custrecord_ng_cs_eb_start_time",
              value: new Date(startTimeofDay),
            });
            createBooking.setValue({
              fieldId: "custrecord_ng_cs_eb_end_date",
              value: new Date(endDate),
            });
            createBooking.setValue({
              fieldId: "custrecord_ng_cs_eb_end_time",
              value: new Date(endTimeofDay),
            });
            createBooking.save();
          } else {
            // this means the booking exists so we update any changes made to it
            record.submitFields({
              type: "customrecord_ng_cs_event_booking",
              id: eventLine.eventDetails.id,
              values: {
                name: eventLine.eventDetails.title,
                custrecord_ng_cs_eb_event:
                  eventLine.eventDetails.extendedProps.eventId,
                custrecord_ng_cs_eb_venue: eventLine.venueId,
                custrecord_ng_cs_eb_space: eventLine.resourceId,
                custrecord_ng_cs_eb_start_date: startDate,
                custrecord_ng_cs_eb_start_time: startTimeofDay,
                custrecord_ng_cs_eb_end_date: endDate,
                custrecord_ng_cs_eb_end_time: endTimeofDay,
              },
              enablesourcing: false,
              ignoreMandatoryFields: true,
            });
          }
        });
      } else if (context.request.method === "GET") {
        // Do GET Code Here (get events for scheduler)
        // Instantiate obj container
        let obj = {};

        // get start and end dates from fullcalendar events request
        const startRange = context.request.parameters["start"];
        const endRange = context.request.parameters["end"];

        log.audit({
          title: "FullCalendar Events Request Start/End Date Range Parameters",
          details:
            "Start range: " +
            new Date(startRange) +
            ", End range: " +
            new Date(endRange),
        });

        const startStr = format.format({
          type: format.Type.DATE,
          value: new Date(startRange),
        });
        const endStr = format.format({
          type: format.Type.DATE,
          value: new Date(endRange),
        });

        // Get case obj
        const currentEvents = s
          .create({
            type: "customrecord_ng_cs_event_booking",
            filters: [
              ["custrecord_ng_cs_eb_start_date", "onorafter", startStr],
              "AND",
              ["custrecord_ng_cs_eb_end_date", "onorbefore", endStr],
            ],
            columns: [
              s.createColumn({
                name: "id",
                sort: s.Sort.ASC,
              }),
              s.createColumn({ name: "name" }),
              s.createColumn({ name: "custrecord_ng_cs_eb_event" }),
              s.createColumn({
                name: "custrecord_ng_cs_eb_venue",
                label: "Venue",
              }),
              s.createColumn({
                name: "custrecord_ng_cs_eb_space",
              }),
              s.createColumn({
                name: "custrecord_ng_cs_eb_event",
              }),
              s.createColumn({
                name: "custrecord_ng_cs_eb_start_date",
              }),
              s.createColumn({
                name: "custrecord_ng_cs_eb_start_time",
              }),
              s.createColumn({
                name: "custrecord_ng_cs_eb_end_date",
              }),
              s.createColumn({
                name: "custrecord_ng_cs_eb_end_time",
              }),
              s.createColumn({
                name: "custrecord_ng_cs_eb_status",
              }),
              s.createColumn({
                name: "custrecord_ng_cs_eb_description",
              }),
            ],
          })
          .run()
          .getRange({ start: 0, end: 1000 });

        // Clean up ns object
        const eventSort = currentEvents.map((r) => {
          return {
            id: r.getValue({
              name: "id",
            }),
            title: r.getValue({
              name: "name",
            }),
            eventName: r.getText({
              name: "custrecord_ng_cs_eb_event",
            }),
            eventId: r.getValue({
              name: "custrecord_ng_cs_eb_event",
            }),
            venue: r.getValue({
              name: "custrecord_ng_cs_eb_venue",
            }),
            resourceId: r.getValue({
              name: "custrecord_ng_cs_eb_space",
            }),
            start: convertToISO8601(
              r.getValue({
                name: "custrecord_ng_cs_eb_start_date",
              }),
              r.getValue({
                name: "custrecord_ng_cs_eb_start_time",
              })
            ),
            end: convertToISO8601(
              r.getValue({
                name: "custrecord_ng_cs_eb_end_date",
              }),
              r.getValue({
                name: "custrecord_ng_cs_eb_end_time",
              })
            ),
          };
        });
        // Compile and return obj
        obj = eventSort;

        context.response.write({ output: JSON.stringify(obj) });
      }

      function convertToISO8601(dateValue, timeValue) {
        var datePieces = dateValue.split("/");
        var timePieces = timeValue.split(":");
        var adtlTimePieces = timePieces[1].split(" ");
        var hourValue = timePieces[0];
        var minuteValue = adtlTimePieces[0];
        var isAM = timeValue.search(/am/i) >= 0;

        var m = datePieces[0];
        var d = datePieces[1];
        var y = datePieces[2];
        var finalM = "";
        var finalD = "";
        if (m.length == 1) {
          finalM = "0" + m;
        } else {
          finalM = m;
        }
        if (d.length == 1) {
          finalD = "0" + d;
        } else {
          finalD = d;
        }

        var finalHour = "";
        if (isAM && hourValue == "12") {
          finalHour = "00";
        } else if (!isAM && parseInt(hourValue) < 12) {
          finalHour = Math.round(parseInt(hourValue) + 12).toFixed(0);
        } else {
          if (hourValue.length == 1) {
            finalHour = "0" + hourValue;
          } else {
            finalHour = hourValue;
          }
        }

        var finalMinute = "";
        if (minuteValue.length == 1) {
          finalMinute = "0" + minuteValue;
        } else {
          finalMinute = minuteValue;
        }

        var timeArr = [y, finalM, finalD, finalHour, finalMinute];
        var baseTimeString = "{0}-{1}-{2}T{3}:{4}:00";
        var isoTimeString = baseTimeString.replace(
          /{(\d+)}/g,
          function (match, number) {
            return typeof timeArr[number] != "undefined"
              ? timeArr[number]
              : "null";
          }
        );
        return isoTimeString;
      }
    }
  }

  return {
    onRequest: onRequest,
  };
});
