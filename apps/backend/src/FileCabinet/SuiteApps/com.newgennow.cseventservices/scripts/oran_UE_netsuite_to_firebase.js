/**
 * @NApiVersion 2.x
 * @NScriptType UserEventScript
 *
 */
define(["N/record", "N/search", "N/file", "N/https"], function (
  record,
  search,
  file,
  https
) {
  function afterSubmit(context) {
    var headers = {
      "Content-Type": "application/json",
    };

    var rec = context.newRecord;
    var fields = rec.getFields();
    var type = rec.getValue("type");
    var body = {};

    for (var i = 0; i < fields.length; i++) {
      //if(fields[i] == "storedisplayimage") body[fields[i]] = file.load({ id: rec.getValue(fields[i]) }).url;
      //else body[fields[i]] = rec.getValue(fields[i]);
      var field = rec.getField(fields[i]);

      body[fields[i]] = {};
      body[fields[i]].fieldValue = rec.getValue(fields[i]);
      body[fields[i]].fieldText = rec.getText(fields[i]);
      body[fields[i]].field = field;
    }

    var response = https.post({
      url: "https://netsuite-ecommerce.firebaseio.com/" + type + "/.json",
      body: JSON.stringify(body),
      headers: headers,
    });
  }

  return {
    afterSubmit: afterSubmit,
  };
});
