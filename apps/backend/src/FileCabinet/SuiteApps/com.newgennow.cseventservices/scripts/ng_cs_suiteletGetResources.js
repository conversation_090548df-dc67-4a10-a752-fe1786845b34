/**
 *@NApiVersion 2.1
 *@NScriptType Suitelet
 */

define(["N/search", "N/log"], function (s, log) {
  function onRequest(context) {
    // Instantiate obj container
    let obj = {};

    // Get all resources (venues and their spaces)
    const currentResources = s
      .create({
        type: "customrecord_facility",
        filters: [],
        columns: [
          s.createColumn({
            name: "internalid",
          }),
          s.createColumn({
            name: "name",
            sort: s.Sort.ASC,
          }),
          s.createColumn({
            name: "internalid",
            join: "CUSTRECORD_HALL_FACILITY",
          }),
          s.createColumn({
            name: "name",
            join: "CUSTRECORD_HALL_FACILITY",
          }),
          s.createColumn({
            name: "custrecord_ng_cs_space_occupancy",
            join: "CUSTRECORD_HALL_FACILITY",
          }),
        ],
      })
      .run()
      .getRange({ start: 0, end: 1000 });

    // Create resource object for fullcalendar
    const resourceSort = currentResources.map((r) => {
      return {
        id: r.getValue({
          name: "internalid",
          join: "CUSTRECORD_HALL_FACILITY",
        }),
        title: r.getValue({
          name: "name",
          join: "CUSTRECORD_HALL_FACILITY",
        }),
        occupancy: r.getValue({
          name: "custrecord_ng_cs_space_occupancy",
          join: "CUSTRECORD_HALL_FACILITY",
        }),
        venue: r.getValue({
          name: "name",
        }),
        venueInternalId: r.getValue({
          name: "internalid",
        }),
      };
    });

    // Create resource object for antd treeview
    const treeView = currentResources.map((r) => {
      return {
        title: r.getValue({ name: "name" }),
        nsInternalId: r.getValue({ name: "internalid" }),
        value:
          Math.random().toString(36).substring(2, 15) +
          Math.random().toString(36).substring(2, 15),
        children: {
          title: r.getValue({ name: "name", join: "CUSTRECORD_HALL_FACILITY" }),
          nsInternalId: r.getValue({
            name: "internalid",
            join: "CUSTRECORD_HALL_FACILITY",
          }),

          value:
            Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15),
        },
      };
    });

    // Consolidate subcategories by category id
    const treeViewClean = treeView.reduce((acc, curr) => {
      let match = acc.find((x) => x.nsInternalId === curr.nsInternalId); //+ customer, project if needed
      if (!match) {
        match = { ...curr, children: [] };
        acc.push(match);
      }
      match.children.push(curr.children);
      return acc;
    }, []);

    // get all events list
    const fullEventsList = s
      .create({
        type: "customrecord_show",
        filters: [["isinactive", "is", "F"]],
        columns: [
          s.createColumn({
            name: "internalid",
          }),
          s.createColumn({
            name: "name",
          }),
        ],
      })
      .run()
      .getRange({ start: 0, end: 1000 });

    // Create resource object for fullcalendar
    const fullEventsSort = fullEventsList.map((r) => {
      return {
        id: r.getValue({
          name: "internalid",
        }),
        name: r.getValue({
          name: "name",
        }),
      };
    });

    // Compile and return obj
    obj.resourceSort = resourceSort;
    obj.treeViewClean = treeViewClean;
    obj.fullEventsSort = fullEventsSort;

    context.response.write({ output: JSON.stringify(obj) });
  }
  return {
    onRequest: onRequest,
  };
});
