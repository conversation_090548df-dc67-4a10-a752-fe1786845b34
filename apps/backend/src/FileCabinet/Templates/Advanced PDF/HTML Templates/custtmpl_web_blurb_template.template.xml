<pdf>
    <head>
        <style type="text/css">
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 0;
            }
            .container {
                width: 90%;
                margin: auto;
                padding: 25px;
                background-color: #f9f9f9;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
            h1, h2, h3 {
                color: #005b99;
                margin-bottom: 12px;
            }
            p {
                margin-bottom: 12px;
            }
            .important {
                font-weight: bold;
                color: #d9534f;
                font-size: 1.1em;
            }
            .table-container {
                margin: 20px 0;
            }
            .styled-table {
                width: 100%;
                border-collapse: collapse;
                font-size: 0.95em;
            }
            .styled-table th, .styled-table td {
                padding: 10px;
                border: 1px solid #ddd;
                text-align: left;
            }
            .styled-table th {
                background-color: #005b99;
                color: #ffffff;
            }
            .styled-table tr:nth-child(even) {
                background-color: #f3f3f3;
            }
            ul {
                padding-left: 18px;
            }
            .notes {
                color: #666;
                font-size: 0.85em;
            }
            .section-header {
                font-size: 1.2em;
                color: #333;
                border-bottom: 2px solid #e0e0e0;
                padding-bottom: 6px;
                margin-bottom: 10px;
            }

          .styled-table-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        font-size: 0.95em;
        font-family: Arial, sans-serif;
        margin: 20px 0;
    }
    .styled-table-row,
    .styled-table-header {
        display: flex;
        padding: 10px 0;
    }
    .styled-table-header {
        background-color: #005b99;
        color: #ffffff;
        font-weight: bold;
    }
    .styled-table-cell {
        flex: 1;
        padding: 10px;
        
    }
    .styled-table-row:nth-child(even) {
        background-color: #f3f3f3;
    }
    .styled-table-row:nth-child(odd) {
        background-color: #ffffff;
    }
          .styled-shipment-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin-bottom: 20px;
    }
    .styled-shipment-row {
        display: flex;
        padding: 10px 0;
    }
    .styled-shipment-header {
        font-weight: bold;
        background-color: #005b99;
        color: white;
        flex: 1;
        display: flex;
        justify-content: center;
        padding: 10px;
    }
    .styled-shipment-cell {
        flex: 1;
        padding: 10px;
        border: 1px solid #ddd;
    }
    .styled-shipment-header-group {
        display: flex;
    }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Important Schedule Information</h1>
            <p class="important">Discount Price Deadline: ${record.custrecord_adv_ord_date}</p>

            <h2>Event Schedule</h2>
            <p><strong>Exhibitor Move In:</strong> ${record.custrecord_ng_cses_event_start_date}</p>
            <p><strong>Show Dates:</strong> ${record.custrecord_ng_cses_event_start_date} - ${record.custrecord_ng_cses_event_end_date}</p>
            <p><strong>Exhibitor Move Out:</strong> ${record.custrecord_ng_cses_event_end_date}</p>
            <p><strong>Preshow Order Closing Date:</strong> ${record.custrecord_ng_cses_event_end_date}</p>

            <h2>Booth Package</h2>
            <p>The items below come with your registration for the event and will be automaticaly placed in your booth.:</p>
            <p>A standard ${record.custrecord_booth_size} booth space will include:</p>
            <ul>
                <li>${record.booth_drape} Back Drape and Side Drape</li>
                <li>(1) Identification Sign</li>
                <li>Aisles are carpeted in ${record.aisle_carpet_color} color carpet. Note: Floor coverings are mandatory.</li>
            </ul>

            <#assign eventDateData = '${eventBlurb.custrecord_ng_cs_blrb_event_date_data}'>

            <#if eventDateData?has_content>
                <#assign parsedEventData = eventDateData?eval_json>
                <div class="table-container">
                    <h3 class="section-header">Event Schedule Details</h3>
                    <div class="styled-table-container">
    
    <div class="styled-table-header">
        <div class="styled-table-cell">Type</div>
        <div class="styled-table-cell">Date</div>
        <div class="styled-table-cell">Start Time</div>
        <div class="styled-table-cell">End Time</div>
    </div>
   
    <#list parsedEventData.eventDate as eventDateRec>
        <div class="styled-table-row">
            <div class="styled-table-cell">${eventDateRec.custrecord_date_type}</div>
            <div class="styled-table-cell">${eventDateRec.custrecord_date}</div>
            <div class="styled-table-cell">${eventDateRec.custrecord_start_time}</div>
            <div class="styled-table-cell">${eventDateRec.custrecord_end_time}</div>
        </div>
    </#list>
</div>
                </div>
            </#if>

            <h2>Standard/Overtime/Double Time Information</h2>
            <p>
               Straight Time: 8:00AM to 4:30PM (Mon-Fri)<br/>
               Overtime: 4:30PM to 8:00AM (Mon-Fri); All day Saturday<br/>
               Double Time: All day Sunday and Holidays</p>
            <p class="notes">Note: Overtime and double-time rates may apply based on schedule and venue policies.</p>

            <h2>Inbound Shipping Information</h2>
            <div class="table-container">
                <div class="styled-shipment-container">
  
    <div class="styled-shipment-row styled-shipment-header-group">
        <div class="styled-shipment-header" style="flex: 3;">Advance Shipments</div>
        <div class="styled-shipment-header" style="flex: 3;">Direct Shipments</div>
    </div>
   
    <div class="styled-shipment-row">
        <div class="styled-shipment-cell" style="flex: 3;">${record.custrecord_ship_to_warehouse_address}</div>
        <div class="styled-shipment-cell" style="flex: 3;">${record.custrecord_ship_to_facility_address}</div>
    </div>
</div>
            </div>

            <h2>Receiving Dates</h2>
            <p>Last Day for Advance Shipments: ${record.custrecord_last_adv_ship}</p>
            <p>Last Day for Late Shipments: ${record.custrecord_last_late_ship}</p>
            <p>Shipments received outside the date range or without required event details may incur surcharges.</p>

            <h2>Outbound Shipping Information</h2>
            <p><strong>Outbound Pick Up Address:</strong></p> 
               <p>${record.custrecord_ship_to_facility_address}</p>
            <p><strong>Carrier Check In Date & Time:</strong></p> 
               <p>${record.custrecord_wh_ship_date}</p>

            <h2>Material Handling</h2>
            <p>Handling fees apply to all shipments. Details are available in the "Material Handling" tab.</p>

            <h2>Exhibitor Supervised Labor and Forklift Service</h2>
            <p>Labor requests can be made at the ABC Company Service Desk. No-show fees apply for unrequested services.</p>

            <h2>Payment and Cancellation Policies</h2>
            <p>A credit card is required for billing. Cancellations 15+ days before the event incur a 50% fee; cancellations within 15 days incur a 100% fee.</p>
        </div>
    </body>
</pdf>
