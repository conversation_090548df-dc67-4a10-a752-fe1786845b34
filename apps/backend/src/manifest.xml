<manifest projecttype="SUITEAPP">
  <publisherid>com.newgennow</publisherid>
  <projectid>cseventservices</projectid>
  <projectname>ConventionSuite Event Services</projectname>
  <projectversion>9.10.50</projectversion>
  <frameworkversion>1.0</frameworkversion>
  <dependencies>
    <features>
      <feature required="false">WEBSERVICES</feature>
      <feature required="true">INVENTORY</feature>
      <feature required="true">MULTILOCINVT</feature>
      <feature required="true">CRM</feature>
      <feature required="true">ADVRECEIVING</feature>
      <feature required="true">GROSSPROFIT</feature>
      <feature required="true">APPROVALROUTING</feature>
      <feature required="true">ADVFORECASTING</feature>
      <feature required="false">WEBSTORE</feature>
      <feature required="true">SALESORDERS</feature>
      <feature required="true">WEBSITE</feature>
      <feature required="true">ADVANCEDJOBS</feature>
      <feature required="true">MULTICURRENCY</feature>
      <feature required="true">CLASSES</feature>
      <feature required="true">SFA</feature>
      <feature required="true">INVENTORYSTATUS</feature>
      <feature required="true">CHARGEBASEDBILLING</feature>
      <feature required="true">SUBSCRIPTIONBILLING</feature>
      <feature required="false">EXTREMELIST</feature>
      <feature required="true">ADVTAXENGINE</feature>
      <feature required="true">ADVANCEDSITECUST</feature>
      <feature required="false">MAILMERGE</feature>
      <feature required="true">OPPORTUNITIES</feature>
      <feature required="true">JOBCOSTING</feature>
      <feature required="true">PAYABLES</feature>
      <feature required="false">CUSTOMSEGMENTS</feature>
      <feature required="true">EXPREPORTS</feature>
      <feature required="true">BILLINGACCOUNTS</feature>
      <feature required="true">MATRIXITEMS</feature>
      <feature required="true">MARKETING</feature>
      <feature required="true">LOCATIONS</feature>
      <feature required="true">MULTIBOOK</feature>
      <feature required="false">SUPPORT</feature>
      <feature required="true">ADVANCEDPRINTING</feature>
      <feature required="true">DEPARTMENTS</feature>
      <feature required="true">WORKFLOW</feature>
      <feature required="true">RECEIVABLES</feature>
      <feature required="true">JOBS</feature>
      <feature required="true">ADVSHIPPING</feature>
      <feature required="true">TIMETRACKING</feature>
      <feature required="true">CUSTOMTRANSACTIONS</feature>
      <feature required="true">SERVERSIDESCRIPTING</feature>
      <feature required="true">BUSINESS</feature>
      <feature required="true">BILLSCOSTS</feature>
      <feature required="true">DROPSHIPMENTS</feature>
      <feature required="true">ESTIMATES</feature>
      <feature required="true">PURCHASEREQS</feature>
      <feature required="true">ACCOUNTINGPERIODS</feature>
      <feature required="true">ACCOUNTING</feature>
      <feature required="true">ITEMOPTIONS</feature>
      <feature required="true">MULTPRICE</feature>
      <feature required="true">MULTIPARTNER</feature>
      <feature required="true">CCTRACKING</feature>
      <feature required="true">CUSTOMRECORDS</feature>
      <feature required="true">SUBSIDIARIES</feature>
      <feature required="true">UNITSOFMEASURE</feature>
      <feature required="true">PURCHASEORDERS</feature>
      <feature required="true">CUSTOMGLLINES</feature>
    </features>
    <objects>
      <object>customrecord_ng_cs_venue_contacts</object>
      <object>customrecord_error_log</object>
      <object>customrecord_ng_cs_show_table_web_blurbs</object>
      <object>customrecord_ng_cs_area_images</object>
      <object>customrecord_subcategory</object>
      <object>customrecord_ng_cs_shipment</object>
      <object>customrecord_ng_cs_show_mstr_labor_schd</object>
      <object>customrecord_show_display_forms</object>
      <object>customrecord_time</object>
      <object>customrecord_order_type</object>
      <object>customrecord_ng_cs_task_template</object>
      <object>customrecord_ng_cs_area_details</object>
      <object>customrecord_ng_cses_upload_attachment</object>
      <object>cseg_ng_cs_job</object>
      <object>customrecord_ng_cs_show_labor_schedule</object>
      <object>customrecord_show_date</object>
      <object>customrecord_ng_cs_settings</object>
      <object>customrecord_ng_cs_date_types</object>
      <object>customrecord_show_booths</object>
      <object>customrecord_ng_stk_item</object>
      <object>customrecord_ng_paytrace_settings</object>
      <object>customrecord_exhb_import_log</object>
      <object>customrecord_ng_cs_show_table_adtnl_info</object>
      <object>customrecord_exhibition_hall</object>
      <object>customrecord_freight_table</object>
      <object>customrecord_facility</object>
      <object>customrecord_ng_cs_item_collection</object>
      <object>customrecord_ng_paytrace_integration</object>
      <object>customrecord_ng_pt_ecrypted_card</object>
      <object>customrecord_ng_cs_item_stock_avail</object>
      <object>customrecord_show</object>
    </objects>
    <applications>
      <application id="com.newgennow.cseventservices">
        <objects>
          <object>customrecord_ng_cs_event_function</object>
          <object>customrecord_cseg_ng_cs_job</object>
        </objects>
      </application>
    </applications>
  </dependencies>
</manifest>