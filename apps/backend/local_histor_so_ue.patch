Index: src/FileCabinet/SuiteScripts/CS UI Script Files SS 2.0/User Event/ng_cs_ueSalesOrder.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/FileCabinet/SuiteScripts/CS UI Script Files SS 2.0/User Event/ng_cs_ueSalesOrder.js b/src/FileCabinet/SuiteScripts/CS UI Script Files SS 2.0/User Event/ng_cs_ueSalesOrder.js
--- a/src/FileCabinet/SuiteScripts/CS UI Script Files SS 2.0/User Event/ng_cs_ueSalesOrder.js	
+++ b/src/FileCabinet/SuiteScripts/CS UI Script Files SS 2.0/User Event/ng_cs_ueSalesOrder.js	(date 1655081913905)
@@ -153,6 +153,8 @@
 						 * */
 						orderType;
 
+					log.debug({ title: 'Request received from source?', details: context?.request })
+
 					if (context?.request && Object.keys(context.request).length !== 0) {
 						exhibitorId = context.request.parameters['entity'] || null;
 						eventId = context.request.parameters['custbody_show_table'] || null;
@@ -585,10 +587,7 @@
 
 			log.audit({title: '⚡ Running script BEFORESUBMIT...', details: runtime.getCurrentScript()})
 
-			let currentSalesOrder = context.newRecord,
-				ccErr = false,
-				authErr,
-				errLine;
+			let currentSalesOrder = context.newRecord, ccErr = false, authErr;
 
 			try {
 				log.audit({ title: '⚡  Inside root try BeforeSubmit...', details: '⌛'})
@@ -597,16 +596,6 @@
 				setScriptPaths();
 				setFormFlags(context);
 
-				const getAllResultsFor = (searchObj, callback) => {
-					let myPagedData = searchObj.runPaged();
-					myPagedData.pageRanges.forEach(function (pageRange) {
-						let myPage = myPagedData.fetch({index: pageRange.index});
-						myPage.data.forEach(function (result) {
-							callback(result)
-						});
-					})
-				}
-
 				_RunGlobalBO = csLib.settings.GlobalBoothOrderScripting;
 				_ActiveFormBO = false;
 
@@ -620,8 +609,7 @@
 					details: `Run booth ordering scripts: ${_RunGlobalBO} -- Booth ordering form present: ${_ActiveFormBO}`
 				});
 
-				let eventData = {},
-					eventId,
+				let eventId,
 					boothId,
 					billParty
 
@@ -644,10 +632,7 @@
 						details: cCard?.id ? `Card ID ${cCard.id} is valid! - ✅` : `❌ Card id "${cCard.id}" is not a valid card.`
 					})
 
-					// Set event Id upon init
-					if (eventId && !isNaN(Number(eventId))) {
-						eventData = getEventData(eventId);
-					}
+
 
 					switch (context.type) {
 						case 'create': {
@@ -661,7 +646,7 @@
 
 							log.audit({title: 'Loading values for exhibitor form...', details: '⌛'})
 
-							setUpExhibitorFormItems()
+							setUpExhibitorFormItems(context, currentSalesOrder)
 							// SET UP EXHIBITOR FORM ITEMS END
 
 							switch (runtime.executionContext) {
@@ -675,19 +660,23 @@
 									cCard.name = currentSalesOrder.getValue({fieldId: "ccname"});
 									cCard.code = currentSalesOrder.getValue({fieldId: "ccsecuritycode"});
 									cCard.number = currentSalesOrder.getValue({fieldId: "ccnumber"}) // field unauthorized on suitelet
-									setIsWebOrder()
+									setIsWebOrder(currentSalesOrder)
 
 									// RUN SHIPPING ADDRESS UPDATE
-									runShippingAddressUpdate()
+									runShippingAddressUpdate(context, eventId)
 									// RUN SHIPPING ADDRESS UPDATE END
 
 									// BEGIN ORDER PROCESSING
-									runWebOrderOperations()
+									runWebOrderOperations(context, eventId, boothId, billParty, cCard)
 										.then((r) => {
 											log.audit({ title: '⚡ Web order ops finished!', details: r})
 										})
 										.catch(err => {
 											log.error({ title: '❌ Internal web processing op error!', details: err })
+											if (err?.name === 'CREDIT_CARD_ERROR') {
+												authErr = err
+												ccErr = true
+											}
 										})
 									// ORDER PROCESSING END
 
@@ -700,19 +689,23 @@
 									cCard.name = currentSalesOrder.getValue({fieldId: "ccname"});
 									cCard.code = currentSalesOrder.getValue({fieldId: "ccsecuritycode"});
 									cCard.number = currentSalesOrder.getValue({fieldId: "ccnumber"}) // field unauthorized on suitelet
-									setIsWebOrder()
+									setIsWebOrder(currentSalesOrder)
 
 									// RUN SHIPPING ADDRESS UPDATE
-									runShippingAddressUpdate()
+									runShippingAddressUpdate(context, eventId)
 									// RUN SHIPPING ADDRESS UPDATE END
 
 									// BEGIN ORDER PROCESSING
-									runWebOrderOperations()
+									runWebOrderOperations(context, eventId, boothId, billParty, cCard)
 										.then((r) => {
 											log.audit({ title: '⚡ Web order ops finished!', details: r})
 										})
 										.catch(err => {
 											log.error({ title: '❌ Internal web processing op error!', details: err })
+											if (err?.name === 'CREDIT_CARD_ERROR') {
+												authErr = err
+												ccErr = true
+											}
 										})
 									// ORDER PROCESSING END
 								}
@@ -726,7 +719,7 @@
 									cCard.number = currentSalesOrder.getValue({fieldId: "ccnumber"}) // field unauthorized on suitelet
 
 									// BEGIN USER ORDER CREATE LOGIC
-									runUserOrderCreateProcessing()
+									runUserOrderCreateProcessing(context, eventId, billParty, boothId)
 									// USER ORDER CREATE LOGIC END
 
 								}
@@ -748,7 +741,7 @@
 							// Run exhibitor order reporting
 							log.audit({title: 'Loading values for exhibitor form...', details: '⌛'})
 
-							setUpExhibitorFormItems()
+							setUpExhibitorFormItems(context, currentSalesOrder)
 							// SET UP EXHIBITOR FORM ITEMS END
 
 
@@ -757,7 +750,7 @@
 									log.audit({title: `Running RESTLET case 🌟 - on "${context.type}"`, details: ''})
 
 									// RUN SHIPPING ADDRESS UPDATE
-									runShippingAddressUpdate()
+									runShippingAddressUpdate(context, eventId)
 									// RUN SHIPPING ADDRESS UPDATE END
 
 									cCard.exp = currentSalesOrder.getValue({fieldId: "ccexpiredate"});
@@ -770,7 +763,7 @@
 									log.audit({title: `Running WEBSTORE case 🌟 - on "${context.type}"`, details: ''})
 
 									// RUN SHIPPING ADDRESS UPDATE
-									runShippingAddressUpdate()
+									runShippingAddressUpdate(context, eventId)
 									// RUN SHIPPING ADDRESS UPDATE END
 
 									cCard.exp = currentSalesOrder.getValue({fieldId: "ccexpiredate"});
@@ -810,7 +803,7 @@
 							// Run exhibitor order reporting
 							log.audit({title: 'Loading values for exhibitor form...', details: '⌛'})
 
-							setUpExhibitorFormItems()
+							setUpExhibitorFormItems(context, currentSalesOrder)
 							// SET UP EXHIBITOR FORM ITEMS END
 
 							switch (runtime.executionContext) {
@@ -834,9 +827,7 @@
 						}
 						case 'delete': {
 							log.audit({title: `Running delete case 🌟 `, details: ''})
-
 							return; // Skip
-							break;
 						}
 						default:
 							log.audit({title: 'Context type not found!', details: `⚡ - "${context.type}"`})
@@ -862,38 +853,13 @@
 					details: currentSalesOrder.getValue({fieldId: "taxitem"})
 				});
 
-				currTaxItem = currentSalesOrder.getValue({fieldId: "taxitem"})
-
 				// OVERRIDE FORCE TAXITEM RESET
-
-
-				/*	} catch (err) {
-						log.error({title: "❗ General error encountered during CS order BeforeSubmit processing!", details: err})
-
-						if (err.name === 'CREDIT_CARD_ERROR') {
-							ccErr = true
-							log.audit({
-								title: "CS UE Sales Order - BS - Terminating processing by throwing card auth error",
-								details: ""
-							});
-							NG.log.logError(authErr, "Card auth error");
+				currTaxItem = currentSalesOrder.getValue({fieldId: "taxitem"})
 
-							authErr = error.create({
-								name: err.name,
-								message: err.message
-							})
-						}
 
-						log.debug({title: 'Check err obj for auth 💳', details: authErr})
-
+				log.debug({ title: 'Is there a CC Error?', details: ccErr })
+				log.debug({ title: 'Is there a Auth Error?', details: authErr })
 
-						if (ccErr && authErr?.message) {
-							log.audit({title: 'Auth error being thrown ❗', details: ''})
-
-							throw authErr
-						}
-					}
-		*/
 				if (ccErr && authErr?.message) {
 					log.audit({title: 'Auth error being thrown ❗', details: ''})
 					throw authErr
@@ -905,1020 +871,7 @@
 				throw err
 			}
 
-			function setUpExhibitorFormItems() {
-				// SET UP EXHIBITOR FORM ITEMS START
-				let lineNumber = 0;
-				log.audit({
-					title: 'EF - Exhibitor Form setting item values (create)...',
-					details: `⌛ - In context: "${context.type}"`
-				})
-
-				log.debug({title: 'EF - Get item count...', details: `⌛`})
-
-				let lines = currentSalesOrder.getLineCount({sublistId: "item"});
-
-				lines && log.audit({title: 'EF - Get item count gathered! ✅', details: `🌟 - ${lines}`})
-
-				for (lineNumber = 0; lineNumber < lines; lineNumber++) {
-					log.debug({title: 'EF - Looping item line 🤏', details: `Item: ${lineNumber}`})
-
-					log.audit({
-						title: 'EF - Setting Current Line Code 🔢',
-						details: `⌛ - In context: "${context.type}"`
-					})
-
-					let currLineCode = currentSalesOrder.getSublistValue({
-						sublistId: "item",
-						fieldId: "custcol_linecode",
-						line: lineNumber
-					});
-
-					if (currLineCode) log.audit({
-						title: 'EF - Current Line Code Set! ✅',
-						details: `Code: ${currLineCode}`
-					})
-					else log.audit({
-						title: 'EF - Current Line Code Not Set❗',
-						details: `Invalid Code: ${currLineCode}`
-					})
-
-					log.audit({
-						title: 'EF - Setting Current Item Type...',
-						details: `⌛ - In context: "${context.type}"`
-					})
-
-					let currLineItemType = currentSalesOrder.getSublistValue({
-						sublistId: "item",
-						fieldId: "itemtype",
-						line: lineNumber
-					});
-
-					if (currLineItemType) log.audit({
-						title: 'Current Item Type Set! ✅',
-						details: `Code: ${currLineItemType}`
-					})
-					else log.audit({
-						title: 'Current Item Type Not Set❗',
-						details: `❌ Invalid Type: ${currLineItemType}`
-					})
-
-					log.audit({title: 'Checking EndGroup and line code def...', details: '⌛'})
-					if (typeof currLineCode === 'string' && currLineCode && currLineItemType !== "EndGroup") {
-						log.audit({
-							title: 'EF - Setting random string value for line code...',
-							details: '⌛'
-						})
-						currentSalesOrder.setSublistValue({
-							sublistId: "item",
-							fieldId: "custcol_linecode",
-							value: NG.tools.randomString(6, 6) + currentSalesOrder.getSublistValue({
-								sublistId: "item",
-								fieldId: "item",
-								line: lineNumber
-							}) + lineNumber.toString(),
-							line: lineNumber
-						});
-
-						log.audit({title: 'Getting item description...', details: '⌛'})
-
-						let currLineDesc = currentSalesOrder.getSublistValue({
-							sublistId: "item",
-							fieldId: "description",
-							line: lineNumber
-						});
-
-						if (currLineDesc) log.audit({
-							title: 'EF - Item description valid! ✅',
-							details: `"${currLineDesc}"`
-						})
-						else log.audit({
-							title: 'EF - Item description invalid! ❌',
-							details: currLineDesc
-						})
-
-						log.audit({title: 'EF - Setting description on item...', details: `⌛`})
-
-						let item_desc_marked = currentSalesOrder.setSublistValue({
-							sublistId: "item",
-							fieldId: "custcol_description",
-							value: currLineDesc,
-							line: lineNumber
-						});
-
-						if (item_desc_marked) log.audit({
-							title: 'EF - Set description on item! ✅',
-							details: `Desc: ${item_desc_marked}`
-						})
-						else log.audit({
-							title: 'EF - Set description on item failed! ❌',
-							details: `Desc: ${item_desc_marked}`
-						})
-
-					}
-				}
-				// SET UP EXHIBITOR FORM ITEMS END
-
-				// SET UP EXHIBITOR FORM ITEMS START
-				lineNumber = 0
-
-				log.audit({
-					title: 'EF - Exhibitor Form setting item desc (create)...',
-					details: `⌛ - In context: "${context.type}"`
-				})
-
-				log.debug({title: 'EF - Get item count...', details: `⌛`})
-
-				lines = currentSalesOrder.getLineCount({sublistId: "item"});
-
-				lines && log.audit({title: 'EF - Get item count gathered! ✅', details: `🌟 - ${lines}`})
-
-				// Set custcol_description for booth order printing form
-				if (lines > 0) {
-					for (lineNumber = 0; lineNumber < lines; lineNumber++) {
-						log.debug({title: 'EF - Looping item line descriptions 🤏', details: `Item: ${lineNumber}`})
-
-						log.audit({
-							title: 'EF - Setting Current Line Code 🔢',
-							details: `⌛ - In context: "${context.type}"`
-						})
-
-						let currLineCode = currentSalesOrder.getSublistValue({
-							sublistId: "item",
-							fieldId: "custcol_linecode",
-							line: lineNumber
-						});
-
-						if (currLineCode) log.audit({
-							title: 'EF - Current Line Code Set! ✅',
-							details: `Code: ${currLineCode}`
-						})
-						else log.audit({
-							title: 'EF - Current Line Code Not Set ❗',
-							details: `Invalid Code: ${currLineCode}`
-						})
-
-						log.audit({
-							title: 'EF - Setting Current Item Type...',
-							details: `⌛ - In context: "${context.type}"`
-						})
-
-						let currLineItemType = currentSalesOrder.getSublistValue({
-							sublistId: "item",
-							fieldId: "itemtype",
-							line: lineNumber
-						});
-
-						if (currLineItemType) log.audit({
-							title: 'EF - Current Item Type Set! ✅',
-							details: `Code: ${currLineItemType}`
-						})
-						else log.audit({
-							title: 'EF - Current Item Type Not Set❗',
-							details: `❌ Invalid Type: ${currLineItemType}`
-						})
-
-						log.audit({title: 'EF - Getting item description...', details: '⌛'})
-
-						let currLineName = currentSalesOrder.getSublistValue({
-							sublistId: "item",
-							fieldId: "item_display",
-							line: lineNumber
-						});
-
-						let currLineDesc = currentSalesOrder.getSublistValue({
-							sublistId: "item",
-							fieldId: "description",
-							line: lineNumber
-						});
-
-						if (currLineDesc) log.audit({
-							title: `EF - Item description valid! ✅ - "${currLineName}"`,
-							details: `"${currLineDesc}"`
-						})
-						else log.audit({
-							title: `EF - Item description invalid! ❌ - "${currLineName}"`,
-							details: currLineDesc
-						})
-
-						log.audit({title: 'EF - Setting description on item...', details: `⌛`})
-
-						let item_desc_marked = currentSalesOrder.setSublistValue({
-							sublistId: "item",
-							fieldId: "custcol_description",
-							value: currLineDesc,
-							line: lineNumber
-						});
-
-						if (item_desc_marked) log.audit({
-							title: 'EF - Set description on item! ✅',
-							details: `Desc: ${item_desc_marked}`
-						})
-						else log.audit({
-							title: 'EF - Set description on item failed! ❌',
-							details: `Desc: ${item_desc_marked}`
-						})
-
-
-					}
-				}
-
-				// SET UP EXHIBITOR FORM ITEMS END
-			}
-
-			function runShippingAddressUpdate() {
-				// Check event data and detect if order is meant for web processing
-				// If its not set and format shipping address fields
-				// If CS Event changes then set shipping
-				let currentSalesOrder = context.newRecord
-				let eventData = {}
-				// Set event Id upon init
-				if (eventId && !isNaN(Number(eventId))) {
-					eventData = getEventData(eventId);
-				}
-
-				let webOrdProc = Boolean(runtime.executionContext === runtime.ContextType.SCHEDULED) && currentSalesOrder.getValue({fieldId: "custbody_isweborder"});
-
-
-				if (Object.keys(eventData).length !== 0 && !webOrdProc) {
-					let oldRec = context.oldRecord
-					if (!isNaN(Number(eventData.custrecord_facility))) {
-						let shipAddrValues = NG.tools.getLookupFields("customrecord_facility", eventData.custrecord_facility, ["custrecord_facility_address1", "custrecord_facility_address2",
-							"custrecord_facility_city", "custrecord_facility_state", "custrecord_facility_zip", "custrecord_facility_phone"
-						], ["custrecord_facility_state"], []);
-
-						// Check for show site address to be set
-						if (shipAddrValues?.custrecord_facility_address1) {
-							let updShipAddr = false;
-
-							try {
-								let shpAdr = currentSalesOrder.getSubrecord({fieldId: "shippingaddress"});
-								let shippingAddressName = shpAdr.getValue({fieldId: "addr1"})
-								let shippingAddressCity = shpAdr.getValue({fieldId: "city"})
-								let shippingAddressState = shpAdr.getValue({fieldId: "state"})
-
-								log.audit({
-									title: "Shipping address subrecord is loaded for edit 🏠",
-									details: `Addr1: ${shippingAddressName}\n -- City: ${shippingAddressCity}\n -- State: ${shippingAddressState}\n`
-								});
-
-								// Validate all shipping address fields are set up with values
-								if (shpAdr.getValue({fieldId: "addr1"}) !== shipAddrValues.custrecord_facility_address1 ||
-									shpAdr.getValue({fieldId: "addr2"}) !== shipAddrValues.custrecord_facility_address2 ||
-									shpAdr.getValue({fieldId: "city"}) !== shipAddrValues.custrecord_facility_city ||
-									shpAdr.getValue({fieldId: "state"}) !== shipAddrValues.custrecord_facility_state) {
-									updShipAddr = true;
-								}
-
-							} catch (err) {
-								log.error({
-									title: "Failed to load/process shipping address subrecord",
-									details: `[${err.name}] : ${err.message}`
-								});
-								updShipAddr = true;
-							}
-
-							// Check if shipping address is valid to set values on record. For Advanced Warehouse Address
-							log.audit({
-								title: "Is New Shipping Address Valid?",
-								details: updShipAddr ? "✅" : "❌"
-							});
-
-							if (updShipAddr) {
-
-								log.audit({
-									title: "Updating shipping address...",
-									details: "'shipaddresslist' value: {0}".NG_Format(currentSalesOrder.getValue({fieldId: "shipaddresslist"}))
-								});
-
-								currentSalesOrder.setValue({fieldId: "shipaddresslist", value: ""});
-								currentSalesOrder.removeSubrecord({fieldId: "shippingaddress"});
-
-								let shipAddy = currentSalesOrder.getSubrecord({fieldId: "shippingaddress"});
-								shipAddy.setValue({fieldId: "country", value: "US"});
-								shipAddy.setValue({
-									fieldId: "addr1",
-									value: shipAddrValues.custrecord_facility_address1
-								});
-								shipAddy.setValue({
-									fieldId: "addr2",
-									value: shipAddrValues.custrecord_facility_address2
-								});
-								shipAddy.setValue({
-									fieldId: "addrphone",
-									value: shipAddrValues.custrecord_facility_phone
-								});
-								shipAddy.setValue({
-									fieldId: "city",
-									value: shipAddrValues.custrecord_facility_city
-								});
-								shipAddy.setValue({
-									fieldId: "state",
-									value: shipAddrValues.custrecord_facility_state
-								});
-								shipAddy.setValue({
-									fieldId: "zip",
-									value: shipAddrValues.custrecord_facility_zip
-								});
-
-							}
-
-							log.audit({
-								title: "tax item id (BeforeSubmit) (post address change)",
-								details: currentSalesOrder.getValue({fieldId: "taxitem"})
-							});
-						}
-					}
-				}
-			}
-
-			function setIsWebOrder() {
-				// WIll only be applied to REST & WEBSTORE on create
-				currentSalesOrder.setValue({fieldId: "custbody_isweborder", value: true});
-			}
-
-			async function runWebOrderOperations() {
-				log.audit({ title: '⚡ Running Web order operations', details: ''})
-				let processingStatus = 'Staging'
-				await runWebOrderCreateProcessing()
-					.then(r => {
-						log.audit({ title: '✔ Order Processing finished', details: r })
-						processingStatus = r
-					})
-					.catch((err) => {
-						log.error({ title: '❌ Error occurred running web processing!', details: err })
-						processingStatus = 'Error check logs!'
-					})
-				return processingStatus
-			}
-
-			async function runWebOrderCreateProcessing() {
-				// Applied to CREATE of REST & WEBSTORE exec contexts only!
-				// Create auth item on test order to pre-authorize card
-				// Then if authorization passes status then create actual order with the exhibitor's items
-				let currentSalesOrder = context.newRecord
-				try {
-					log.audit({ title: '⚡ Running Web order processing BEFORESUB!', details: ''})
-					let authLine = currentSalesOrder.findSublistLineWithValue({
-						sublistId: "item",
-						fieldId: "item",
-						value: csLib.settings.AuthItem
-					});
-
-					if (authLine > 0) {
-						log.audit({title: "Authorization order; Terminating script functions", details: ""});
-						return;
-					}
-
-					log.audit({
-						title: `✨ Running switch case as ${runtime.executionContext}`,
-						details: 'WebProcessing - beforeSubmit()'
-					})
-
-					let lineNumber = 0;
-					switch (runtime.executionContext) {
-						case "RESTLET": {
-							/*
-							* Set isweborder to true to accomplish the following:
-							* - Only on CREATE of the new sales order will this code block run
-							* 1. Grab all the event information that has been set on the sales order to then be used later
-							* 2. Check tax rate assigned to event if it has been set (expecting true) while tax on line item lines is true along with canadian tax is true
-							* 3. Iterate through each item that is not the convienence fee item set the tax on that item from the event
-							*    and if candian tax it not equipped set the taxrate1 field on the item with the float value removing the "%" to make it a decimal number instead.
-							*    Else if there is canadian tax use the custom tax fields to apply that tax to each item.
-							* 4. Then tax group gets set from event when group is defined and rate is defined
-							*
-							* */
-
-							log.audit({title: 'Case RESTLET running...', details: '⌛'})
-							eventId = currentSalesOrder.getValue({fieldId: "custbody_show_table"});
-							boothId = currentSalesOrder.getValue({fieldId: "custbody_booth"});
-							billParty = currentSalesOrder.getValue({fieldId: "entity"});
-							let orderTax = currentSalesOrder.getValue({fieldId: "taxitem"});
-
-							if (!isNaN(Number(eventId)) && Object.keys(eventData).length === 0) { // This may not be needed hence set up during exec context
-								eventData = getEventData(eventId);
-							}
-							// TODO: Rewrite to include better logic in handling tax
-							// Both settings fields reside on Canadian users
-							if (eventData.custrecord_tax_rate && (csLib.settings.SalesTaxOnItemLines || csLib.settings.UseCanadianSalesTax)) {
-								// Set each items tax code when iterating on items in the order.
-								let itemLineCount = currentSalesOrder.getLineCount({sublistId: "item"});
-								for (lineNumber = 0; lineNumber < itemLineCount; lineNumber++) {
-									if (currentSalesOrder.getSublistValue({
-										sublistId: "item",
-										fieldId: "item",
-										line: lineNumber
-									}) !== csLib.settings.ConvenienceFeeItem) {
-										currentSalesOrder.setSublistValue({
-											sublistId: "item",
-											fieldId: "taxcode",
-											value: eventData.custrecord_tax_rate,
-											line: lineNumber
-										});
-										// Check if settings holds NON Canadian Tax to then set from the CS Event
-										if (!csLib.settings.UseCanadianSalesTax) {
-											currentSalesOrder.setSublistValue({
-												sublistId: "item",
-												fieldId: "taxrate1",
-												value: parseFloat(Number(eventData.custrecord_tax_percent) / 100),
-												line: lineNumber
-											});
-
-										} else {
-											currentSalesOrder.setSublistValue({
-												sublistId: "item",
-												fieldId: "taxrate1",
-												value: eventData.custrecord_ng_cs_evt_gst_pct.replace("%", ""),
-												line: lineNumber
-											});
-											currentSalesOrder.setSublistValue({
-												sublistId: "item",
-												fieldId: "taxrate2",
-												value: eventData.custrecord_ng_cs_evt_pst_pct.replace("%", ""),
-												line: lineNumber
-											});
-										}
-									} else {
-										currentSalesOrder.setSublistValue({
-											sublistId: "item",
-											fieldId: "taxcode",
-											value: csLib.settings.ConvFeeTax,
-											line: lineNumber
-										});
-										if (!csLib.settings.UseCanadianSalesTax) {
-											currentSalesOrder.setSublistValue({
-												sublistId: "item",
-												fieldId: "taxrate1",
-												value: "0",
-												line: lineNumber
-											});
-										} else {
-											currentSalesOrder.setSublistValue({
-												sublistId: "item",
-												fieldId: "taxrate1",
-												value: "0",
-												line: lineNumber
-											});
-											currentSalesOrder.setSublistValue({
-												sublistId: "item",
-												fieldId: "taxrate2",
-												value: "0",
-												line: lineNumber
-											});
-										}
-									}
-								}
-							}
-							// This will apply to US users to apply tax on the order instead.
-							else if (!isNaN(Number(eventData.custrecord_tax_rate)) && !isNaN(Number(orderTax)) && Number(eventData.custrecord_tax_rate) !== Number(orderTax)) {
-								currentSalesOrder.setValue({
-									fieldId: "taxitem",
-									value: eventData.custrecord_tax_rate
-								});
-							}
-
-							try {
-								// If paytrace is enabled run paytrace pre step logic in order to process transaction
-								// Else run the webstore processing method fit for non-paytrace payments.
-								log.audit({title: 'Checking if paytrace is enabled...', details: '⌛'})
-								if (csLib.settings.EnablePayTrace) {
-									log.audit({
-										title: 'Paytrace is enabled! - Running Paytrace beforeSubmit processes.',
-										details: '⤵'
-									})
-									if (csLib.settings.AutoChargeWebOrders) {
-										log.audit({
-											title: 'AuthCharge is enabled! - Running Paytrace beforeSubmit processing steps.',
-											details: '⌛'
-										})
-
-										log.audit({title: "CS UE Sales Order - BS - cCard", details: cCard});
-
-										let totalData = calcTotals(currentSalesOrder, eventData);
-
-										let updData = {
-											show: eventId
-											, booth: boothId
-											, billParty: billParty
-											, totalData: totalData
-											, eventData: eventData
-											, order: currentSalesOrder
-										};
-
-										let updTotalData = totalData;
-
-										log.audit({
-											title: "CS UE Sales Order - BS - totalData",
-											details: totalData
-										});
-
-										log.audit({
-											title: "CS UE Sales Order - BS - updTotalData",
-											details: updTotalData
-										});
-
-										let rplyJSON = null;
-
-										try {
-											let boothData = NG.tools.getLookupFields("customrecord_show_booths", boothId, ["name", "custrecord_booth_show_table", "custrecord_booth_exhibitor"], ["custrecord_booth_show_table", "custrecord_booth_exhibitor"], [], true);
-											log.audit({
-												title: "CS UE Sales Order - BS - boothData",
-												details: boothData
-											});
-
-											let orderData = {
-												custbody_ng_cs_order_type: currentSalesOrder.getValue({fieldId: "custbody_ng_cs_order_type"})
-												, exhibitor: billParty
-												, exhibitorName: boothData.custrecord_booth_exhibitor_text
-												, eventName: boothData.custrecord_booth_show_table_text
-												, boothName: boothData.name
-											};
-
-											log.audit({
-												title: "CS UE Sales Order - BS - orderData",
-												details: orderData
-											});
-
-											res = https.post({
-												url: paySuiteletUrl,
-												body: {
-													soid: null,
-													total: updTotalData.total,
-													totalInit: updTotalData.totalInit,
-													tax: updTotalData.tax,
-													taxInit: updTotalData.taxInit,
-													card: JSON.stringify(cCard),
-													ordData: JSON.stringify(orderData),
-													getAuth: "T",
-													subtNickel: "F"
-												}
-											})
-											/*.then((res) => {
-												log.audit({
-													title: '✅ Return from POST (res):',
-													details: res
-												})
-												if (res && res.code === 200 && res?.body) {
-													rplyJSON = JSON.parse(res.body);
-													if (rplyJSON?.status && rplyJSON?.status) {
-														let status = rplyJSON.status
-														if (status !== 'SUCCESS') {
-															throw {
-																name: "CREDIT_CARD_ERROR",
-																message: rplyJSON.message,
-																options: {
-																	code: res.code,
-																	lineNumber: 854,
-																	cause: rplyJSON
-																}
-															}
-														}
-													}
-												}
-											})
-											.catch((err) => {
-												log.error({
-													title: '❌ Error Authing Paytrace Post:',
-													details: err
-												})
-												ccErr = true;
-
-												authErr = error.create({
-													name: "CREDIT_CARD_ERROR",
-													message: err?.message || err,
-													notifyOff: true
-												})
-
-												throw authErr
-
-											})*/
-
-
-											log.audit({
-												title: "CS UE Sales Order - BS - res",
-												details: res
-											});
-
-
-											if (res?.body) {
-												log.audit({
-													title: '✅ Return from POST (res):',
-													details: res?.body
-												})
-												rplyJSON = JSON.parse(res?.body);
-											}
-
-										} catch (err) {
-											// Going to leave this just in case anything else fails prior to the POST request.
-											ccErr = true;
-											NG.log.logError(err, "Error encountered authing web order for full amount");
-
-										}
-
-										if (rplyJSON?.status && rplyJSON.status !== "OK") {
-											// require(['N/error'], function(error) {
-											ccErr = true;
-											errLine = 758;
-											log.audit({
-												title: "CS UE Sales Order - BS - Throwing card auth error (A)",
-												details: ""
-											});
-											authErr = error.create({
-												name: 'CREDIT_CARD_ERROR',
-												message: rplyJSON.message,
-												notifyOff: true
-											});
-											// authErr = new Error(rplyJSON.name, { cause : rplyJSON.message });
-											// log.audit({ title : "authErr (A)" , details : authErr });
-											// });
-										} else if (rplyJSON) {
-											let authSessionId = "_CS_WEB_ORDER_{0}_{1}_{2}_".NG_Format(eventId, boothId, billParty);
-											runtime.getCurrentSession().set({
-												name: authSessionId,
-												value: encode.convert({
-													string: JSON.stringify(rplyJSON),
-													inputEncoding: encode.Encoding.UTF_8,
-													outputEncoding: encode.Encoding.BASE_64
-												})
-											});
-											log.audit({
-												title: "CS UE Sales Order - BS - Session Data Set",
-												details: ""
-											});
-										} else {
-											// require(['N/error'], function(error) {
-											ccErr = true;
-											errLine = 776;
-											log.audit({
-												title: "CS UE Sales Order - BS - Throwing card auth error (B)",
-												details: ""
-											});
-											authErr = error.create({
-												name: "NG_CS_ERROR",
-												message: "Unable to authorize credit card",
-												notifyOff: true
-											});
-
-											// authErr = new Error("NG_CS_ERROR", { cause : "Unable to authorize credit card" });
-											// log.audit({ title : "authErr (B)" , details : authErr });
-											// });
-										}
-									} else if (!csLib.settings.AutoChargeWebOrders && !NG.tools.isEmpty(csLib.settings.AuthItem)) {
-										//
-									}
-								} else {
-									// Run order process as webstore process
-									log.audit({title: 'Running non paytrace logic beforeSub...', details: ''})
-
-									// Run authorization for order
-									log.audit({title: 'Running non PT Authorization...', details: '⌛'})
-									if (authLine) {
-										log.audit({title: 'Auth line exists!', details: '✅'})
-										log.audit({title: 'Running Auth POST...', details: '⌛'})
-										try {
-											let authRes = https.post({
-												url: authSuiteletUrl,
-												body: {
-													cstId: billParty,
-													card: JSON.stringify(cCard)
-												}
-											})
-											log.audit({title: 'POST finished...', details: authRes})
-											if (authRes?.body && authRes.code === 200) {
-												let status = JSON.parse(authRes.body)?.status
-												if (status === 'SUCCESS') {
-													log.audit({
-														title: 'POST finished successfully! ✅',
-														details: status
-													})
-												} else {
-													// Want to place a THROW here to halt order
-													log.error({
-														title: '❌ Error occurred when authing card:',
-														details: authRes
-													})
-													throw {
-														name: "CREDIT_CARD_ERROR",
-														message: authRes.details,
-														options: {
-															code: authRes.code,
-															cause: authRes
-														}
-													}
-												}
-											}
-										} catch (err) {
-											log.error({
-												title: '❌ Internal error occurred when authing card:',
-												details: err
-											})
-											throw {
-												name: "CREDIT_CARD_ERROR",
-												message: "Internal error occurred when authorizing card.",
-												options: {
-													code: res.code,
-													lineNumber: 914,
-													cause: err
-												}
-											}
-										}
-									}
-
-									if (isNaN(Number(eventId)) || isNaN(Number(boothId))) {
-										// Check event id and booth id for valid #s
-										if (isNaN(Number(eventId))) NG.log.logError(null, "WEB ORDER (non PT) -- NO SHOW!!!!!");
-										if (isNaN(Number(boothId))) NG.log.logError(null, "WEB ORDER (non PT) -- NO BOOTH!!!!!");
-									}
-
-									log.audit({title: 'Setting default sales order form...', details: '⚡'})
-									currentSalesOrder.setValue({
-										fieldId: "customform",
-										value: csLib.settings.DefaultBoothOrderForm
-									});
-
-									let formIdSet = currentSalesOrder.getValue({fieldId: "customform"});
-
-									log.audit({title: 'Sales Order form set!', details: `🧾 - ${formIdSet}`})
-
-									/*
-									* TODO: Why do we need to separate if statements to validate event and booth ids when beginning block captures it? - AKA: LINE: 513 🤷‍
-									* */
-
-									log.debug({ title: '🔎 Checking booth id and gonna set actual exhibitor', details: boothId })
-									if (boothId && !isNaN(Number(boothId))) {
-										let boothLookup = search.lookupFields({
-											type: "customrecord_show_booths",
-											id: boothId,
-											columns: ['custrecord_booth_actual_exhibitor']
-										})
-										log.debug({ title: '🔎 Booth lookup results...', details: boothLookup })
-										currentSalesOrder.setValue({
-											fieldId: "custbody_booth_actual_exhibitor",
-											value: boothLookup?.custrecord_booth_actual_exhibitor[0].value
-										});
-										log.debug({ title: '🌟 Set Booth exhibitor as:', details: boothLookup?.custrecord_booth_actual_exhibitor[0].value })
-									}
-
-									log.debug({ title: '🔎 Checking event ID and setting job & class...', details: `Event: "${eventId}"` })
-									if (eventId && !isNaN(Number(eventId))) {
-										log.debug({ title: 'Is event data existent?', details: eventData })
-
-										if (Object.keys(eventData).length === 0) {
-											eventData = getEventData(eventId);
-										}
-										// Check if CS JOB is enabled
-										if (csLib.settings.UseCustomJob) {
-											log.debug({ title: '🔗 Set CSJOB & class from event data:', details: `Job: "${eventData?.custrecord_show_job}" - Class: "${eventData?.custrecord_fin_show}"`})
-											currentSalesOrder.setValue({
-												fieldId: "class",
-												value: eventData.custrecord_fin_show
-											});
-											currentSalesOrder.setValue({
-												fieldId: "custbody_cseg_ng_cs_job",
-												value: eventData.custrecord_show_job
-											});
-											log.audit({
-												title: "Web Order Job/Class",
-												details: "Job ID: {0} - Class ID: {1}".NG_Format(currentSalesOrder.getValue({fieldId: "custbody_cseg_ng_cs_job"}), currentSalesOrder.getValue({fieldId: "class"}))
-											});
-										} else {
-											currentSalesOrder.setValue({
-												fieldId: "class",
-												value: eventData.custrecord_fin_show
-											});
-											log.audit({
-												title: "Web Order Job",
-												details: "Job ID: {0}".NG_Format(currentSalesOrder.getValue({fieldId: "class"}))
-											});
-										}
-									}
-
-									// Library automatically returns NULL if the select field isn't valid removed .isEmpty function
-									// TODO: Will update to deprecate lib for settings and use SuiteQL to grab everything we need instead
-									let departmentId = currentSalesOrder.getValue({fieldId: "department"})
-									if (NG.NSFeatures.DEPARTMENTS() && csLib.settings.DefaultExhibitorDepartment) {
-										currentSalesOrder.setValue({
-											fieldId: "department",
-											value: csLib.settings.DefaultExhibitorDepartment
-										});
-										log.audit({
-											title: "Web Order Department",
-											details: `Department ID: ${departmentId} [👷‍♂️]`
-										});
-									}
-
-									log.audit({
-										title: "Setting Web Order Type: cs_order_type",
-										details: `Department ID: ${departmentId} [👷‍♂️]`
-									});
-
-									currentSalesOrder.setValue({
-										fieldId: "custbody_ng_cs_order_type",
-										value: csLib.settings.DefaultExhibitorOrderType
-									});
-									log.audit({
-										title: "Web Order Type Set!",
-										details: `Order Type ID: ${currentSalesOrder.getValue({fieldId: "custbody_ng_cs_order_type"})}`
-									});
-
-									currentSalesOrder.setValue({
-										fieldId: "customform",
-										value: csLib.settings.DefaultBoothOrderForm
-									});
-
-									return Promise.resolve('Success!')
-
-								}
-							} catch (err) {
-								log.audit({title: "try-catch-err", details: "try-catch-err"});
-								log.audit({title: "err", details: {name: err.name, message: err.message}});
-								NG.log.logError(err, "❗ General error encountered during CS online order credit card authorization processing!");
-
-								if (err.name === 'CREDIT_CARD_ERROR') {
-									authErr = err
-									ccErr = true
-								}
-
-								throw err
-							}
-							break;
-						}
-
-						case "WEBSTORE": {
-							// Checking context of Webstore order (sitebuilder)
-							log.audit({title: 'Running webstore logic beforeSub...', details: ''})
-
-							if (isNaN(Number(eventId)) || isNaN(Number(boothId))) {
-								// Check event id and booth id for valid #s
-								if (isNaN(Number(eventId))) NG.log.logError(null, "WEB ORDER -- NO SHOW!!!!!");
-								if (isNaN(Number(boothId))) NG.log.logError(null, "WEB ORDER -- NO BOOTH!!!!!");
-							}
-							log.audit({title: 'Setting default sales order form...', details: '⚡'})
-							currentSalesOrder.setValue({
-								fieldId: "customform",
-								value: csLib.settings.DefaultBoothOrderForm
-							});
-
-							let formIdSet = currentSalesOrder.getValue({fieldId: "customform"});
-
-							log.audit({title: 'Sales Order form set!', details: `🧾 - ${formIdSet}`})
-
-
-							/*
-							* TODO: Why do we need to separate if statements to validate event and booth ids when beginning block captures it? - AKA: LINE: 513 🤷‍
-							* */
-
-							if (!isNaN(Number(boothId))) {
-								currentSalesOrder.setValue({
-									fieldId: "custbody_booth_actual_exhibitor",
-									value: NG.tools.getLookupFields("customrecord_show_booths", boothId, ["custrecord_booth_actual_exhibitor"], ["custrecord_booth_actual_exhibitor"], []).custrecord_booth_actual_exhibitor
-								});
-							}
-
-							if (!isNaN(Number(eventId))) {
-								if (Object.keys(eventData).length === 0) {
-									eventData = getEventData(eventId);
-								}
-								// Check if CS JOB is enabled
-								if (csLib.settings.UseCustomJob) {
-									currentSalesOrder.setValue({
-										fieldId: "class",
-										value: eventData.custrecord_fin_show
-									});
-									currentSalesOrder.setValue({
-										fieldId: "custbody_cseg_ng_cs_job",
-										value: eventData.custrecord_show_job
-									});
-									log.audit({
-										title: "Web Order Job/Class",
-										details: "Job ID: {0} - Class ID: {1}".NG_Format(currentSalesOrder.getValue({fieldId: "custbody_cseg_ng_cs_job"}), currentSalesOrder.getValue({fieldId: "class"}))
-									});
-								} else {
-									currentSalesOrder.setValue({
-										fieldId: "class",
-										value: eventData.custrecord_fin_show
-									});
-									log.audit({
-										title: "Web Order Job",
-										details: "Job ID: {0}".NG_Format(currentSalesOrder.getValue({fieldId: "class"}))
-									});
-								}
-							}
-
-							// Library automatically returns NULL if the select field isn't valid removed .isEmpty function
-							// TODO: Will update to deprecate lib for settings and use SuiteQL to grab everything we need instead
-							let departmentId = currentSalesOrder.getValue({fieldId: "department"})
-							if (NG.NSFeatures.DEPARTMENTS() && csLib.settings.DefaultExhibitorDepartment) {
-								currentSalesOrder.setValue({
-									fieldId: "department",
-									value: csLib.settings.DefaultExhibitorDepartment
-								});
-								log.audit({
-									title: "Web Order Department",
-									details: `Department ID: ${departmentId} [👷‍♂️]`
-								});
-							}
-							log.audit({
-								title: "Setting Web Order Type: cs_order_type",
-								details: `Department ID: ${departmentId} [👷‍♂️]`
-							});
-
-							currentSalesOrder.setValue({
-								fieldId: "custbody_ng_cs_order_type",
-								value: csLib.settings.DefaultExhibitorOrderType
-							});
-							log.audit({
-								title: "Web Order Type Set!",
-								details: `Order Type ID: ${currentSalesOrder.getValue({fieldId: "custbody_ng_cs_order_type"})}`
-							});
-
-							currentSalesOrder.setValue({
-								fieldId: "customform",
-								value: csLib.settings.DefaultBoothOrderForm
-							});
-
-							break;
-						}
-						default: {
-							log.audit({title: 'No execution context was ran for web order', details: ''})
-						}
-					}
-
-					currentSalesOrder.setValue({fieldId: "getauth", value: false});
-					currentSalesOrder.setValue({fieldId: "tobeemailed", value: false});
-
-					return rplyJSON
-				} catch (err) {
-					log.error({ title: 'Internal Error running processing!', details: err })
-					throw err
-				}
-			}
-
-			function runUserOrderCreateProcessing() {
-				// APPLY to USERINTERFACE exec context of CREATE only!
-				// Check if orders are to be consolidated into 1 single order
-				if (runtime.executionContext === runtime.ContextType.USER_INTERFACE && csLib.settings.PreventAdditionalOrders) {
-					// Make sure the event id and booth ids are present
-					if (!isNaN(Number(eventId)) && !isNaN(Number(boothId))) {
-						let filt = [
-							["custbody_show_table", "anyof", [eventId]]
-							, "and"
-							, ["custbody_booth", "anyof", [boothId]]
-							, "and"
-							, ["custbody_ng_cs_order_type", "anyof", [csLib.settings.DefaultExhibitorOrderType]]
-						];
-
-						log.audit({title: 'Is Multi Billing Parties enabled?...', details: '⌛'})
-						// Check for multi billing party to be allowed from settings and use that for our filter in the search of sales orders
-						if (csLib.settings.AllowMultiBillingParties) {
-							log.audit({
-								title: 'Is Multi Billing Parties enabled! - Adding entity filter on billParty',
-								details: '✅'
-							})
-							filt.push(
-								"and"
-								, ["entity", "anyof", [billParty]]
-							);
-						} else {
-							log.audit({
-								title: 'Is Multi Billing Parties disabled! - Skipping entity filter on billParty',
-								details: '⤵'
-							})
-						}
-
-						let salesOrderResultCount
-
-						log.audit({title: 'Running sales order search...', details: '⌛ - 479'})
-
-						let searchObj = search.create({
-							type: search.Type.SALES_ORDER,
-							filters: filt,
-							columns: []
-						});
-						getAllResultsFor(searchObj, (result) => searchSalesOrdersResults.push(result)) // Not sure if this is needed hence no visible columns
-						salesOrderResultCount = searchObj.runPaged().count
-
-						log.audit({title: 'Search on sales order complete!', details: '✅'})
-
-
-						// in the event that someone manages to bypass all warnings in the UI indicating the existence of a sales order for the
-						// defined booth and show and block all attempts to redirect them to the existing order, an error will be thrown causing
-						// the BeforeSubmit event to fail, preventing the order from being saved to the system
-
-						log.audit({title: 'Sales orders found 🔎', details: salesOrderResultCount})
-						if (salesOrderResultCount !== 0) {
-							require(['N/error'], function (error) {
-								throw error.create({
-									name: "FLAGRANT SYSTEM ERROR",
-									message: "You cannot save this order as one already exists for this booth.",
-									notifyOff: "false"
-								});
-							});
-						}
-					}
-				}
-
-			}
+			return true;
 		}
 
 		/**
@@ -2767,22 +1720,14 @@
 									});
 
 									let cCard = {
-										exp: soRecWeb.getValue({fieldId: "ccexpiredate"})
-										,
-										name: soRecWeb.getValue({fieldId: "ccname"})
-										,
-										number: soRecWeb.getValue({fieldId: "ccnumber"})
-										,
-										code: soRecWeb.getValue({fieldId: "ccsecuritycode"})
-										,
-										adr: soRecWeb.getValue({fieldId: "ccstreet"})
-										,
-										zip: soRecWeb.getValue({fieldId: "cczipcode"})
-										,
-										method: soRecWeb.getValue({fieldId: "paymentmethod"})
-										,
-										id: soRecWeb.getValue({fieldId: "creditcard"})
-										,
+										exp: soRecWeb.getValue({fieldId: "ccexpiredate"}),
+										name: soRecWeb.getValue({fieldId: "ccname"}),
+										number: soRecWeb.getValue({fieldId: "ccnumber"}),
+										code: soRecWeb.getValue({fieldId: "ccsecuritycode"}),
+										adr: soRecWeb.getValue({fieldId: "ccstreet"}),
+										zip: soRecWeb.getValue({fieldId: "cczipcode"}),
+										method: soRecWeb.getValue({fieldId: "paymentmethod"}),
+										id: soRecWeb.getValue({fieldId: "creditcard"}),
 										encNumber: soRecWeb.getValue({fieldId: "custbody_ng_paytrace_web_enc_cc_data"})
 									}
 
@@ -3140,187 +2085,1228 @@
 
 		}
 
-		function setUpExhibitorFormItemsDescriptions(currentSalesOrder, context) {
-		// SET UP EXHIBITOR FORM ITEMS START
-		let lineNumber = 0
+		const setUpExhibitorFormItems = (context, currentSalesOrder) => {
+			// SET UP EXHIBITOR FORM ITEMS START
+			let lineNumber = 0;
+			log.audit({
+				title: 'EF - Exhibitor Form setting item values (create)...',
+				details: `⌛ - In context: "${context.type}"`
+			})
+	
+			log.debug({title: 'EF - Get item count...', details: `⌛`})
+	
+			let lines = currentSalesOrder.getLineCount({sublistId: "item"});
+	
+			lines && log.audit({title: 'EF - Get item count gathered! ✅', details: `🌟 - ${lines}`})
+	
+			for (lineNumber = 0; lineNumber < lines; lineNumber++) {
+				log.debug({title: 'EF - Looping item line 🤏', details: `Item: ${lineNumber}`})
+	
+				log.audit({
+					title: 'EF - Setting Current Line Code 🔢',
+					details: `⌛ - In context: "${context.type}"`
+				})
+	
+				let currLineCode = currentSalesOrder.getSublistValue({
+					sublistId: "item",
+					fieldId: "custcol_linecode",
+					line: lineNumber
+				});
+	
+				if (currLineCode) log.audit({
+					title: 'EF - Current Line Code Set! ✅',
+					details: `Code: ${currLineCode}`
+				})
+				else log.audit({
+					title: 'EF - Current Line Code Not Set❗',
+					details: `Invalid Code: ${currLineCode}`
+				})
+	
+				log.audit({
+					title: 'EF - Setting Current Item Type...',
+					details: `⌛ - In context: "${context.type}"`
+				})
+	
+				let currLineItemType = currentSalesOrder.getSublistValue({
+					sublistId: "item",
+					fieldId: "itemtype",
+					line: lineNumber
+				});
+	
+				if (currLineItemType) log.audit({
+					title: 'Current Item Type Set! ✅',
+					details: `Code: ${currLineItemType}`
+				})
+				else log.audit({
+					title: 'Current Item Type Not Set❗',
+					details: `❌ Invalid Type: ${currLineItemType}`
+				})
+	
+				log.audit({title: 'Checking EndGroup and line code def...', details: '⌛'})
+				if (typeof currLineCode === 'string' && currLineCode && currLineItemType !== "EndGroup") {
+					log.audit({
+						title: 'EF - Setting random string value for line code...',
+						details: '⌛'
+					})
+					currentSalesOrder.setSublistValue({
+						sublistId: "item",
+						fieldId: "custcol_linecode",
+						value: NG.tools.randomString(6, 6) + currentSalesOrder.getSublistValue({
+							sublistId: "item",
+							fieldId: "item",
+							line: lineNumber
+						}) + lineNumber.toString(),
+						line: lineNumber
+					});
+	
+					log.audit({title: 'Getting item description...', details: '⌛'})
+	
+					let currLineDesc = currentSalesOrder.getSublistValue({
+						sublistId: "item",
+						fieldId: "description",
+						line: lineNumber
+					});
+	
+					if (currLineDesc) log.audit({
+						title: 'EF - Item description valid! ✅',
+						details: `"${currLineDesc}"`
+					})
+					else log.audit({
+						title: 'EF - Item description invalid! ❌',
+						details: currLineDesc
+					})
+	
+					log.audit({title: 'EF - Setting description on item...', details: `⌛`})
+	
+					let item_desc_marked = currentSalesOrder.setSublistValue({
+						sublistId: "item",
+						fieldId: "custcol_description",
+						value: currLineDesc,
+						line: lineNumber
+					});
+	
+					if (item_desc_marked) log.audit({
+						title: 'EF - Set description on item! ✅',
+						details: `Desc: ${item_desc_marked}`
+					})
+					else log.audit({
+						title: 'EF - Set description on item failed! ❌',
+						details: `Desc: ${item_desc_marked}`
+					})
+	
+				}
+			}
+			// SET UP EXHIBITOR FORM ITEMS END
+	
+			// SET UP EXHIBITOR FORM ITEMS START
+			lineNumber = 0
+	
+			log.audit({
+				title: 'EF - Exhibitor Form setting item desc (create)...',
+				details: `⌛ - In context: "${context.type}"`
+			})
+	
+			log.debug({title: 'EF - Get item count...', details: `⌛`})
+	
+			lines = currentSalesOrder.getLineCount({sublistId: "item"});
+	
+			lines && log.audit({title: 'EF - Get item count gathered! ✅', details: `🌟 - ${lines}`})
+	
+			// Set custcol_description for booth order printing form
+			if (lines > 0) {
+				for (lineNumber = 0; lineNumber < lines; lineNumber++) {
+					log.debug({title: 'EF - Looping item line descriptions 🤏', details: `Item: ${lineNumber}`})
+	
+					log.audit({
+						title: 'EF - Setting Current Line Code 🔢',
+						details: `⌛ - In context: "${context.type}"`
+					})
+	
+					let currLineCode = currentSalesOrder.getSublistValue({
+						sublistId: "item",
+						fieldId: "custcol_linecode",
+						line: lineNumber
+					});
+	
+					if (currLineCode) log.audit({
+						title: 'EF - Current Line Code Set! ✅',
+						details: `Code: ${currLineCode}`
+					})
+					else log.audit({
+						title: 'EF - Current Line Code Not Set ❗',
+						details: `Invalid Code: ${currLineCode}`
+					})
+	
+					log.audit({
+						title: 'EF - Setting Current Item Type...',
+						details: `⌛ - In context: "${context.type}"`
+					})
+	
+					let currLineItemType = currentSalesOrder.getSublistValue({
+						sublistId: "item",
+						fieldId: "itemtype",
+						line: lineNumber
+					});
+	
+					if (currLineItemType) log.audit({
+						title: 'EF - Current Item Type Set! ✅',
+						details: `Code: ${currLineItemType}`
+					})
+					else log.audit({
+						title: 'EF - Current Item Type Not Set❗',
+						details: `❌ Invalid Type: ${currLineItemType}`
+					})
+	
+					log.audit({title: 'EF - Getting item description...', details: '⌛'})
+	
+					let currLineName = currentSalesOrder.getSublistValue({
+						sublistId: "item",
+						fieldId: "item_display",
+						line: lineNumber
+					});
+	
+					let currLineDesc = currentSalesOrder.getSublistValue({
+						sublistId: "item",
+						fieldId: "description",
+						line: lineNumber
+					});
+	
+					if (currLineDesc) log.audit({
+						title: `EF - Item description valid! ✅ - "${currLineName}"`,
+						details: `"${currLineDesc}"`
+					})
+					else log.audit({
+						title: `EF - Item description invalid! ❌ - "${currLineName}"`,
+						details: currLineDesc
+					})
+	
+					log.audit({title: 'EF - Setting description on item...', details: `⌛`})
+	
+					let item_desc_marked = currentSalesOrder.setSublistValue({
+						sublistId: "item",
+						fieldId: "custcol_description",
+						value: currLineDesc,
+						line: lineNumber
+					});
+	
+					if (item_desc_marked) log.audit({
+						title: 'EF - Set description on item! ✅',
+						details: `Desc: ${item_desc_marked}`
+					})
+					else log.audit({
+						title: 'EF - Set description on item failed! ❌',
+						details: `Desc: ${item_desc_marked}`
+					})
+	
+	
+				}
+			}
+	
+			// SET UP EXHIBITOR FORM ITEMS END
+		}
+	
+		const runShippingAddressUpdate = (context, eventId) => {
+			// Check event data and detect if order is meant for web processing
+			// If its not set and format shipping address fields
+			// If CS Event changes then set shipping
+			let currentSalesOrder = context.newRecord
+			let eventData = {}
+			// Set event Id upon init
+			if (eventId && !isNaN(Number(eventId))) {
+				eventData = getEventData(eventId);
+			}
+	
+			let webOrdProc = Boolean(runtime.executionContext === runtime.ContextType.SCHEDULED) && currentSalesOrder.getValue({fieldId: "custbody_isweborder"});
+	
+	
+			if (Object.keys(eventData).length !== 0 && !webOrdProc) {
+				let oldRec = context.oldRecord
+				if (!isNaN(Number(eventData.custrecord_facility))) {
+					let shipAddrValues = NG.tools.getLookupFields("customrecord_facility", eventData.custrecord_facility, ["custrecord_facility_address1", "custrecord_facility_address2",
+						"custrecord_facility_city", "custrecord_facility_state", "custrecord_facility_zip", "custrecord_facility_phone"
+					], ["custrecord_facility_state"], []);
+	
+					// Check for show site address to be set
+					if (shipAddrValues?.custrecord_facility_address1) {
+						let updShipAddr = false;
+	
+						try {
+							let shpAdr = currentSalesOrder.getSubrecord({fieldId: "shippingaddress"});
+							let shippingAddressName = shpAdr.getValue({fieldId: "addr1"})
+							let shippingAddressCity = shpAdr.getValue({fieldId: "city"})
+							let shippingAddressState = shpAdr.getValue({fieldId: "state"})
+	
+							log.audit({
+								title: "Shipping address subrecord is loaded for edit 🏠",
+								details: `Addr1: ${shippingAddressName}\n -- City: ${shippingAddressCity}\n -- State: ${shippingAddressState}\n`
+							});
+	
+							// Validate all shipping address fields are set up with values
+							if (shpAdr.getValue({fieldId: "addr1"}) !== shipAddrValues.custrecord_facility_address1 ||
+								shpAdr.getValue({fieldId: "addr2"}) !== shipAddrValues.custrecord_facility_address2 ||
+								shpAdr.getValue({fieldId: "city"}) !== shipAddrValues.custrecord_facility_city ||
+								shpAdr.getValue({fieldId: "state"}) !== shipAddrValues.custrecord_facility_state) {
+								updShipAddr = true;
+							}
+	
+						} catch (err) {
+							log.error({
+								title: "Failed to load/process shipping address subrecord",
+								details: `[${err.name}] : ${err.message}`
+							});
+							updShipAddr = true;
+						}
+	
+						// Check if shipping address is valid to set values on record. For Advanced Warehouse Address
+						log.audit({
+							title: "Is New Shipping Address Valid?",
+							details: updShipAddr ? "✅" : "❌"
+						});
+	
+						if (updShipAddr) {
+	
+							log.audit({
+								title: "Updating shipping address...",
+								details: "'shipaddresslist' value: {0}".NG_Format(currentSalesOrder.getValue({fieldId: "shipaddresslist"}))
+							});
+	
+							currentSalesOrder.setValue({fieldId: "shipaddresslist", value: ""});
+							currentSalesOrder.removeSubrecord({fieldId: "shippingaddress"});
+	
+							let shipAddy = currentSalesOrder.getSubrecord({fieldId: "shippingaddress"});
+							shipAddy.setValue({fieldId: "country", value: "US"});
+							shipAddy.setValue({
+								fieldId: "addr1",
+								value: shipAddrValues.custrecord_facility_address1
+							});
+							shipAddy.setValue({
+								fieldId: "addr2",
+								value: shipAddrValues.custrecord_facility_address2
+							});
+							shipAddy.setValue({
+								fieldId: "addrphone",
+								value: shipAddrValues.custrecord_facility_phone
+							});
+							shipAddy.setValue({
+								fieldId: "city",
+								value: shipAddrValues.custrecord_facility_city
+							});
+							shipAddy.setValue({
+								fieldId: "state",
+								value: shipAddrValues.custrecord_facility_state
+							});
+							shipAddy.setValue({
+								fieldId: "zip",
+								value: shipAddrValues.custrecord_facility_zip
+							});
+	
+						}
+	
+						log.audit({
+							title: "tax item id (BeforeSubmit) (post address change)",
+							details: currentSalesOrder.getValue({fieldId: "taxitem"})
+						});
+					}
+				}
+			}
+			log.audit({ title: 'Update shipping completed! ✅', details: ''})
+		}
+	
+		const setIsWebOrder = (currentSalesOrder) => {
+			// WIll only be applied to REST & WEBSTORE on create
+			currentSalesOrder.setValue({fieldId: "custbody_isweborder", value: true});
+		}
+	
+		const runWebOrderOperations = async (context, eventId, boothId, billParty, cCard) => {
+			log.audit({ title: '⚡ Running Web order operations', details: ''})
+			let processingStatus = 'Staging'
+			//  TODO; ReplyJson not returning
+			await runWebOrderCreateProcessing(context, eventId, boothId, billParty, cCard)
+				.then(r => {
+					log.audit({ title: '✔ Order Processing finished', details: `"${r}"` })
+					log.audit({ title: '✔ Order Processing response', details: r })
+					processingStatus = r
+				})
+				.catch((err) => {
+					log.error({ title: '❌ Error occurred running web processing!', details: err })
+					processingStatus = err
+				})
+			return processingStatus
+		}
+
+		const runWebOrderCreateProcessing = async (context, eventId, boothId, billParty, cCard) => {
+		// Applied to CREATE of REST & WEBSTORE exec contexts only!
+		// Create auth item on test order to pre-authorize card
+		// Then if authorization passes status then create actual order with the exhibitor's items
+		// TODO; Figure out how to get ccErr and authErr accessable from main beforeSubmit method
+		let currentSalesOrder = context.newRecord
+		try {
+			log.audit({ title: 'RWOP - ⚡ Running Web order processing BEFORESUB!', details: ''})
+			let authLine = currentSalesOrder.findSublistLineWithValue({
+				sublistId: "item",
+				fieldId: "item",
+				value: csLib.settings.AuthItem
+			});
+
+			log.debug({ title: 'RWOP - Current Auth line item...', details: authLine })
+
+			if (authLine > 0) {
+				log.audit({title: "RWOP - Authorization order; Terminating script functions", details: ""});
+				return;
+			}
+
+			log.audit({
+				title: `RWOP - ✨ Running switch case as ${runtime.executionContext}`,
+				details: 'WebProcessing - beforeSubmit()'
+			})
+
+			let eventData = {}
+			let processingStatus = 'Running Checks'
+			// Set event Id upon init
+			if (eventId && !isNaN(Number(eventId)) && Object.keys(eventData).length === 0) {
+				eventData = getEventData(eventId);
+			}
+
+			let lineNumber = 0;
+			let rplyJSON = null
+			switch (runtime.executionContext) {
+				case "RESTLET":
+					/*
+					* Set isweborder to true to accomplish the following:
+					* - Only on CREATE of the new sales order will this code block run
+					* 1. Grab all the event information that has been set on the sales order to then be used later
+					* 2. Check tax rate assigned to event if it has been set (expecting true) while tax on line item lines is true along with canadian tax is true
+					* 3. Iterate through each item that is not the convienence fee item set the tax on that item from the event
+					*    and if candian tax it not equipped set the taxrate1 field on the item with the float value removing the "%" to make it a decimal number instead.
+					*    Else if there is canadian tax use the custom tax fields to apply that tax to each item.
+					* 4. Then tax group gets set from event when group is defined and rate is defined
+					*
+					* */
+					processingStatus = 'Running Sales Order Record Value Sets'
+					log.audit({title: 'RWOP - Case RESTLET running...', details: '⌛'})
+					eventId = currentSalesOrder.getValue({fieldId: "custbody_show_table"});
+					boothId = currentSalesOrder.getValue({fieldId: "custbody_booth"});
+					billParty = currentSalesOrder.getValue({fieldId: "entity"});
+					let orderTax = currentSalesOrder.getValue({fieldId: "taxitem"});
+
+					// TODO: Rewrite to include better logic in handling tax
+					// Both settings fields reside on Canadian users
+					if (eventData.custrecord_tax_rate && (csLib.settings.SalesTaxOnItemLines || csLib.settings.UseCanadianSalesTax)) {
+						// Set each items tax code when iterating on items in the order.
+						let itemLineCount = currentSalesOrder.getLineCount({sublistId: "item"});
+						for (lineNumber = 0; lineNumber < itemLineCount; lineNumber++) {
+							let currentItem = currentSalesOrder.getSublistValue({
+								sublistId: "item",
+								fieldId: "item",
+								line: lineNumber
+							})
+
+							if (currentItem !== csLib.settings.ConvenienceFeeItem) {
+								currentSalesOrder.setSublistValue({
+									sublistId: "item",
+									fieldId: "taxcode",
+									value: eventData.custrecord_tax_rate,
+									line: lineNumber
+								});
+								// Check if settings holds NON Canadian Tax to then set from the CS Event
+								if (!csLib.settings.UseCanadianSalesTax) {
+									currentSalesOrder.setSublistValue({
+										sublistId: "item",
+										fieldId: "taxrate1",
+										value: parseFloat(Number(eventData.custrecord_tax_percent) / 100),
+										line: lineNumber
+									});
+
+								} else {
+									currentSalesOrder.setSublistValue({
+										sublistId: "item",
+										fieldId: "taxrate1",
+										value: eventData.custrecord_ng_cs_evt_gst_pct.replace("%", ""),
+										line: lineNumber
+									});
+									currentSalesOrder.setSublistValue({
+										sublistId: "item",
+										fieldId: "taxrate2",
+										value: eventData.custrecord_ng_cs_evt_pst_pct.replace("%", ""),
+										line: lineNumber
+									});
+								}
+							} else {
+								currentSalesOrder.setSublistValue({
+									sublistId: "item",
+									fieldId: "taxcode",
+									value: csLib.settings.ConvFeeTax,
+									line: lineNumber
+								});
+								if (!csLib.settings.UseCanadianSalesTax) {
+									currentSalesOrder.setSublistValue({
+										sublistId: "item",
+										fieldId: "taxrate1",
+										value: "0",
+										line: lineNumber
+									});
+								} else {
+									currentSalesOrder.setSublistValue({
+										sublistId: "item",
+										fieldId: "taxrate1",
+										value: "0",
+										line: lineNumber
+									});
+									currentSalesOrder.setSublistValue({
+										sublistId: "item",
+										fieldId: "taxrate2",
+										value: "0",
+										line: lineNumber
+									});
+								}
+							}
+						}
+					}
+					// This will apply to US users to apply tax on the order instead.
+					else if (!isNaN(Number(eventData.custrecord_tax_rate)) && !isNaN(Number(orderTax)) && Number(eventData.custrecord_tax_rate) !== Number(orderTax)) {
+						currentSalesOrder.setValue({
+							fieldId: "taxitem",
+							value: eventData.custrecord_tax_rate
+						});
+					}
+
+					let payRes, authRes;
+					try {
+						// If paytrace is enabled run paytrace pre step logic in order to process transaction
+						// Else run the webstore processing method fit for non-paytrace payments.
+						log.audit({title: 'RWOP - Checking if paytrace is enabled...', details: '⌛'})
+						processingStatus = 'Running Web Processing'
+						if (csLib.settings.EnablePayTrace) {
+							processingStatus = 'Running Paytrace Processing'
+							log.audit({
+								title: 'RWOP - Paytrace is enabled! - Running Paytrace beforeSubmit processes.',
+								details: '⤵'
+							})
+							if (csLib.settings.AutoChargeWebOrders) {
+								log.audit({
+									title: 'RWOP - AuthCharge is enabled! - Running Paytrace beforeSubmit processing steps.',
+									details: '⌛'
+								})
+
+								log.audit({title: "RWOP - CS UE Sales Order - BS - cCard", details: cCard});
+
+								let totalData = calcTotals(currentSalesOrder, eventData);
+
+								let updData = {
+									show: eventId
+									, booth: boothId
+									, billParty: billParty
+									, totalData: totalData
+									, eventData: eventData
+									, order: currentSalesOrder
+								};
+
+								let updTotalData = totalData;
+
+								log.audit({
+									title: "RWOP - CS UE Sales Order - BS - totalData",
+									details: totalData
+								});
+
+								log.audit({
+									title: "RWOP - CS UE Sales Order - BS - updTotalData",
+									details: updTotalData
+								});
+
+								try {
+									let boothData = NG.tools.getLookupFields("customrecord_show_booths", boothId, ["name", "custrecord_booth_show_table", "custrecord_booth_exhibitor"], ["custrecord_booth_show_table", "custrecord_booth_exhibitor"], [], true);
+									log.audit({
+										title: "RWOP - CS UE Sales Order - BS - boothData",
+										details: boothData
+									});
+
+									let orderData = {
+										custbody_ng_cs_order_type: currentSalesOrder.getValue({fieldId: "custbody_ng_cs_order_type"})
+										, exhibitor: billParty
+										, exhibitorName: boothData.custrecord_booth_exhibitor_text
+										, eventName: boothData.custrecord_booth_show_table_text
+										, boothName: boothData.name
+									};
+
+									log.audit({
+										title: "RWOP - CS UE Sales Order - BS - orderData",
+										details: orderData
+									});
+
+									payRes = https.post({
+										url: paySuiteletUrl,
+										body: {
+											soid: null,
+											total: updTotalData.total,
+											totalInit: updTotalData.totalInit,
+											tax: updTotalData.tax,
+											taxInit: updTotalData.taxInit,
+											card: JSON.stringify(cCard),
+											ordData: JSON.stringify(orderData),
+											getAuth: "T",
+											subtNickel: "F"
+										}
+									})
+									/*.then((payRes) => {
+										log.audit({
+											title: '✅ Return from POST (payRes):',
+											details: payRes
+										})
+										if (payRes && payRes.code === 200 && payRes?.body) {
+											rplyJSON = JSON.parse(payRes.body);
+											if (rplyJSON?.status && rplyJSON?.status) {
+												let status = rplyJSON.status
+												if (status !== 'SUCCESS') {
+													throw {
+														name: "CREDIT_CARD_ERROR",
+														message: rplyJSON.message,
+														options: {
+															code: payRes.code,
+															lineNumber: 854,
+															cause: rplyJSON
+														}
+													}
+												}
+											}
+										}
+									})
+									.catch((err) => {
+										log.error({
+											title: '❌ Error Authing Paytrace Post:',
+											details: err
+										})
+										ccErr = true;
+
+										authErr = error.create({
+											name: "CREDIT_CARD_ERROR",
+											message: err?.message || err,
+											notifyOff: true
+										})
+
+										throw authErr
+
+									})*/
+
+
+									log.audit({
+										title: "RWOP - CS UE Sales Order - BS - payRes",
+										details: payRes
+									});
+
+
+									if (payRes?.body) {
+										log.audit({
+											title: 'RWOP - ✅ Return from POST (payRes):',
+											details: payRes?.body
+										})
+										rplyJSON = JSON.parse(payRes?.body);
+									}
+
+								} catch (err) {
+									// Going to leave this just in case anything else fails prior to the POST request.
+									ccErr = true;
+									NG.log.logError(err, "RWOP - Error encountered authing web order for full amount");
+								}
+
+								if (rplyJSON?.status && rplyJSON.status !== "OK") {
+									// require(['N/error'], function(error) {
+									log.audit({
+										title: "RWOP - CS UE Sales Order - BS - Throwing card auth error (A)",
+										details: ""
+									});
+									throw error.create({
+										name: 'CREDIT_CARD_ERROR',
+										message: rplyJSON.message,
+										notifyOff: true
+									});
+									// authErr = new Error(rplyJSON.name, { cause : rplyJSON.message });
+									// log.audit({ title : "authErr (A)" , details : authErr });
+									// });
+								} else if (rplyJSON) {
+									let authSessionId = "_CS_WEB_ORDER_{0}_{1}_{2}_".NG_Format(eventId, boothId, billParty);
+									runtime.getCurrentSession().set({
+										name: authSessionId,
+										value: encode.convert({
+											string: JSON.stringify(rplyJSON),
+											inputEncoding: encode.Encoding.UTF_8,
+											outputEncoding: encode.Encoding.BASE_64
+										})
+									});
+									log.audit({
+										title: "RWOP - CS UE Sales Order - BS - Session Data Set",
+										details: ""
+									});
+								} else {
+									// require(['N/error'], function(error) {
+									log.audit({
+										title: "RWOP - CS UE Sales Order - BS - Throwing card auth error (B)",
+										details: ""
+									});
+									throw error.create({
+										name: "NG_CS_ERROR",
+										message: "Unable to authorize credit card",
+										notifyOff: true
+									});
+
+									// authErr = new Error("NG_CS_ERROR", { cause : "Unable to authorize credit card" });
+									// log.audit({ title : "authErr (B)" , details : authErr });
+									// });
+								}
+							} else if (!csLib.settings.AutoChargeWebOrders && !NG.tools.isEmpty(csLib.settings.AuthItem)) {
+								//
+							}
+						} else {
+							// Run order process as webstore process
+							processingStatus = 'Running Non PT Processing'
+							log.audit({title: 'RWOP - Running non paytrace logic beforeSub...', details: ''})
+
+							// Run authorization for order
+							log.audit({title: 'RWOP - Running non PT Authorization...', details: '⌛'})
+							if (authLine) {
+								log.audit({title: 'RWOP - Auth line exists!', details: '✅'})
+								log.audit({title: 'RWOP - Running Auth POST...', details: '⌛'})
+								log.debug({title: 'RWOP - Running Auth POST to body...', details: {
+										body: {
+											cstId: billParty,
+											card: JSON.stringify(cCard)
+										}
+									}
+								})
+
+								try {
+									authRes = https.post({
+										url: authSuiteletUrl,
+										body: {
+											cstId: billParty,
+											card: JSON.stringify(cCard)
+										}
+									})
+									log.audit({title: 'RWOP - POST finished...', details: authRes})
+									if (authRes?.body && authRes.code === 200) {
+										let status = JSON.parse(authRes.body)?.status
+										if (status === 'SUCCESS') {
+											log.audit({
+												title: 'RWOP - POST finished successfully! ✅',
+												details: status
+											})
+										} else {
+											// Want to place a THROW here to halt order
+											log.error({
+												title: 'RWOP - ❌ Error occurred when authing card:',
+												details: authRes
+											})
+											throw {
+												name: "CREDIT_CARD_ERROR",
+												message: authRes.details,
+												options: {
+													code: authRes.code,
+													cause: authRes
+												}
+											}
+										}
+									}
+								} catch (err) {
+									processingStatus = 'Error authing card'
+									log.error({
+										title: 'RWOP - ❌ Internal error occurred when authing card:',
+										details: err
+									})
+									throw error.create({
+										name: "CREDIT_CARD_ERROR",
+										message: 'Internal error occurred when authorizing card.'
+									})
+								}
+							}
+
+							if (isNaN(Number(eventId)) || isNaN(Number(boothId))) {
+								// Check event id and booth id for valid #s
+								if (isNaN(Number(eventId))) NG.log.logError(null, "WEB ORDER (non PT) -- NO SHOW!!!!!");
+								if (isNaN(Number(boothId))) NG.log.logError(null, "WEB ORDER (non PT) -- NO BOOTH!!!!!");
+							}
+
+							log.audit({title: 'RWOP - Setting default sales order form...', details: '⚡'})
+							currentSalesOrder.setValue({
+								fieldId: "customform",
+								value: csLib.settings.DefaultBoothOrderForm
+							});
+
+							let formIdSet = currentSalesOrder.getValue({fieldId: "customform"});
+
+							log.audit({title: 'RWOP - Sales Order form set!', details: `🧾 - ${formIdSet}`})
+
+							/*
+							* TODO: Why do we need to separate if statements to validate event and booth ids when beginning block captures it? - AKA: LINE: 513 🤷‍
+							* */
+
+							log.debug({
+								title: 'RWOP - 🔎 Checking booth id and gonna set actual exhibitor',
+								details: boothId
+							})
+							processingStatus = 'Finalizing Event & Booth Set Up'
+							if (boothId && !isNaN(Number(boothId))) {
+								processingStatus = 'Setting booth on order'
+								let boothLookup = search.lookupFields({
+									type: "customrecord_show_booths",
+									id: boothId,
+									columns: ['custrecord_booth_actual_exhibitor']
+								})
+								log.debug({title: 'RWOP - 🔎 Booth lookup results...', details: boothLookup})
+								currentSalesOrder.setValue({
+									fieldId: "custbody_booth_actual_exhibitor",
+									value: boothLookup?.custrecord_booth_actual_exhibitor[0].value
+								});
+								log.debug({
+									title: 'RWOP - 🌟 Set Booth exhibitor as:',
+									details: boothLookup?.custrecord_booth_actual_exhibitor[0].value
+								})
+							}
+
+							log.debug({
+								title: 'RWOP - 🔎 Checking event ID and setting job & class...',
+								details: `Event: "${eventId}"`
+							})
+							if (eventId && !isNaN(Number(eventId))) {
+								log.debug({title: 'RWOP - Is event data existent?', details: eventData})
+								processingStatus = 'Set Booth - Setting event'
+								if (Object.keys(eventData).length === 0) {
+									eventData = getEventData(eventId);
+								}
+								// Check if CS JOB is enabled
+								if (csLib.settings.UseCustomJob) {
+									log.debug({
+										title: 'RWOP - 🔗 Set CSJOB & class from event data:',
+										details: `Job: "${eventData?.custrecord_show_job}" - Class: "${eventData?.custrecord_fin_show}"`
+									})
+									// Don't have to set class but if is there mines well set it on the order
+									if (eventData?.custrecord_fin_show) {
+										currentSalesOrder.setValue({
+											fieldId: "class",
+											value: eventData.custrecord_fin_show
+										});
+									}
+
+									currentSalesOrder.setValue({
+										fieldId: "custbody_cseg_ng_cs_job",
+										value: eventData.custrecord_show_job
+									});
+									log.audit({
+										title: "RWOP - Web Order Job/Class",
+										details: "Job ID: {0} - Class ID: {1}".NG_Format(currentSalesOrder.getValue({fieldId: "custbody_cseg_ng_cs_job"}), currentSalesOrder.getValue({fieldId: "class"}))
+									});
+								} else {
+									currentSalesOrder.setValue({
+										fieldId: "class",
+										value: eventData.custrecord_fin_show
+									});
+									log.audit({
+										title: "RWOP - Web Order Job",
+										details: "Job ID: {0}".NG_Format(currentSalesOrder.getValue({fieldId: "class"}))
+									});
+								}
+								processingStatus = 'Event Set Moving to Order type'
+							}
+
+							// Library automatically returns NULL if the select field isn't valid removed .isEmpty function
+							// TODO: Will update to deprecate lib for settings and use SuiteQL to grab everything we need instead
+
+							currentSalesOrder.setValue({
+								fieldId: "custbody_ng_cs_order_type",
+								value: csLib.settings.DefaultExhibitorOrderType
+							});
+
+							processingStatus = 'Order type set'
+							log.audit({
+								title: "RWOP - Web Order Type Set!",
+								details: `Order Type ID: ${currentSalesOrder.getValue({fieldId: "custbody_ng_cs_order_type"})}`
+							});
+
+							currentSalesOrder.setValue({
+								fieldId: "customform",
+								value: csLib.settings.DefaultBoothOrderForm
+							});
+							processingStatus = 'Order form set'
+						}
+						processingStatus = 'Processing Finished'
+					} catch (err) {
+						log.audit({title: "RWOP - try-catch-err", details: "try-catch-err"});
+						log.audit({title: "RWOP - err", details: {name: err.name, message: err.message}});
+						NG.log.logError(err, "RWOP - ❗ General error encountered during CS online order credit card authorization processing!");
+
+						throw err
+					}
+					break;
+				case "WEBSTORE":
+					// Checking context of Webstore order (sitebuilder)
+					log.audit({title: 'RWOP - Running webstore logic beforeSub...', details: ''})
+					processingStatus = 'Processing Webstore Order'
+					if (isNaN(Number(eventId)) || isNaN(Number(boothId))) {
+						// Check event id and booth id for valid #s
+						if (isNaN(Number(eventId))) NG.log.logError(null, "WEB ORDER -- NO SHOW!!!!!");
+						if (isNaN(Number(boothId))) NG.log.logError(null, "WEB ORDER -- NO BOOTH!!!!!");
+					}
+					log.audit({title: 'RWOP - Setting default sales order form...', details: '⚡'})
+					currentSalesOrder.setValue({
+						fieldId: "customform",
+						value: csLib.settings.DefaultBoothOrderForm
+					});
+
+					let formIdSet = currentSalesOrder.getValue({fieldId: "customform"});
+
+					log.audit({title: 'RWOP - Sales Order form set!', details: `🧾 - ${formIdSet}`})
+
+					/*
+					* TODO: Why do we need to separate if statements to validate event and booth ids when beginning block captures it? - AKA: LINE: 513 🤷‍
+					* */
+
+					if (!isNaN(Number(boothId))) {
+						currentSalesOrder.setValue({
+							fieldId: "custbody_booth_actual_exhibitor",
+							value: NG.tools.getLookupFields("customrecord_show_booths", boothId, ["custrecord_booth_actual_exhibitor"], ["custrecord_booth_actual_exhibitor"], []).custrecord_booth_actual_exhibitor
+						});
+					}
+
+					if (!isNaN(Number(eventId))) {
+						if (Object.keys(eventData).length === 0) {
+							eventData = getEventData(eventId);
+						}
+						// Check if CS JOB is enabled
+						if (csLib.settings.UseCustomJob) {
+							currentSalesOrder.setValue({
+								fieldId: "class",
+								value: eventData.custrecord_fin_show
+							});
+							currentSalesOrder.setValue({
+								fieldId: "custbody_cseg_ng_cs_job",
+								value: eventData.custrecord_show_job
+							});
+							log.audit({
+								title: "RWOP - Web Order Job/Class",
+								details: "Job ID: ${0} - Class ID: ${1}".NG_Format(currentSalesOrder.getValue({fieldId: "custbody_cseg_ng_cs_job"}), currentSalesOrder.getValue({fieldId: "class"}))
+							});
+						} else {
+							currentSalesOrder.setValue({
+								fieldId: "class",
+								value: eventData.custrecord_fin_show
+							});
+							log.audit({
+								title: "RWOP - Web Order Job",
+								details: "Job ID: {0}".NG_Format(currentSalesOrder.getValue({fieldId: "class"}))
+							});
+						}
+					}
+
+					// Library automatically returns NULL if the select field isn't valid removed .isEmpty function
+					// TODO: Will update to deprecate lib for settings and use SuiteQL to grab everything we need instead
+					let departmentId = currentSalesOrder.getValue({fieldId: "department"})
+					if (NG.NSFeatures.DEPARTMENTS() && csLib.settings.DefaultExhibitorDepartment) {
+						currentSalesOrder.setValue({
+							fieldId: "department",
+							value: csLib.settings.DefaultExhibitorDepartment
+						});
+						log.audit({
+							title: "RWOP - Web Order Department",
+							details: `Department ID: ${departmentId} [👷‍♂️]`
+						});
+					}
+					log.audit({
+						title: "RWOP - Setting Web Order Type: cs_order_type",
+						details: `Department ID: ${departmentId} [👷‍♂️]`
+					});
+
+					currentSalesOrder.setValue({
+						fieldId: "custbody_ng_cs_order_type",
+						value: csLib.settings.DefaultExhibitorOrderType
+					});
+					log.audit({
+						title: "RWOP - Web Order Type Set!",
+						details: `Order Type ID: ${currentSalesOrder.getValue({fieldId: "custbody_ng_cs_order_type"})}`
+					});
+
+					currentSalesOrder.setValue({
+						fieldId: "customform",
+						value: csLib.settings.DefaultBoothOrderForm
+					});
+
+					break;
+				default: {
+					log.audit({title: 'No execution context was ran for web order', details: ''})
+				}
+			}
+
+			currentSalesOrder.setValue({fieldId: "getauth", value: false});
+			currentSalesOrder.setValue({fieldId: "tobeemailed", value: false});
+
+			return processingStatus
+
+		} catch (err) {
+			log.error({ title: 'RWOP - Internal Error running processing!', details: err })
+			throw err
+		}
+	}
+	
+		const runUserOrderCreateProcessing = (context, eventId ,billParty, boothId) => {
+			// APPLY to USERINTERFACE exec context of CREATE only!
+			// Check if orders are to be consolidated into 1 single order
+			if (runtime.executionContext === runtime.ContextType.USER_INTERFACE && csLib.settings.PreventAdditionalOrders) {
+				// Make sure the event id and booth ids are present
+				if (!isNaN(Number(eventId)) && !isNaN(Number(boothId))) {
+					let filt = [
+						["custbody_show_table", "anyof", [eventId]]
+						, "and"
+						, ["custbody_booth", "anyof", [boothId]]
+						, "and"
+						, ["custbody_ng_cs_order_type", "anyof", [csLib.settings.DefaultExhibitorOrderType]]
+					];
+	
+					log.audit({title: 'Is Multi Billing Parties enabled?...', details: '⌛'})
+					// Check for multi billing party to be allowed from settings and use that for our filter in the search of sales orders
+					if (csLib.settings.AllowMultiBillingParties) {
+						log.audit({
+							title: 'Is Multi Billing Parties enabled! - Adding entity filter on billParty',
+							details: '✅'
+						})
+						filt.push(
+							"and"
+							, ["entity", "anyof", [billParty]]
+						);
+					} else {
+						log.audit({
+							title: 'Is Multi Billing Parties disabled! - Skipping entity filter on billParty',
+							details: '⤵'
+						})
+					}
+	
+					let salesOrderResultCount
+	
+					log.audit({title: 'Running sales order search...', details: '⌛ - 479'})
+	
+					let searchObj = search.create({
+						type: search.Type.SALES_ORDER,
+						filters: filt,
+						columns: []
+					});
+
+					salesOrderResultCount = searchObj.runPaged().count
+	
+					log.audit({title: 'Search on sales order complete!', details: '✅'})
+	
+	
+					// in the event that someone manages to bypass all warnings in the UI indicating the existence of a sales order for the
+					// defined booth and show and block all attempts to redirect them to the existing order, an error will be thrown causing
+					// the BeforeSubmit event to fail, preventing the order from being saved to the system
+	
+					log.audit({title: 'Sales orders found 🔎', details: salesOrderResultCount})
+					if (salesOrderResultCount !== 0) {
+						require(['N/error'], function (error) {
+							throw error.create({
+								name: "FLAGRANT SYSTEM ERROR",
+								message: "You cannot save this order as one already exists for this booth.",
+								notifyOff: "false"
+							});
+						});
+					}
+				}
+			}
+	
+		}
+		
+		const setUpExhibitorFormItemsDescriptions = (currentSalesOrder, context) => {
+			try {
+				// SET UP EXHIBITOR FORM ITEMS START
+				let lineNumber = 0
 
-		let loadedSalesOrder = record.load({
-			type: record.Type.SALES_ORDER,
-			id: currentSalesOrder.id,
-		})
+				let loadedSalesOrder = record.load({
+					type: record.Type.SALES_ORDER,
+					id: currentSalesOrder.id,
+				})
 
-		log.audit({
-			title: 'Exhibitor Form setting item values (create)...',
-			details: `⌛ - In context: "${context.type}"`
-		})
+				log.audit({
+					title: 'Exhibitor Form setting item values (create)...',
+					details: `⌛ - In context: "${context.type}"`
+				})
 
-		log.debug({title: 'Get item count...', details: `⌛`})
+				log.debug({title: 'Get item count...', details: `⌛`})
 
-		let lines = currentSalesOrder.getLineCount({sublistId: "item"});
+				let lines = currentSalesOrder.getLineCount({sublistId: "item"});
 
-		lines && log.audit({title: 'Get item count gathered! ✅', details: `🌟 - ${lines}`})
+				lines && log.audit({title: 'Get item count gathered! ✅', details: `🌟 - ${lines}`})
 
-		// Set custcol_description for booth order printing form
-		if (lines > 0) {
-			for (lineNumber = 0; lineNumber < lines; lineNumber++) {
-				log.debug({title: 'Looping item line descriptions 🤏', details: `Item: ${lineNumber}`})
+				// Set custcol_description for booth order printing form
+				if (lines > 0) {
+					for (lineNumber = 0; lineNumber < lines; lineNumber++) {
+						log.debug({title: 'Looping item line descriptions 🤏', details: `Item: ${lineNumber}`})
 
-				log.audit({
-					title: 'Setting Current Line Code 🔢',
-					details: `⌛ - In context: "${context.type}"`
-				})
+						log.audit({
+							title: 'Setting Current Line Code 🔢',
+							details: `⌛ - In context: "${context.type}"`
+						})
 
-				let currLineCode = currentSalesOrder.getSublistValue({
-					sublistId: "item",
-					fieldId: "custcol_linecode",
-					line: lineNumber
-				});
+						let currLineCode = currentSalesOrder.getSublistValue({
+							sublistId: "item",
+							fieldId: "custcol_linecode",
+							line: lineNumber
+						});
 
-				if (currLineCode) log.audit({
-					title: 'Current Line Code Set! ✅',
-					details: `Code: ${currLineCode}`
-				})
-				else log.audit({
-					title: 'Current Line Code Not Set ❗',
-					details: `Invalid Code: ${currLineCode}`
-				})
+						if (currLineCode) log.audit({
+							title: 'Current Line Code Set! ✅',
+							details: `Code: ${currLineCode}`
+						})
+						else log.audit({
+							title: 'Current Line Code Not Set ❗',
+							details: `Invalid Code: ${currLineCode}`
+						})
 
-				log.audit({
-					title: 'Setting Current Item Type...',
-					details: `⌛ - In context: "${context.type}"`
-				})
+						log.audit({
+							title: 'Setting Current Item Type...',
+							details: `⌛ - In context: "${context.type}"`
+						})
 
-				let currLineItemType = currentSalesOrder.getSublistValue({
-					sublistId: "item",
-					fieldId: "itemtype",
-					line: lineNumber
-				});
+						let currLineItemType = currentSalesOrder.getSublistValue({
+							sublistId: "item",
+							fieldId: "itemtype",
+							line: lineNumber
+						});
 
-				if (currLineItemType) log.audit({
-					title: 'Current Item Type Set! ✅',
-					details: `Code: ${currLineItemType}`
-				})
-				else log.audit({
-					title: 'Current Item Type Not Set❗',
-					details: `❌ Invalid Type: ${currLineItemType}`
-				})
+						if (currLineItemType) log.audit({
+							title: 'Current Item Type Set! ✅',
+							details: `Code: ${currLineItemType}`
+						})
+						else log.audit({
+							title: 'Current Item Type Not Set❗',
+							details: `❌ Invalid Type: ${currLineItemType}`
+						})
 
-				log.audit({title: 'Getting item description...', details: '⌛'})
+						log.audit({title: 'Getting item description...', details: '⌛'})
 
-				let currLineName = currentSalesOrder.getSublistValue({
-					sublistId: "item",
-					fieldId: "item_display",
-					line: lineNumber
-				});
+						let currLineName = currentSalesOrder.getSublistValue({
+							sublistId: "item",
+							fieldId: "item_display",
+							line: lineNumber
+						});
 
-				let currLineDesc = currentSalesOrder.getSublistValue({
-					sublistId: "item",
-					fieldId: "description",
-					line: lineNumber
-				});
+						let currLineDesc = currentSalesOrder.getSublistValue({
+							sublistId: "item",
+							fieldId: "description",
+							line: lineNumber
+						});
 
-				if (currLineDesc) log.audit({
-					title: `Item description valid! ✅ - "${currLineName}"`,
-					details: `"${currLineDesc}"`
-				})
-				else log.audit({
-					title: `Item description invalid! ❌ - "${currLineName}"`,
-					details: currLineDesc
-				})
+						if (currLineDesc) log.audit({
+							title: `Item description valid! ✅ - "${currLineName}"`,
+							details: `"${currLineDesc}"`
+						})
+						else log.audit({
+							title: `Item description invalid! ❌ - "${currLineName}"`,
+							details: currLineDesc
+						})
 
-				log.audit({title: 'Setting description on item...', details: `⌛`})
+						log.audit({title: 'Setting description on item...', details: `⌛`})
 
-				let item_desc_marked = loadedSalesOrder.setSublistValue({
-					sublistId: "item",
-					fieldId: "custcol_description",
-					value: currLineDesc,
-					line: lineNumber
-				});
+						let item_desc_marked = loadedSalesOrder.setSublistValue({
+							sublistId: "item",
+							fieldId: "custcol_description",
+							value: currLineDesc,
+							line: lineNumber
+						});
 
-				if (item_desc_marked) log.audit({
-					title: 'Set description on item! ✅',
-					details: `Desc: ${item_desc_marked}`
-				})
-				else log.audit({
-					title: 'Set description on item failed! ❌',
-					details: `Desc: ${item_desc_marked}`
-				})
+						if (item_desc_marked) log.audit({
+							title: 'Set description on item! ✅',
+							details: `Desc: ${item_desc_marked}`
+						})
+						else log.audit({
+							title: 'Set description on item failed! ❌',
+							details: `Desc: ${item_desc_marked}`
+						})
 
 
-			}
+					}
 
-			loadedSalesOrder.save({
-				ignoreMandatoryFields: true,
-			})
-
-		}
-
-		// SET UP EXHIBITOR FORM ITEMS END
-	}
+					loadedSalesOrder.save({
+						ignoreMandatoryFields: true,
+					})
+				}
+			} catch (err) {
+				log.error({ title: '❌ Error setting Item descriptions...', details: err })
+			}
+			// SET UP EXHIBITOR FORM ITEMS END
+		}
 
 		const getEventData = (eventId) => {
-			return NG.tools.getLookupFields("customrecord_show", eventId, csLib.settings._EVENT_FIELDS, csLib.settings._EVENT_FIELDS_S, []);
+			try {
+				return NG.tools.getLookupFields("customrecord_show", eventId, csLib.settings._EVENT_FIELDS, csLib.settings._EVENT_FIELDS_S, []);
+			} catch (err) {
+				log.error({ title: '❌ Error running event data fetch...', details: err })
+			}
 		}
 
 		const setFormFlags = (context) => {
-			let formId;
-			if (NG.tools.isInArray(context.type, ["create", "copy"])) {
-				formId = context.newRecord.getValue({fieldId: "customform"});
-			} else if (!NG.tools.isEmpty(context.newRecord.id) && context.newRecord.id > 0) {
-				formId = NG.tools.getLookupFields(context.newRecord.type, context.newRecord.id, ["tranid", "customform"], ["customform"], []).customform;
-			}
+			log.audit({ title: '⚡ Running form flag set...', details: ''})
+			try {
+				let formId;
+				if (["create", "copy"].includes(context.type)) {
+					formId = context.newRecord.getValue({fieldId: "customform"});
+				} else if (!NG.tools.isEmpty(context.newRecord.id) && context.newRecord.id > 0) {
+					formId = NG.tools.getLookupFields(context.newRecord.type, context.newRecord.id, ["tranid", "customform"], ["customform"], []).customform;
+				}
 
-			if (!NG.tools.isEmpty(formId)) {
-				_RunGlobalBO = csLib.settings.GlobalBoothOrderScripting;
-				_ActiveFormBO = NG.tools.isInArray(formId, csLib.settings.BoothOrderFormIdListing);
-				_RunGlobalAL = csLib.settings.GlobalAddItemScripting;
-				_ActiveFormAL = NG.tools.isInArray(formId, csLib.settings.AddItemFormIdListing);
-				_RunGlobalSM = csLib.settings.GlobalShowMGtScripting;
-				_ActiveFormSM = NG.tools.isInArray(formId, csLib.settings.ShowMgtFormIdListing);
+				if (!NG.tools.isEmpty(formId)) {
+					_RunGlobalBO = csLib.settings.GlobalBoothOrderScripting;
+					_ActiveFormBO = NG.tools.isInArray(formId, csLib.settings.BoothOrderFormIdListing);
+					_RunGlobalAL = csLib.settings.GlobalAddItemScripting;
+					_ActiveFormAL = NG.tools.isInArray(formId, csLib.settings.AddItemFormIdListing);
+					_RunGlobalSM = csLib.settings.GlobalShowMGtScripting;
+					_ActiveFormSM = NG.tools.isInArray(formId, csLib.settings.ShowMgtFormIdListing);
+				}
+			} catch (err) {
+				log.error({ title: '❌ Error running FormFlag set...', details: err })
 			}
 		}
 
 		const setPriceLevel = (exhibId, eventId, currRec) => {
-			if (!NG.tools.isEmpty(eventId)) {
-				let d = new Date();
-				let eventData, startDate, gpl;
-				let today = (new Date(d.getFullYear(), d.getMonth(), d.getDate(), 0, 0, 0, 0)).getTime();
-				try {
-					eventData = getEventData(eventId);
-				} catch (err) {
-					NG.log.logError(err, "Error encountered retrieving show data");
-				}
+			log.audit({ title: '⚡ Running set Price Level...', details: ''})
+			try {
+				if (eventId) {
+					let d = new Date();
+					let eventData, startDate, gpl;
+					let today = (new Date(d.getFullYear(), d.getMonth(), d.getDate(), 0, 0, 0, 0)).getTime();
+					try {
+						eventData = getEventData(eventId);
+					} catch (err) {
+						NG.log.logError(err, "Error encountered retrieving show data");
+					}
 
-				if (!NG.tools.isEmpty(eventData)) {
-					let orderType = currRec.getValue({fieldId: "custbody_ng_cs_order_type"});
-					if (orderType !== csLib.settings.DefaultShowMgmtOrderType) {
-						if (!NG.tools.isEmpty(eventData.custrecord_adv_ord_date)) {
-							let _AdvDate = NG.time.convertStringToDate({dateString: eventData.custrecord_adv_ord_date});
-							log.audit({title: "Advanced Order Date (1)", details: eventData.custrecord_adv_ord_date});
-							log.audit({title: "Advanced Order Date (2)", details: _AdvDate});
-							let advDate = _AdvDate.getTime();
+					if (!NG.tools.isEmpty(eventData)) {
+						let orderType = currRec.getValue({fieldId: "custbody_ng_cs_order_type"});
+						if (orderType !== csLib.settings.DefaultShowMgmtOrderType) {
+							if (!NG.tools.isEmpty(eventData.custrecord_adv_ord_date)) {
+								let _AdvDate = NG.time.convertStringToDate({dateString: eventData.custrecord_adv_ord_date});
+								log.audit({title: "Advanced Order Date (1)", details: eventData.custrecord_adv_ord_date});
+								log.audit({title: "Advanced Order Date (2)", details: _AdvDate});
+								let advDate = _AdvDate.getTime();
 
-							if (today <= advDate) {
-								gpl = eventData.custrecord_adv_price_level;
-							} else {
-								gpl = getStandardGPL({eventData: eventData, eventId: eventId, today: today});
-							}
-						} else {
-							gpl = getStandardGPL({eventData: eventData, eventId: eventId, today: today});
-						}
-					} else {
-						if (!NG.tools.isEmpty(eventData.custrecord_show_mgmnt_price_lvl)) {
-							currRec.setValue({
-								fieldId: "custbody_price_level",
-								value: eventData.custrecord_show_mgmnt_price_lvl
-							});
-							gpl = eventData.custrecord_show_mgmnt_price_lvl;
-						}
-					}
+								if (today <= advDate) {
+									gpl = eventData.custrecord_adv_price_level;
+								} else {
+									gpl = getStandardGPL({eventData: eventData, eventId: eventId, today: today});
+								}
+							} else {
+								gpl = getStandardGPL({eventData: eventData, eventId: eventId, today: today});
+							}
+						} else {
+							if (!NG.tools.isEmpty(eventData.custrecord_show_mgmnt_price_lvl)) {
+								currRec.setValue({
+									fieldId: "custbody_price_level",
+									value: eventData.custrecord_show_mgmnt_price_lvl
+								});
+								gpl = eventData.custrecord_show_mgmnt_price_lvl;
+							}
+						}
 
-					gpl = gpl || "1";
-					currRec.setValue({fieldId: "custbody_price_level", value: gpl});
+						gpl = gpl || "1";
+						currRec.setValue({fieldId: "custbody_price_level", value: gpl});
+					}
 				}
+			} catch (err) {
+				log.error({ title: '❌ Error running price level...', details: err })
 			}
 		}
 
@@ -3332,16 +3318,22 @@
 		 * @returns {*}
 		 */
 		const getStandardGPL = (options) => {
-			let gpl, startDate = csLib.func.getStartDate(options.eventId);
-			if (!NG.tools.isEmpty(startDate) && options.today >= startDate) {
-				gpl = options.eventData.custrecord_site_price_level || (options.eventData.custrecord_std_price_level || gpl);
-			} else {
-				gpl = options.eventData.custrecord_std_price_level || gpl;
-			}
-			return gpl;
+			log.audit({ title: '⚡ Running get GPL...', details: ''})
+			try {
+				let gpl, startDate = csLib.func.getStartDate(options.eventId);
+				if (!NG.tools.isEmpty(startDate) && options.today >= startDate) {
+					gpl = options.eventData.custrecord_site_price_level || (options.eventData.custrecord_std_price_level || gpl);
+				} else {
+					gpl = options.eventData.custrecord_std_price_level || gpl;
+				}
+				return gpl;
+			} catch (err) {
+				log.error({ title: '❌ Error running GPL', details: err })
+			}
 		}
 
 		const updateDeposits = (orderId) => {
+			log.audit({ title: '⚡ Running Update Deposits....', details: ''})
 			try {
 				let depFilt = [
 					["mainline", "is", "T"]
@@ -3501,91 +3493,100 @@
 		}
 
 		const calcTotals = (rec, eventData) => {
-			return csLib.settings.AutoChargeCategoryExclusions.length > 0 && csLib.settings.ExemptEstimatedItems ? csLib.func.getExemptedTotal(rec, true, eventData) :
-				(csLib.settings.AutoChargeCategoryExclusions.length > 0 && !csLib.settings.ExemptEstimatedItems ? csLib.func.calcWithoutExclCats(rec, eventData) :
-					(!csLib.settings.AutoChargeCategoryExclusions.length > 0 && csLib.settings.ExemptEstimatedItems ? csLib.func.getExemptedTotal(rec, false, eventData) :
-							defaultTotals(rec, eventData)
-					));
+			log.audit({ title: '⚡ Running Calc Totals...', details: ''})
+			try {
+				return csLib.settings.AutoChargeCategoryExclusions.length > 0 && csLib.settings.ExemptEstimatedItems ? csLib.func.getExemptedTotal(rec, true, eventData) :
+					(csLib.settings.AutoChargeCategoryExclusions.length > 0 && !csLib.settings.ExemptEstimatedItems ? csLib.func.calcWithoutExclCats(rec, eventData) :
+						(!csLib.settings.AutoChargeCategoryExclusions.length > 0 && csLib.settings.ExemptEstimatedItems ? csLib.func.getExemptedTotal(rec, false, eventData) :
+								defaultTotals(rec, eventData)
+						));
+			} catch (err) {
+				log.error({ title: '❌ Error running calc totals!', details: err })
+			}
 		}
 
 		const defaultTotals = (currRec, eventData) => {
-			let tTotal = 0, tTotal1 = 0, tTotal2 = 0, ntTotal = 0, topTax, taxRate, taxRate1, taxRate2, l;
-			let lines = currRec.getLineCount({sublistId: "item"});
+			try {
+				let tTotal = 0, tTotal1 = 0, tTotal2 = 0, ntTotal = 0, topTax, taxRate, taxRate1, taxRate2, l;
+				let lines = currRec.getLineCount({sublistId: "item"});
 
-			if (!csLib.settings.UseCanadianSalesTax) {
-				topTax = !NG.tools.isEmpty(eventData) ? (eventData['custrecord_tax_percent'] || "").replace("%", "") : "";
-				taxRate = Number(topTax || 0) / 100;
-				if (csLib.settings.SalesTaxOnItemLines) {
-					taxRate1 = Number(topTax || 0) / 100;
-					taxRate2 = 0;
-				}
-			} else {
-				taxRate1 = Number(!NG.tools.isEmpty(eventData) ? (eventData['custrecord_ng_cs_evt_gst_pct'] || "").replace("%", "") : "0") / 100;
-				taxRate2 = Number(!NG.tools.isEmpty(eventData) ? (eventData['custrecord_ng_cs_evt_pst_pct'] || "").replace("%", "") : "0") / 100;
-				tTotal1 = 0;
-				tTotal2 = 0;
-			}
+				if (!csLib.settings.UseCanadianSalesTax) {
+					topTax = !NG.tools.isEmpty(eventData) ? (eventData['custrecord_tax_percent'] || "").replace("%", "") : "";
+					taxRate = Number(topTax || 0) / 100;
+					if (csLib.settings.SalesTaxOnItemLines) {
+						taxRate1 = Number(topTax || 0) / 100;
+						taxRate2 = 0;
+					}
+				} else {
+					taxRate1 = Number(!NG.tools.isEmpty(eventData) ? (eventData['custrecord_ng_cs_evt_gst_pct'] || "").replace("%", "") : "0") / 100;
+					taxRate2 = Number(!NG.tools.isEmpty(eventData) ? (eventData['custrecord_ng_cs_evt_pst_pct'] || "").replace("%", "") : "0") / 100;
+					tTotal1 = 0;
+					tTotal2 = 0;
+				}
 
-			for (l = 0; l < lines; l++) {
-				let taxable = currRec.getSublistValue({sublistId: "item", fieldId: "istaxable", line: l}) || true;
-				let amount = Number(currRec.getSublistValue({sublistId: "item", fieldId: "amount", line: l}));
-				if (!csLib.settings.SalesTaxOnItemLines && !csLib.settings.UseCanadianSalesTax) {
-					if (taxable) {
-						tTotal += amount;
-					} else {
-						ntTotal += amount;
-					}
-				} else {
-					tTotal += amount;
-					let taxAmount1 = NG.M.roundToHundredths(NG.M.roundToThousandths(taxRate1 * amount));
-					tTotal1 += taxAmount1;
-					if (csLib.settings.UseCanadianSalesTax) {
-						let taxAmount2 = NG.M.roundToHundredths(NG.M.roundToThousandths(taxRate2 * amount));
-						tTotal2 += taxAmount2;
-					}
-				}
-			}
+				for (l = 0; l < lines; l++) {
+					let taxable = currRec.getSublistValue({sublistId: "item", fieldId: "istaxable", line: l}) || true;
+					let amount = Number(currRec.getSublistValue({sublistId: "item", fieldId: "amount", line: l}));
+					if (!csLib.settings.SalesTaxOnItemLines && !csLib.settings.UseCanadianSalesTax) {
+						if (taxable) {
+							tTotal += amount;
+						} else {
+							ntTotal += amount;
+						}
+					} else {
+						tTotal += amount;
+						let taxAmount1 = NG.M.roundToHundredths(NG.M.roundToThousandths(taxRate1 * amount));
+						tTotal1 += taxAmount1;
+						if (csLib.settings.UseCanadianSalesTax) {
+							let taxAmount2 = NG.M.roundToHundredths(NG.M.roundToThousandths(taxRate2 * amount));
+							tTotal2 += taxAmount2;
+						}
+					}
+				}
 
-			tTotal = NG.M.roundToHundredths(tTotal);
-			ntTotal = NG.M.roundToHundredths(ntTotal);
-			tTotal1 = NG.M.roundToHundredths(tTotal1);
-			tTotal2 = NG.M.roundToHundredths(tTotal2);
+				tTotal = NG.M.roundToHundredths(tTotal);
+				ntTotal = NG.M.roundToHundredths(ntTotal);
+				tTotal1 = NG.M.roundToHundredths(tTotal1);
+				tTotal2 = NG.M.roundToHundredths(tTotal2);
 
-			if (!csLib.settings.UseCanadianSalesTax) {
-				let taxTotal = !csLib.settings.SalesTaxOnItemLines
-					? NG.M.roundToHundredths(NG.M.roundToThousandths(taxRate * tTotal))
-					: NG.M.roundToHundredths(tTotal1 + tTotal2);
-				let billableTotal = NG.M.roundToHundredths(tTotal + ntTotal + taxTotal);
+				if (!csLib.settings.UseCanadianSalesTax) {
+					let taxTotal = !csLib.settings.SalesTaxOnItemLines
+						? NG.M.roundToHundredths(NG.M.roundToThousandths(taxRate * tTotal))
+						: NG.M.roundToHundredths(tTotal1 + tTotal2);
+					let billableTotal = NG.M.roundToHundredths(tTotal + ntTotal + taxTotal);
 
-				return {
-					total: billableTotal
-					,
-					tax: taxTotal
-					,
-					sub: NG.M.roundToHundredths((Number(currRec.getValue({fieldId: "total"}))) - (Number(currRec.getValue({fieldId: "taxtotal"}))))
-					,
-					taxrate: taxRate
-					,
-					t: tTotal
-					,
-					nt: ntTotal
-					,
-					tax1: tTotal1
-					,
-					tax2: tTotal2
-				};
-			} else {
-				let taxTotal = NG.M.roundToHundredths((Number(currRec.getValue({fieldId: "taxtotal"}))) + (Number(currRec.getValue({fieldId: "tax2total"}))));
-				return {
-					total: Number(currRec.getValue({fieldId: "total"}))
-					, tax: taxTotal
-					, sub: NG.M.roundToHundredths((Number(currRec.getValue({fieldId: "total"}))) - taxTotal)
-					, taxrate: taxRate
-					, t: tTotal
-					, nt: ntTotal
-					, tax1: tTotal1
-					, tax2: tTotal2
-				};
+					return {
+						total: billableTotal
+						,
+						tax: taxTotal
+						,
+						sub: NG.M.roundToHundredths((Number(currRec.getValue({fieldId: "total"}))) - (Number(currRec.getValue({fieldId: "taxtotal"}))))
+						,
+						taxrate: taxRate
+						,
+						t: tTotal
+						,
+						nt: ntTotal
+						,
+						tax1: tTotal1
+						,
+						tax2: tTotal2
+					};
+				} else {
+					let taxTotal = NG.M.roundToHundredths((Number(currRec.getValue({fieldId: "taxtotal"}))) + (Number(currRec.getValue({fieldId: "tax2total"}))));
+					return {
+						total: Number(currRec.getValue({fieldId: "total"}))
+						, tax: taxTotal
+						, sub: NG.M.roundToHundredths((Number(currRec.getValue({fieldId: "total"}))) - taxTotal)
+						, taxrate: taxRate
+						, t: tTotal
+						, nt: ntTotal
+						, tax1: tTotal1
+						, tax2: tTotal2
+					};
+				}
+			} catch (err) {
+				log.error({ title: '❌ Error running default totals', details: err })
 			}
 		}
 
@@ -3774,6 +3775,16 @@
 		}
 	}
 
+	const getAllResultsFor = (searchObj, callback) => {
+		let myPagedData = searchObj.runPaged();
+		myPagedData.pageRanges.forEach(function (pageRange) {
+			let myPage = myPagedData.fetch({index: pageRange.index});
+			myPage.data.forEach(function (result) {
+				callback(result)
+			});
+		})
+	}
+
 	function runServerSubsidiarySet(sc, record) {
 		/* * * * * * * * * * * * * * * * * * * * * * * * * * * *
 		* Run subsidiary check on customer,
