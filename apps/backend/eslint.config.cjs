const suitescript = require("eslint-plugin-suitescript");
const js = require("@eslint/js");
const { MODULES } = require("eslint-plugin-suitescript/lib/util/modules");

module.exports = [
  js.configs.recommended,
  {
    name: "SuiteScript",
    plugins: {
      suitescript,
    },
    languageOptions: {
      ecmaVersion: 12,
      sourceType: "script",
    },
    rules: {
      "suitescript/script-type": "error",
      "suitescript/no-log-module": "warn",
      "suitescript/api-version": "error",
      "suitescript/entry-points": "error",
      "suitescript/log-args": "error",
      "suitescript/module-vars": ["error", MODULES],
      "suitescript/no-amd-name": "error",
      "suitescript/no-extra-modules": "error",
      "suitescript/no-invalid-modules": "error",
      "suitescript/no-module-extensions": "error",
      "no-undef": "off",
    },
    ignores: ["./eslint.config.cjs", "node_modules/*"]
  },
];


