import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";
import {
  makeNetSuiteRequest,
  buildNetSuiteUrl,
  handleApiError,
  setOptimizedHeaders,
  addPerformanceHeaders,
} from "@event-services/netsuite-http";

// Request deduplication map to prevent multiple simultaneous requests to the same event
const pendingRequests = new Map();

// Cleanup old requests every 5 minutes to prevent memory leaks
setInterval(() => {
  const now = Date.now();
  for (const [key, promise] of pendingRequests.entries()) {
    if (promise._startTime && now - promise._startTime > 120000) {
      console.log(`[Cleanup] Removing stale request: ${key}`);
      pendingRequests.delete(key);
    }
  }
}, 300000);

export default async function handler(req, res) {
  const requestStart = Date.now();

  // Only allow GET requests
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Check authentication first to fail fast
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({
        error:
          "You must be signed in to view the protected content on this page.",
      });
    }

    const { eid } = req.query;

    // Validate event ID
    if (!eid) {
      return res.status(400).json({ error: "Event ID is required" });
    }

    // Create a unique key for request deduplication
    const requestKey = `${session.user.id}-${eid}`;

    // Check if there's already a pending request
    if (pendingRequests.has(requestKey)) {
      console.log(`[Dedup] Waiting for existing request: ${requestKey}`);

      try {
        const existingResult = await pendingRequests.get(requestKey);

        setOptimizedHeaders(res);
        res.setHeader("X-Response-Time", `${Date.now() - requestStart}ms`);
        res.setHeader("X-Request-Source", "deduplicated");

        return res.status(200).json(existingResult);
      } catch (error) {
        console.log(
          `[Dedup] Existing request failed, making new request: ${requestKey}`,
        );
        pendingRequests.delete(requestKey);
      }
    }

    // Build NetSuite URL
    const url = buildNetSuiteUrl(
      "customscript_get_event_ssr_details",
      "customdeploy_get_event_ssr_details",
      { id: eid },
    );

    // Create the request promise and store it for deduplication
    const requestPromise = (async () => {
      try {
        console.log(`[Request] Starting new request: ${requestKey}`);

        // Make request to NetSuite
        const { data, duration } = await makeNetSuiteRequest({
          url,
          method: "GET",
        });

        console.log(
          `[Request] Completed successfully: ${requestKey}, Duration: ${duration}ms`,
        );
        return { data, duration };
      } finally {
        pendingRequests.delete(requestKey);
      }
    })();

    // Store the request promise for deduplication with timestamp
    requestPromise._startTime = Date.now();
    pendingRequests.set(requestKey, requestPromise);

    // Wait for the request to complete
    const { data, duration } = await requestPromise;

    // Set optimized response headers
    setOptimizedHeaders(res);

    // Add performance headers
    addPerformanceHeaders(res, requestStart, duration);

    return res.status(200).json(data);
  } catch (error) {
    const session = await getServerSession(req, res, authOptions);

    // Clean up pending request on error
    if (req.query.eid) {
      const requestKey = `${session?.user?.id}-${req.query.eid}`;
      pendingRequests.delete(requestKey);
    }

    return handleApiError(error, res, requestStart);
  }
}

// Disable body parsing for this API route as we don't need it
export const config = {
  api: {
    bodyParser: false,
  },
};
