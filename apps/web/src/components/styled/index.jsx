import { alpha, Avatar, Badge, Box, styled } from "@mui/material";

// ===========================================================
//  Styled Components
// ===========================================================
const BoxUploadWrapper = styled(Box)(
  ({ theme }) => `
    border-radius: ${theme.shape.borderRadius}px;
    padding: ${theme.spacing(3)};
    background: ${alpha(theme.palette.primary.light, 0.3)};
    border: 1px dashed ${alpha(theme.palette.dark.main, 0.8)};
    outline: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: ${theme.transitions.create(["border", "background"])};

    &:hover {
      background: ${alpha(theme.palette.common.white, 0.3)};
      border-color: ${theme.palette.primary.main};
    }
`,
);

const AvatarWrapper = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.palette.primary.light};
    color: ${theme.palette.primary.main};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`,
);

const AvatarSuccess = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.palette.success.light};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`,
);

const AvatarDanger = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.palette.error.light};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`,
);

export { BoxUploadWrapper, AvatarWrapper, AvatarSuccess, AvatarDanger };
