import Link from "next/link";
import React, {
  Fragment,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import {
  AddShoppingCartOutlined,
  CheckTwoTone,
  CloseTwoTone,
  CloudUploadTwoTone,
  CreditCardOffOutlined,
  DriveFolderUploadOutlined,
  FileUploadTwoTone,
  HelpOutlineOutlined,
  Remove,
  WarningRounded,
} from "@mui/icons-material";

import {
  Alert,
  alpha,
  Avatar,
  Badge,
  Box,
  Button,
  ButtonGroup,
  Checkbox,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  LinearProgress,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Select,
  Stack,
  styled,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
} from "@mui/material";

import { motion } from "motion/react";
import { useSnackbar } from "notistack";
import LazyImage from "components/LazyImage";
import BazaarCard from "components/BazaarCard";
import { H3, Paragraph, Span } from "components/Typography";
import ReactHtmlParser, { convertNodeToElement } from "react-html-parser";
import axios from "axios";
import { useDropzone } from "react-dropzone";
import { useTranslation } from "next-i18next";
import { DesktopTimePicker, MobileTimePicker } from "@mui/x-date-pickers";
import { useTheme } from "@mui/material/styles";
import { makeStyles } from "@mui/styles";
import { LoadingButton } from "@mui/lab";
import { NumericFormat } from "react-number-format";
import PropTypes from "prop-types";
import { Montserrat } from "next/font/google";
import moment from "moment";
import { useAppContext } from "contexts/AppContext";
import { currency } from "lib";
import { FlexBox } from "../flex-box";
import { useAddToCart, useCart } from "../../store/zustandCartStore";
import { useSettings } from "../../store/zSettingsStore";
import { useBooth, useEvent } from "../../store/eventStore";
import { usePrevious, useSetState } from "../../utils/customHooks";
import LaborTablePreview from "../modals/LaborTablePreview";
import InlineQuantityEditField from "../InlineTextEditField";
import FormikDynamic from "../item-attributes/FormikDynamic";

import { useUser } from "../../store/zUserStore";

const montserrat = Montserrat({
  weight: ["100", "200", "300", "400", "500", "600", "800", "900"],
  subsets: ["latin"],
  style: ["normal", "italic"],
});

// ===========================================================
//  Forwarded Ref Components
// ===========================================================

const NumericWorkerFormat = React.forwardRef(
  function NumericWorkerFormat(props, ref) {
    const { onChange, ...other } = props;

    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
        valueIsNumericString
        allowNegative={false}
        min={1}
      />
    );
  },
);

NumericWorkerFormat.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const NumericFreightFormat = React.forwardRef(
  function NumericFreightFormat(props, ref) {
    const { onChange, ...other } = props;

    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
        thousandSeparator
        valueIsNumericString
        prefix="WT "
      />
    );
  },
);

NumericFreightFormat.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const NumericFreightQuantityFormat = React.forwardRef(
  function NumericFreightQuantityFormat(props, ref) {
    const { onChange, ...other } = props;

    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
        thousandSeparator
        valueIsNumericString
      />
    );
  },
);

NumericFreightQuantityFormat.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const NumericLengthFormat = React.forwardRef(
  function NumericLengthFormat(props, ref) {
    const { onChange, ...other } = props;

    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
        thousandSeparator
        valueIsNumericString
      />
    );
  },
);

NumericLengthFormat.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const NumericWidthFormat = React.forwardRef(
  function NumericLengthFormat(props, ref) {
    const { onChange, ...other } = props;

    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
        thousandSeparator
        valueIsNumericString
      />
    );
  },
);

NumericWidthFormat.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const NumericTotalSquareFootFormat = React.forwardRef(
  function NumericTotalSquareFootFormat(props, ref) {
    const { onChange, ...other } = props;

    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
        thousandSeparator
        valueIsNumericString
        suffix=""
      />
    );
  },
);

NumericTotalSquareFootFormat.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

// ===========================================================
//  Styled Components
// ===========================================================
const BoxUploadWrapper = styled(Box)(
  ({ theme }) => `
    border-radius: ${theme.shape.borderRadius}px;
    padding: ${theme.spacing(3)};
    background: ${alpha(theme.palette.primary.light, 0.3)};
    border: 1px dashed ${alpha(theme.palette.dark.main, 0.8)};
    outline: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: ${theme.transitions.create(["border", "background"])};

    &:hover {
      background: ${alpha(theme.palette.common.white, 0.3)};
      border-color: ${theme.palette.primary.main};
    }
`,
);

const AvatarWrapper = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.palette.primary.light};
    color: ${theme.palette.primary.main};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`,
);

const AvatarSuccess = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.palette.success.light};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`,
);

const AvatarDanger = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.palette.error.light};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`,
);

const StyledBazaarCard = styled(BazaarCard)({
  height: "100%",
  margin: "auto",
  display: "flex",
  overflow: "hidden",
  borderRadius: "8px",
  position: "relative",
  flexDirection: "column",
  justifyContent: "space-between",
  transition: "all 250ms ease-in-out",
  ":hover": {
    "& .hover-box": {
      opacity: 1,
    },
  },
});
const ImageWrapper = styled(Box)(({ theme }) => ({
  textAlign: "center",
  position: "relative",
  display: "inline-block",
  [theme.breakpoints.down("sm")]: {
    display: "block",
  },
  height: "15rem",
}));
const StyledChip = styled(Chip)({
  zIndex: 1,
  top: "10px",
  left: "10px",
  paddingLeft: 3,
  paddingRight: 3,
  fontWeight: 600,
  fontSize: "10px",
  position: "absolute",
});
const HoverIconWrapper = styled(Box)({
  zIndex: 2,
  top: "7px",
  opacity: 0,
  right: "15px",
  display: "flex",
  cursor: "pointer",
  position: "absolute",
  flexDirection: "column",
  transition: "all 0.3s ease-in-out",
});
const ContentWrapper = styled(Box)({
  padding: "1rem",
  "& .title, & .categories": {
    overflow: "hidden",
    whiteSpace: "nowrap",
    textOverflow: "ellipsis",
  },
});

const FileCountBadge = styled(Badge)(({ theme }) => ({
  "& .MuiBadge-badge": {
    right: -3,
    top: 10,
    border: `2px solid ${theme.palette.background.paper}`,
    padding: "0 2px",
  },
}));

// ========================================================

// ========================================================

// ===========================================================
//  Memoized Components
// ===========================================================
const ItemFields = memo(
  ({
    isFreight,
    isSquareFt,
    isLabor,
    quantity,
    handleQuantityChange,
    handleQuantityCapture,
  }) => {
    let itemFieldsRender = null;

    if (isSquareFt) {
      itemFieldsRender = null;
    } else if (isLabor) {
      itemFieldsRender = null;
    } else if (isFreight) {
      itemFieldsRender = (
        <TextField
          hiddenLabel
          size="small"
          onChange={(e) => handleQuantityChange(e)}
          onBlur={(e) => handleQuantityCapture(e)}
          variant="outlined"
          value={quantity}
          disabled
          InputProps={{
            inputComponent: NumericFreightQuantityFormat,
          }}
        />
      );
    } else {
      /*
      * (
        <Paper style={{ width: "fit-content" }}>
          <IconButton
            size="small"
            onClick={() => decreaseQuantity()}
            color="primary"
            aria-label="add to shopping cart"
          >
            <RemoveIcon />
          </IconButton>
          <InputBase
            size="small"
            style={{
              display: "inline-flex",
              fontSize: "8pt",
              minWidth: "2.5rem",
              width: "min-content",
              paddingLeft: "1vh",
              marginLeft: "auto",
              marginRight: "auto",
              flexShrink: 3,
            }}
            className="text-center"
            onChange={(e) => handleQuantityChange(e)}
            onBlur={(e) => handleQuantityCapture(e)}
            type="number"
            variant="standard"
            value={quantity}
          />
          <IconButton
            onClick={() => increaseQuantity()}
            color="primary"
            aria-label="add to shopping cart"
            size="small"
          >
            <AddIcon />
          </IconButton>
        </Paper>
      );
      * */
      itemFieldsRender = null;
    }

    return itemFieldsRender;
  },
);

const ItemBlurb = memo(({ description }) => {
  function transform(node, index) {
    // console.log('Working Nodes: ', node)
    if (node.type === "tag" && node.name === "b") {
      node.name = "strong";
      return convertNodeToElement(node, index, transform);
    }
    // if (node.type === 'tag' && node.name === 'span') {
    // 	return null
    // }

    // if (node.type === 'tag' && node.name === 'ul') {
    // 	node.name = 'ol';
    // 	return convertNodeToElement(node, index, transform);
    // }

    if (node.type === "tag" && node.name === "table") {
      node.name = "div";
      // console.log('table node: ', node)
      node.attribs = {
        ...node.attribs,
        width: null,
        class: "container",
        "product-test": "container",
        style: `padding: 0;`,
      };
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "tr") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "td") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "tbody") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }

    return convertNodeToElement(node, index, transform);
  }

  const renderBlurb = (blurb) => {
    return ReactHtmlParser(blurb, { decodeEntities: true, transform });
  };

  return renderBlurb(description);
});

const VariantSelectors = memo(
  ({
    colors,
    sizes,
    colorError,
    validatingItem,
    handleColorChange,
    sizeError,
    color,
    size,
    handleSizeChange,
    materials,
    handleMaterialChange,
    material,
    materialError,
    orientations,
    handleOrientationChange,
    orientation,
    orientationError,
    hasColorOptions,
    hasOrientOptions,
    hasGraphicOptions,
    hasSizeOptions,
    category,
  }) => {
    // setLoadingVariantFields(true)

    let hasVariants = [
      { exists: hasColorOptions, name: "colors" },
      { exists: hasOrientOptions, name: "orientations" },
      { exists: hasGraphicOptions, name: "materials" },
      { exists: hasSizeOptions, name: "sizes" },
    ];
    const firstLetterUpperCase = (word) => {
      return word.charAt(0).toUpperCase() + word.substring(1);
    };

    let boolKeys = hasVariants.filter((v) => v.exists).map((v) => v.name);
    let resultElements = [];

    console.log("boolKeys: ", boolKeys);
    // console.log('value Keys: ', valueKeys)

    let element = [];
    boolKeys.forEach((boolKey, i) => {
      switch (boolKey) {
        case "colors":
          if (colors.length !== 0) {
            const renderMenuItems = () => {
              return colors.map((color, i) => {
                return (
                  <MenuItem key={`${boolKey}-${i}`} value={color.text}>
                    {color.text}
                  </MenuItem>
                );
              });
            };

            const getCurrentColor = (c) =>
              colors.filter((currColor, i) => currColor.text === c)[0];

            element.push(
              <Grid item xs={12} key="column-colors">
                <FormControl
                  fullWidth
                  error={colorError}
                  size="small"
                  variant="outlined"
                  disabled={validatingItem}
                >
                  <InputLabel id="demo-simple-select-outlined-label">
                    {firstLetterUpperCase(boolKey)}
                  </InputLabel>
                  <Select
                    labelId="demo-simple-select-outlined-label"
                    id="demo-simple-select-outlined"
                    value={color}
                    onChange={(e) =>
                      handleColorChange(e, getCurrentColor(e.target.value))
                    }
                    label="Color"
                    name="color"
                    error={colorError}
                  >
                    {/* <MenuItem value=""> */}
                    {/*	<em>None</em> */}
                    {/* </MenuItem> */}
                    {renderMenuItems()}
                  </Select>
                  {colorError ? (
                    <FormHelperText>Please select a color.</FormHelperText>
                  ) : null}
                </FormControl>
              </Grid>,
            );
          }
          break;
        case "sizes":
          if (sizes.length !== 0) {
            const renderMenuItems = () => {
              return sizes.map((size, i) => {
                return (
                  <MenuItem key={`${boolKey}-${i}`} value={size.text}>
                    {size.text}
                  </MenuItem>
                );
              });
            };

            const getCurrentSize = (s) =>
              sizes.filter((currSize) => currSize.text === s)[0];

            element.push(
              <Grid item xs={12} key="column-size">
                <FormControl
                  error={sizeError}
                  fullWidth
                  size="small"
                  variant="outlined"
                  disabled={validatingItem}
                >
                  <InputLabel id="demo-simple-select-outlined-label">
                    {firstLetterUpperCase("sizes")}
                  </InputLabel>
                  <Select
                    labelId="demo-simple-select-outlined-label"
                    id="demo-simple-select-outlined"
                    value={size}
                    onChange={(e) =>
                      handleSizeChange(e, getCurrentSize(e.target.value))
                    }
                    label="Size"
                    name="sizes"
                    error={sizeError}
                  >
                    {/* <MenuItem value=""> */}
                    {/*	<em>None</em> */}
                    {/* </MenuItem> */}
                    {renderMenuItems()}
                  </Select>
                  {sizeError ? (
                    <FormHelperText>Please select a size.</FormHelperText>
                  ) : null}
                </FormControl>
              </Grid>,
            );
          }
          break;
        case "materials":
          if (materials.length !== 0) {
            const renderMenuItems = () => {
              return materials.map((size, i) => {
                return (
                  <MenuItem key={`${boolKey}-${i}`} value={size.text}>
                    {size.text}
                  </MenuItem>
                );
              });
            };

            const getCurrentMaterial = (m) =>
              materials.filter((currMaterial, i) => currMaterial.text === m)[0];

            element.push(
              <Grid item xs={12} key="column-material">
                <FormControl
                  error={materialError}
                  fullWidth
                  size="small"
                  variant="outlined"
                  disabled={validatingItem}
                >
                  <InputLabel id="demo-simple-select-outlined-label">
                    {firstLetterUpperCase("Material")}
                  </InputLabel>
                  <Select
                    labelId="demo-simple-select-outlined-label"
                    id="demo-simple-select-outlined"
                    value={material}
                    onChange={(e) =>
                      handleMaterialChange(
                        e,
                        getCurrentMaterial(e.target.value),
                      )
                    }
                    label="Material"
                    name="materials"
                    error={materialError}
                  >
                    {/* <MenuItem value=""> */}
                    {/*	<em>None</em> */}
                    {/* </MenuItem> */}
                    {renderMenuItems()}
                  </Select>
                  {materialError ? (
                    <FormHelperText>Please select a material.</FormHelperText>
                  ) : null}
                </FormControl>
              </Grid>,
            );
          }
          break;
        case "orientations":
          if (orientations.length !== 0) {
            const renderMenuItems = () => {
              return orientations.map((size, i) => {
                return (
                  <MenuItem
                    key={`menuItem-${i}-${size.text}`}
                    value={size.text}
                  >
                    {size.text}
                  </MenuItem>
                );
              });
            };
            const getCurrentOrientation = (orient) =>
              orientations.filter(
                (currOrient, i) => currOrient.text === orient,
              )[0];

            element.push(
              <Grid item xs={12} key="column-orientation">
                <FormControl
                  error={orientationError}
                  fullWidth
                  size="small"
                  variant="outlined"
                  disabled={validatingItem}
                >
                  <InputLabel id="demo-simple-select-outlined-label">
                    {firstLetterUpperCase("variants")}
                  </InputLabel>
                  <Select
                    labelId="demo-simple-select-outlined-label"
                    id="demo-simple-select-outlined"
                    value={orientation}
                    onChange={(e) =>
                      handleOrientationChange(
                        e,
                        getCurrentOrientation(e.target.value),
                      )
                    }
                    label="Variant"
                    name={boolKey}
                    error={orientationError}
                  >
                    {/* <MenuItem value=""> */}
                    {/*	<em>None</em> */}
                    {/* </MenuItem> */}
                    {renderMenuItems()}
                  </Select>
                  {orientationError ? (
                    <FormHelperText>Please select a variant.</FormHelperText>
                  ) : null}
                </FormControl>
              </Grid>,
            );
          }
          break;

        default:
          console.error("No value key component specified to render");
      }
    });

    console.log("Menu elements: ", element);

    // setLoadingVariantFields(false)
    // setItemVariantFieldsRendered(element)
    return element;
  },
);

const useCardStyles = makeStyles((theme) => ({
  root: {
    maxWidth: "100%",
    minWidth: "22em",
    marginTop: "1rem",
    // height: 'fit-content'
  },
  media: {
    height: "15rem",
    paddingTop: "56.25%", // 16:9
    position: "relative",
  },
  expand: {
    transform: "rotate(0deg)",
    marginLeft: "auto",
    transition: theme.transitions.create("transform", {
      duration: theme.transitions.duration.shortest,
    }),
  },
  expandOpen: {
    transform: "rotate(180deg)",
  },
}));

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",

  // These options are needed to round to whole numbers if that's what you want.
  // minimumFractionDigits: 0, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
  // maximumFractionDigits: 0, // (causes 2500.99 to be printed as $2,501)
});

const ProductCard1 = ({
  id,
  slug,
  title,
  price,
  imgUrl,
  rating = 5,
  hideRating,
  hoverEffect,
  discount = null,
  showProductSize,
  images = [],
  description = "",
  colors,
  sizes,
  materials,
  orientations,
  hasColorOptions,
  hasOrientOptions,
  hasGraphicOptions,
  hasSizeOptions,
  isShowDuration,
  isFreight,
  isLabor,
  isSquareFt,
  isEstimated,
  isDaysCalc,
  isMemoItem,
  saleUnit,
  item,
  itemAttributes,
  category,
  comparePrice,
  enforceMinQty,
  minimumQuantity,
  maxQuantity,
  isUpload,
  materialHandlingSchedule,
  freightMaximum,
  ...props
}) => {
  // Todo: refoactor out of each property of item as props. Lets make a single property called item to hold all these props.
  const { enqueueSnackbar } = useSnackbar();
  const { t } = useTranslation();
  const { state, dispatch } = useAppContext();
  const [openModal, setOpenModal] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);

  const { cart, setItemQuantity, removeCartItem, setCart } = useCart();
  const { settings } = useSettings();
  const { event } = useEvent();
  const { booth } = useBooth();
  const { user } = useUser();

  const {
    eventDates: showDates,
    laborDates,
    daysCalcDates,
    laborSchedule,
  } = event;

  const isPerPieceItem =
    isFreight && materialHandlingSchedule?.chargeType?.value === "2";

  const [color, setColor] = useState("");
  const [size, setSize] = useState("");
  const [material, setMaterial] = useState("");
  const [screenSize, setScreenSize] = useState("");
  const [orientation, setOrientation] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [memoText, setMemoText] = useState("");
  const [attributes, setAttributes] = useState({});

  // Netsuite Payload build
  const [netSize, setNetSize] = useState({});
  const [netColor, setNetColor] = useState({});
  const [netMaterial, setNetMaterial] = useState({});
  const [netScreenSize, setNetScreenSize] = useState({});
  const [netOrientation, setNetOrientations] = useState({});

  const [colorError, setColorError] = useState(false);
  const [sizeError, setSizeError] = useState(false);
  const [materialError, setMaterialError] = useState(false);
  const [orientationError, setOrientationError] = useState(false);

  const [validatedItem, setValidatedItem] = useState({
    childId: "",
    price: null,
  });

  // showDates = [],
  // daysCalcDates = [],
  // laborDates = [],
  // laborSchedule = [],

  // Special Item type state
  let freightMinimum = Number(props.freightMinimum) || 0;

  if (!settings.custrecord_enable_material_handling_beta) {
    freightMinimum =
      Number(event?.details.rates.freight.minimum) ||
      Number(settings.custrecord_ng_cs_freight_minimum);
  }

  const [cwtFreightWeight, setCwtFreightWeight] = useState(() => {
    if (isFreight) {
      return freightMinimum;
    }
    return 0;
  });
  const [cwtFreightWeightEntered, setCwtFreightWeightEntered] = useState(() => {
    if (isFreight) {
      return freightMinimum;
    }
    return 0;
  });

  const [optionWidth, setOptionWidth] = useState(10);
  const [optionLength, setOptionLength] = useState(10);
  const [optionTotalSqFt, setOptionTotalSqFt] = useState(100);
  const [daysCalcDateSelected, setDaysCalcDateSelected] = useState("");
  const [laborDateSelected, setLaborDateSelected] = useState("");
  const [laborStartTimeSelected, setLaborStartTimeSelected] = useState(
    new Date(new Date().setHours(8, 0)),
  );
  const [laborEndTimeSelected, setLaborEndTimeSelected] = useState(
    new Date(new Date().setHours(17, 0)),
  );
  const [laborSupervision, setLaborSupervision] = useState(false);
  const [laborBucketBuild, setLaborBucketBuild] = useState([]);
  const [laborSupervisionCost, setLaborSupervisionCost] = useState(0);
  const [laborTableOpen, setLaborTableOpen] = useState(false);
  const [laborWorkerQuantity, setLaborWorkerQuantity] = useState(1);
  const [laborCostTotal, setLaborCostTotal] = useState("");
  const [laborBareCostTotal, setLaborBareCostTotal] = useState(0);
  const [laborHoursTotal, setLaborHoursTotal] = useState(0);
  const [laborHourlyAvg, setLaborHourlyAvg] = useState(0);
  const [unaccountedLaborHoursTotal, setUnaccountedLaborHoursTotal] =
    useState(0);
  const [itemFieldsRendered, setItemFieldsRendered] = useState(null);
  const [itemVariantFieldsRendered, setItemVariantFieldsRendered] =
    useState(null);
  const [loadingVariantFields, setLoadingVariantFields] = useState(false);
  const prevSelectedLaborDate = usePrevious(laborDateSelected);
  const prevWorkerQuantity = usePrevious(laborWorkerQuantity);
  const prevStartSelected = usePrevious(laborStartTimeSelected);
  const prevEndSelected = usePrevious(laborEndTimeSelected);
  const prevSupervisionSelected = usePrevious(laborSupervision);
  let [cartUpdated, setUpdatedCart] = useSetState(cart);
  let first = useRef(true);
  const initialRender = useRef(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  // validating loader
  const [validatingItem, setValidatingItem] = useState(false);
  const isMounted = useRef(false);
  const [expanded, setExpanded] = useState(false);
  const [optionModalVisible, setOptionModalVisible] = useState(false);
  const [isAddToCartDisabled, setIsAddToCartDisabled] = useState(true);

  const openOptionModal = (prevState) => {
    setOptionModalVisible(!prevState);
  };

  const { addToCart } = useAddToCart();

  const formRef = useRef();

  // Add function to handle the file upload

  // Item Upload State
  const [filesUploaded, setFilesUploaded] = useState([]);
  const [fileAttachments, setFilesAttachments] = useState({
    attachments: [],
    attachmentGroup: -1,
  });
  const [deleteingFile, setDeletingFile] = useState(new Set());
  const [uploadingFile, setUploadingFile] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const attachedFiles = useMemo(() => {
    process.env.NEXT_PUBLIC_DEBUG_MODE === "true" &&
      console.log("attached files: ", fileAttachments);

    let updatedAttachments = {
      attachments: [],
      attachmentGroup: -1,
    };

    if (fileAttachments.attachments.length !== 0) {
      updatedAttachments = fileAttachments;
    }

    return updatedAttachments;
  }, [fileAttachments]);

  const onDrop = useCallback(
    async (acceptedFiles) => {
      // Do something with the files
      console.log("Files Accepted: ", acceptedFiles);
      let fileData = [];
      if (filesUploaded.length !== 0) {
        // Always upload the last file in the array
        // Append each file to the formData
        let updatedFiles = [...filesUploaded];
        let updatedDropFiles = [...acceptedFiles];
        let updatedFileNames = updatedFiles.map((fil) => fil.name);
        let updatedDropFileNames = updatedDropFiles.map((fil) => fil.name);

        console.log("Current files uploaded: ", updatedFileNames);
        console.log("Current files being uploaded: ", updatedDropFileNames);

        // Check if the file already exists in the array
        let isDuplicate = updatedFileNames.some((name) =>
          updatedDropFileNames.includes(name),
        );

        console.log("❌ Is duplicate: ", isDuplicate);

        if (!isDuplicate) {
          let updatedAttachments = [...filesUploaded, ...acceptedFiles];
          updatedAttachments.forEach((file, i) => {
            if (!file?.id) {
              fileData.push(file);
            }
          });
        }
      } else {
        acceptedFiles.forEach((file, i) => {
          fileData.push(file);
        });
      }
      console.log("Files Uploaded: ", fileData);

      const formData = new FormData();
      formData.append("event", JSON.stringify(event.details));
      formData.append("booth", JSON.stringify(booth));
      formData.append("user", JSON.stringify(user));
      formData.append("slug", slug);
      formData.append("attachments", JSON.stringify(attachedFiles));

      Array.from(fileData).forEach((file, i) => {
        formData.append(`files`, file);
      });
      setUploadingFile(true);

      let requestOptions = {
        method: "POST",
        body: formData,
        onUploadProgress: (progressEvent) => {
          const percentage = (progressEvent.loaded * 100) / progressEvent.total;
          setUploadProgress(+percentage.toFixed(2));
        },
      };

      if (!formData.get("files")) {
        setUploadingFile(false);
        setUploadProgress(0);
        enqueueSnackbar(
          "File is a duplicate or currently uploading. Please try again.",
          {
            variant: "warning",
          },
        );
        return;
      }

      await axios
        .post(`/api/product/${slug}/upload`, formData, requestOptions)
        .then((res) => {
          console.log("POST Response:", res);
          if (res.status === 200) {
            enqueueSnackbar("Files uploaded successfully!", {
              variant: "success",
            });

            // Set the files to the state
            setFilesAttachments(res.data.upload);

            if (cartItem?.id) {
              let updatedItem = {
                ...cartItem,
                fileAttachments: res.data.upload,
              };
              const updatedCart = [...cart];
              const index = updatedCart.findIndex(
                (item) => item.id === cartItem.id,
              );
              updatedCart[index] = updatedItem;
              setCart(updatedCart);
            }

            setUploadingFile(false);
            setUploadProgress(0);
          } else {
            enqueueSnackbar("Files failed to upload!", {
              variant: "error",
            });
            setUploadingFile(false);
            setUploadProgress(0);
          }
        })
        .catch((err) => {
          console.log("POST ERROR:", err);
          enqueueSnackbar("Files failed to upload!", {
            variant: "error",
          });
          setUploadingFile(false);
          setUploadProgress(0);
        });
    },
    [
      attachedFiles,
      booth,
      enqueueSnackbar,
      event.details,
      filesUploaded,
      slug,
      user,
    ],
  );

  const {
    acceptedFiles,
    isDragActive,
    isDragAccept,
    isDragReject,
    getRootProps,
    getInputProps,
    fileRejections,
  } = useDropzone({
    accept: {
      "image/png": [".png"],
      "image/jpeg": [".jpg", ".jpeg"],
      "application/pdf": [".pdf"],
      "application/msword": [".doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [".docx"],
      "application/vnd.ms-excel": [".xls"],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
        ".xlsx",
      ],
      "model/gltf-binary": [".glb"],
      "model/gltf+json": [".gltf"],
    },
    onDrop,
    disabled: !isUpload || !event.details.id || uploadingFile,
  });

  useEffect(() => {
    if (acceptedFiles.length !== 0 && filesUploaded.length !== 0) {
      let updatedFiles = [...filesUploaded];
      let updatedDropFiles = [...acceptedFiles];
      let updatedFileNames = updatedFiles.map((fil) => fil.name);
      let updatedDropFileNames = updatedDropFiles.map((fil) => fil.name);

      console.log("Current files uploaded: ", updatedFileNames);
      console.log("Current files being uploaded: ", updatedDropFileNames);

      // Check if the file already exists in the array
      let isDuplicate = updatedFileNames.some((name) =>
        updatedDropFileNames.includes(name),
      );

      console.log("❌ Is duplicate: ", isDuplicate);

      if (!isDuplicate) {
        setFilesUploaded((prevState) => [...prevState, ...updatedDropFiles]);
      } else {
        return () => {
          setFilesUploaded((prevState) => updatedFiles);
          console.log("File is duplicate");
        };
      }
    } else if (acceptedFiles.length !== 0 && filesUploaded.length === 0) {
      setFilesUploaded(acceptedFiles);
    }

    return () => {
      console.log("File upload cleanup");
    };
  }, [acceptedFiles, slug]);

  const handleFileRemove = async (e, file) => {
    console.log("File removed: ", file);
    e.preventDefault();
    e.stopPropagation();
    let updatedFiles = [...filesUploaded];
    const index = updatedFiles.indexOf(file);

    // Remove the file from the array and network API
    let found = fileAttachments.attachments.find((f) => f.name === file.name);
    let attachmentGroup = fileAttachments.attachmentGroup;
    let updatedDeletedFiles = new Set(deleteingFile);
    updatedDeletedFiles.add(file.name);
    setDeletingFile(updatedDeletedFiles);
    console.log("Found file for deletion: ", found);

    if (found) {
      await axios
        .delete(`/api/product/${slug}/upload`, {
          params: {
            attachmentGroup,
            attachmentId: found.id,
            pid: slug,
          },
        })
        .then((res) => {
          console.log("DELETE Response:", res);
          if (res.status === 200) {
            enqueueSnackbar("File removed successfully!", {
              variant: "success",
            });

            // Reset the file upload attachments when deleting last file
            if (updatedFiles.length === 1) {
              updatedFiles.splice(index, 1);
              setFilesAttachments({
                attachments: [],
                attachmentGroup: -1,
              });
            } else {
              updatedFiles.splice(index, 1);
            }

            updatedDeletedFiles = new Set(deleteingFile);
            updatedDeletedFiles.delete(id);
            setDeletingFile(updatedDeletedFiles);
            setFilesUploaded(updatedFiles);

            // Delete file in cart item options while still in cart
            if (cartItem?.id) {
              let updatedItem = {
                ...cartItem,
                fileAttachments: res.data.upload,
              };
              const updatedCart = [...cart];
              const index = updatedCart.findIndex(
                (item) => item.id === cartItem.id,
              );
              updatedCart[index] = updatedItem;
              setCart(updatedCart);
            }
          } else {
            enqueueSnackbar("File failed to remove!", {
              variant: "error",
            });
          }
        })
        .catch((err) => {
          console.log("DELETE ERROR:", err);
          if (err?.error?.error?.code === "RCRD_DSNT_EXIST") {
            enqueueSnackbar("File failed to remove! Already has been deleted", {
              variant: "error",
            });
            setFilesUploaded([]);
          } else {
            enqueueSnackbar("File failed to remove!", {
              variant: "error",
            });
          }
        });
    } else {
      setFilesUploaded(updatedFiles);
    }
  };

  const files = filesUploaded.map((file, index) => (
    <ListItem disableGutters component="div" key={index}>
      <ListItemText primary={file.name} />
      <Divider />
      <ListItemSecondaryAction>
        <b>{file.size} bytes</b>
        <IconButton
          disabled={deleteingFile.size !== 0}
          onClick={(e) => handleFileRemove(e, file, index)}
        >
          {deleteingFile && deleteingFile.has(file.name) ? (
            <CircularProgress size={10} disableShrink />
          ) : (
            <CloseTwoTone />
          )}
        </IconButton>
      </ListItemSecondaryAction>
    </ListItem>
  ));

  const cartOptions = {
    detail: false,
    laborDateSelected,
    laborHoursTotal,
    laborSupervision,
    laborSupervisionCost,
    laborWorkerQuantity,
    laborStartTimeSelected,
    laborEndTimeSelected,
    laborBucketBuild,
    laborHourlyAvg,
    optionTotalSqFt,
    optionWidth,
    optionLength,
    color,
    size,
    material,
    orientation,
    screenSize,
    memoText,
    attributes,
    setAttributes,
    daysCalcDateSelected,
    quantity,
    validatedItem,
    validatingItem,
    cwtFreightWeightEntered,
    fileAttachments,
    // functions
    setQuantity,
    setDaysCalcDateSelected,
    setOptionWidth,
    setOptionLength,
    setOptionTotalSqFt,
    setColor,
    setSize,
    setMaterial,
    setScreenSize,
    setOrientation,
    setCwtFreightWeight,
    setCwtFreightWeightEntered,
    setLaborTableOpen,
    setLaborBucketBuild,
    setLaborDateSelected,
    setLaborCostTotal,
    setLaborHoursTotal,
    setLaborHourlyAvg,
    setLaborWorkerQuantity,
    setLaborSupervision,
    setLaborSupervisionCost,
    setLaborStartTimeSelected,
    setLaborEndTimeSelected,
    openOptionModal,
    setColorError,
    setSizeError,
    setMaterialError,
    setOrientationError,
    setMemoText,
    colorError,
    sizeError,
    materialError,
    orientationError,
    setFilesUploaded,
    setFilesAttachments,
  };

  const handleSubmit = (e) => {
    console.log("formRef?.current", formRef?.current);
    let submittedAttributes = formRef?.current?.values;

    console.log("HANDLESUBMITE");
    formRef?.current.validateForm().then((res) => {
      formRef?.current?.submitForm().then((ret) => {
        if (Object.keys(res).length === 0) {
          cartOptions.attributes = submittedAttributes;
          formRef?.current.resetForm();
          addToCart(e, "", itemProps, cartOptions).then(() => {
            // Reset the button state after adding to cart
            setIsAddToCartDisabled(true);
            setValidatedItem({ childId: "", price: null });
          });
        }
      });
    });
  };

  const quantityChange = (value) => {
    const minQty = Number(minimumQuantity) || 1;
    const maxQty = Number(maxQuantity) || Infinity;
    let updatedQuantity = value;

    const setItemQty = (item, qty) => setItemQuantity(item, Number(qty));
    const deleteItem = (item) => {
      return confirm("Are you sure you want to delete this item?")
        ? removeCartItem(item)
        : setItemQty(item, minQty);
    };

    // Check if the updated quantity exceeds the maximum quantity
    if (updatedQuantity > maxQty) {
      console.log(
        `Cannot set quantity to ${updatedQuantity}. Maximum quantity allowed is ${maxQty}.`,
      );
      updatedQuantity = maxQty;
    }

    // Check if enforceMinQty is enabled and the updated quantity drops below the minimum quantity
    if (enforceMinQty && updatedQuantity < minQty) {
      console.log(
        `Cannot set quantity to ${updatedQuantity}. Minimum quantity required is ${minQty}. Removing item from cart.`,
      );
      // Remove the item from the cart or perform any necessary action
      deleteItem(cartItem);
      return;
    }

    if (!value) {
      setItemQty(cartItem, minQty);
    } else if (value > 0) {
      setItemQty(cartItem, updatedQuantity);
    } else if (!enforceMinQty && value <= 0) {
      deleteItem(cartItem);
    }
  };

  let hasVariants = useMemo(() => [
    { exists: hasColorOptions, name: "color" },
    { exists: hasOrientOptions, name: "orientations" },
    { exists: hasGraphicOptions, name: "materials" },
    { exists: hasSizeOptions, name: "sizes" },
  ], [hasColorOptions, hasOrientOptions, hasGraphicOptions, hasSizeOptions]);

  let variantOptions = hasVariants.filter((v) => v.exists);

  const toggleIsFavorite = () => setIsFavorite((fav) => !fav);
  const toggleDialog = useCallback(() => setOpenModal((open) => !open), []);
  
  const cartItem =
    (Array.isArray(cart) &&
      cart.find((item) => {
        if (variantOptions.length !== 0 && !isDaysCalc && !isSquareFt) {
          return (
            (item.internalid === id ||
              item.internalid === validatedItem.childId) &&
            item.variant.color === color &&
            item.variant.sizes === size &&
            item.variant.materials === material &&
            item.variant.orientations === orientation
          );
        }

        if (isSquareFt) {
          const sameDimensions =
            item.squareFtWidth === optionWidth &&
            item.squareFtLength === optionLength;

          if (isShowDuration) {
            return (
              item.internalid === id &&
              showDates
                .map((dt) => dt?.values?.custrecord_date)
                .includes(item.showDate) &&
              sameDimensions
            );
          }

          if (isDaysCalc) {
            return (
              item.internalid === id &&
              item.showDate === daysCalcDateSelected?.values?.custrecord_date &&
              sameDimensions
            );
          }
        }

        if (isMemoItem && (!isDaysCalc || !isShowDuration)) {
          return item.internalid === id && item.memoText === memoText;
        }

        if (isDaysCalc && isMemoItem) {
          return (
            item.internalid === id &&
            item.showDate === daysCalcDateSelected?.values?.custrecord_date &&
            item.memoText === memoText
          );
        }

        if (isShowDuration && isMemoItem) {
          return (
            item.internalid === id &&
            showDates
              .map((dt) => dt?.values?.custrecord_date)
              .includes(item.showDate) &&
            item.memoText === memoText
          );
        }

        if (isDaysCalc) {
          return (
            item.internalid === id &&
            item.showDate === daysCalcDateSelected?.values?.custrecord_date
          );
        }

        return item.internalid === id;
      })) ||
    {};

  const prevCartItem = usePrevious(cartItem);

  useEffect(() => {
    if (prevCartItem?.id !== cartItem?.id) {
      setFilesUploaded([]);
      setFilesAttachments({
        attachments: [],
        attachmentGroup: -1,
      });
    }
  }, [cartItem?.id, prevCartItem]);

  // File upload Starter Function
  useEffect(() => {
    let active = true;

    if (
      active &&
      cartItem?.id &&
      (!Object.prototype.hasOwnProperty.call(cartItem, "fileAttachments") ||
        cartItem?.fileAttachments?.attachments.length === 0)
    ) {
      console.log("Cart Item set to file attachments: ", fileAttachments);
      setFilesUploaded([]);
      setFilesAttachments({
        attachments: [],
        attachmentGroup: -1,
      });
    }

    if (active && cartItem?.fileAttachments) {
      setFilesAttachments(cartItem.fileAttachments);
      setFilesUploaded(cartItem.fileAttachments?.attachments);
    }

    return () => {
      active = false;
    };
  }, [cartItem, slug, optionModalVisible]);

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  // SqFt Calc
  useEffect(() => {
    let active = true;

    function getTotalSqFt() {
      if (active && isMounted) {
        let squareFoot = optionWidth * optionLength;
        setOptionTotalSqFt(squareFoot);
      }
    }

    getTotalSqFt();

    return () => {
      active = false;
    };
  }, [optionLength, optionWidth]);

  // Run validation on last item option selected to get child id of item prior to adding to cart.
  useEffect(() => {
    let active = true;

    if (isMounted.current && active) {
      if (color || size || material || screenSize || orientation) {
        handleItemPrefetchValidation(active).then((r) => {
          console.log("🟢 Prefetch finished", r);
        });
      }
    }

    return () => {
      active = false;
    };
  }, [color, size, material, screenSize, orientation]);

  useEffect(() => {
    let active = true;
    let weightMinimum =
      Number(event?.details.rates.freight.minimum) ||
      Number(settings.custrecord_ng_cs_freight_minimum);

    function getFreightItemQuantity() {
      let freightQuantity = Math.ceil(cwtFreightWeight / 100);

      if (active && isMounted) {
        if (isFreight && settings?.custrecord_ng_cs_freight_minimum) {
          // If the weight is exactly 100, set quantity to 1
          if (cwtFreightWeight === 100) {
            setQuantity(1);
          }
          // If the current quantity is less than the calculated freight quantity, update it
          else if (quantity < freightQuantity) {
            setQuantity(freightQuantity);
          }
          // If the weight is valid and greater than the minimum weight, update the quantity
          else if (
            !Number.isNaN(cwtFreightWeight) &&
            cwtFreightWeight > weightMinimum
          ) {
            setQuantity(freightQuantity);
          }
          // If the weight is valid but less than the minimum weight, set the minimum quantity
          else if (
            !Number.isNaN(cwtFreightWeight) &&
            cwtFreightWeight < weightMinimum
          ) {
            setQuantity(Math.ceil(parseInt(freightMinimum) / 100));
          }
        }
      }
    }

    getFreightItemQuantity();
  }, [cwtFreightWeight, isFreight]);

  let roundOff = (num, places) => {
    const x = 10 ** places;
    return Math.round(num * x) / x;
  };

  function createData(
    id,
    type,
    date,
    workerQuantity,
    hours,
    start,
    end,
    rate,
    totalLabor,
    supervision,
    supervisionCost,
    amount,
  ) {
    return {
      id,
      type,
      date,
      workerQuantity,
      hours,
      start,
      end,
      rate,
      totalLabor,
      supervision,
      supervisionCost,
      amount,
    };
  }

  const itemProps = useMemo(
    () => ({
      ...item,
      collection: {
        name: item.category,
        id: item.collection,
      },
      internalId: validatedItem.childId || item.id,
      image: item.image || "/assets/images/products/NO-PRODUCT-IMAGE.jpg",
      ...(validatedItem.childId && {
        parentProductId: item.id,
      }),
    }),
    [item, validatedItem.childId],
  );

  // Update modal state on close - resetting validation
  useEffect(() => {
    if (validatedItem.childId && validatedItem.price) {
      setIsAddToCartDisabled(false);
    } else if (variantOptions.length === 0 && itemAttributes.length === 0) {
      // Enable button if there are no variants or attributes to validate
      setIsAddToCartDisabled(false);
    } else {
      setIsAddToCartDisabled(true);
    }
  }, [validatedItem, variantOptions.length, itemAttributes.length]);

  // Labor item field detect and hourly handler
  useEffect(() => {
    const calculateLabor = () => {
      let allLaborCostPrices = [];
      let laborAvgArr = [];
      let supervisionCostPerBucket = [];

      const reducer = (accumulator, currentValue) => accumulator + currentValue;
      // Retrieve the rate based on the labor date, start time, and end time
      if (laborDateSelected && laborStartTimeSelected && laborEndTimeSelected) {
        if (laborSchedule && laborSchedule.length !== 0) {
          const laborDate = laborDateSelected.date;
          const rates = getRatesByDate(laborDate);
          let totalHourlyDifference = calculateHours(
            moment(laborStartTimeSelected),
            moment(laborEndTimeSelected),
          ); // Total labor hours set
          console.log("Hours span: ", totalHourlyDifference);
          console.log("Found rates: ", rates);

          const missingTimeSlots = findMissingTimeSlots(rates);
          if (missingTimeSlots.length !== 0)
            console.log(
              `%cTime slots missing for ${laborDate}!`,
              "color: red; font-size: 3rem; padding: 1rem; border: 1px dashed cyan;",
              missingTimeSlots,
            );

          // Grabbed dated from labor schedule to get modifying price data.
          let startTimeString = moment(laborStartTimeSelected).format(
            "hh:mm a",
          );
          let endTimeString = moment(laborEndTimeSelected).format("hh:mm a");
          let startTimeMilitaryString = moment(laborStartTimeSelected).format(
            "HH:mm",
          );
          let endTimeMilitaryString =
            moment(laborEndTimeSelected).format("HH:mm");

          const ratesWithinTimeWindow = getEventsWithinTimeWindow(
            rates,
            startTimeMilitaryString,
            endTimeMilitaryString,
          );
          let laborHoursSum = 0;

          // Build the labor buckets
          let startTime = moment(laborStartTimeSelected);
          let endTime = moment(laborEndTimeSelected);

          if (startTime.isAfter(endTime)) {
            alert(
              "Start time cannot be after end time. Please adjust your start time.",
            );
            setLaborStartTimeSelected(new Date(new Date().setHours(8, 0)));
            setLaborEndTimeSelected(new Date(new Date().setHours(17, 0)));
            return;
          }

          if (ratesWithinTimeWindow.length !== 0) {
            ratesWithinTimeWindow.forEach((rate) => {
              let multiplier = parseFloat(rate.multiplier);
              let supervisorMarkup = parseFloat(rate.supervision) / 100;
              let laborRate = multiplier * price * laborWorkerQuantity;
              let laborType = rate.type;
              let totalHoursApplied = rate.durationMinutes / 60;
              let totalLaborCost = laborRate * totalHoursApplied;
              let supervisorCost =
                laborRate * totalHoursApplied * supervisorMarkup;
              let amountCharged = laborSupervision
                ? totalLaborCost + supervisorCost
                : totalLaborCost;

              console.log(
                "Bucket check:",
                `
								${startTimeString} Start time picked,
								${endTimeString} End Time picked,
								${rate.start} Start,
								${rate.end} End,
								${multiplier} Multiplier,
								${supervisorMarkup} markup,
								${laborType}
								`,
              );
              // Push to average array
              laborAvgArr.push(totalLaborCost);

              // Push to supervision cost array
              supervisionCostPerBucket.push(supervisorCost);

              // Push to labor bucket
              setLaborBucketBuild((prevState) => [
                ...prevState,
                createData(
                  rate.id,
                  laborType,
                  laborDate,
                  laborWorkerQuantity,
                  roundOff(totalHoursApplied, 2),
                  `${moment(rate.start, "HH:mm").format("hh:mm a")}`,
                  `${moment(rate.end, "HH:mm").format("hh:mm a")}`,
                  formatter.format(laborRate),
                  formatter.format(totalLaborCost),
                  laborSupervision ? `Yes @ ${rate.supervision}` : "No",
                  laborSupervision ? formatter.format(supervisorCost) : "None",
                  formatter.format(amountCharged),
                ),
              ]);

              allLaborCostPrices.push(amountCharged);
              laborHoursSum += totalHoursApplied;
            });

            setLaborHoursTotal(laborHoursSum);
          }

          if (allLaborCostPrices.length !== 0) {
            let totalSupervisorCost = laborSupervision
              ? supervisionCostPerBucket.reduce(reducer)
              : 0;
            let allHourlyCost = laborAvgArr.reduce(reducer);
            let avgRate = roundOff(allHourlyCost / totalHourlyDifference, 2);
            let totalLaborCost = avgRate * totalHourlyDifference;

            setLaborCostTotal(
              formatter.format(totalLaborCost + totalSupervisorCost),
            );
          }
          if (laborAvgArr.length !== 0) {
            let allHourlyCost = laborAvgArr.reduce(reducer);
            let totalSupervisorCost = supervisionCostPerBucket.reduce(reducer);
            let avgRate = roundOff(allHourlyCost / totalHourlyDifference, 2);
            console.log("avgRate: ", roundOff(avgRate, 2));
            setLaborHourlyAvg(avgRate);
            setLaborBareCostTotal(avgRate * totalHourlyDifference);
            // let supervisorCost = (avgRate * totalHourlyDifference) * parseFloat(settings.custrecord_ng_cs_default_sprvisor_markup || 0) / 100
            setLaborSupervisionCost(totalSupervisorCost);
            console.log(
              "supervisor cost from buckets",
              roundOff(totalSupervisorCost, 2),
            );
          }
          console.log("Rates within time window: ", ratesWithinTimeWindow);
        } else {
          // No rates to find
        }
      }
    };

    // Reset the labor preview table on any change to the time selection.
    if (
      prevStartSelected !== laborStartTimeSelected ||
      prevEndSelected !== laborEndTimeSelected ||
      prevSupervisionSelected !== laborSupervision ||
      prevWorkerQuantity !== laborWorkerQuantity ||
      prevSelectedLaborDate !== laborDateSelected
    ) {
      setLaborBucketBuild([]);
    }

    calculateLabor();
  }, [
    laborDateSelected,
    laborEndTimeSelected,
    laborStartTimeSelected,
    laborSupervision,
    laborWorkerQuantity,
  ]);

  function getEventsWithinTimeWindow(events, startTime, endTime) {
    const dayStart = moment(startTime, "HH:mm");
    const dayEnd = moment(endTime, "HH:mm");
    const dayDuration = moment.duration(dayEnd.diff(dayStart));

    const dayEvents = [];
    let currentEventStart = dayStart.clone();
    let lastEvent = null;

    for (let i = 0; i < events.length; i++) {
      const eventStart = moment(events[i].start, "HH:mm");
      const eventEnd = moment(events[i].end, "HH:mm");
      const eventDuration = moment.duration(eventEnd.diff(eventStart));

      if (eventStart.isBefore(dayEnd) && eventEnd.isAfter(dayStart)) {
        const start = moment.max(eventStart, dayStart);
        const end = moment.min(eventEnd, dayEnd);
        const duration = moment.duration(end.diff(start));

        dayEvents.push({
          id: events[i].id,
          start: start.format("HH:mm"),
          end: end.format("HH:mm"),
          duration: `${duration.asMinutes()} minutes`,
          durationMinutes: duration.asMinutes(),
          multiplier: events[i].multiplier,
          type: events[i].type,
          supervision: events[i].supervision,
        });

        currentEventStart = eventEnd;
        lastEvent = events[i];
      }
    }

    if (currentEventStart.isBefore(dayEnd)) {
      const duration = moment.duration(dayEnd.diff(currentEventStart));

      dayEvents.push({
        id: lastEvent.id,
        start: currentEventStart.format("HH:mm"),
        end: dayEnd.format("HH:mm"),
        duration: `${duration.asMinutes()} minutes`,
        durationMinutes: duration.asMinutes(),
        multiplier: lastEvent.multiplier,
        type: lastEvent.type,
        supervision: lastEvent.supervision,
      });
    }

    return dayEvents;
  }

  function handleLaborTimeDisabled(date, view) {
    let timeDisabled = false;
    // look at current labor date to fetch the labor schedule
    const laborDate = laborDateSelected?.values?.custrecord_date;
    const timeChosen = moment(date).format("HH:mm");
    if (date && laborDate) {
      // Check for rates on the labor date
      const rates = getRatesByDate(laborDate);
      const missingTimeSlots = findMissingTimeSlots(rates);
      if (missingTimeSlots.length !== 0) {
        missingTimeSlots.forEach((slot) => {
          if (
            moment(timeChosen, "HH:mm").isBetween(
              moment(slot.start, "HH:mm"),
              moment(slot.end, "HH:mm"),
            )
          ) {
            timeDisabled = true;
          }
        });
      } else {
        timeDisabled = false;
      }
    }

    return timeDisabled;
  }

  function findMissingTimeSlots(timeSlots) {
    const startTime = moment("00:00", "HH:mm");
    const endTime = moment("23:59", "HH:mm");
    const missingSlots = [];

    // Sort the time slots based on start time
    timeSlots.sort((a, b) => {
      return moment(a.start, "HH:mm").diff(moment(b.start, "HH:mm"));
    });

    let previousEndTime = startTime;
    // Iterate over the sorted time slots
    for (let i = 0; i < timeSlots.length; i++) {
      const currentSlot = timeSlots[i];
      const currentStartTime = moment(currentSlot.start, "HH:mm");
      const currentEndTime = moment(currentSlot.end, "HH:mm");

      // Check if there is a gap between the previous end time and current start time
      if (previousEndTime.isBefore(currentStartTime)) {
        const missingSlot = {
          start: previousEndTime.format("HH:mm"),
          end: currentStartTime.format("HH:mm"),
        };

        missingSlots.push(missingSlot);
      }

      // Update the previous end time
      if (currentEndTime.isAfter(previousEndTime)) {
        previousEndTime = currentEndTime;
      }
    }

    // Check if there is a gap between the last time slot's end time and the day's end time
    if (previousEndTime.isBefore(endTime)) {
      const missingSlot = {
        start: previousEndTime.format("HH:mm"),
        end: endTime.format("HH:mm"),
      };

      missingSlots.push(missingSlot);
    }

    return missingSlots;
  }

  /**
   *  Function to retrieve the rate(s) based on the labor dates format (YYYY-MM-DD)
   *
   * @param {string} date - Date string from on change event
   * @returns {Array<Object>} foundPriceDates
   * */
  const getRatesByDate = useCallback(
    /**
     * @param {string} date
     * @returns {Array<Object>} foundPriceDates
     * */
    (date) => {
      // Implement your logic to retrieve the rate based on the labor date, start time, and end time
      // You can use an array or an API call to fetch the rates for different dates and times
      // For simplicity, let's assume we have a fixed rate of $10 per hour for all dates and times
      // console.log("Rate args:", date);
      let foundPriceDates = laborSchedule.filter((laborDate) => {
        // console.log('Filtering: ', new Date(laborDate.values.custrecord_ng_cs_labor_date).toISOString())
        // console.log('Selected: ', selectedDate)
        return moment(moment(date).format("yyyy-MM-DD")).isSame(laborDate.date);
      });

      return foundPriceDates || [];
    },
    [laborSchedule],
  );

  /**
   * Function to calculate the labor hours based on the start and end time
   * @param {Moment} startTime
   * @param {Moment} endTime
   * */
  const calculateHours = (startTime, endTime) => {
    // Implement your logic to calculate the labor hours based on the start and end time
    // Calculate the duration in milliseconds
    const duration = endTime.diff(startTime);

    // Convert the duration to hours
    const hours = duration / (1000 * 60 * 60);

    // Return the duration in decimal form
    return hours;
  };

  // Opening of description accordion
  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  // Quantity button increment handlers
  const increaseQuantity = () => {
    let nextQuantity = quantity + 1;
    setQuantity(nextQuantity);
  };

  const decreaseQuantity = () => {
    let nextQuantity = quantity - 1;
    if (quantity > 1) {
      setQuantity(nextQuantity);
    }
  };

  const decreaseExistingQuantity = () => {
    const minQty = Number(minimumQuantity) || 1;
    console.log("Enforce Min Qty: ", enforceMinQty);
    if (enforceMinQty && cartItem.quantity > minQty) {
      if (cartItem.isShowDuration) {
        console.log("Show Duration item: ", cartItem);
        console.log("Current Item: ", item);

        cart
          .filter(
            (el) => el.isShowDuration && Number(el.internalid) === Number(id),
          )
          .forEach((existingItem) => {
            if (existingItem.quantity > 1) {
              setItemQuantity(existingItem, existingItem.quantity - 1);
            }
          });
      } else {
        setItemQuantity(cartItem, cartItem.quantity - 1);
      }

      enqueueSnackbar(`Removed a "${cartItem.name}" from cart`, {
        variant: "error",
      });
    } else if (enforceMinQty && cartItem.quantity <= minQty) {
      if (cartItem.isShowDuration) {
        console.log("Show Duration item: ", cartItem);
        console.log("Current Item: ", item);
        if (cartItem.isUpload) {
          setFilesUploaded([]);
          setFilesAttachments({
            attachments: [],
            attachmentGroup: -1,
          });
        }
        cart
          .filter((el) => el.isShowDuration && el.internalid === id)
          .forEach((existingItem) => {
            if (existingItem.quantity <= 1) {
              removeCartItem(existingItem);
            }
          });
      } else {
        if (cartItem.isUpload) {
          setFilesUploaded([]);
          setFilesAttachments({
            attachments: [],
            attachmentGroup: -1,
          });
        }
        removeCartItem(cartItem);
      }

      enqueueSnackbar(`Removed "${cartItem.name}" from cart`, {
        variant: "error",
      });
    } else if (cartItem.quantity > 1) {
      console.log("Quantity is 1: ", cartItem);
      if (cartItem.isShowDuration) {
        console.log("Show Duration item: ", cartItem);
        console.log("Current Item: ", item);

        cart
          .filter(
            (el) => el.isShowDuration && Number(el.internalid) === Number(id),
          )
          .forEach((existingItem) => {
            if (existingItem.quantity > 1) {
              setItemQuantity(existingItem, existingItem.quantity - 1);
            }
          });
      } else {
        setItemQuantity(cartItem, cartItem.quantity - 1);
      }

      enqueueSnackbar(`Removed a "${cartItem.name}" from cart`, {
        variant: "error",
      });
    } else if (cartItem.quantity <= 1) {
      console.log("Quantity is 1: ", cartItem);
      if (cartItem.isShowDuration) {
        console.log("Show Duration item: ", cartItem);
        console.log("Current Item: ", item);
        if (cartItem.isUpload) {
          setFilesUploaded([]);
          setFilesAttachments({
            attachments: [],
            attachmentGroup: -1,
          });
        }
        cart
          .filter((el) => el.isShowDuration && el.internalid === id)
          .forEach((existingItem) => {
            if (existingItem.quantity <= 1) {
              removeCartItem(existingItem);
            }
          });
      } else {
        removeCartItem(cartItem);
        if (cartItem.isUpload) {
          setFilesUploaded([]);
          setFilesAttachments({
            attachments: [],
            attachmentGroup: -1,
          });
        }
      }

      enqueueSnackbar(`Removed "${cartItem.name}" from cart`, {
        variant: "error",
      });
    }
  };

  // Sq Ft change field handling
  const handleLengthChange = (e) => {
    console.log("Length: ", optionLength, `"${e.target.value}"`);
    if (!Number.isNaN(parseInt(e.target.value))) {
      setOptionLength(parseInt(e.target.value));
    } else {
      setOptionLength("");
    }
  };

  const handleLengthCapture = (e) => {
    console.log("Length Capture: ", optionLength, `"${e.target.value}"`);

    const cleanedInput = e.target.value.replace(/,/g, "");

    // Parse the cleaned input into a number
    const parsedNumber = parseFloat(cleanedInput);

    console.log(
      "Length Capture is NaN: ",
      Number.isNaN(parsedNumber),
      `"${parsedNumber}"`,
    );
    if (Number.isNaN(parsedNumber) || e.target.value === "") {
      // console.log('Quantity Capture is NaN OR empty: ', Number.isNaN(e.target.value), `"${e.target.value}"`)
      setOptionLength(1);
    } else {
      setOptionLength(parsedNumber);
    }
  };

  const handleWidthChange = (e) => {
    console.log("Width: ", optionWidth, `"${e.target.value}"`);
    if (!Number.isNaN(parseInt(e.target.value))) {
      setOptionWidth(parseInt(e.target.value));
    } else {
      setOptionWidth("");
    }
  };

  const handleWidthCapture = (e) => {
    console.log("Width Capture: ", optionWidth, `"${e.target.value}"`);

    const cleanedInput = e.target.value.replace(/,/g, "");

    // Parse the cleaned input into a number
    const parsedNumber = parseFloat(cleanedInput);

    console.log(
      "Width Capture is NaN: ",
      Number.isNaN(parsedNumber),
      `"${parsedNumber}"`,
    );
    if (Number.isNaN(parsedNumber) || e.target.value === "") {
      // console.log('Quantity Capture is NaN OR empty: ', Number.isNaN(e.target.value), `"${e.target.value}"`)
      setOptionWidth(1);
    } else {
      setOptionWidth(parsedNumber);
    }
  };

  const handleTotalFootChange = (e) => {
    console.log("Total Ft: ", optionTotalSqFt, `"${e.target.value}"`);
    if (!Number.isNaN(parseInt(e.target.value))) {
      setOptionTotalSqFt(parseInt(e.target.value));
    } else {
      setOptionTotalSqFt("");
    }
  };

  const handleTotalFootCapture = (e) => {
    console.log("Total Ft Capture: ", optionTotalSqFt, `"${e.target.value}"`);
    // Remove commas and 'Ft²' suffix from input
    const cleanedInput = e.target.value.replace(/,/g, "").replace("Ft²", "");

    // Parse the cleaned input into a number
    const parsedNumber = parseFloat(cleanedInput);

    console.log(
      "Total Ft Capture is NaN: ",
      Number.isNaN(parsedNumber),
      `"${parsedNumber}"`,
    );
    if (Number.isNaN(parsedNumber) || e.target.value === "") {
      // console.log('Quantity Capture is NaN OR empty: ', Number.isNaN(e.target.value), `"${e.target.value}"`)
      setOptionTotalSqFt(1);
    } else {
      setOptionTotalSqFt(parsedNumber);
    }
  };

  // Labor Worker change handler
  const handleWorkerQuantityChange = (e) => {
    console.log("Worker #: ", laborWorkerQuantity, `"${e.target.value}"`);
    if (!Number.isNaN(parseInt(e.target.value))) {
      setLaborWorkerQuantity(parseInt(e.target.value));
    } else {
      setLaborWorkerQuantity("");
    }
  };

  const handleWorkerQuantityCapture = (e) => {
    console.log("Worker #: ", laborWorkerQuantity, `"${e.target.value}"`);
    console.log(
      "Worker # is NaN: ",
      Number.isNaN(e.target.value),
      `"${e.target.value}"`,
    );
    if (Number.isNaN(e.target.value) || e.target.value === "") {
      // console.log('Quantity Capture is NaN OR empty: ', Number.isNaN(e.target.value), `"${e.target.value}"`)
      setLaborWorkerQuantity(1);
    } else {
      setLaborWorkerQuantity(parseInt(e.target.value));
    }
  };

  // Regular & matrix item change handlers
  const handleQuantityChange = (e) => {
    console.log("Quantity: ", quantity, `"${e.target.value}"`);
    if (!Number.isNaN(parseInt(e.target.value))) {
      setQuantity(parseInt(e.target.value));
    } else {
      setQuantity("");
    }
  };

  const handleQuantityCapture = (e) => {
    console.log("Quantity Capture: ", quantity, `"${e.target.value}"`);
    console.log(
      "Quantity Capture is NaN: ",
      Number.isNaN(e.target.value),
      `"${e.target.value}"`,
    );
    if (Number.isNaN(e.target.value) || e.target.value === "") {
      // console.log('Quantity Capture is NaN OR empty: ', Number.isNaN(e.target.value), `"${e.target.value}"`)
      setQuantity(1);
    } else {
      setQuantity(parseInt(e.target.value));
    }
  };

  // CWT Fright item change handler
  const handleFreightWeightChange = (e) => {
    console.log("Weight: ", cwtFreightWeight, `"${e.target.value}"`);
    // eslint-disable-files no-restricted-globals
    if (!Number.isNaN(parseInt(e.target.value))) {
      setCwtFreightWeight(parseInt(e.target.value));
    } else {
      setCwtFreightWeight("");
    }
  };

  const handleFreightWeightCapture = (e) => {
    console.log("Weight Capture: ", quantity, `"${e.target.value}"`);

    const cleanedInput = e.target.value.replace(/,/g, "").replace("WT ", "");

    // Parse the cleaned input into a number
    const parsedNumber = parseFloat(cleanedInput);

    // eslint-disable-next-line no-restricted-globals
    console.log(
      "Weight Capture is NaN?: ",
      Number.isNaN(parsedNumber),
      `"${parsedNumber}"`,
    );
    let weightMinimum = null;
    weightMinimum = Number(props.freightMinimum);
    let weightMaximum = Number(freightMaximum) || Infinity;

    if (!settings.custrecord_enable_material_handling_beta) {
      weightMinimum =
        Number(event?.details.rates.freight.minimum) ||
        Number(settings.custrecord_ng_cs_freight_minimum);
      weightMaximum = Infinity;
    }

    if (!Number.isNaN(parsedNumber)) {
      if (parsedNumber > weightMaximum) {
        enqueueSnackbar(
          `Maximum weight exceeded must be below ${weightMaximum}. Resetting to maximum.`,
          {
            variant: "warning",
            autoHideDuration: 3000,
          },
        );

        setCwtFreightWeightEntered(weightMaximum);
        setCwtFreightWeight(Math.ceil(weightMaximum / 100) * 100);
      } else if (parsedNumber < weightMinimum) {
        console.log("Weight min not met:", { parsedNumber, weightMinimum });
        enqueueSnackbar(
          `Minimum weight must be above ${weightMinimum}. Resetting to minimum.`,
          {
            variant: "warning",
            autoHideDuration: 3000,
          },
        );
        setCwtFreightWeight(Math.ceil(weightMinimum / 100) * 100);
        setCwtFreightWeightEntered(weightMinimum);
      } else {
        setCwtFreightWeight(Math.ceil(parsedNumber / 100) * 100);
        setCwtFreightWeightEntered(parsedNumber);
      }
    } else {
      setCwtFreightWeight(Math.ceil(weightMinimum / 100) * 100);
      setCwtFreightWeightEntered(weightMinimum);
    }
  };

  const handleSizeChange = (e, size) => {
    setSize(e.target.value);
    setNetSize(size);
  };

  const handleColorChange = (e, color) => {
    setColor(e.target.value);
    setNetColor(color);
  };

  const handleMaterialChange = (e, material) => {
    setMaterial(e.target.value);
    setNetMaterial(material);
  };

  const handleOrientationChange = (e, size) => {
    setOrientation(e.target.value);
    setNetOrientations(size);
  };
  // Change handlers end

  const handleCartAmountChange = (product, type) => () => {
    dispatch({
      type: "CHANGE_CART_AMOUNT",
      payload: product,
    });
    // SHOW ALERT PRODUCT ADDED OR REMOVE
    if (type === "remove")
      enqueueSnackbar("Remove from Cart", {
        variant: "error",
      });
    else
      enqueueSnackbar("Added to Cart", {
        variant: "success",
      });
  };

  // Item validation against created payload including the value & text.
  const checkNetsuiteCartItem = async () => {
    if (!validatingItem) {
      // eslint-disable-next-line no-inner-declarations
      async function runVariantCheck(id) {
        let chosenVariants = {
          color: netColor,
          size: netSize,
          orientation: netOrientation,
          material: netMaterial,
          screenSize: netScreenSize,
        };

        let netPayload = {
          parentId: id,
          variantsChosen: chosenVariants,
          eventId: event?.details.id,
        };

        let raw = JSON.stringify(netPayload);

        let requestOptions = {
          method: "POST",
          redirect: "follow",
          body: raw,
        };

        let res = "";

        setValidatingItem(true);
        await fetch(`/api/customer/cart/item_check`, requestOptions)
          .then((res) => res.json())
          .then((resJson) => {
            console.log("POST Response:", resJson);
            res = resJson;
            setValidatingItem(false);
            return resJson;
          })
          .catch((err) => {
            console.log("POST ERROR:", err);
            res = err;
            setValidatingItem(false);
            return err;
          });
        return res;
      }

      // eslint-disable-next-line no-return-await
      return await runVariantCheck(id);
    }
    return {
      error: {
        name: "Spam Detect",
        message:
          "Add item spam detected, your item was still being validated. Items are not added to the cart at this time. Please try again after it finished.",
      },
    };
  };

  // eslint-disable-next-line consistent-return
  const handleItemPrefetchValidation = async (active) => {
    console.log("⚡ Running prefetch...");

    let inputCheck = [color, size, orientation, material];
    let variantsChecking = ["color", "materials", "sizes", "orientations"];
    const buildVariantCheckObjArr = (checkingArr) => {
      if (Array.isArray(checkingArr)) {
        return checkingArr.map((variantName) => {
          switch (variantName) {
            case "color":
              return {
                name: variantName,
                value: color,
              };
            case "materials":
              return {
                name: variantName,
                value: material,
              };
            case "orientations":
              return {
                name: variantName,
                value: orientation,
              };
            case "sizes":
              return {
                name: variantName,
                value: size,
              };
            default:
              return null;
          }
        });
      }

      return "Need an array of variant names to check when items get added to cart have variants.";
    };
    const buildVariantNameList = () => {
      return buildVariantCheckObjArr(variantsChecking).filter((v) => v.value);
    };

    console.log("Variant name list item check", buildVariantNameList());

    if (variantOptions.length !== 0 && active) {
      if (buildVariantNameList().length !== variantOptions.length) {
        console.log("Not All Variants are filled.");
      } else {
        // eslint-disable-next-line no-return-await
        return await checkNetsuiteCartItem()
          .then((res) => {
            console.log("Then");
            if (res) {
              if (Object.keys(res).includes("error")) {
                if (res.error.name === "Spam Detect") {
                  enqueueSnackbar(res.error.message, {
                    variant: "error",
                    anchorOrigin: {
                      vertical: "top",
                      horizontal: "center",
                    },
                  });
                } else {
                  enqueueSnackbar(
                    "There was a problem with this item. Didn't get added to your cart",
                    {
                      variant: "error",
                      anchorOrigin: {
                        vertical: "top",
                        horizontal: "center",
                      },
                    },
                  );
                }
                console.error("Cart validation error", res);
                return;
              }
              if (
                res.message === "Item not a matrix type." &&
                inputCheck.includes("")
              ) {
                console.error("Cart validation error", res);
                setColor("");
                setSize("");
                setMaterial("");
                setOrientation("");
                setScreenSize("");
                return;
              }
              console.log("Item prevalidated: ", res);
              setValidatedItem(res);

              console.log("Item validated");
            } else {
              // Do logic if nothing comes back.
            }
          })
          .catch((err) => {
            console.error("A function error occurred: ", err);
          })
          .finally(() => {
            console.log("Check state: ", validatedItem);
          });
      }
    } else {
      return "Prefetch not ready yet...";
    }
  };

  const renderQuantityPicker = () => {
    let quantityPicker = (
      <>
        <Box
          color="text.primary"
          fontWeight="600"
          sx={{ width: 20, flexDirection: "column" }}
        >
          <InlineQuantityEditField
            onCard
            initialValue={cartItem?.quantity}
            onClickAway={quantityChange}
            disabled={isLabor || isFreight}
            textStyle={{ fontSize: "0.875rem", textAlign: "center", py: 1 }}
            inputStyle={{
              fontSize: "0.875rem",
              textAlign: "center",
              borderRadius: "5px",
            }}
          />
        </Box>

        <Button
          color="primary"
          variant="outlined"
          sx={{
            padding: "3px",
          }}
          onClick={() => decreaseExistingQuantity()}
        >
          <Remove fontSize="small" />
        </Button>
      </>
    );

    if (cartItem?.quantity && !isLabor && !isFreight) {
      return quantityPicker;
    }

    return null;
  };

  // Add to cart logic - checks if item is already in cart or not to then handle a quantity increase or item addition.
  // Also validates fields of matrix items understanding what fields have not been filled.

  /* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
   * Set up multiple days in relation to cart Items when adding labor item *
   * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
  // eslint-disable-next-line consistent-return
  const understandAddCartItem = (e, modalState) => {
    // If any pre calcs need down this method is where it should happen prior to running the addToCart() Func.
    // renderVariantSelectors().length === 0 ? addToCart(e) : openOptionModal(optionModalVisible)
    let hasVariants = [
      { exists: hasColorOptions, name: "color" },
      { exists: hasOrientOptions, name: "orientations" },
      { exists: hasGraphicOptions, name: "materials" },
      { exists: hasSizeOptions, name: "sizes" },
    ];

    let variantOptions = hasVariants.filter((v) => v.exists);

    let itemSpecials = {
      isSquareFt,
      isFreight,
      isLabor,
      isEstimated,
      isShowDuration,
    };

    console.log("Understanding cart...", {
      hasVariants,
      variantOptions,
      itemSpecials,
    });

    if (variantOptions.length === 0) {
      if (isUpload) {
        return openOptionModal(modalState);
      }

      if (itemAttributes.length !== 0) {
        if (isLabor) {
          if (laborBucketBuild.length !== 0) {
            return setLaborTableOpen(!laborTableOpen);
          }
          alert("Choose a date, following a START and END time to continue.");
          return;
        }
        return openOptionModal(modalState);
      }

      if (isSquareFt) {
        console.log("Detected Area calc item type.");
        if (isShowDuration) {
          return addToCart(e, "", itemProps, cartOptions);
        }
        if (isDaysCalc) {
          // Check if a date has been selected
          if (!daysCalcDateSelected) {
            alert("Please select a date before adding this item to your cart.");
            return;
          }
          return addToCart(e, daysCalcDateSelected, itemProps, cartOptions);
        }
        return addToCart(e, "", itemProps, cartOptions);
      }
      if (isFreight) {
        return addToCart(e, "", itemProps, cartOptions);
      }

      if (isLabor) {
        if (laborBucketBuild.length !== 0) {
          return setLaborTableOpen(!laborTableOpen);
        }
        alert("Choose a date, following a START and END time to continue.");
      } else if (isDaysCalc) {
        // Check if a date has been selected
        if (!daysCalcDateSelected) {
          alert("Please select a date before adding this item to your cart.");
          return;
        }
        return addToCart(e, daysCalcDateSelected, itemProps, cartOptions);
      } else if (isShowDuration) {
        return addToCart(e, "", itemProps, cartOptions);
      } else if (isEstimated) {
        return addToCart(e, "", itemProps, cartOptions);
      } else {
        console.log("Running cart spite of variants");
        return addToCart(e, "", itemProps, cartOptions);
      }
    } else {
      console.log("Running variant view bc of variants");
      return openOptionModal(modalState);
    }
  };

  console.log('isAddToCartDisabled', isAddToCartDisabled)

  return (
    <StyledBazaarCard
      animate={{
        opacity: isPerPieceItem ? 0.5 : 1,
      }}
      whileHover={{
        opacity: isPerPieceItem ? 0.75 : 1,
      }}
      component={motion.div}
      hoverEffect={hoverEffect}
    >
      <ImageWrapper>
        {!!discount && (
          <StyledChip color="primary" size="small" label={`${discount}% off`} />
        )}

        <HoverIconWrapper className="hover-box">
          {/*  TODO: Usage with product dialog */}
          {/* <IconButton onClick={toggleDialog}> */}
          {/*  <RemoveRedEye color="disabled" fontSize="small" /> */}
          {/* </IconButton> */}

          {/* <IconButton onClick={toggleIsFavorite}>
            {isFavorite ? (
              <Favorite color="primary" fontSize="small" />
            ) : (
              <FavoriteBorder fontSize="small" color="disabled" />
            )}
          </IconButton> */}
          <ButtonGroup>
            {isPerPieceItem && (
              <Tooltip
                arrow
                title="Per Piece items are not yet availble for checkout. Please contact exhibitor services to purchase this type of item"
              >
                <IconButton color="warning">
                  <WarningRounded color="warning" />
                </IconButton>
              </Tooltip>
            )}
          </ButtonGroup>
        </HoverIconWrapper>

        <Link style={{ position: "static" }} href={`/product/${id}`}>
          <div style={{ position: "relative", width: "100%", height: "100%" }}>
            <LazyImage
              priority
              style={{
                objectFit:
                  imgUrl === "/assets/images/products/NO-PRODUCT-IMAGE.jpg"
                    ? "cover"
                    : "contain",
              }}
              src={imgUrl}
              fill
              sizes="(max-width: 768px) 100vw,
								  (max-width: 1200px) 50vw,
								  33vw"
              alt={title}
            />
          </div>
        </Link>
      </ImageWrapper>

      {/*  TODO: Hook up for usage to same mechanics as the product detail page. */}
      {/* <ProductViewDialog */}
      {/*  openDialog={openModal} */}
      {/*  handleCloseDialog={toggleDialog} */}
      {/*  product={{ */}
      {/*    title, */}
      {/*    price, */}
      {/*    id, */}
      {/*    description, */}
      {/*    imgGroup: images.length > 0 ? images : [imgUrl], */}
      {/*    category, */}
      {/*  }} */}
      {/* /> */}

      <ContentWrapper>
        <FlexBox>
          <Box flex="1 1 0" minWidth="0px" mr={1}>
            <Link href={`/product/${id}`}>
              <H3
                mb={1}
                title={title}
                fontSize="14px"
                fontWeight="600"
                className="title whitespace-pre-line"
                color="text.secondary"
              >
                {title}
              </H3>
            </Link>

            {isDaysCalc && (
              <>
                <Divider sx={{ mb: 2 }} />
                <Stack
                  direction="row"
                  spacing={1}
                  useFlexGap
                  flexWrap="wrap"
                  sx={{ pb: 1 }}
                >
                  <Paragraph
                    sx={{
                      fontSize: 13,
                      fontFamily: montserrat.style.fontFamily,
                    }}
                  >
                    Select a date:
                  </Paragraph>
                  {daysCalcDates
                    .slice()
                    .sort((d1, d2) => new Date(d1.date) - new Date(d2.date))
                    .map((item, i) => (
                      <Chip
                        size="small"
                        sx={{ position: "relative", borderRadius: 1.5 }}
                        key={`chip-${i}`}
                        onClick={() => setDaysCalcDateSelected(item)}
                        label={item.date}
                        variant={
                          daysCalcDateSelected !== item ? "outlined" : "filled"
                        }
                        color={
                          daysCalcDateSelected !== item ? "default" : "primary"
                        }
                      />
                    ))}
                </Stack>
              </>
            )}
            {isLabor && (
              <Box>
                <Divider sx={{ mb: 2 }} />
                <Stack
                  direction="row"
                  spacing={2}
                  useFlexGap
                  flexWrap="wrap"
                  sx={{ pb: 3 }}
                >
                  <Paragraph
                    sx={{
                      fontSize: 13,
                      fontFamily: montserrat.style.fontFamily,
                    }}
                  >
                    Select a date:
                  </Paragraph>
                  {laborDates
                    .slice()
                    .sort((d1, d2) => new Date(d1.date) - new Date(d2.date))
                    .map((item, i) => (
                      <Chip
                        size="small"
                        key={`labor-date-${i}`}
                        sx={{ borderRadius: 1.5 }}
                        onClick={() => setLaborDateSelected(item)}
                        variant={
                          laborDateSelected !== item ? "outlined" : "filled"
                        }
                        label={item.date}
                        color={
                          laborDateSelected !== item ? "default" : "primary"
                        }
                      />
                    ))}
                </Stack>
                <Grid container spacing={2}>
                  <Grid xs item>
                    {isMobile ? (
                      <MobileTimePicker
                        fullWidth
                        disabled={!laborDateSelected}
                        label="Labor Start"
                        margin="normal"
                        value={laborStartTimeSelected}
                        onChange={(newValue) =>
                          setLaborStartTimeSelected(newValue)
                        }
                        KeyboardTimePickerProps={{
                          "aria-label": "change time",
                        }}
                        shouldDisableTime={(date, view) =>
                          handleLaborTimeDisabled(date, view)
                        }
                      />
                    ) : (
                      <DesktopTimePicker
                        fullWidth
                        disabled={!laborDateSelected}
                        label="Labor Start"
                        margin="normal"
                        value={laborStartTimeSelected}
                        onChange={(newValue) =>
                          setLaborStartTimeSelected(newValue)
                        }
                        minutesStep={1}
                        KeyboardTimePickerProps={{
                          "aria-label": "change time",
                        }}
                        shouldDisableTime={(date, view) =>
                          handleLaborTimeDisabled(date, view)
                        }
                      />
                    )}
                  </Grid>
                  <Grid xs item flexDirection="column">
                    {isMobile ? (
                      <MobileTimePicker
                        fullWidth
                        disabled={!laborDateSelected}
                        margin="normal"
                        label="Labor End"
                        value={laborEndTimeSelected}
                        onChange={(newValue) =>
                          setLaborEndTimeSelected(newValue)
                        }
                        KeyboardTimePickerProps={{
                          "aria-label": "change time",
                        }}
                        shouldDisableTime={(date, view) =>
                          handleLaborTimeDisabled(date, view)
                        }
                      />
                    ) : (
                      <DesktopTimePicker
                        fullWidth
                        disabled={!laborDateSelected}
                        margin="normal"
                        label="Labor End"
                        value={laborEndTimeSelected}
                        onChange={(newValue) =>
                          setLaborEndTimeSelected(newValue)
                        }
                        KeyboardTimePickerProps={{
                          "aria-label": "change time",
                        }}
                        shouldDisableTime={(date, view) =>
                          handleLaborTimeDisabled(date, view)
                        }
                      />
                    )}
                  </Grid>
                </Grid>
                <Grid container pt={2}>
                  <Grid xs item>
                    <TextField
                      fullWidth
                      sx={{ py: 2, pr: 2 }}
                      label="Worker Quantity"
                      onChange={(e) => handleWorkerQuantityChange(e)}
                      onBlur={(e) => handleWorkerQuantityCapture(e)}
                      type="number"
                      variant="outlined"
                      value={laborWorkerQuantity}
                      InputProps={{
                        inputComponent: NumericWorkerFormat,
                      }}
                    />
                  </Grid>
                  <Grid
                    xs
                    item
                    justifySelf="end"
                    alignSelf="end"
                    sx={{ textAlign: "center" }}
                  >
                    <FormControlLabel
                      control={
                        <Checkbox
                          onChange={(e, v) => setLaborSupervision(v)}
                          checked={laborSupervision}
                          name="checkedH"
                        />
                      }
                      label={
                        <span>
                          <Typography variant="caption">
                            Supervision Required?
                          </Typography>
                        </span>
                      }
                    />
                  </Grid>
                </Grid>
              </Box>
            )}
            {isFreight && (
              <>
                <Divider sx={{ mb: 2 }} />
                <Grid container display="flex" flexDirection="row" pt={2}>
                  <Grid item md={6} xs={12}>
                    <TextField
                      fullWidth
                      size="small"
                      onChange={(e) => handleFreightWeightChange(e)}
                      onBlur={(e) => handleFreightWeightCapture(e)}
                      variant="outlined"
                      value={cwtFreightWeight}
                      InputProps={{
                        inputComponent: NumericFreightFormat,
                      }}
                    />
                  </Grid>
                </Grid>
              </>
            )}
            {isSquareFt && (
              <>
                <Divider sx={{ mt: 1, mx: 1, mb: 2 }} />
                <Typography variant="caption" sx={{ pt: 3 }}>
                  Enter Dimensions
                </Typography>
                <Grid
                  container
                  display="flex"
                  flexDirection="row"
                  sx={{ py: 2 }}
                  spacing={2}
                >
                  <Grid item md={4} xs={12}>
                    <TextField
                      fullWidth
                      size="small"
                      className="text-center"
                      onChange={(e) => handleLengthChange(e)}
                      onBlur={(e) => handleLengthCapture(e)}
                      variant="outlined"
                      value={optionLength}
                      InputProps={{
                        inputComponent: NumericLengthFormat,
                      }}
                    />
                  </Grid>
                  <Grid item xs={0}>
                    x
                  </Grid>
                  <Grid item md={4} xs={12}>
                    <TextField
                      fullWidth
                      size="small"
                      className="text-center"
                      onChange={(e) => handleWidthChange(e)}
                      onBlur={(e) => handleWidthCapture(e)}
                      variant="outlined"
                      value={optionWidth}
                      InputProps={{
                        inputComponent: NumericWidthFormat,
                      }}
                    />
                  </Grid>
                  <Grid item xs={0}>
                    =
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      size="small"
                      className="text-center"
                      onChange={(e) => handleTotalFootChange(e)}
                      onBlur={(e) => handleTotalFootCapture(e)}
                      variant="outlined"
                      value={optionTotalSqFt}
                      InputProps={{
                        inputComponent: NumericTotalSquareFootFormat,
                      }}
                    />
                  </Grid>
                </Grid>
              </>
            )}
            {isShowDuration && (
              <>
                <Divider sx={{ mb: 2 }} />
                <Stack
                  direction="row"
                  spacing={2}
                  useFlexGap
                  flexWrap="wrap"
                  sx={{ pb: 1, width: "100%" }}
                >
                  <Tooltip
                    arrow
                    sx={{ alignSelf: "center" }}
                    placement="right"
                    title="All dates will be included for the duration of the event"
                  >
                    <IconButton>
                      <HelpOutlineOutlined fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  {showDates.map((item) => (
                    <Chip
                      key={item.id}
                      sx={{ borderRadius: 1.5 }}
                      color="primary"
                      disabled
                      label={item.date}
                    />
                  ))}
                </Stack>
              </>
            )}

            {showProductSize && (
              <Span color="grey.600" mb={1} display="block">
                {showProductSize}
              </Span>
            )}

            <FlexBox alignItems="center" gap={1} mt={0.5}>
              <Box fontWeight="600" color="primary.main">
                {price === "" || price === null || Number.isNaN(price) ? (
                  "Not Available"
                ) : variantOptions.length !== 0 && price === "" || price === null || Number.isNaN(price) ? (
                  "Prices Vary"
                ) : (
                  <>
                    {currency(price)}
                    <Span sx={{ fontSize: 11 }} color="grey.600">
                      {saleUnit && `/ ${saleUnit}`}
                    </Span>
                  </>
                )}
              </Box>

              {!!discount && (
                <Box color="grey.600" fontWeight="600">
                  <del>{currency(comparePrice)}</del>
                </Box>
              )}

              {isEstimated && (
                <Span>
                  <Tooltip
                    arrow
                    title="This is an estimated item, you will not be charged on checkout"
                  >
                    <IconButton
                      size="small"
                      sx={{ positive: "relative", ml: -0.5 }}
                    >
                      <CreditCardOffOutlined sx={{ fontSize: 16 }} />
                    </IconButton>
                  </Tooltip>
                </Span>
              )}

              {isUpload && (
                <Span>
                  <Tooltip
                    arrow
                    title="This is an upload item, you will be prompted to upload files to this item before adding to your cart."
                  >
                    <IconButton
                      size="small"
                      sx={{ positive: "relative", ml: -0.5 }}
                    >
                      <FileUploadTwoTone sx={{ fontSize: 16 }} />
                    </IconButton>
                  </Tooltip>
                  {cartItem?.fileAttachments &&
                    cartItem?.fileAttachments.attachments.length !== 0 && (
                      <Tooltip
                        arrow
                        placement="right"
                        title={`${
                          cartItem?.fileAttachments?.attachments.length
                        } file${
                          cartItem?.fileAttachments?.attachments.length > 1
                            ? "s"
                            : ""
                        } are attached to this item. To upload more or remove files please revisit item page.`}
                      >
                        <IconButton>
                          <FileCountBadge
                            showZero
                            badgeContent={
                              cartItem?.fileAttachments.attachments.length || 0
                            }
                            color="primary"
                          >
                            <DriveFolderUploadOutlined
                              color="primary"
                              fontSize="small"
                            />
                          </FileCountBadge>
                        </IconButton>
                      </Tooltip>
                    )}
                </Span>
              )}
            </FlexBox>

            {isMemoItem && (
              <>
                <Divider sx={{ mb: 2 }} />
                <TextField
                  multiline
                  fullWidth
                  label="Memo"
                  variant="outlined"
                  placeholder="Add your memo..."
                  value={memoText}
                  onChange={(e) => {
                    setMemoText(e.target.value);
                  }}
                />
              </>
            )}
            <ItemFields
              isLabor={isLabor}
              quantity={quantity}
              isFreight={isFreight}
              isSquareFt={isSquareFt}
              handleQuantityCapture={handleQuantityCapture}
              handleQuantityChange={handleQuantityChange}
              increaseQuantity={increaseQuantity}
              decreaseQuantity={decreaseQuantity}
            />
          </Box>

          <FlexBox
            width="30px"
            alignItems="center"
            className="add-cart"
            flexDirection="column-reverse"
            justifyContent={cartItem?.quantity ? "space-between" : "flex-start"}
          >
            {isMobile ? (
              <Tooltip title="Add to cart">
                <IconButton
                  disabled={
                    price === "" ||
                    price === null ||
                    Number.isNaN(price) ||
                    isPerPieceItem
                  }
                  onClick={(e) => understandAddCartItem(e, optionModalVisible)}
                  aria-label="add to cart"
                >
                  <AddShoppingCartOutlined fontSize="small" />
                </IconButton>
              </Tooltip>
            ) : (
              <Button
                disabled={
                  price === "" ||
                  price === null ||
                  Number.isNaN(price) ||
                  isPerPieceItem
                }
                variant="outlined"
                fullWidth
                color="primary"
                onClick={(e) => understandAddCartItem(e, optionModalVisible)}
              >
                <AddShoppingCartOutlined fontSize="small" />
              </Button>
            )}

            {isLabor && laborBucketBuild.length !== 0 && (
              <LaborTablePreview
                size="xl"
                frame={isMobile}
                rows={laborBucketBuild}
                isOpen={laborTableOpen}
                itemAttributes={itemAttributes}
                innerRef={formRef}
                handleAttributeSubmit={handleSubmit}
                workerCount={laborWorkerQuantity}
                totalAllLaborCost={laborCostTotal}
                laborDateSelected={laborDateSelected}
                laborEndTimeSelected={laborEndTimeSelected}
                laborStartTimeSelected={laborStartTimeSelected}
                supervisionRequired={laborSupervision}
                laborBareCostTotal={formatter.format(laborBareCostTotal)}
                totalSupervisonCost={formatter.format(laborSupervisionCost)}
                toggle={() => setLaborTableOpen(!laborTableOpen)}
                addToCart={(e) =>
                  addToCart(
                    e,
                    moment(laborDateSelected).format("m/d/yyyy"),
                    itemProps,
                    cartOptions,
                  )
                }
                totalHoursChosen={roundOff(
                  Math.abs(
                    laborStartTimeSelected.getTime() -
                      laborEndTimeSelected.getTime(),
                  ) / 36e5,
                  2,
                )}
              />
            )}

            <Dialog
              open={optionModalVisible}
              onClose={() => openOptionModal(optionModalVisible)}
              fullScreen={isMobile}
              maxWidth={isUpload ? "xl" : "sm"}
              fullWidth={isUpload}
            >
              <DialogTitle
                sx={{
                  p: 3,
                }}
              >
                <H3 gutterBottom>Finish Adding Product</H3>
                <Typography variant="subtitle2">Product Options</Typography>
                <Box
                  sx={{
                    position: "absolute",
                    top: 3,
                    right: 3,
                  }}
                />
              </DialogTitle>
              <DialogContent dividers>
                <Grid container spacing={2}>
                  {isUpload && (
                    <Grid
                      sx={{
                        mb: `${theme.spacing(3)}`,
                        pt: 3,
                      }}
                      item
                      xs={12}
                    >
                      <Box>
                        <ItemBlurb description={description} />
                      </Box>

                      <BoxUploadWrapper {...getRootProps()}>
                        <input {...getInputProps()} />

                        {isDragAccept && (
                          <>
                            <AvatarSuccess variant="rounded">
                              <CheckTwoTone />
                            </AvatarSuccess>
                            <Typography
                              sx={{
                                mt: 2,
                              }}
                            >
                              {t("Drop the files to start uploading")}
                            </Typography>
                          </>
                        )}
                        {isDragReject && (
                          <>
                            <AvatarDanger variant="rounded">
                              <CloseTwoTone />
                            </AvatarDanger>
                            <Typography
                              sx={{
                                mt: 2,
                              }}
                            >
                              {t("You cannot upload these file types")}
                            </Typography>
                          </>
                        )}
                        {!isDragActive && (
                          <>
                            <AvatarWrapper variant="rounded">
                              <CloudUploadTwoTone />
                            </AvatarWrapper>
                            <Typography
                              sx={{
                                mt: 2,
                              }}
                            >
                              {t(
                                `Drag & drop ${
                                  fileAttachments?.attachments.length !== 0
                                    ? "additional"
                                    : ""
                                } files here to attach to ${
                                  fileAttachments?.attachments.length !== 0
                                    ? "your current"
                                    : ""
                                } item`,
                              )}
                            </Typography>
                          </>
                        )}
                      </BoxUploadWrapper>
                      {filesUploaded.length > 0 && (
                        <>
                          {uploadingFile && (
                            <LinearProgress
                              variant="determinate"
                              value={uploadProgress}
                            />
                          )}
                          <Alert
                            sx={{
                              py: 0,
                              mt: 2,
                            }}
                            severity="success"
                          >
                            {t("You have uploaded")}{" "}
                            <b>{filesUploaded.length}</b> {t("files")}!
                          </Alert>
                          <Divider
                            sx={{
                              mt: 2,
                            }}
                          />
                          <List disablePadding component="div">
                            {files}
                          </List>
                        </>
                      )}
                    </Grid>
                  )}
                  <Grid item xs={12}>
                    {variantOptions.length !== 0 ? (
                      <Grid container spacing={3}>
                        {loadingVariantFields ? (
                          <Grid item xs={12}>
                            <CircularProgress />
                          </Grid>
                        ) : (
                          <VariantSelectors
                            sizeError={sizeError}
                            colorError={colorError}
                            orientationError={orientationError}
                            materialError={materialError}
                            color={color}
                            size={size}
                            orientation={orientation}
                            material={material}
                            materials={materials}
                            colors={colors}
                            sizes={sizes}
                            orientations={orientations}
                            hasSizeOptions={hasSizeOptions}
                            hasColorOptions={hasColorOptions}
                            hasOrientOptions={hasOrientOptions}
                            hasGraphicOptions={hasGraphicOptions}
                            handleColorChange={handleColorChange}
                            handleSizeChange={handleSizeChange}
                            handleOrientationChange={handleOrientationChange}
                            handleMaterialChange={handleMaterialChange}
                          />
                        )}
                      </Grid>
                    ) : null}
                  </Grid>
                  {itemAttributes.length !== 0 && (
                    <Grid item xs={12}>
                      <FormikDynamic
                        itemAttributes={itemAttributes}
                        setAttributes={setAttributes}
                        innerRef={formRef}
                      />
                    </Grid>
                  )}
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button
                  color="secondary"
                  variant="outlined"
                  onClick={() => openOptionModal(optionModalVisible)}
                  fullWidth={isMobile}
                >
                  Close
                </Button>
                <LoadingButton
                  onClick={
                    itemAttributes.length === 0
                      ? (e) => {
                          return addToCart(e, "", itemProps, cartOptions).then(
                            () => {
                              // Reset the button state after adding to cart
                              setIsAddToCartDisabled(true);
                              setValidatedItem({ childId: "", price: null });
                            },
                          );
                        }
                      : handleSubmit
                  }
                  variant="contained"
                  disabled={
                    (variantOptions.length > 0 && isAddToCartDisabled) || // Only disable for variants that need validation
                    uploadingFile
                  }
                  color="primary"
                  loading={validatingItem}
                  fullWidth={isMobile}
                  endIcon={validatingItem ? <CircularProgress size="small" /> : null}
                >
                  {validatingItem ? "Validating Product" : "Add to cart"}&nbsp;
                  {validatedItem.price ? `(${currency(validatedItem.price)})` : null}
                </LoadingButton>
              </DialogActions>
            </Dialog>

            {/* <Button */}
            {/*  color="primary" */}
            {/*  variant="outlined" */}
            {/*  sx={{ */}
            {/*    padding: "3px", */}
            {/*  }} */}
            {/*  onClick={handleCartAmountChange({ */}
            {/*    id, */}
            {/*    slug, */}
            {/*    price, */}
            {/*    imgUrl, */}
            {/*    name: title, */}
            {/*    qty: (cartItem?.qty || 0) + 1, */}
            {/*  })} */}
            {/* > */}
            {/*  <Add fontSize="small" /> */}
            {/* </Button> */}

            {renderQuantityPicker()}
          </FlexBox>
        </FlexBox>
      </ContentWrapper>
    </StyledBazaarCard>
  );
};

export default ProductCard1;
