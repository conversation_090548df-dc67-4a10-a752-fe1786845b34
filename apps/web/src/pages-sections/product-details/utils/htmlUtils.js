import ReactHtmlParser, { convertNodeToElement } from "react-html-parser";

/**
 * HTML transformation utility functions
 */

export const transform = (node, index) => {
  if (node.type === "tag" && node.name === "table") {
    node.name = "div";
    node.attribs = {
      ...node.attribs,
      width: null,
      class: "container",
      "product-test": "container",
      style: `padding: 0;`,
    };
    return convertNodeToElement(node, index, transform);
  }

  if (node.type === "tag" && node.name === "tr") {
    node.name = "div";
    return convertNodeToElement(node, index, transform);
  }

  if (node.type === "tag" && node.name === "td") {
    node.name = "div";
    return convertNodeToElement(node, index, transform);
  }

  if (node.type === "tag" && node.name === "tbody") {
    node.name = "div";
    return convertNodeToElement(node, index, transform);
  }

  return convertNodeToElement(node, index, transform);
};

export const renderBlurb = (blurb) => {
  return ReactHtmlParser(blurb, { decodeEntities: true, transform });
};

export const roundOff = (num, places) => {
  const x = 10 ** places;
  return Math.round(num * x) / x;
};
