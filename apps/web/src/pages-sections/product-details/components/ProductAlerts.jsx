import { Box, Typography } from "@mui/material";
import { Alert, AlertTitle } from "@mui/lab";

const ProductAlerts = ({ isPerPieceItem, isEstimated }) => {
  return (
    <>
      {isPerPieceItem && (
        <Alert severity="warning" sx={{ "padding-bottom": "1em" }}>
          <AlertTitle>Not Available</AlertTitle>
          This is a Per Piece Item - It is not yet available for web
          purchasing. Please contact exhibitor services to process this type
          of item.
        </Alert>
      )}

      {isEstimated && (
        <Box sx={{ "padding-bottom": "1em" }}>
          <Typography variant="caption">
            This is an estimated item, you will not be charged on checkout
          </Typography>
        </Box>
      )}
    </>
  );
};

export default ProductAlerts;