import React from 'react';
import {
  Box,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  useTheme,
} from '@mui/material';
import { useTranslation } from 'next-i18next';

/**
 * ProductVariantSelector component
 * Renders variant selection dropdowns (color, size, material, etc.)
 */
const ProductVariantSelector = ({ 
  itemOptions, 
  selectedOptions, 
  handleVariantSelect, 
  variantErrors,
  variantOptionState 
}) => {
  const theme = useTheme();
  const { t } = useTranslation();

  if (Object.keys(itemOptions).length === 0) {
    return null;
  }

  return (
    <Grid container spacing={3}>
      {Object.keys(itemOptions).map((variant, vindex) => {
        let variantLabel = `${variant[0].toUpperCase()}${variant.slice(1)}`;
        
        return (
          <Grid
            item
            xs={12}
            sx={{ paddingTop: "2vh" }}
            key={`${variant}-${vindex}`}
          >
            <Grid container flexDirection="column" display="flex">
              <Box display="flex" justifyContent="center" pb={2}>
                <Grid
                  item
                  xs={12}
                  sm={4}
                  justifyContent="flex-end"
                >
                  <Box
                    pr={3}
                    sx={{
                      pt: `${theme.spacing(0)}`,
                      pb: { xs: 1, md: 0 },
                    }}
                    alignSelf="center"
                  >
                    <b>{t(variantLabel)}:</b>
                  </Box>
                </Grid>
                <Grid
                  sx={{
                    mb: `${theme.spacing(0)}`,
                  }}
                  item
                  xs
                >
                  <FormControl
                    fullWidth
                    error={variantErrors[variant]}
                  >
                    <InputLabel
                      id={`variant-${variant}-label`}
                    >{`Choose ${variantLabel}`}</InputLabel>
                    <Select
                      labelId={`variant-${variant}-label`}
                      id={`variant-${variant}-select`}
                      value={variantOptionState[variant] || ""}
                      label={`Choose ${variantLabel}`}
                      onChange={(e) => {
                        const selectedOption = itemOptions[variant].find(
                          (option) => option.value === e.target.value
                        );
                        if (selectedOption) {
                          const mockEvent = {
                            props: {
                              dataset: JSON.stringify(selectedOption)
                            }
                          };
                          handleVariantSelect(mockEvent, variant, e.target.value);
                        }
                      }}
                    >
                      {itemOptions[variant].map((option, oindex) => (
                        <MenuItem
                          key={`${variant}-${option.value}-${oindex}`}
                          value={option.value}
                          data-set={JSON.stringify(option)}
                        >
                          {option.text}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Box>
            </Grid>
          </Grid>
        );
      })}
    </Grid>
  );
};

export default ProductVariantSelector;
