import { Box, H2, H3, FlexBox } from "components/ui";
import { BarLoader } from "react-spinners";

const PricingDisplay = ({
  validatingItem,
  validatedItem,
  variantOptions,
  price,
  currency,
  discount,
  comparePrice,
  settings
}) => {
  return (
    <FlexBox alignItems="center" mb={2} gap={1}>
      <Box>
        <H2 color="primary.main" mb={0.5} lineHeight="1">
          {validatingItem ? (
            <BarLoader
              color={
                settings?.custrecord_ng_cs_accent_color
                  ? settings.custrecord_ng_cs_accent_color
                  : "#FFF555"
              }
            />
          ) : validatedItem.price ? (
            currency(validatedItem.price)
          ) : variantOptions.legnth !== 0 && price === "" || price === null || Number.isNaN(price) ? (
            "Prices Vary"
          ) : (
            currency(price)
          )}
        </H2>
      </Box>
      {!!discount && (
        <Box>
          <H3 color="grey.600" fontWeight="600">
            {validatingItem ? (
              <BarLoader
                color={
                  settings?.custrecord_ng_cs_accent_color
                    ? settings.custrecord_ng_cs_accent_color
                    : "#FFF555"
                }
              />
            ) : validatedItem.compare_price ? (
              <del>{currency(validatedItem.compare_price)}</del>
            ) : (
              <del>{currency(comparePrice)}</del>
            )}
          </H3>
        </Box>
      )}
    </FlexBox>
  );
};

export default PricingDisplay;