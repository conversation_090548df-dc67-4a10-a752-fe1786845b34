import { Grid, Typography, Alert, Divider, List } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useTranslation } from "react-i18next";
import {
  CheckTwoTone,
  CloseTwoTone,
  CloudUploadTwoTone,
} from "@mui/icons-material";
import {
  BoxUploadWrapper,
  AvatarSuccess,
  AvatarDanger,
  AvatarWrapper,
} from "components/ui";

const FileUploadSection = ({
  getRootProps,
  getInputProps,
  isDragAccept,
  isDragReject,
  isDragActive,
  acceptedFiles,
  filesUploaded,
  files
}) => {
  const theme = useTheme();
  const { t } = useTranslation();

  return (
    <Grid
      sx={{
        mb: `${theme.spacing(3)}`,
        pt: 3,
      }}
      item
      xs={12}
    >
      <BoxUploadWrapper {...getRootProps()}>
        <input {...getInputProps()} />
        {isDragAccept && (
          <>
            <AvatarSuccess variant="rounded">
              <CheckTwoTone />
            </AvatarSuccess>
            <Typography sx={{ mt: 2 }}>
              {t("Drop the files to start uploading")}
            </Typography>
          </>
        )}
        {isDragReject && (
          <>
            <AvatarDanger variant="rounded">
              <CloseTwoTone />
            </AvatarDanger>
            <Typography sx={{ mt: 2 }}>
              {t("You cannot upload these file types")}
            </Typography>
          </>
        )}
        {!isDragActive && (
          <>
            <AvatarWrapper variant="rounded">
              <CloudUploadTwoTone />
            </AvatarWrapper>
            <Typography sx={{ mt: 2 }}>
              {t("Drag & drop files here to attach to item")}
            </Typography>
          </>
        )}
      </BoxUploadWrapper>
      {(acceptedFiles.length > 0 || filesUploaded.length > 0) && (
        <>
          <Alert sx={{ py: 0, mt: 2 }} severity="success">
            {t("You have uploaded")} <b>{filesUploaded.length}</b>{" "}
            {t("files")}!
          </Alert>
          <Divider sx={{ mt: 2 }} />
          <List disablePadding component="div">
            {files}
          </List>
        </>
      )}
    </Grid>
  );
};

export default FileUploadSection;