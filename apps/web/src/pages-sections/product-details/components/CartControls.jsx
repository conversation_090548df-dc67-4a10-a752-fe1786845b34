import { Box, Button, FlexBox } from "components/ui";
import { Remove, Add } from "@mui/icons-material";
import InlineQuantityEditField from "components/InlineQuantityEditField";

const CartControls = ({
  cartItem,
  validatingItem,
  uploadingFile,
  isPerPieceItem,
  variantOptions,
  isAddToCartDisabled,
  itemAttributes,
  isLabor,
  isFreight,
  minimumQuantity,
  maxQuantity,
  validatedItem,
  currency,
  handleSubmit,
  understandAddCartItem,
  optionModalVisible,
  decreaseExistingQuantity,
  quantityChange
}) => {
  if (!cartItem?.quantity) {
    return (
      <Button
        disabled={
          validatingItem || 
          uploadingFile || 
          isPerPieceItem ||
          (variantOptions.length > 0 && isAddToCartDisabled) 
        }
        color="primary"
        variant="contained"
        onClick={
          itemAttributes.length !== 0 && !isLabor
            ? handleSubmit
            : (e) => understandAddCartItem(e, optionModalVisible)
        }
        sx={{
          mb: 4.5,
          px: "1.75rem",
          height: 40,
        }}
      >
        {validatingItem ? "Validating Product" : "Add to Cart"}
        {validatedItem.price ? ` (${currency(validatedItem.price)})` : null}
      </Button>
    );
  }

  return (
    <Box>
      {!isFreight && !isLabor ? (
        <FlexBox alignItems="center" mb={4.5}>
          <Button
            size="small"
            sx={{ p: 1 }}
            color="primary"
            variant="outlined"
            onClick={() => decreaseExistingQuantity()}
          >
            <Remove fontSize="small" />
          </Button>

          <InlineQuantityEditField
            disabled={isLabor || isFreight}
            initialValue={cartItem?.quantity}
            onClickAway={quantityChange}
            textVariant="h4"
            min={Number(minimumQuantity) || 1}
            max={Number(maxQuantity) || Infinity}
            inputStyle={{
              mx: 3,
              px: 1,
              fontSize: "1.5rem",
              border: "1px solid #c4c4c4",
              borderRadius: "5px",
            }}
          />

          <Button
            size="small"
            sx={{ p: 1 }}
            color="primary"
            variant="outlined"
            onClick={(e) => understandAddCartItem(e, optionModalVisible)}
          >
            <Add fontSize="small" />
          </Button>
        </FlexBox>
      ) : (
        <Button
          disabled={validatingItem || uploadingFile || isPerPieceItem}
          color="primary"
          variant="contained"
          onClick={(e) => understandAddCartItem(e, optionModalVisible)}
          sx={{
            mb: 4.5,
            px: "1.75rem",
            height: 40,
          }}
        >
          Add to Cart
        </Button>
      )}
    </Box>
  );
};

export default CartControls;