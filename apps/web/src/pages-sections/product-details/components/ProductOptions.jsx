import React from 'react';
import {
  Box,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Checkbox,
  FormControlLabel,
  Button,
  Typography,
} from '@mui/material';
import { DesktopTimePicker, MobileTimePicker } from '@mui/x-date-pickers';
import { useMediaQuery, useTheme } from '@mui/material';
import {
  NumericWorkerFormat,
  NumericFreightFormat,
  NumericFreightQuantityFormat,
  NumericLengthFormat,
  NumericWidthFormat,
  NumericTotalSquareFootFormat,
} from '../ProductIntro';

/**
 * ProductOptions component
 * Handles special product options like square footage, freight, labor, and memo
 */
const ProductOptions = ({
  isSquareFt,
  isFreight,
  isLabor,
  isDaysCalc,
  isMemoItem,
  optionWidth,
  optionLength,
  optionTotalSqFt,
  cwtFreightWeight,
  cwtFreightWeightEntered,
  daysCalcDates,
  daysCalcDateSelected,
  laborDates,
  laborDateSelected,
  laborStartTimeSelected,
  laborEndTimeSelected,
  laborSupervision,
  laborWorkerQuantity,
  laborTableOpen,
  laborBucketBuild,
  memoText,
  handleWidthChange,
  handleWidthCapture,
  handleLengthChange,
  handleLengthCapture,
  handleTotalFootChange,
  handleTotalFootCapture,
  handleFreightWeightChange,
  handleFreightWeightCapture,
  setDaysCalcDateSelected,
  setLaborDateSelected,
  setLaborStartTimeSelected,
  setLaborEndTimeSelected,
  setLaborSupervision,
  handleWorkerQuantityChange,
  handleWorkerQuantityCapture,
  setLaborTableOpen,
  handleLaborTimeDisabled,
  setMemoText,
}) => {
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <>
      {/* Square Footage Options */}
      {isSquareFt && (
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Width (ft)"
              variant="outlined"
              value={optionWidth}
              onChange={handleWidthChange}
              onBlur={handleWidthCapture}
              InputProps={{
                inputComponent: NumericWidthFormat,
              }}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Length (ft)"
              variant="outlined"
              value={optionLength}
              onChange={handleLengthChange}
              onBlur={handleLengthCapture}
              InputProps={{
                inputComponent: NumericLengthFormat,
              }}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Total Sq Ft"
              variant="outlined"
              value={optionTotalSqFt}
              onChange={handleTotalFootChange}
              onBlur={handleTotalFootCapture}
              InputProps={{
                inputComponent: NumericTotalSquareFootFormat,
              }}
              disabled
            />
          </Grid>
        </Grid>
      )}

      {/* Freight Options */}
      {isFreight && (
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Freight Weight"
              variant="outlined"
              value={cwtFreightWeightEntered}
              onChange={handleFreightWeightChange}
              onBlur={handleFreightWeightCapture}
              InputProps={{
                inputComponent: NumericFreightFormat,
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Quantity (CWT)"
              variant="outlined"
              value={Math.ceil(cwtFreightWeight / 100)}
              InputProps={{
                inputComponent: NumericFreightQuantityFormat,
              }}
              disabled
            />
          </Grid>
        </Grid>
      )}

      {/* Days Calculation Date Selection */}
      {isDaysCalc && daysCalcDates && (
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Select Date</InputLabel>
              <Select
                value={daysCalcDateSelected?.date || ""}
                onChange={(e) => {
                  const selectedDate = daysCalcDates.find(
                    date => date.date === e.target.value
                  );
                  setDaysCalcDateSelected(selectedDate);
                }}
                label="Select Date"
              >
                {daysCalcDates.map((date, index) => (
                  <MenuItem key={index} value={date.date}>
                    {date.date}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      )}

      {/* Labor Options */}
      {isLabor && (
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Labor Date</InputLabel>
              <Select
                value={laborDateSelected?.date || ""}
                onChange={(e) => {
                  const selectedDate = laborDates.find(
                    date => date.date === e.target.value
                  );
                  setLaborDateSelected(selectedDate);
                }}
                label="Labor Date"
              >
                {laborDates?.map((date, index) => (
                  <MenuItem key={index} value={date.date}>
                    {date.date}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={3}>
            {mobile ? (
              <MobileTimePicker
                label="Start Time"
                value={laborStartTimeSelected}
                onChange={setLaborStartTimeSelected}
                shouldDisableTime={handleLaborTimeDisabled}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            ) : (
              <DesktopTimePicker
                label="Start Time"
                value={laborStartTimeSelected}
                onChange={setLaborStartTimeSelected}
                shouldDisableTime={handleLaborTimeDisabled}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            )}
          </Grid>
          
          <Grid item xs={12} sm={3}>
            {mobile ? (
              <MobileTimePicker
                label="End Time"
                value={laborEndTimeSelected}
                onChange={setLaborEndTimeSelected}
                shouldDisableTime={handleLaborTimeDisabled}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            ) : (
              <DesktopTimePicker
                label="End Time"
                value={laborEndTimeSelected}
                onChange={setLaborEndTimeSelected}
                shouldDisableTime={handleLaborTimeDisabled}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            )}
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Number of Workers"
              variant="outlined"
              value={laborWorkerQuantity}
              onChange={handleWorkerQuantityChange}
              onBlur={handleWorkerQuantityCapture}
              InputProps={{
                inputComponent: NumericWorkerFormat,
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={laborSupervision}
                  onChange={(e) => setLaborSupervision(e.target.checked)}
                />
              }
              label="Include Supervision"
            />
          </Grid>

          {laborBucketBuild.length > 0 && (
            <Grid item xs={12}>
              <Button
                variant="outlined"
                onClick={() => setLaborTableOpen(!laborTableOpen)}
                fullWidth
              >
                Preview Labor Schedule
              </Button>
            </Grid>
          )}
        </Grid>
      )}

      {/* Memo Text */}
      {isMemoItem && (
        <Box sx={{ mb: 2 }}>
          <TextField
            multiline
            fullWidth
            label="Memo"
            variant="outlined"
            placeholder="Add your memo..."
            minRows={3}
            value={memoText}
            onChange={(e) => setMemoText(e.target.value)}
          />
        </Box>
      )}
    </>
  );
};

export default ProductOptions;
