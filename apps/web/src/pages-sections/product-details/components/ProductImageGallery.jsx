import React from 'react';
import { Avatar, Grid } from '@mui/material';
import LazyImage from 'components/LazyImage';
import { FlexBox, FlexRowCenter } from '../../../components/flex-box';

/**
 * ProductImageGallery component
 * Displays the main product image and thumbnail gallery
 */
const ProductImageGallery = ({ 
  title, 
  images, 
  selectedImage, 
  handleImageClick 
}) => {
  return (
    <Grid item md={6} xs={12} alignItems="center">
      <FlexBox justifyContent="center" mb={6}>
        <LazyImage
          alt={title}
          width={300}
          height={300}
          loading="eager"
          src={images[selectedImage]}
          sx={{
            objectFit: "contain",
          }}
        />
      </FlexBox>

      <FlexBox overflow="auto">
        {images.map((url, ind) => (
          <FlexRowCenter
            key={ind}
            width={64}
            height={64}
            minWidth={64}
            bgcolor="white"
            border="1px solid"
            borderRadius="10px"
            ml={ind === 0 ? "auto" : 0}
            style={{
              cursor: "pointer",
            }}
            onClick={handleImageClick(ind)}
            mr={ind === images.length - 1 ? "auto" : "10px"}
            borderColor={
              selectedImage === ind ? "primary.main" : "grey.400"
            }
          >
            <Avatar
              src={url}
              variant="square"
              sx={{
                height: 40,
              }}
            />
          </FlexRowCenter>
        ))}
      </FlexBox>
    </Grid>
  );
};

export default ProductImageGallery;
