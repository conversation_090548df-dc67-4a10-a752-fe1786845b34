import React from 'react';
import { Box } from '@mui/material';
import { H2, H3 } from 'components/Typography';
import { currency } from 'lib';
import { BarLoader } from '@/components/animate/bar-loader';
import { AnimatedText } from '@/components/animate/animated-text';
import { FlexBox } from '../../../components/flex-box';

/**
 * ProductPricing component
 * Displays product pricing with validation states and loading animations
 */
const ProductPricing = ({ 
  validatingItem, 
  validatedItem, 
  price, 
  discount, 
  comparePrice, 
  variantOptions, 
  settings 
}) => {
  return (
    <FlexBox pt={1} mb={3} alignItems="center" gap={1}>
      <Box>
        <H2 color="primary.main" mb={0.5} lineHeight="1">
          {validatingItem ? (
            <Box sx={{ width: 200 }}>
              <BarLoader color={settings?.custrecord_ng_cs_accent_color || "#FFF555"} />
              <AnimatedText
                sx={{ color: settings?.custrecord_ng_cs_accent_color || "#FFF555", fontWeight: 400 }}
                phrases={["Validating Selection", "Validating Price", "Gathering Details", "Price Fetched"]}
              />
            </Box>
          ) : validatedItem.price ? (
            currency(validatedItem.price)
          ) : variantOptions.length !== 0 && (price === "" || price === null || Number.isNaN(price)) ? (
            "Prices Vary"
          ) : (
            currency(price)
          )}
        </H2>
      </Box>
      {!!discount && (
        <Box>
          <H3 color="grey.600" fontWeight="600">
            {validatingItem ? (
              <BarLoader
                color={
                  settings?.custrecord_ng_cs_accent_color
                    ? settings.custrecord_ng_cs_accent_color
                    : "#FFF555"
                }
              />
            ) : validatedItem.compare_price ? (
              <del>{currency(validatedItem.compare_price)}</del>
            ) : (
              <del>{currency(comparePrice)}</del>
            )}
          </H3>
        </Box>
      )}
    </FlexBox>
  );
};

export default ProductPricing;
