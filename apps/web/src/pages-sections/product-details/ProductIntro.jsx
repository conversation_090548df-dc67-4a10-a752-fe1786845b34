import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Box, Grid, useMediaQuery } from "@mui/material";
import { Montserrat } from "next/font/google";
import { H1, H6 } from "components/Typography";
import { currency } from "lib";
import Cookies from "js-cookie";
import { useTranslation } from "next-i18next";
import { useTheme } from "@mui/material/styles";
import { useRouter } from "next/router";
import moment from "moment";
import { useSettings } from "../../store/zSettingsStore";
import { useBooth, useEvent } from "../../store/eventStore";
import { useCart } from "../../store/zustandCartStore";
import { FlexBox } from "../../components/flex-box";
import LaborTablePreview from "../../components/modals/LaborTablePreview";
import FormikDynamic from "../../components/item-attributes/FormikDynamic";
import { useUser } from "../../store/zUserStore";

// Custom hooks
import { useProductState } from "../../hooks/useProductState";
import { useProductValidation } from "../../hooks/useProductValidation";
import { useFileUpload } from "../../hooks/useFileUpload";
import { useLaborCalculation } from "../../hooks/useLaborCalculation";
import { useFreightCalculation } from "../../hooks/useFreightCalculation";
import { useCartOperations } from "../../hooks/useCartOperations";

// Components
import ProductImageGallery from "./components/ProductImageGallery";
import ProductPricing from "./components/ProductPricing";
import ProductVariantSelector from "./components/ProductVariantSelector";
import ProductOptions from "./components/ProductOptions";
import FileUploadSection from "./components/FileUploadSection";
import CartControls from "./components/CartControls";
import ProductAlerts from "./components/ProductAlerts";

// Utilities
import {
  NumericWorkerFormat,
  NumericFreightFormat,
  NumericFreightQuantityFormat,
  NumericLengthFormat,
  NumericWidthFormat,
  NumericTotalSquareFootFormat,
} from "./utils/numericFormats";
import { renderBlurb } from "./utils/htmlUtils";

const montserrat = Montserrat({
  weight: ["100", "200", "300", "400", "500", "600", "800", "900"],
  subsets: ["latin"],
  style: ["normal", "italic"],
});

// Export numeric formats for use in ProductOptions component
export {
  NumericWorkerFormat,
  NumericFreightFormat,
  NumericFreightQuantityFormat,
  NumericLengthFormat,
  NumericWidthFormat,
  NumericTotalSquareFootFormat,
};

const ProductIntro = ({ product, collection }) => {
  process.env.NEXT_PUBLIC_DEBUG_MODE === "true" &&
    console.log("Server loaded product: ", product);

  const { cart, setCart } = useCart();
  const { t } = useTranslation();
  const { event } = useEvent();
  const { booth } = useBooth();
  const { user } = useUser();

  const theme = useTheme();
  const router = useRouter();
  const { slug } = router.query;
  const mobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { settings } = useSettings();

  const [itemOptions, setItemOptions] = useState({});
  const [attributes, setAttributes] = useState({});
  const [optionModalVisible, setOptionModalVisible] = useState(false);
  const [saleUnit, setSaleUnit] = useState("");
  const [primaryImage, setPrimaryImage] = useState([]);
  const menuItemRef = useRef(null);
  const formRef = useRef();

  // Initialize custom hooks
  const productState = useProductState(1);
  const productValidation = useProductValidation(
    product,
    productState.selectedOptions,
  );
  const laborCalculation = useLaborCalculation(product, settings);
  const freightCalculation = useFreightCalculation(
    settings,
    event,
    product?.item?.isFreight,
    productState.quantity,
    productState.setQuantity,
  );
  const fileUpload = useFileUpload(
    event,
    booth,
    user,
    slug,
    cart,
    setCart,
    product?.item?.isUpload,
  );

  // Data item type bools
  let productData = useMemo(
    () => ({
      ...product.item,
      images:
        product.item.images.length === 0
          ? ["/assets/images/products/NO-PRODUCT-IMAGE.jpg"]
          : product.item.images,
      currentPrice: product.itemPrice,
      internalId: productValidation.validatedItem.childId || product.item.id,
      ...(productValidation.validatedItem.childId && {
        parentProductId: product.item.id,
      }),
      // Include collection information from props or fall back to product data
      collection: collection ||
        product?.collection || {
          id: null,
          name: "Unknown Collection",
        },
    }),
    [product, productValidation.validatedItem],
  );

  const {
    itemPrice: price,
    discount,
    comparePrice,
    daysCalcDates,
    laborDates,
    showDates,
    laborScheduleNew: laborSchedule,
  } = product;

  const {
    hasColorOptions,
    hasOrientOptions,
    hasGraphicOptions,
    hasSizeOptions,
    isShowDuration,
    isFreight,
    isLabor,
    isSquareFt,
    isEstimated,
    isDaysCalc,
    isMemoItem,
    isUpload,
    images,
    id,
    itemAttributes,
    title,
    webDescription: description,
    minimumQuantity,
    maxQuantity = Infinity,
    enforceMinQty,
    materialHandlingSchedule = null,
  } = productData;

  const isPerPieceItem =
    isFreight && materialHandlingSchedule?.chargeType?.value === "2";

  // Variant options for validation
  let hasVariants = [
    { exists: hasColorOptions, name: "color" },
    { exists: hasOrientOptions, name: "orientations" },
    { exists: hasGraphicOptions, name: "materials" },
    { exists: hasSizeOptions, name: "sizes" },
  ];

  let variantOptions = hasVariants.filter((v) => v.exists);

  // Find cart item
  const cartItem = cart.find((item) => {
    if (variantOptions.length !== 0 && !isDaysCalc && !isSquareFt) {
      return (
        item.internalid === productData.internalId &&
        item.variant.color === productState.color &&
        item.variant.sizes === productState.size &&
        item.variant.materials === productState.material &&
        item.variant.orientations === productState.variantSelected
      );
    }

    if (isSquareFt) {
      if (isShowDuration) {
        return (
          item.internalid === id &&
          showDates.map((dt) => dt?.date).includes(item.showDate) &&
          item.squareFtWidth === productState.optionWidth &&
          item.squareFtLength === productState.optionLength
        );
      }

      if (isDaysCalc) {
        return (
          item.internalid === id &&
          item.showDate === laborCalculation.daysCalcDateSelected?.date &&
          item.squareFtWidth === productState.optionWidth &&
          item.squareFtLength === productState.optionLength
        );
      }
    }

    if (isMemoItem && (!isDaysCalc || !isShowDuration)) {
      return item.internalid === id && item.memoText === productState.memoText;
    }

    if (isDaysCalc && isMemoItem) {
      return (
        item.internalid === id &&
        item.showDate === laborCalculation.daysCalcDateSelected?.date &&
        item.memoText === productState.memoText
      );
    }

    if (isShowDuration && isMemoItem) {
      return (
        item.internalid === id &&
        showDates.map((dt) => dt?.date).includes(item.showDate) &&
        item.memoText === productState.memoText
      );
    }

    if (isDaysCalc) {
      return (
        item.internalid === id &&
        item.showDate === laborCalculation.daysCalcDateSelected?.date
      );
    }

    return item.internalid === id;
  });

  // Initialize cart operations hook
  const cartOperations = useCartOperations(
    cartItem,
    minimumQuantity,
    maxQuantity,
    enforceMinQty,
    productData,
    itemOptions,
    isSquareFt,
    isShowDuration,
    isDaysCalc,
    isFreight,
    isLabor,
    isEstimated,
    laborCalculation.laborBucketBuild,
    laborCalculation.laborTableOpen,
    laborCalculation.setLaborTableOpen,
    laborCalculation.daysCalcDateSelected,
    cart,
    id,
  );

  // Cart options for add to cart functionality
  const cartOptions = useMemo(
    () => ({
      detail: true,
      laborDateSelected: laborCalculation.laborDateSelected,
      laborHoursTotal: laborCalculation.laborHoursTotal,
      laborSupervision: laborCalculation.laborSupervision,
      laborSupervisionCost: laborCalculation.laborSupervisionCost,
      laborWorkerQuantity: laborCalculation.laborWorkerQuantity,
      laborStartTimeSelected: laborCalculation.laborStartTimeSelected,
      laborEndTimeSelected: laborCalculation.laborEndTimeSelected,
      laborBucketBuild: laborCalculation.laborBucketBuild,
      laborHourlyAvg: laborCalculation.laborHourlyAvg,
      memoText: productState.memoText,
      attributes,
      setAttributes,
      optionTotalSqFt: productState.optionTotalSqFt,
      optionWidth: productState.optionWidth,
      optionLength: productState.optionLength,
      color: productState.color,
      size: productState.size,
      material: productState.material,
      orientation: productState.variantSelected,
      screenSize: productState.screenSize,
      daysCalcDateSelected: laborCalculation.daysCalcDateSelected,
      quantity: productState.quantity,
      validatedItem: productValidation.validatedItem,
      validatingItem: productValidation.validatingItem,
      cwtFreightWeightEntered: freightCalculation.cwtFreightWeightEntered,
      fileAttachments: fileUpload.fileAttachments,
      // functions
      setQuantity: productState.setQuantity,
      setDaysCalcDateSelected: laborCalculation.setDaysCalcDateSelected,
      setOptionWidth: productState.setOptionWidth,
      setOptionLength: productState.setOptionLength,
      setOptionTotalSqFt: productState.setOptionTotalSqFt,
      setColor: productState.setColor,
      setSize: productState.setSize,
      setMaterial: productState.setMaterial,
      setScreenSize: productState.setScreenSize,
      setOrientation: productState.setVariantSelected,
      setCwtFreightWeight: freightCalculation.setCwtFreightWeight,
      setCwtFreightWeightEntered: freightCalculation.setCwtFreightWeightEntered,
      setLaborTableOpen: laborCalculation.setLaborTableOpen,
      setLaborBucketBuild: laborCalculation.setLaborBucketBuild,
      setLaborDateSelected: laborCalculation.setLaborDateSelected,
      setLaborCostTotal: laborCalculation.setLaborCostTotal,
      setLaborHoursTotal: laborCalculation.setLaborHoursTotal,
      setLaborHourlyAvg: laborCalculation.setLaborHourlyAvg,
      setLaborWorkerQuantity: laborCalculation.setLaborWorkerQuantity,
      setLaborSupervision: laborCalculation.setLaborSupervision,
      setLaborSupervisionCost: laborCalculation.setLaborSupervisionCost,
      setLaborStartTimeSelected: laborCalculation.setLaborStartTimeSelected,
      setLaborEndTimeSelected: laborCalculation.setLaborEndTimeSelected,
      setMemoText: productState.setMemoText,
      setColorError: productValidation.setColorError,
      setSizeError: productValidation.setSizeError,
      setMaterialError: productValidation.setMaterialError,
      setOrientationError: productValidation.setVariantError,
      colorError: productValidation.colorError,
      sizeError: productValidation.sizeError,
      materialError: productValidation.materialError,
      orientationError: productValidation.variantError,
      setFilesUploaded: fileUpload.setFilesUploaded,
      setFilesAttachments: fileUpload.setFilesAttachments,
    }),
    [
      laborCalculation,
      productState,
      productValidation,
      freightCalculation,
      fileUpload,
      attributes,
    ],
  );

  const handleSubmit = useCallback(
    (e) => {
      let submittedAttributes = formRef?.current.values;

      formRef?.current.validateForm().then((res) => {
        formRef?.current?.submitForm().then((ret) => {
          if (Object.keys(res).length === 0) {
            cartOptions.attributes = submittedAttributes;
            formRef?.current.resetForm();
            return cartOperations.understandAddCartItem(
              e,
              optionModalVisible,
              cartOptions,
            );
          }
        });
      });
    },
    [cartOptions, cartOperations, optionModalVisible],
  );

  // ====================================================
  // Effects
  // ==================================================

  // Set state of item once dom loads
  useEffect(() => {
    if (product?.item) {
      setSaleUnit(product?.item?.saleUnit);
      setPrimaryImage(
        images.length !== 0 ? images[0] : "/NO-PRODUCT-IMAGE.jpg",
      );
      productData.image = primaryImage;
      productData.index = cart.length - 1;
    }
  }, [images, primaryImage, product, productData, cart.length]);

  // Set up item options for variants
  useEffect(() => {
    if (product) {
      let variantCheck = [
        { name: "color", value: product.item.colors || [] },
        { name: "sizes", value: product.item.sizes || [] },
        { name: "variants", value: product.item.orientations || [] },
        { name: "materials", value: product.item.materials || [] },
      ];

      let verifiedVariants = variantCheck.filter(
        (variant) => variant.value.length !== 0,
      );

      if (verifiedVariants.length !== 0) {
        let variants = {};
        verifiedVariants.forEach((variant) => {
          variants[variant.name] = variant.value;
        });
        setItemOptions(variants);
      } else {
        setItemOptions({});
        productState.setSelectedOptions({
          color: "",
          sizes: "",
          materials: "",
          variants: "",
        });
      }
    }
  }, [product?.item, slug, productState]);

  // Handle item validation
  useEffect(() => {
    let active = true;
    if (active) {
      productValidation.handleItemPrefetchValidation(
        active,
        hasColorOptions,
        hasOrientOptions,
        hasGraphicOptions,
        hasSizeOptions,
        productState.netColor,
        productState.netSize,
        productState.netVariant,
        productState.netMaterial,
        productState.netScreenSize,
      );
    }
    return () => {
      active = false;
    };
  }, [
    productState.selectedOptions,
    productValidation,
    hasColorOptions,
    hasOrientOptions,
    hasGraphicOptions,
    hasSizeOptions,
    productState,
  ]);

  // SqFt Calc
  useEffect(() => {
    if (isSquareFt) {
      const squareFoot = productState.optionWidth * productState.optionLength;
      productState.setOptionTotalSqFt(squareFoot);
    }
  }, [
    productState.optionLength,
    productState.optionWidth,
    productState,
    isSquareFt,
  ]);

  // Update add to cart disabled state
  useEffect(() => {
    if (
      productValidation.validatedItem.childId &&
      productValidation.validatedItem.price
    ) {
      productValidation.setIsAddToCartDisabled(false);
    } else if (variantOptions.length === 0 && !itemAttributes?.length) {
      productValidation.setIsAddToCartDisabled(false);
    } else {
      productValidation.setIsAddToCartDisabled(true);
    }
  }, [
    productValidation.validatedItem,
    variantOptions.length,
    itemAttributes,
    productValidation,
  ]);

  // File upload effect for cart items
  useEffect(() => {
    let active = true;
    if (cartItem?.fileAttachments && !isSquareFt && active) {
      fileUpload.setFilesAttachments(cartItem.fileAttachments);
      fileUpload.setFilesUploaded(cartItem.fileAttachments?.attachments);
      return () => {
        active = false;
      };
    }

    if (
      active &&
      cartItem &&
      fileUpload.fileAttachments.attachments.length !== 0 &&
      !Object.prototype.hasOwnProperty.call(cartItem, "fileAttachments")
    ) {
      fileUpload.setFilesUploaded([]);
      fileUpload.setFilesAttachments({
        attachments: [],
        attachmentGroup: -1,
      });
    }

    return () => {
      active = false;
    };
  }, [cartItem, slug, fileUpload]);

  return (
    <Box width="100%">
      {isLabor && (
        <LaborTablePreview
          open={laborCalculation.laborTableOpen}
          onClose={() => laborCalculation.setLaborTableOpen(false)}
          laborBucketBuild={laborCalculation.laborBucketBuild}
        />
      )}

      <Grid container spacing={3} justifyContent="space-around">
        <ProductImageGallery
          title={title}
          images={images}
          selectedImage={productState.selectedImage}
          handleImageClick={productState.handleImageClick}
        />

        <Grid item md={6} xs={12} alignItems="center">
          <H1 mb={1}>{title}</H1>

          <FlexBox alignItems="center" mb={1}>
            <Box>Category:&nbsp;</Box>
            <H6>{productData.category}</H6>
          </FlexBox>

          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Box display="flex" py={3}>
                <ProductVariantSelector
                  itemOptions={itemOptions}
                  selectedOptions={productState.selectedOptions}
                  handleVariantSelect={productState.handleVariantSelect}
                  variantErrors={{
                    color: productValidation.colorError,
                    sizes: productValidation.sizeError,
                    variants: productValidation.variantError,
                    materials: productValidation.materialError,
                  }}
                  variantOptionState={{
                    color: productState.color,
                    sizes: productState.size,
                    variants: productState.variantSelected,
                    materials: productState.material,
                  }}
                />
              </Box>
            </Grid>
          </Grid>

          <ProductOptions
            isSquareFt={isSquareFt}
            isFreight={isFreight}
            isLabor={isLabor}
            isDaysCalc={isDaysCalc}
            isMemoItem={isMemoItem}
            optionWidth={productState.optionWidth}
            optionLength={productState.optionLength}
            optionTotalSqFt={productState.optionTotalSqFt}
            cwtFreightWeight={freightCalculation.cwtFreightWeight}
            cwtFreightWeightEntered={freightCalculation.cwtFreightWeightEntered}
            daysCalcDates={daysCalcDates}
            daysCalcDateSelected={laborCalculation.daysCalcDateSelected}
            laborDates={laborDates}
            laborDateSelected={laborCalculation.laborDateSelected}
            laborStartTimeSelected={laborCalculation.laborStartTimeSelected}
            laborEndTimeSelected={laborCalculation.laborEndTimeSelected}
            laborSupervision={laborCalculation.laborSupervision}
            laborWorkerQuantity={laborCalculation.laborWorkerQuantity}
            laborTableOpen={laborCalculation.laborTableOpen}
            laborBucketBuild={laborCalculation.laborBucketBuild}
            memoText={productState.memoText}
            handleWidthChange={productState.handleWidthChange}
            handleWidthCapture={productState.handleWidthCapture}
            handleLengthChange={productState.handleLengthChange}
            handleLengthCapture={productState.handleLengthCapture}
            handleTotalFootChange={productState.handleTotalFootChange}
            handleTotalFootCapture={productState.handleTotalFootCapture}
            handleFreightWeightChange={
              freightCalculation.handleFreightWeightChange
            }
            handleFreightWeightCapture={
              freightCalculation.handleFreightWeightCapture
            }
            setDaysCalcDateSelected={laborCalculation.setDaysCalcDateSelected}
            setLaborDateSelected={laborCalculation.setLaborDateSelected}
            setLaborStartTimeSelected={
              laborCalculation.setLaborStartTimeSelected
            }
            setLaborEndTimeSelected={laborCalculation.setLaborEndTimeSelected}
            setLaborSupervision={laborCalculation.setLaborSupervision}
            handleWorkerQuantityChange={
              laborCalculation.handleWorkerQuantityChange
            }
            handleWorkerQuantityCapture={
              laborCalculation.handleWorkerQuantityCapture
            }
            setLaborTableOpen={laborCalculation.setLaborTableOpen}
            handleLaborTimeDisabled={laborCalculation.handleLaborTimeDisabled}
            setMemoText={productState.setMemoText}
          />

          {itemAttributes.length !== 0 && (
            <Grid item xs={12}>
              <FormikDynamic
                itemAttributes={itemAttributes}
                setAttributes={setAttributes}
                innerRef={formRef}
              />
            </Grid>
          )}

          <ProductPricing
            validatingItem={productValidation.validatingItem}
            validatedItem={productValidation.validatedItem}
            price={price}
            discount={discount}
            comparePrice={comparePrice}
            variantOptions={variantOptions}
            settings={settings}
          />

          <CartControls
            cartItem={cartItem}
            validatingItem={productValidation.validatingItem}
            uploadingFile={fileUpload.uploadingFile}
            isPerPieceItem={isPerPieceItem}
            variantOptions={variantOptions}
            isAddToCartDisabled={productValidation.isAddToCartDisabled}
            itemAttributes={itemAttributes}
            isLabor={isLabor}
            isFreight={isFreight}
            minimumQuantity={minimumQuantity}
            maxQuantity={maxQuantity}
            validatedItem={productValidation.validatedItem}
            currency={currency}
            handleSubmit={handleSubmit}
            understandAddCartItem={(e) =>
              cartOperations.understandAddCartItem(
                e,
                optionModalVisible,
                cartOptions,
              )
            }
            optionModalVisible={optionModalVisible}
            decreaseExistingQuantity={cartOperations.decreaseExistingQuantity}
            quantityChange={cartOperations.quantityChange}
          />

          <ProductAlerts
            isPerPieceItem={isPerPieceItem}
            isEstimated={isEstimated}
          />

          <FileUploadSection
            isUpload={isUpload}
            getRootProps={fileUpload.getRootProps}
            getInputProps={fileUpload.getInputProps}
            isDragActive={fileUpload.isDragActive}
            isDragAccept={fileUpload.isDragAccept}
            isDragReject={fileUpload.isDragReject}
            acceptedFiles={fileUpload.acceptedFiles}
            filesUploaded={fileUpload.filesUploaded}
            deleteingFile={fileUpload.deleteingFile}
            handleFileRemove={async (e, file) => {
              // File removal logic would be implemented in the hook
              console.log("File remove:", file);
            }}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default ProductIntro;
