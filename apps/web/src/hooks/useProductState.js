import { useState, useCallback } from 'react';

/**
 * Custom hook for managing basic product state
 * Handles quantity, image selection, and variant selections
 */
export const useProductState = (initialQuantity = 1) => {
  // Basic product state
  const [quantity, setQuantity] = useState(initialQuantity);
  const [selectedImage, setSelectedImage] = useState(0);
  
  // Variant selections
  const [selectedOptions, setSelectedOptions] = useState({
    color: "",
    sizes: "",
    materials: "",
    variants: "",
  });
  
  // Individual variant states
  const [color, setColor] = useState("");
  const [size, setSize] = useState("");
  const [material, setMaterial] = useState("");
  const [screenSize, setScreenSize] = useState("");
  const [variantSelected, setVariantSelected] = useState("");
  
  // Netsuite payload build states
  const [netSize, setNetSize] = useState({});
  const [netColor, setNetColor] = useState({});
  const [netMaterial, setNetMaterial] = useState({});
  const [netScreenSize, setNetScreenSize] = useState({});
  const [netVariant, setNetVariant] = useState({});
  
  // Special item type states
  const [optionWidth, setOptionWidth] = useState(10);
  const [optionLength, setOptionLength] = useState(10);
  const [optionTotalSqFt, setOptionTotalSqFt] = useState(100);
  const [memoText, setMemoText] = useState("");
  
  // Handlers
  const handleQuantityChange = useCallback((e) => {
    const value = parseInt(e.target.value);
    if (!Number.isNaN(value)) {
      setQuantity(value);
    } else {
      setQuantity("");
    }
  }, []);

  const handleQuantityCapture = useCallback((e) => {
    const value = e.target.value;
    if (Number.isNaN(value) || value === "") {
      setQuantity(1);
    } else {
      setQuantity(parseInt(value));
    }
  }, []);

  const increaseQuantity = useCallback(() => {
    setQuantity((prevState) => prevState + 1);
  }, []);

  const decreaseQuantity = useCallback(() => {
    setQuantity((prevState) => (prevState > 1 ? prevState - 1 : 1));
  }, []);

  const handleImageClick = useCallback((ind) => () => setSelectedImage(ind), []);

  const handleVariantSelect = useCallback((n, variant, variantVal) => {
    setSelectedOptions(prev => ({ ...prev, [variant]: variantVal }));
    const variantData = JSON.parse(n.props.dataset);
    
    switch (variant) {
      case "color":
        setColor(variantVal);
        setNetColor(variantData);
        break;
      case "sizes":
        setSize(variantVal);
        setNetSize(variantData);
        break;
      case "materials":
        setMaterial(variantVal);
        setNetMaterial(variantData);
        break;
      case "variants":
        setVariantSelected(variantVal);
        setNetVariant(variantData);
        break;
      default:
        console.error("No variant type specified in onChange handler.");
    }
  }, []);

  // Dimension handlers
  const handleLengthChange = useCallback((e) => {
    const value = parseInt(e.target.value);
    if (!Number.isNaN(value)) {
      setOptionLength(value);
    } else {
      setOptionLength("");
    }
  }, []);

  const handleLengthCapture = useCallback((e) => {
    const value = e.target.value;
    if (Number.isNaN(value) || value === "") {
      setOptionLength(1);
    } else {
      setOptionLength(parseInt(value));
    }
  }, []);

  const handleWidthChange = useCallback((e) => {
    const value = parseInt(e.target.value);
    if (!Number.isNaN(value)) {
      setOptionWidth(value);
    } else {
      setOptionWidth("");
    }
  }, []);

  const handleWidthCapture = useCallback((e) => {
    const value = e.target.value;
    if (Number.isNaN(value) || value === "") {
      setOptionWidth(1);
    } else {
      setOptionWidth(parseInt(value));
    }
  }, []);

  const handleTotalFootChange = useCallback((e) => {
    const value = parseInt(e.target.value);
    if (!Number.isNaN(value)) {
      setOptionTotalSqFt(value);
    } else {
      setOptionTotalSqFt("");
    }
  }, []);

  const handleTotalFootCapture = useCallback((e) => {
    const value = e.target.value;
    if (Number.isNaN(value) || value === "") {
      setOptionTotalSqFt(1);
    } else {
      setOptionTotalSqFt(parseInt(value));
    }
  }, []);

  const resetVariantSelections = useCallback(() => {
    setSelectedOptions({
      color: "",
      sizes: "",
      materials: "",
      variants: "",
    });
    setColor("");
    setSize("");
    setMaterial("");
    setVariantSelected("");
    setScreenSize("");
  }, []);

  return {
    // State
    quantity,
    selectedImage,
    selectedOptions,
    color,
    size,
    material,
    screenSize,
    variantSelected,
    netSize,
    netColor,
    netMaterial,
    netScreenSize,
    netVariant,
    optionWidth,
    optionLength,
    optionTotalSqFt,
    memoText,
    
    // Setters
    setQuantity,
    setSelectedImage,
    setSelectedOptions,
    setColor,
    setSize,
    setMaterial,
    setScreenSize,
    setVariantSelected,
    setNetSize,
    setNetColor,
    setNetMaterial,
    setNetScreenSize,
    setNetVariant,
    setOptionWidth,
    setOptionLength,
    setOptionTotalSqFt,
    setMemoText,
    
    // Handlers
    handleQuantityChange,
    handleQuantityCapture,
    increaseQuantity,
    decreaseQuantity,
    handleImageClick,
    handleVariantSelect,
    handleLengthChange,
    handleLengthCapture,
    handleWidthChange,
    handleWidthCapture,
    handleTotalFootChange,
    handleTotalFootCapture,
    resetVariantSelections,
  };
};
