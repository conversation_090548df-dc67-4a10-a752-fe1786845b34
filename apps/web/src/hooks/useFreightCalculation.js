import { useState, useEffect, useCallback } from 'react';
import { useSnackbar } from 'notistack';

/**
 * Custom hook for managing freight calculation logic
 * Handles CWT freight weight calculations and quantity adjustments
 */
export const useFreightCalculation = (settings, event, isFreight, quantity, setQuantity) => {
  const [cwtFreightWeight, setCwtFreightWeight] = useState(0);
  const [cwtFreightWeightEntered, setCwtFreightWeightEntered] = useState(0);
  const { enqueueSnackbar } = useSnackbar();

  // Initialize freight weight with minimum
  useEffect(() => {
    if (Object.keys(settings).length !== 0) {
      const weightMinimum =
        Number(event?.details.rates.freight.minimum) ||
        Number(settings.custrecord_ng_cs_freight_minimum);

      setCwtFreightWeight(weightMinimum);
      setCwtFreightWeightEntered(weightMinimum);
    }
  }, [settings, event]);

  // Auto-adjust quantity based on freight weight
  useEffect(() => {
    const weightMinimum =
      Number(event?.details.rates.freight.minimum) ||
      Number(settings.custrecord_ng_cs_freight_minimum);

    const getFreightItemQuantity = () => {
      const freightQuantity = Math.ceil(cwtFreightWeight / 100);
      
      if (isFreight && Object.keys(settings).length !== 0) {
        if (quantity < freightQuantity) {
          setQuantity(freightQuantity);
        } else if (
          !Number.isNaN(cwtFreightWeight) &&
          cwtFreightWeight > weightMinimum
        ) {
          setQuantity(Math.ceil(cwtFreightWeight / 100));
        } else if (
          !Number.isNaN(cwtFreightWeight) &&
          cwtFreightWeight < weightMinimum
        ) {
          setQuantity(Math.ceil(parseInt(weightMinimum) / 100));
        }
      }
    };

    getFreightItemQuantity();
  }, [cwtFreightWeight, quantity, isFreight, settings, event, setQuantity]);

  const handleFreightWeightChange = useCallback((e) => {
    const value = parseInt(e.target.value);
    if (!Number.isNaN(value)) {
      setCwtFreightWeight(value);
    } else {
      setCwtFreightWeight("");
    }
  }, []);

  const handleFreightWeightCapture = useCallback((e) => {
    const cleanedInput = e.target.value.replace(/,/g, "").replace("WT ", "");
    const parsedNumber = parseFloat(cleanedInput);

    const weightMinimum =
      Number(event?.details.rates.freight.minimum) ||
      Number(settings.custrecord_ng_cs_freight_minimum);

    if (!Number.isNaN(parsedNumber) && parsedNumber > weightMinimum) {
      setCwtFreightWeightEntered(parsedNumber);
      setCwtFreightWeight(Math.ceil(parsedNumber / 100) * 100);
    } else if (!Number.isNaN(parsedNumber) && parsedNumber < weightMinimum) {
      enqueueSnackbar(
        `Minimum weight must be above ${weightMinimum}. Resetting to minimum.`,
        {
          variant: "warning",
          autoHideDuration: 3000,
        },
      );
      setCwtFreightWeight(Math.ceil(weightMinimum / 100) * 100);
      setCwtFreightWeightEntered(weightMinimum);
    } else {
      setCwtFreightWeight(weightMinimum);
      setCwtFreightWeightEntered(weightMinimum);
    }
  }, [event, settings, enqueueSnackbar]);

  return {
    cwtFreightWeight,
    cwtFreightWeightEntered,
    setCwtFreightWeight,
    setCwtFreightWeightEntered,
    handleFreightWeightChange,
    handleFreightWeightCapture,
  };
};
