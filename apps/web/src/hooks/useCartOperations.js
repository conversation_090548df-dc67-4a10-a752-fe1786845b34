import { useCallback } from 'react';
import { useSnackbar } from 'notistack';
import { useCart, useAddToCart } from '../store/zustandCartStore';

/**
 * Custom hook for cart operations
 * Handles adding items to cart, quantity changes, and cart item management
 */
export const useCartOperations = (
  cartItem,
  minimumQuantity,
  maxQuantity,
  enforceMinQty,
  productData,
  itemOptions,
  isSquareFt,
  isShowDuration,
  isDaysCalc,
  isFreight,
  isLabor,
  isEstimated,
  laborBucketBuild,
  laborTableOpen,
  setLaborTableOpen,
  daysCalcDateSelected,
  cart,
  id
) => {
  const { setItemQuantity, removeCartItem } = useCart();
  const { addToCart } = useAddToCart();
  const { enqueueSnackbar } = useSnackbar();

  const quantityChange = useCallback((value) => {
    const minQty = Number(minimumQuantity) || 1;
    const maxQty = Number(maxQuantity) || Infinity;
    let updatedQuantity = value;

    const setItemQty = (item, qty) => setItemQuantity(item, Number(qty));
    const deleteItem = (item) => {
      return confirm("Are you sure you want to delete this item?")
        ? removeCartItem(item)
        : setItemQty(item, minQty);
    };

    // Check if the updated quantity exceeds the maximum quantity
    if (updatedQuantity > maxQty) {
      console.log(
        `Cannot set quantity to ${updatedQuantity}. Maximum quantity allowed is ${maxQty}.`,
      );
      updatedQuantity = maxQty;
    }

    // Check if enforceMinQty is enabled and the updated quantity drops below the minimum quantity
    if (enforceMinQty && updatedQuantity < minQty) {
      console.log(
        `Cannot set quantity to ${updatedQuantity}. Minimum quantity required is ${minQty}. Removing item from cart.`,
      );
      deleteItem(cartItem);
      return;
    }

    if (!value) {
      setItemQty(cartItem, minQty);
    } else if (value > 0) {
      setItemQty(cartItem, updatedQuantity);
    } else if (!enforceMinQty && value <= 0) {
      deleteItem(cartItem);
    }
  }, [cartItem, minimumQuantity, maxQuantity, enforceMinQty, setItemQuantity, removeCartItem]);

  const decreaseExistingQuantity = useCallback(() => {
    const minQty = Number(minimumQuantity) || 1;

    if (enforceMinQty && cartItem.quantity > minQty) {
      if (cartItem.isShowDuration) {
        cart
          .filter(
            (el) => el.isShowDuration && Number(el.internalid) === Number(id),
          )
          .forEach((existingItem) => {
            if (existingItem.quantity > 1) {
              setItemQuantity(existingItem, existingItem.quantity - 1);
            }
          });
      } else {
        setItemQuantity(cartItem, cartItem.quantity - 1);
      }

      enqueueSnackbar(`Removed a "${cartItem.name}" from cart`, {
        variant: "error",
      });
    } else if (enforceMinQty && cartItem.quantity <= minQty) {
      if (cartItem.isShowDuration) {
        cart
          .filter(
            (el) => el.isShowDuration && Number(el.internalid) === Number(id),
          )
          .forEach((existingItem) => {
            if (confirm("Are you sure you want to delete this item?")) {
              removeCartItem(existingItem);
            }
          });
      } else if (confirm("Are you sure you want to delete this item?")) {
        removeCartItem(cartItem);
      }
    } else {
      setItemQuantity(cartItem, cartItem.quantity - 1);
      enqueueSnackbar(`Removed a "${cartItem.name}" from cart`, {
        variant: "error",
      });
    }
  }, [cartItem, minimumQuantity, enforceMinQty, cart, id, setItemQuantity, removeCartItem, enqueueSnackbar]);

  const understandAddCartItem = useCallback(async (e, modalState, cartOptions) => {
    // If any pre calcs need down this method is where it should happen prior to running the addToCart() Func.
    if (Object.keys(itemOptions).length === 0) {
      if (isSquareFt) {
        console.log("Detected Area calc item type.");
        if (isShowDuration) {
          return addToCart(e, "", productData, cartOptions);
        }
        if (isDaysCalc) {
          // Check if a date has been selected
          if (!daysCalcDateSelected) {
            alert("Please select a date before adding this item to your cart.");
            return;
          }
          return addToCart(e, daysCalcDateSelected, productData, cartOptions);
        }
        return addToCart(e, "", productData, cartOptions);
      }
      if (isFreight) {
        return addToCart(e, "", productData, cartOptions);
      }
      if (isLabor) {
        if (laborBucketBuild.length !== 0) {
          return setLaborTableOpen(!laborTableOpen);
        }
        alert("Choose a date, following a START and END time to continue.");
      } else if (isDaysCalc) {
        // Check if a date has been selected
        if (!daysCalcDateSelected) {
          alert("Please select a date before adding this item to your cart.");
          return;
        }
        return addToCart(e, daysCalcDateSelected, productData, cartOptions);
      } else if (isShowDuration) {
        return addToCart(e, "", productData, cartOptions);
      } else if (isEstimated) {
        return addToCart(e, "", productData, cartOptions);
      } else {
        console.log("Running cart spite of itemOptions");
        return addToCart(e, "", productData, cartOptions);
      }
    } else {
      // Run matrix and general item logic
      await addToCart(e, "", productData, cartOptions);
    }
  }, [
    itemOptions,
    isSquareFt,
    isShowDuration,
    isDaysCalc,
    isFreight,
    isLabor,
    isEstimated,
    laborBucketBuild,
    laborTableOpen,
    setLaborTableOpen,
    daysCalcDateSelected,
    addToCart,
    productData
  ]);

  return {
    decreaseExistingQuantity,
    quantityChange,
    understandAddCartItem
  };
};