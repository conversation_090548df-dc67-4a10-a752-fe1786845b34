import { useState, useEffect, useCallback } from 'react';
import moment from 'moment';
import { usePrevious } from '../utils/customHooks';
import { currency } from 'lib';

/**
 * Custom hook for labor calculation logic
 * Handles labor dates, times, workers, costs, and bucket building
 */
export const useLaborCalculation = (product, settings) => {
  // Labor state
  const [daysCalcDateSelected, setDaysCalcDateSelected] = useState("");
  const [laborDateSelected, setLaborDateSelected] = useState("");
  const [laborStartTimeSelected, setLaborStartTimeSelected] = useState(
    new Date(new Date().setHours(8, 0)),
  );
  const [laborEndTimeSelected, setLaborEndTimeSelected] = useState(
    new Date(new Date().setHours(17, 0)),
  );
  const [laborSupervision, setLaborSupervision] = useState(false);
  const [laborBucketBuild, setLaborBucketBuild] = useState([]);
  const [laborSupervisionCost, setLaborSupervisionCost] = useState(0);
  const [laborTableOpen, setLaborTableOpen] = useState(false);
  const [laborWorkerQuantity, setLaborWorkerQuantity] = useState(1);
  const [laborCostTotal, setLaborCostTotal] = useState("");
  const [laborBareCostTotal, setLaborBareCostTotal] = useState(0);
  const [laborHoursTotal, setLaborHoursTotal] = useState(0);
  const [laborHourlyAvg, setLaborHourlyAvg] = useState(0);
  const [unaccountedLaborHoursTotal, setUnaccountedLaborHoursTotal] = useState(0);

  // Previous values for change detection
  const prevSelectedLaborDate = usePrevious(laborDateSelected);
  const prevWorkerQuantity = usePrevious(laborWorkerQuantity);
  const prevStartSelected = usePrevious(laborStartTimeSelected);
  const prevEndSelected = usePrevious(laborEndTimeSelected);
  const prevSupervisionSelected = usePrevious(laborSupervision);

  // Utility functions
  const roundOff = (num, places) => {
    const x = 10 ** places;
    return Math.round(num * x) / x;
  };

  const createData = (
    id,
    type,
    date,
    workerQuantity,
    hours,
    start,
    end,
    rate,
    totalLabor,
    supervision,
    supervisionCost,
    amount,
  ) => {
    return {
      id,
      type,
      date,
      workerQuantity,
      hours,
      start,
      end,
      rate,
      totalLabor,
      supervision,
      supervisionCost,
      amount,
    };
  };

  const findMissingTimeSlots = (timeSlots) => {
    const startTime = moment("00:00", "HH:mm");
    const endTime = moment("23:59", "HH:mm");
    const missingSlots = [];

    // Sort the time slots based on start time
    timeSlots.sort((a, b) => {
      return moment(a.start, "HH:mm").diff(moment(b.start, "HH:mm"));
    });

    let previousEndTime = startTime;

    // Iterate over the sorted time slots
    for (let i = 0; i < timeSlots.length; i++) {
      const currentSlot = timeSlots[i];
      const currentStartTime = moment(currentSlot.start, "HH:mm");
      const currentEndTime = moment(currentSlot.end, "HH:mm");

      // Check if there is a gap between the previous end time and current start time
      if (previousEndTime.isBefore(currentStartTime)) {
        const missingSlot = {
          start: previousEndTime.format("HH:mm"),
          end: currentStartTime.format("HH:mm"),
        };

        missingSlots.push(missingSlot);
      }

      // Update the previous end time
      if (currentEndTime.isAfter(previousEndTime)) {
        previousEndTime = currentEndTime;
      }
    }

    // Check if there is a gap between the last time slot's end time and the day's end time
    if (previousEndTime.isBefore(endTime)) {
      const missingSlot = {
        start: previousEndTime.format("HH:mm"),
        end: endTime.format("HH:mm"),
      };

      missingSlots.push(missingSlot);
    }

    return missingSlots;
  };

  const getEventsWithinTimeWindow = (events, startTime, endTime) => {
    const dayStart = moment(startTime, "HH:mm");
    const dayEnd = moment(endTime, "HH:mm");
    const dayDuration = moment.duration(dayEnd.diff(dayStart));

    const dayEvents = [];
    let currentEventStart = dayStart.clone();
    let lastEvent = null;

    for (let i = 0; i < events.length; i++) {
      const eventStart = moment(events[i].start, "HH:mm");
      const eventEnd = moment(events[i].end, "HH:mm");
      const eventDuration = moment.duration(eventEnd.diff(eventStart));

      if (eventStart.isBefore(dayEnd) && eventEnd.isAfter(dayStart)) {
        const start = moment.max(eventStart, dayStart);
        const end = moment.min(eventEnd, dayEnd);
        const duration = moment.duration(end.diff(start));

        dayEvents.push({
          id: events[i].id,
          start: start.format("HH:mm"),
          end: end.format("HH:mm"),
          duration: `${duration.asMinutes()} minutes`,
          durationMinutes: duration.asMinutes(),
          multiplier: events[i].multiplier,
          type: events[i].type,
          supervision: events[i].supervision,
        });

        currentEventStart = eventEnd;
        lastEvent = events[i];
      }
    }

    if (currentEventStart.isBefore(dayEnd)) {
      const duration = moment.duration(dayEnd.diff(currentEventStart));

      dayEvents.push({
        id: lastEvent.id,
        start: currentEventStart.format("HH:mm"),
        end: dayEnd.format("HH:mm"),
        duration: `${duration.asMinutes()} minutes`,
        durationMinutes: duration.asMinutes(),
        multiplier: lastEvent.multiplier,
        type: lastEvent.type,
        supervision: lastEvent.supervision,
      });
    }

    return dayEvents;
  };

  const getRatesByDate = useCallback(
    (date) => {
      let foundPriceDates = product?.laborScheduleNew.filter((laborDate) => {
        return moment(moment(date).format("yyyy-MM-DD")).isSame(laborDate.date);
      });

      if (foundPriceDates) {
        console.log("Date selected: ", date);
      } else {
        foundPriceDates = [];
      }

      return foundPriceDates;
    },
    [product.laborScheduleNew],
  );

  const calculateHours = (startTime, endTime) => {
    const duration = endTime.diff(startTime);
    const hours = duration / (1000 * 60 * 60);
    return hours;
  };

  const handleLaborTimeDisabled = (date, view) => {
    let timeDisabled = false;
    const laborDate = laborDateSelected?.date;
    const timeChosen = moment(date).format("HH:mm");
    if (date && laborDate) {
      const rates = getRatesByDate(laborDate);
      const missingTimeSlots = findMissingTimeSlots(rates);
      if (missingTimeSlots.length !== 0) {
        missingTimeSlots.forEach((slot) => {
          if (
            moment(timeChosen, "HH:mm").isBetween(
              moment(slot.start, "HH:mm"),
              moment(slot.end, "HH:mm"),
            )
          ) {
            timeDisabled = true;
          }
        });
      } else {
        timeDisabled = false;
      }
    }

    return timeDisabled;
  };

  const handleWorkerQuantityChange = useCallback((e) => {
    if (!Number.isNaN(parseInt(e.target.value))) {
      setLaborWorkerQuantity(parseInt(e.target.value));
    } else {
      setLaborWorkerQuantity("");
    }
  }, []);

  const handleWorkerQuantityCapture = useCallback((e) => {
    if (Number.isNaN(e.target.value) || e.target.value === "") {
      setLaborWorkerQuantity(1);
    } else {
      setLaborWorkerQuantity(parseInt(e.target.value));
    }
  }, []);

  return {
    // State
    daysCalcDateSelected,
    laborDateSelected,
    laborStartTimeSelected,
    laborEndTimeSelected,
    laborSupervision,
    laborBucketBuild,
    laborSupervisionCost,
    laborTableOpen,
    laborWorkerQuantity,
    laborCostTotal,
    laborBareCostTotal,
    laborHoursTotal,
    laborHourlyAvg,
    unaccountedLaborHoursTotal,
    
    // Setters
    setDaysCalcDateSelected,
    setLaborDateSelected,
    setLaborStartTimeSelected,
    setLaborEndTimeSelected,
    setLaborSupervision,
    setLaborBucketBuild,
    setLaborSupervisionCost,
    setLaborTableOpen,
    setLaborWorkerQuantity,
    setLaborCostTotal,
    setLaborBareCostTotal,
    setLaborHoursTotal,
    setLaborHourlyAvg,
    setUnaccountedLaborHoursTotal,
    
    // Utility functions
    roundOff,
    createData,
    findMissingTimeSlots,
    getEventsWithinTimeWindow,
    getRatesByDate,
    calculateHours,
    handleLaborTimeDisabled,
    handleWorkerQuantityChange,
    handleWorkerQuantityCapture,
  };
};
