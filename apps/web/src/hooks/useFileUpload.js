import { useState, useCallback } from 'react';

export const useFileUpload = () => {
  const [uploadingFile, setUploadingFile] = useState(false);
  const [acceptedFiles, setAcceptedFiles] = useState([]);
  const [filesUploaded, setFilesUploaded] = useState([]);

  const handleFileUpload = useCallback(async (files) => {
    setUploadingFile(true);
    try {
      // Add file upload logic
      setFilesUploaded(prev => [...prev, ...files]);
    } finally {
      setUploadingFile(false);
    }
  }, []);

  return {
    uploadingFile,
    acceptedFiles,
    filesUploaded,
    handleFileUpload,
    setAcceptedFiles
  };
};