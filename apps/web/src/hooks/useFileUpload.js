import { useState, useCallback, useEffect, useMemo } from 'react';
import { useDropzone } from 'react-dropzone';
import { useSnackbar } from 'notistack';
import axios from 'axios';

/**
 * Custom hook for file upload functionality
 * Handles drag & drop, file validation, upload progress, and file management
 */
export const useFileUpload = (event, booth, user, slug, cart, setCart, isUpload) => {
  const [filesUploaded, setFilesUploaded] = useState([]);
  const [fileAttachments, setFilesAttachments] = useState({
    attachments: [],
    attachmentGroup: -1,
  });
  const [deleteingFile, setDeletingFile] = useState(new Set());
  const [uploadingFile, setUploadingFile] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const { enqueueSnackbar } = useSnackbar();

  const attachedFiles = useMemo(() => {
    let updatedAttachments = {
      attachments: [],
      attachmentGroup: -1,
    };

    if (fileAttachments.attachments.length !== 0) {
      updatedAttachments = fileAttachments;
    }

    return updatedAttachments;
  }, [fileAttachments]);

  const onDrop = useCallback(
    async (acceptedFiles) => {
      let fileData = [];
      if (filesUploaded.length !== 0) {
        let updatedFiles = [...filesUploaded];
        let updatedDropFiles = [...acceptedFiles];
        let updatedFileNames = updatedFiles.map((fil) => fil.name);
        let updatedDropFileNames = updatedDropFiles.map((fil) => fil.name);

        // Check if the file already exists in the array
        let isDuplicate = updatedFileNames.some((name) =>
          updatedDropFileNames.includes(name),
        );

        if (!isDuplicate) {
          let updatedAttachments = [...filesUploaded, ...acceptedFiles];
          updatedAttachments.forEach((file, i) => {
            if (!file?.id) {
              fileData.push(file);
            }
          });
        }
      } else {
        acceptedFiles.forEach((file, i) => {
          fileData.push(file);
        });
      }

      const formData = new FormData();
      formData.append("event", JSON.stringify(event.details));
      formData.append("booth", JSON.stringify(booth));
      formData.append("user", JSON.stringify(user));
      formData.append("slug", slug);
      formData.append("attachments", JSON.stringify(attachedFiles));

      Array.from(fileData).forEach((file, i) => {
        formData.append(`files`, file);
      });
      setUploadingFile(true);

      let requestOptions = {
        method: "POST",
        body: formData,
        onUploadProgress: (progressEvent) => {
          const percentage = (progressEvent.loaded * 100) / progressEvent.total;
          setUploadProgress(+percentage.toFixed(2));
        },
      };

      if (!formData.get("files")) {
        setUploadingFile(false);
        setUploadProgress(0);
        enqueueSnackbar(
          "File is a duplicate or currently uploading. Please try again.",
          {
            variant: "warning",
          },
        );
        return;
      }

      try {
        const res = await axios.post(`/api/product/${slug}/upload`, formData, requestOptions);
        if (res.status === 200) {
          enqueueSnackbar("Files uploaded successfully!", {
            variant: "success",
          });

          // Set the files to the state
          setFilesAttachments(res.data.upload);

          // Update cart item if it exists
          const cartItem = cart.find(item => item.id);
          if (cartItem?.id) {
            let updatedItem = {
              ...cartItem,
              fileAttachments: res.data.upload,
            };
            const updatedCart = [...cart];
            const index = updatedCart.findIndex(
              (item) => item.id === cartItem.id,
            );
            updatedCart[index] = updatedItem;
            setCart(updatedCart);
          }

          setUploadingFile(false);
          setUploadProgress(0);
        } else {
          enqueueSnackbar("Files failed to upload!", {
            variant: "error",
          });
          setUploadingFile(false);
          setUploadProgress(0);
        }
      } catch (err) {
        console.log("POST ERROR:", err);
        enqueueSnackbar("Files failed to upload!", {
          variant: "error",
        });
        setUploadingFile(false);
        setUploadProgress(0);
      }
    },
    [
      attachedFiles,
      booth,
      enqueueSnackbar,
      event.details,
      filesUploaded,
      slug,
      user,
      cart,
      setCart,
    ],
  );

  const {
    acceptedFiles,
    isDragActive,
    isDragAccept,
    isDragReject,
    getRootProps,
    getInputProps,
    fileRejections,
  } = useDropzone({
    accept: {
      "image/png": [".png"],
      "image/jpeg": [".jpg", ".jpeg"],
      "application/pdf": [".pdf"],
      "application/msword": [".doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [".docx"],
      "application/vnd.ms-excel": [".xls"],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
        ".xlsx",
      ],
      "model/gltf-binary": [".glb"],
      "model/gltf+json": [".gltf"],
    },
    onDrop,
    disabled: !isUpload || !event.details.id,
  });

  return {
    filesUploaded,
    fileAttachments,
    deleteingFile,
    uploadingFile,
    uploadProgress,
    acceptedFiles,
    isDragActive,
    isDragAccept,
    isDragReject,
    getRootProps,
    getInputProps,
    fileRejections,
    setFilesUploaded,
    setFilesAttachments,
    setDeletingFile,
    setUploadingFile,
    setUploadProgress,
  };
};