import { useState, useCallback, useEffect } from 'react';
import Cookies from 'js-cookie';
import delay from 'delay';

/**
 * Custom hook for product validation logic
 * Handles item validation against NetSuite and error states
 */
export const useProductValidation = (product, selectedOptions) => {
  const [validatingItem, setValidatingItem] = useState(false);
  const [validatedItem, setValidatedItem] = useState({
    childId: "",
    price: null,
  });
  const [retryCount, setRetryCount] = useState(0);
  const [isAddToCartDisabled, setIsAddToCartDisabled] = useState(true);

  // Validation error states
  const [colorError, setColorError] = useState(false);
  const [sizeError, setSizeError] = useState(false);
  const [materialError, setMaterialError] = useState(false);
  const [screenSizeError, setScreenSizeError] = useState(false);
  const [variantError, setVariantError] = useState(false);

  // Item validation against created payload including the value & text
  const checkNetsuiteCartItem = useCallback(async (netColor, netSize, netVariant, netMaterial, netScreenSize) => {
    if (!validatingItem) {
      async function runVariantCheck(id) {
        const chosenVariants = {
          color: netColor,
          size: netSize,
          orientation: netVariant,
          material: netMaterial,
          screenSize: netScreenSize,
        };

        const netPayload = {
          parentId: id,
          variantsChosen: chosenVariants,
          eventId: Cookies.get("eventId"),
        };

        const raw = JSON.stringify(netPayload);
        const requestOptions = {
          method: "POST",
          redirect: "follow",
          body: raw,
        };

        async function fetchRetry(url, timeout, tries, fetchOptions = {}) {
          setRetryCount(tries);
          function onError(err) {
            setRetryCount(-1);
            if (retryCount === 0) {
              throw err;
            }
            return delay(timeout).then(() =>
              fetchRetry(url, timeout, retryCount, fetchOptions),
            );
          }
          return fetch(url, fetchOptions).catch(onError);
        }

        setValidatingItem(true);
        try {
          const res = await fetchRetry(
            `/api/customer/cart/item_check`,
            1500,
            5,
            requestOptions,
          );
          const resJson = await res.json();
          setValidatingItem(false);
          return resJson;
        } catch (err) {
          setValidatingItem(false);
          return err;
        }
      }

      return await runVariantCheck(product?.item?.id);
    }
    return {
      error: {
        name: "Spam Detect",
        message:
          "Add item spam detected, your item was still being validated. Items are not added to the cart at this time. Please try again after it finished.",
      },
    };
  }, [validatingItem, retryCount, product?.item?.id]);

  const handleItemPrefetchValidation = useCallback(async (
    active,
    hasColorOptions,
    hasOrientOptions,
    hasGraphicOptions,
    hasSizeOptions,
    netColor,
    netSize,
    netVariant,
    netMaterial,
    netScreenSize
  ) => {
    const hasVariants = [
      { exists: hasColorOptions, name: "color" },
      { exists: hasOrientOptions, name: "variants" },
      { exists: hasGraphicOptions, name: "materials" },
      { exists: hasSizeOptions, name: "sizes" },
    ];

    const variantOptions = hasVariants.filter((v) => v.exists);
    const variantsChecking = ["color", "materials", "sizes", "variants"];

    const buildVariantCheckObjArr = (checkingArr) => {
      if (Array.isArray(checkingArr)) {
        return checkingArr.map((variantName) => {
          switch (variantName) {
            case "color":
              return { name: variantName, value: selectedOptions.color };
            case "materials":
              return { name: variantName, value: selectedOptions.materials };
            case "variants":
              return { name: variantName, value: selectedOptions.variants };
            case "sizes":
              return { name: variantName, value: selectedOptions.sizes };
            default:
              return null;
          }
        });
      }
      return "Need an array of variant names to check when items get added to cart have itemOptions.";
    };

    const buildVariantNameList = () => {
      return buildVariantCheckObjArr(variantsChecking).filter((v) => v.value);
    };

    if (variantOptions.length !== 0 && active) {
      if (buildVariantNameList().length !== variantOptions.length) {
        console.log("Not all variants ready...");
      } else {
        try {
          const res = await checkNetsuiteCartItem(netColor, netSize, netVariant, netMaterial, netScreenSize);
          if (res) {
            if (Object.keys(res).includes("error")) {
              if (res.error.name !== "Spam Detect") {
                console.error("Cart validation error", res);
              }
              return;
            }
            if (
              res.message === "Item not a matrix type." &&
              buildVariantNameList().length === 0
            ) {
              console.error("Cart validation error", res);
              return;
            }
            setValidatedItem(res);
          }
        } catch (err) {
          console.error("A function error occurred: ", err);
        }
      }
    }
  }, [selectedOptions, checkNetsuiteCartItem]);

  return {
    validatingItem,
    validatedItem,
    retryCount,
    isAddToCartDisabled,
    colorError,
    sizeError,
    materialError,
    screenSizeError,
    variantError,
    setValidatingItem,
    setValidatedItem,
    setRetryCount,
    setIsAddToCartDisabled,
    setColorError,
    setSizeError,
    setMaterialError,
    setScreenSizeError,
    setVariantError,
    checkNetsuiteCartItem,
    handleItemPrefetchValidation,
  };
};