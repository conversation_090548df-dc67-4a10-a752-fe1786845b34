# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Web Build Status

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

# defaults:
  #  run:
    # change this if your nextjs app does not live at the root of the repo
    # working-directory: ./apps/web

jobs:
  build:
    env:
      SKIP_ENV_VALIDATION: true
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}

    runs-on: windows-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "yarn"

      - name: Install dependencies
        run: yarn install --immutable

      - name: Restore next build
        uses: actions/cache@v3
        id: restore-build-cache
        env:
          cache-name: cache-next-web-build
        with:
          path: .next/cache
          # change this if you prefer a more strict cache
          key: ${{ runner.os }}-build-${{ env.cache-name }}

      - name: Build next.js app
        env:
          SKIP_BUILD_PRODUCT_REDIRECTS: 1
          NEXT_PUBLIC_SCAN_DOMAIN: https://cs-event-services-scan-pwa.vercel.app

        # change this if your site requires a custom build command
        run: yarn run build-web
