Index: apps/booking-calendar/src/components/Scheduler/TopBar/SchedulerToolbar.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/apps/booking-calendar/src/components/Scheduler/TopBar/SchedulerToolbar.tsx b/apps/booking-calendar/src/components/Scheduler/TopBar/SchedulerToolbar.tsx
--- a/apps/booking-calendar/src/components/Scheduler/TopBar/SchedulerToolbar.tsx	
+++ b/apps/booking-calendar/src/components/Scheduler/TopBar/SchedulerToolbar.tsx	(date 1718041613769)
@@ -1,20 +1,11 @@
 import React, { RefObject, useCallback, useEffect, useRef, useState } from 'react';
-import {
-  Button,
-  ButtonConfig,
-  MenuItemConfig,
-  Scheduler,
-  SchedulerFeaturesType,
-} from '@bryntum/schedulerpro';
-import {
-  Bryn<PERSON><PERSON><PERSON>on,
-  BryntumButtonGroup,
-  BryntumNumberField,
-  BryntumScheduler,
-  BryntumViewPresetCombo,
-} from '@bryntum/schedulerpro-react';
+import { MenuItemConfig, Scheduler } from '@bryntum/schedulerpro-thin';
+
 import Divider from '@mui/material/Divider';
 import Toolbar from '@mui/material/Toolbar';
+import { Button, ButtonConfig } from '@bryntum/core-thin';
+import { BryntumButton, BryntumNumberField } from '@bryntum/core-react-thin';
+import { PresetManager, SchedulerFeaturesType, ViewPresetCombo } from '@bryntum/scheduler-thin';
 
 type AppButtonProps = Partial<ButtonConfig> & {
   dataConfig?: {
@@ -31,7 +22,7 @@
 }
 
 interface ToolbarComponentProps {
-  schedulerRef: RefObject<BryntumScheduler>;
+  schedulerRef: RefObject<Scheduler>;
   mode: string;
 }
 
@@ -210,7 +201,7 @@
       <Divider
         orientation="vertical"
         variant="fullWidth"
-        sx={{ mr: 4 }}
+        sx={{ mr: 3 }}
       ></Divider>
       {/*<BryntumButton*/}
       {/*  text="Features"*/}
@@ -218,7 +209,7 @@
       {/*  menu={featuresMenu}*/}
       {/*  hidden={!showCustom}*/}
       {/*/>*/}
-      {showPresetCombo && <BryntumViewPresetCombo height={42} />}
+      {showPresetCombo && <BryntumPres height={42} />}
     </Toolbar>
   );
 };
