const { resolve } = require("node:path");

const project = resolve(process.cwd(), ".eslintrc.json");

/** @type {import("eslint").Linter.Config} */
module.exports = {
  extends: [
    "eslint:recommended",
    "prettier",
    require.resolve("@vercel/style-guide/eslint/next"),
    "eslint-config-turbo",
    "plugin:@next/next/recommended",
    "plugin:oxlint/recommended",
  ],
  ignorePatterns: [
    // Ignore dotfiles
    ".*.js",
    "node_modules/",
  ],
  rules: {
    "import/no-default-export": "off",
  },
  overrides: [{ files: ["*.js?(x)", "*.ts?(x)"] }],
};
