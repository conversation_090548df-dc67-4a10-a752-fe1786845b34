import oxlint from "eslint-plugin-oxlint";

export default {
  settings: {
    next: {
      rootDir: ["apps/*/", "packages/*/"],
    },
  },
  env: {
    browser: true,
    es6: true,
    jest: true,
  },
  extends: [
    "airbnb",
    "plugin:prettier/recommended",
    "plugin:cypress/recommended",
    "next",
    "plugin:oxlint/recommended",
  ],
  parser: "@babel/eslint-parser",
  parserOptions: {
    requireConfigFile: false,
  },
  rules: {
    "@next/next/no-html-link-for-pages": "off",
    "react/jsx-key": "off",
    "no-bitwise": "off",
    "no-underscore-dangle": "off",
    "prefer-const": "off",
    radix: "off",
    "prefer-destructuring": "off",
    "no-shadow": "off",
    "prettier/prettier": "off",
    "react/jsx-filename-extension": "off",
    "import/no-unresolved": "off",
    "import/extensions": "off",
    "react/display-name": "off",
    "import/prefer-default-export": "off",
    "jsx-a11y/anchor-is-valid": "off",
    "comma-dangle": "off",
    "max-len": "off",
    "no-console": "off",
    "no-param-reassign": "off",
    "no-plusplus": "off",
    "func-names": "off",
    "no-return-assign": "off",
    "object-curly-newline": "off",
    "react/jsx-props-no-spreading": "off",
    "react/react-in-jsx-scope": "off",
    "react/require-default-props": "off",
    "import/no-extraneous-dependencies": "off",
    "react/no-unescaped-entities": "off",
    "react/forbid-prop-types": "off",
    "no-use-before-define": "off",
    "no-nested-ternary": "off",
    "react/no-danger": "off",
    "react/default-props-match-prop-types": "off",
    "no-unused-vars": "warn",
    camelcase: "warn",
    "no-else-return": "warn",
    "react/jsx-max-props-per-line": [
      1,
      {
        maximum: 2,
        when: "multiline",
      },
    ],
    "react/no-array-index-key": "off",
    "import/no-unused-modules": ["off"],
    indent: "off",
    "react/prop-types": ["off"],
  },
};
