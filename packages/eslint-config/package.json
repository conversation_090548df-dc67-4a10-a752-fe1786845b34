{"name": "@repo/eslint-config", "version": "0.0.0", "private": true, "files": ["library.js", "next.js", "react-internal.js"], "dependencies": {"@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "@vercel/style-guide": "^6.0.0", "eslint-config-next": "^14.2.3", "eslint-config-prettier": "^9.1.0", "eslint-config-turbo": "^1.13.3", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-oxlint": "^0.2.9", "oxc-parser": "^0.9.0"}, "conditionNames": ["node", "import"], "devDependencies": {"oxlint": "^0.3.2"}}