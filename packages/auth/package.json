{"name": "@event-services/auth", "version": "1.0.0", "description": "Event services auth package for authentication across all services", "main": "./index.ts", "types": "./index.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["auth", "authentication", "event-services"], "author": "Newgen Business Solutions", "dependencies": {"next-auth": "4.24.7"}, "devDependencies": {"config": "*", "tsconfig": "*", "typescript": "^5.4.5"}, "peerDependencies": {"next": "^13.0.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "repository": {"type": "git", "url": "https://github.com/newgen-business-solutions/cs-event-services.git"}}