"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authOptions = exports.createAuthOptions = void 0;
function refreshAccessToken(token) {
    return __awaiter(this, void 0, void 0, function () {
        var url, searchParams, completeUrl, response, refreshedTokens, error_1;
        var _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    console.debug("Hitting refresh function");
                    _b.label = 1;
                case 1:
                    _b.trys.push([1, 4, , 5]);
                    url = new URL("https://".concat(process.env.NEXT_PUBLIC_ACCOUNT_URL_ID, ".suitetalk.api.netsuite.com/services/rest/auth/oauth2/v1/token"));
                    searchParams = new URLSearchParams(url.searchParams);
                    searchParams.append("client_id", process.env.OAUTH_CLIENT_ID);
                    searchParams.append("client_secret", process.env.OAUTH_CLIENT_SECRET);
                    searchParams.append("grant_type", "refresh_token");
                    searchParams.append("refresh_token", token.refreshToken);
                    url.search = searchParams.toString();
                    completeUrl = url.href;
                    return [4 /*yield*/, fetch(completeUrl, {
                            headers: {
                                "Content-Type": "application/x-www-form-urlencoded",
                            },
                            method: "POST",
                        })];
                case 2:
                    response = _b.sent();
                    return [4 /*yield*/, response.json()];
                case 3:
                    refreshedTokens = _b.sent();
                    if (!response.ok) {
                        throw refreshedTokens;
                    }
                    return [2 /*return*/, __assign(__assign({}, token), { accessToken: refreshedTokens.access_token, accessTokenExpires: Date.now() + refreshedTokens.expires_in * 1000, refreshToken: (_a = refreshedTokens.refresh_token) !== null && _a !== void 0 ? _a : token.refreshToken })];
                case 4:
                    error_1 = _b.sent();
                    console.log(error_1);
                    return [2 /*return*/, __assign(__assign({}, token), { error: "RefreshAccessTokenError" })];
                case 5: return [2 /*return*/];
            }
        });
    });
}
var createAuthOptions = function (basePath) {
    if (basePath === void 0) { basePath = ""; }
    return ({
        providers: [
            {
                id: "netsuite",
                name: "NetSuite",
                type: "oauth",
                version: "2.0",
                checks: ["state"],
                authorization: {
                    url: "https://".concat(process.env.NEXT_PUBLIC_ACCOUNT_URL_ID, ".app.netsuite.com/app/login/oauth2/authorize.nl"),
                    params: {
                        client_id: "".concat(process.env.OAUTH_CLIENT_ID),
                        prompt: "login",
                        response_type: "code",
                        scope: "restlets rest_webservices",
                    },
                },
                token: {
                    url: "https://".concat(process.env.NEXT_PUBLIC_ACCOUNT_URL_ID, ".suitetalk.api.netsuite.com/services/rest/auth/oauth2/v1/token"),
                    params: {
                        grant_type: "authorization_code",
                    },
                },
                userinfo: "https://".concat(process.env.NEXT_PUBLIC_ACCOUNT_URL_ID, ".restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_rcs_rl_get_current_user&deploy=customdeploy_rcs_rl_get_current_user"),
                profile: function (profile) {
                    console.debug("Profile: ", profile);
                    return {
                        id: profile.id,
                        name: profile.name,
                        email: profile.email,
                        location: profile.location,
                        role: profile.role,
                        contact: profile === null || profile === void 0 ? void 0 : profile.contact,
                    };
                },
                clientId: "".concat(process.env.OAUTH_CLIENT_ID),
                clientSecret: "".concat(process.env.OAUTH_CLIENT_SECRET),
            },
        ],
        callbacks: {
            jwt: function (_a) {
                return __awaiter(this, arguments, void 0, function (_b) {
                    var token = _b.token, user = _b.user, account = _b.account, profile = _b.profile, isNewUser = _b.isNewUser;
                    return __generator(this, function (_c) {
                        if (account && user) {
                            return [2 /*return*/, {
                                    accessToken: account.access_token,
                                    accessTokenExpires: account.expires_at * 1000,
                                    refreshToken: account.refresh_token,
                                    user: user,
                                }];
                        }
                        if (Date.now() < token.accessTokenExpires) {
                            return [2 /*return*/, token];
                        }
                        return [2 /*return*/, refreshAccessToken(token)];
                    });
                });
            },
            session: function (_a) {
                return __awaiter(this, arguments, void 0, function (_b) {
                    var session = _b.session, user = _b.user, token = _b.token;
                    return __generator(this, function (_c) {
                        if (token) {
                            session.accessToken = token.accessToken;
                            session.error = token.error;
                            session.user = token.user;
                        }
                        return [2 /*return*/, session];
                    });
                });
            },
        },
        pages: {
            signIn: "".concat(basePath, "/auth/signin"),
            signOut: "".concat(basePath, "/auth/signout"),
            error: "".concat(basePath, "/auth/error"),
        },
        debug: process.env.NODE_ENV === "development",
        logger: {
            error: function (code, metadata) {
                console.error("Logging err: ", code, metadata);
            },
            warn: function (code) {
                console.warn("Logging warn: ", code);
            },
            debug: function (code, metadata) {
                console.debug("Logging debug: ", code, metadata);
            },
        },
        secret: process.env.NEXTAUTH_SECRET,
    });
};
exports.createAuthOptions = createAuthOptions;
var authOptions = createAuthOptions();
exports.authOptions = authOptions;
