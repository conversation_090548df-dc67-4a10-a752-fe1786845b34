import type { NextAuthOptions, Session } from "next-auth";

export type ExtendedSession = Session & {
  accessToken?: string;
  error?: string;
  exhibitorUrl?: string;
};

export type SessionWithAccessToken = Session & {
  accessToken: string;
  error?: string;
  refreshToken: string;
};

async function refreshAccessToken(token: SessionWithAccessToken) {
  console.debug("Hitting refresh function");
  try {
    const url = new URL(
      `https://${
        process.env.NEXT_PUBLIC_ACCOUNT_URL_ID
      }.suitetalk.api.netsuite.com/services/rest/auth/oauth2/v1/token`,
    );

    const searchParams = new URLSearchParams(url.searchParams);

    searchParams.append("client_id", process.env.OAUTH_CLIENT_ID!);
    searchParams.append("client_secret", process.env.OAUTH_CLIENT_SECRET!);
    searchParams.append("grant_type", "refresh_token");
    searchParams.append("refresh_token", token.refreshToken);

    url.search = searchParams.toString();

    const completeUrl = url.href;

    const response = await fetch(completeUrl, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      method: "POST",
    });

    const refreshedTokens = (await response.json()) as {
      access_token: string;
      expires_in: number;
      refresh_token?: string;
    };

    if (!response.ok) {
      throw refreshedTokens;
    }

    return {
      ...token,
      accessToken: refreshedTokens.access_token,
      accessTokenExpires: Date.now() + refreshedTokens.expires_in * 1000,
      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken, // Fall back to old refresh token
    };
  } catch (error) {
    console.log(error);

    return {
      ...token,
      error: "RefreshAccessTokenError",
    };
  }
}

const createAuthOptions = (basePath: string = ""): NextAuthOptions => ({
  providers: [
    {
      id: "netsuite",
      name: "NetSuite",
      type: "oauth",
      version: "2.0",
      checks: ["state"],
      authorization: {
        url: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.app.netsuite.com/app/login/oauth2/authorize.nl`,
        params: {
          client_id: `${process.env.OAUTH_CLIENT_ID}`,
          prompt: "login",
          response_type: "code",
          scope: "restlets rest_webservices",
        },
      },
      token: {
        url: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.suitetalk.api.netsuite.com/services/rest/auth/oauth2/v1/token`,
        params: {
          grant_type: "authorization_code",
        },
      },
      userinfo: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_rcs_rl_get_current_user&deploy=customdeploy_rcs_rl_get_current_user`,
      profile(profile: any) {
        console.debug("Profile: ", profile);
        return {
          id: profile.id,
          name: profile.name,
          email: profile.email,
          location: profile.location,
          role: profile.role,
          contact: profile?.contact,
          isScanUser: profile?.isScanUser,
        };
      },
      clientId: `${process.env.OAUTH_CLIENT_ID}`,
      clientSecret: `${process.env.OAUTH_CLIENT_SECRET}`,
    },
  ],
  callbacks: {
    async jwt({ token, user, account, profile, isNewUser }: any) {
      if (account && user) {
        return {
          accessToken: account.access_token,
          accessTokenExpires: (account.expires_at as number) * 1000,
          refreshToken: account.refresh_token,
          exhibitorUrl: process.env.NEXTAUTH_URL,
          user,
        };
      }

      if (Date.now() < (token.accessTokenExpires as number)) {
        return token;
      }

      return refreshAccessToken(token);
    },
    async session({
      session,
      user,
      token,
    }: {
      session: ExtendedSession;
      user: any;
      token: any;
    }) {
      if (token) {
        session.accessToken = token.accessToken;
        session.error = token.error;
        session.user = token.user;
        session.exhibitorUrl = token.exhibitorUrl;
      }

      return session;
    },
    async redirect({ url, baseUrl }) {
      // Use the dynamically set NEXTAUTH_URL
      const dynamicBaseUrl = process.env.NEXTAUTH_URL || baseUrl;
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${dynamicBaseUrl}${basePath}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === dynamicBaseUrl)
        return `${dynamicBaseUrl}${basePath}${new URL(url).pathname}`;

      return `${dynamicBaseUrl}${basePath}`;
    },
  },
  pages: {
    signIn: `${basePath}/auth/signin`,
    signOut: `${basePath}/auth/signout`,
    error: `${basePath}/auth/error`,
  },
  debug: process.env.NODE_ENV === "development",
  logger: {
    error(code: any, metadata: any) {
      console.error("Logging err: ", code, metadata);
    },
    warn(code: any) {
      console.warn("Logging warn: ", code);
    },
    debug(code: any, metadata: any) {
      console.debug("Logging debug: ", code, metadata);
    },
  },
  secret: process.env.NEXTAUTH_SECRET as string,
});

const authOptions = createAuthOptions();

export type { NextAuthOptions, Session };
export { createAuthOptions, authOptions };
