{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "incremental": true, "isolatedModules": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "moduleResolution": "node", "noEmit": true, "noFallthroughCasesInSwitch": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "es6", "types": ["vite/client"], "useUnknownInCatchVariables": false}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}