/**
 * NetSuite OAuth 1.0 configuration
 */
export interface NetSuiteOAuth1Config {
  consumerKey: string;
  consumerSecret: string;
  accessToken: string;
  accessTokenSecret: string;
}

/**
 * NetSuite client configuration
 */
export interface NetSuiteConfig {
  accountId: string;
  accountUrlId: string;
  oauth1: NetSuiteOAuth1Config;
  timeout?: number;
  debug?: boolean;
}

/**
 * Request options for NetSuite API calls
 */
export interface NetSuiteRequestOptions {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  body?: any;
  headers?: Record<string, string>;
  retries?: number;
}

/**
 * NetSuite API response
 */
export interface NetSuiteResponse {
  data: any;
  status: number;
  headers: Record<string, any>;
  duration: number;
}

/**
 * Performance metrics for monitoring
 */
export interface PerformanceMetrics {
  totalRequests: number;
  averageResponseTime: number;
  errorRate: number;
}

/**
 * Error handling options for Next.js API routes
 */
export interface ErrorHandlingOptions {
  res: any; // Next.js response object
  requestStart?: number;
  includeDetails?: boolean;
}

/**
 * Cache control options
 */
export interface CacheControlOptions {
  maxAge?: number;
  staleWhileRevalidate?: number;
  private?: boolean;
}
