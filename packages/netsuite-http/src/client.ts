import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import OAuth from "oauth-1.0a";
import crypto from "crypto";
import pRetry from "p-retry";
import {
  NetSuiteConfig,
  NetSuiteRequestOptions,
  NetSuiteResponse,
  PerformanceMetrics,
} from "./types";

/**
 * NetSuite HTTP Client with OAuth, retry logic, and performance optimizations
 */
export class NetSuiteHttpClient {
  private oauth: OAuth;
  private axiosInstance: AxiosInstance;
  private config: NetSuiteConfig;

  constructor(config: NetSuiteConfig) {
    this.config = config;
    this.oauth = this.createOAuthInstance();
    this.axiosInstance = this.createAxiosInstance();
  }

  /**
   * Create OAuth instance with configuration
   */
  private createOAuthInstance(): OAuth {
    // @ts-ignore
    return OAuth({
      consumer: {
        key: this.config.oauth1.consumerKey,
        secret: this.config.oauth1.consumerSecret,
      },
      signature_method: "HMAC-SHA256",
      hash_function(base_string: string, key: string) {
        return crypto
          .createHmac("sha256", key)
          .update(base_string)
          .digest("base64");
      },
      realm: this.config.accountId,
    });
  }

  /**
   * Create optimized axios instance
   */
  private createAxiosInstance(): AxiosInstance {
    const http = require("http");
    const https = require("https");

    return axios.create({
      timeout: this.config.timeout || 15000,
      headers: {
        "Content-Type": "application/json",
        "Accept-Encoding": "gzip, deflate",
      },
      // Keep connections alive for better performance
      httpAgent: new http.Agent({ keepAlive: true }),
      httpsAgent: new https.Agent({ keepAlive: true }),
      maxRedirects: 5,
      validateStatus: (status) => status < 500, // Don't throw on 4xx errors
    });
  }

  /**
   * Measure performance of an operation
   */
  private async measurePerformance<T>(
    operation: string,
    fn: () => Promise<T>,
  ): Promise<{ result: T; duration: number }> {
    const startTime = Date.now();
    try {
      const result = await fn();
      const duration = Date.now() - startTime;

      // Log performance metrics in development
      if (this.config.debug) {
        console.log(
          `[NetSuite Performance] ${operation} completed in ${duration}ms`,
        );
      }

      return { result, duration };
    } catch (error) {
      const duration = Date.now() - startTime;
      if (this.config.debug) {
        console.error(
          `[NetSuite Performance] ${operation} failed after ${duration}ms`,
        );
      }
      throw error;
    }
  }

  /**
   * Make an authenticated request to NetSuite API
   */
  async request(options: NetSuiteRequestOptions): Promise<NetSuiteResponse> {
    const {
      url,
      method = "GET",
      body = null,
      headers = {},
      retries = 3,
    } = options;

    // Generate OAuth authorization header
    const request_data = { url, method };
    const token = {
      key: this.config.oauth1.accessToken,
      secret: this.config.oauth1.accessTokenSecret,
    };

    const authHeader = this.oauth.toHeader(
      this.oauth.authorize(request_data, token),
    );

    // Prepare request configuration
    const requestConfig: AxiosRequestConfig = {
      method,
      url,
      headers: {
        ...authHeader,
        Authorization: authHeader.Authorization,
        ...headers,
      },
    };

    // Add body for non-GET requests
    if (body && (method === "POST" || method === "PUT" || method === "PATCH")) {
      requestConfig.data = body;
    }

    // Make the request with retry logic
    const fetchData = async (): Promise<AxiosResponse> => {
      const response = await this.axiosInstance(requestConfig);

      // Only accept 200 responses as successful for most NetSuite APIs
      if (response.status !== 200) {
        const error = new Error(
          `NetSuite API returned status ${response.status}`,
        );
        (error as any).response = response;
        throw error;
      }

      return response;
    };

    // Use p-retry for automatic retries on transient failures
    const { result: response, duration } = await this.measurePerformance(
      `NetSuite API ${method} ${url}`,
      () =>
        pRetry(fetchData, {
          retries,
          minTimeout: 1000, // Start with 1 second delay
          maxTimeout: 3000, // Max 3 seconds between retries
          onFailedAttempt: (error) => {
            if (this.config.debug) {
              console.log(
                `NetSuite API attempt ${error.attemptNumber} failed. ${error.retriesLeft} retries left.`,
              );
            }
          },
          // Don't retry on 4xx errors (client errors)
          shouldRetry: (error: any) => {
            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500
            ) {
              return false;
            }
            return true;
          },
        }),
    );

    return {
      data: response.data,
      status: response.status,
      headers: response.headers,
      duration,
    };
  }

  /**
   * Build NetSuite RESTlet URL with query parameters
   */
  buildUrl(
    script: string,
    deploy: string,
    params: Record<string, any> = {},
  ): string {
    const baseUrl = `https://${this.config.accountUrlId}.restlets.api.netsuite.com/app/site/hosting/restlet.nl`;
    const queryParams = new URLSearchParams({
      script,
      deploy,
      ...params,
    });
    return `${baseUrl}?${queryParams.toString()}`;
  }

  /**
   * Get performance metrics for monitoring
   */
  getPerformanceMetrics(): PerformanceMetrics {
    // This could be extended to track more detailed metrics
    return {
      totalRequests: 0, // Would need to implement tracking
      averageResponseTime: 0, // Would need to implement tracking
      errorRate: 0, // Would need to implement tracking
    };
  }
}
