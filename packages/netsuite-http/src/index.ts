export { NetSuiteHttpClient } from './client';
export {
  handleApiError,
  setOptimizedHeaders,
  addPerformanceHeaders,
  validateEnvironment,
  createConfigFromEnv,
} from './utils';
export type {
  NetSuiteConfig,
  NetSuiteOAuth1Config,
  NetSuiteRequestOptions,
  NetSuiteResponse,
  PerformanceMetrics,
  ErrorHandlingOptions,
  CacheControlOptions,
} from './types';

// Re-export for backward compatibility with existing code
import { NetSuiteHttpClient } from './client';
import { createConfigFromEnv } from './utils';

// Create a default client instance for convenience
let defaultClient: NetSuiteHttpClient | null = null;

/**
 * Get or create the default NetSuite client instance
 */
export function getDefaultClient(): NetSuiteHttpClient {
  if (!defaultClient) {
    const config = createConfigFromEnv();
    defaultClient = new NetSuiteHttpClient(config);
  }
  return defaultClient;
}

/**
 * Convenience function for making NetSuite requests with the default client
 * This maintains backward compatibility with the existing makeNetSuiteRequest function
 */
export async function makeNetSuiteRequest(options: {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  body?: any;
  headers?: Record<string, string>;
  retries?: number;
}) {
  const client = getDefaultClient();
  return client.request(options);
}

/**
 * Convenience function for building NetSuite URLs
 * This maintains backward compatibility with the existing buildNetSuiteUrl function
 */
export function buildNetSuiteUrl(
  script: string,
  deploy: string,
  params: Record<string, any> = {}
): string {
  const client = getDefaultClient();
  return client.buildUrl(script, deploy, params);
}
