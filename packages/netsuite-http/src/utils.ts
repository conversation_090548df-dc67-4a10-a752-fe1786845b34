import { ErrorHandlingOptions, CacheControlOptions } from "./types";

/**
 * Handle API errors consistently for Next.js API routes
 */
export function handleApiError(
  error: any,
  options: ErrorHandlingOptions,
): void {
  const { res, requestStart, includeDetails = false } = options;

  console.error("NetSuite API Error:", error.message);

  // Add performance header even on errors
  if (requestStart) {
    res.setHeader("X-Response-Time", `${Date.now() - requestStart}ms`);
  }

  // Handle specific error types
  if (error.code === "ECONNABORTED" || error.code === "ETIMEDOUT") {
    return res.status(504).json({
      error: "Request timeout - NetSuite API is taking too long to respond",
    });
  }

  if (error.response) {
    // NetSuite returned an error response
    const errorResponse: any = {
      error: "Error from NetSuite API",
    };

    if (includeDetails || process.env.NODE_ENV === "development") {
      errorResponse.details = error.response.data;
      errorResponse.status = error.response.status;
    }

    return res.status(error?.response?.status || 500).json(errorResponse);
  }

  // Generic error response
  const errorResponse: any = {
    error: "Internal server error while processing request",
  };

  if (includeDetails || process.env.NODE_ENV === "development") {
    errorResponse.message = error.message;
  }

  return res.status(500).json(errorResponse);
}

/**
 * Set optimized response headers for Next.js API routes
 */
export function setOptimizedHeaders(
  res: any,
  options: CacheControlOptions = {},
): void {
  const {
    maxAge = 300,
    staleWhileRevalidate = 600,
    private: isPrivate = true,
  } = options;

  const cacheType = isPrivate ? "private" : "public";

  res.setHeader(
    "Cache-Control",
    `${cacheType}, max-age=${maxAge}, stale-while-revalidate=${staleWhileRevalidate}`,
  );
  res.setHeader("X-Content-Type-Options", "nosniff");
}

/**
 * Add performance headers to response
 */
export function addPerformanceHeaders(
  res: any,
  requestStart: number,
  netsuiteTime?: number,
): void {
  res.setHeader("X-Response-Time", `${Date.now() - requestStart}ms`);

  if (netsuiteTime) {
    res.setHeader("X-NetSuite-Time", `${netsuiteTime}ms`);
  }
}

/**
 * Validate required environment variables
 */
export function validateEnvironment(): void {
  const required = [
    "OAUTH1_CONSUMER_KEY",
    "OAUTH1_CONSUMER_SECRET",
    "OAUTH1_ACCESS_TOKEN",
    "OAUTH1_TOKEN_SECRET",
    "NEXT_PUBLIC_ACCOUNT_ID",
    "NEXT_PUBLIC_ACCOUNT_URL_ID",
  ];

  const missing = required.filter((key) => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(", ")}`,
    );
  }
}

/**
 * Create NetSuite client configuration from environment variables
 */
export function createConfigFromEnv() {
  validateEnvironment();

  return {
    accountId: process.env.NEXT_PUBLIC_ACCOUNT_ID!,
    accountUrlId: process.env.NEXT_PUBLIC_ACCOUNT_URL_ID!,
    oauth1: {
      consumerKey: process.env.OAUTH1_CONSUMER_KEY!,
      consumerSecret: process.env.OAUTH1_CONSUMER_SECRET!,
      accessToken: process.env.OAUTH1_ACCESS_TOKEN!,
      accessTokenSecret: process.env.OAUTH1_TOKEN_SECRET!,
    },
    timeout: 15000,
    debug: process.env.NODE_ENV === "development",
  };
}
