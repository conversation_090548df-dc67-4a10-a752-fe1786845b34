# @event-services/netsuite-http

A shared NetSuite HTTP client package with OAuth 1.0 authentication, retry logic, and performance optimizations for the CS Event Services monorepo.

## Features

- **OAuth 1.0 Authentication** - Automatic signing of requests with NetSuite credentials
- **Retry Logic** - Automatic retries on transient failures with exponential backoff
- **Performance Optimizations** - Connection pooling, compression, and performance monitoring
- **Error Handling** - Consistent error handling with detailed logging
- **TypeScript Support** - Full TypeScript definitions and type safety
- **Next.js Integration** - Utilities specifically designed for Next.js API routes

## Installation

This package is part of the CS Event Services monorepo and is installed automatically when you run `yarn install` at the root level.

## Usage

### Basic Usage

```typescript
import { makeNetSuiteRequest, buildNetSuiteUrl } from '@event-services/netsuite-http';

// Build a NetSuite RESTlet URL
const url = buildNetSuiteUrl(
  'customscript_my_script',
  'customdeploy_my_deploy',
  { param1: 'value1', param2: 'value2' }
);

// Make a request
const response = await makeNetSuiteRequest({
  url,
  method: 'GET',
});

console.log(response.data);
```

### Advanced Usage with Custom Client

```typescript
import { NetSuiteHttpClient, createConfigFromEnv } from '@event-services/netsuite-http';

// Create a custom client instance
const config = createConfigFromEnv();
const client = new NetSuiteHttpClient(config);

// Make requests with the custom client
const response = await client.request({
  url: client.buildUrl('customscript_my_script', 'customdeploy_my_deploy'),
  method: 'POST',
  body: { data: 'example' },
  retries: 5,
});
```

### Next.js API Route Integration

```typescript
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@event-services/auth';
import {
  makeNetSuiteRequest,
  buildNetSuiteUrl,
  handleApiError,
  setOptimizedHeaders,
  addPerformanceHeaders,
} from '@event-services/netsuite-http';

export default async function handler(req, res) {
  const requestStart = Date.now();
  
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Build NetSuite URL
    const url = buildNetSuiteUrl(
      'customscript_my_script',
      'customdeploy_my_deploy',
      req.query
    );

    // Make request to NetSuite
    const { data, duration } = await makeNetSuiteRequest({
      url,
      method: req.method,
      body: req.body,
    });

    // Set optimized headers
    setOptimizedHeaders(res, { maxAge: 300 });
    addPerformanceHeaders(res, requestStart, duration);

    return res.status(200).json(data);
  } catch (error) {
    handleApiError(error, { res, requestStart });
  }
}
```

## Environment Variables

The package requires the following environment variables:

```bash
# NetSuite OAuth 1.0 credentials
OAUTH1_CONSUMER_KEY=your_consumer_key
OAUTH1_CONSUMER_SECRET=your_consumer_secret
OAUTH1_ACCESS_TOKEN=your_access_token
OAUTH1_TOKEN_SECRET=your_token_secret

# NetSuite account information
NEXT_PUBLIC_ACCOUNT_ID=your_account_id
NEXT_PUBLIC_ACCOUNT_URL_ID=your_account_url_id
```

## API Reference

### Functions

#### `makeNetSuiteRequest(options)`
Make an authenticated request to NetSuite API with retry logic.

#### `buildNetSuiteUrl(script, deploy, params)`
Build a NetSuite RESTlet URL with query parameters.

#### `handleApiError(error, options)`
Handle API errors consistently in Next.js API routes.

#### `setOptimizedHeaders(res, options)`
Set optimized cache and security headers.

#### `addPerformanceHeaders(res, requestStart, netsuiteTime)`
Add performance timing headers to the response.

### Classes

#### `NetSuiteHttpClient`
Main HTTP client class for making authenticated NetSuite requests.

## Migration from Legacy Code

If you're migrating from the old `request-promise-native` pattern:

### Before
```javascript
import request from 'request-promise-native';

const response = await request({
  url: 'https://...',
  method: 'GET',
  // ... OAuth setup
});
```

### After
```typescript
import { makeNetSuiteRequest, buildNetSuiteUrl } from '@event-services/netsuite-http';

const url = buildNetSuiteUrl('script_id', 'deploy_id', params);
const response = await makeNetSuiteRequest({
  url,
  method: 'GET',
});
```

## Performance Features

- **Connection Pooling** - HTTP/HTTPS agents with keep-alive
- **Compression** - Automatic gzip/deflate support
- **Retry Logic** - Exponential backoff with configurable retries
- **Timeout Handling** - 15-second default timeout with proper error handling
- **Performance Monitoring** - Built-in timing and metrics

## Error Handling

The package provides comprehensive error handling:

- **Timeout Errors** - Returns 504 status with descriptive message
- **NetSuite Errors** - Forwards NetSuite error responses with proper status codes
- **Network Errors** - Handles connection failures with retry logic
- **4xx Errors** - No retry for client errors (authentication, validation, etc.)

## Contributing

This package is part of the CS Event Services monorepo. See the main repository README for contribution guidelines.
