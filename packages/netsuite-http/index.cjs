// CommonJS wrapper for compatibility
// This file provides CommonJS exports for environments that don't support ES modules

const axios = require('axios');
const OAuth = require('oauth-1.0a');
const crypto = require('crypto');
const http = require('http');
const https = require('https');

// For p-retry, we need to use dynamic import since it's ESM-only
let pRetry;

async function loadPRetry() {
  if (!pRetry) {
    const module = await import('p-retry');
    pRetry = module.default;
  }
  return pRetry;
}

/**
 * NetSuite HTTP Client with OAuth, retry logic, and performance optimizations
 */
class NetSuiteHttpClient {
  constructor(config) {
    this.config = config;
    this.oauth = this.createOAuthInstance();
    this.axiosInstance = this.createAxiosInstance();
  }

  createOAuthInstance() {
    return OAuth({
      consumer: {
        key: this.config.oauth1.consumerKey,
        secret: this.config.oauth1.consumerSecret,
      },
      signature_method: 'HMAC-SHA256',
      hash_function(base_string, key) {
        return crypto
          .createHmac('sha256', key)
          .update(base_string)
          .digest('base64');
      },
      realm: this.config.accountId,
    });
  }

  createAxiosInstance() {
    return axios.create({
      timeout: this.config.timeout || 15000,
      headers: {
        'Content-Type': 'application/json',
        'Accept-Encoding': 'gzip, deflate',
      },
      httpAgent: new http.Agent({ keepAlive: true }),
      httpsAgent: new https.Agent({ keepAlive: true }),
      maxRedirects: 5,
      validateStatus: (status) => status < 500,
    });
  }

  async measurePerformance(operation, fn) {
    const startTime = Date.now();
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      
      if (this.config.debug) {
        console.log(`[NetSuite Performance] ${operation} completed in ${duration}ms`);
      }
      
      return { result, duration };
    } catch (error) {
      const duration = Date.now() - startTime;
      if (this.config.debug) {
        console.error(`[NetSuite Performance] ${operation} failed after ${duration}ms`);
      }
      throw error;
    }
  }

  async request(options) {
    const {
      url,
      method = 'GET',
      body = null,
      headers = {},
      retries = 3,
    } = options;

    const request_data = { url, method };
    const token = {
      key: this.config.oauth1.accessToken,
      secret: this.config.oauth1.accessTokenSecret,
    };

    const authHeader = this.oauth.toHeader(this.oauth.authorize(request_data, token));

    const requestConfig = {
      method,
      url,
      headers: {
        ...authHeader,
        Authorization: authHeader.Authorization,
        ...headers,
      },
    };

    if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      requestConfig.data = body;
    }

    const fetchData = async () => {
      const response = await this.axiosInstance(requestConfig);

      if (response.status !== 200) {
        const error = new Error(`NetSuite API returned status ${response.status}`);
        error.response = response;
        throw error;
      }

      return response;
    };

    // Load p-retry dynamically
    const retry = await loadPRetry();

    const { result: response, duration } = await this.measurePerformance(
      `NetSuite API ${method} ${url}`,
      () => retry(fetchData, {
        retries,
        minTimeout: 1000,
        maxTimeout: 3000,
        onFailedAttempt: (error) => {
          if (this.config.debug) {
            console.log(
              `NetSuite API attempt ${error.attemptNumber} failed. ${error.retriesLeft} retries left.`
            );
          }
        },
        shouldRetry: (error) => {
          if (error.response && error.response.status >= 400 && error.response.status < 500) {
            return false;
          }
          return true;
        },
      })
    );

    return {
      data: response.data,
      status: response.status,
      headers: response.headers,
      duration,
    };
  }

  buildUrl(script, deploy, params = {}) {
    const baseUrl = `https://${this.config.accountUrlId}.restlets.api.netsuite.com/app/site/hosting/restlet.nl`;
    const queryParams = new URLSearchParams({
      script,
      deploy,
      ...params,
    });
    return `${baseUrl}?${queryParams.toString()}`;
  }

  getPerformanceMetrics() {
    return {
      totalRequests: 0,
      averageResponseTime: 0,
      errorRate: 0,
    };
  }
}

// Utility functions
function handleApiError(error, options) {
  const { res, requestStart, includeDetails = false } = options;
  
  console.error('NetSuite API Error:', error.message);

  if (requestStart) {
    res.setHeader('X-Response-Time', `${Date.now() - requestStart}ms`);
  }

  if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
    return res.status(504).json({
      error: 'Request timeout - NetSuite API is taking too long to respond',
    });
  }

  if (error.response) {
    const errorResponse = {
      error: 'Error from NetSuite API',
    };

    if (includeDetails || process.env.NODE_ENV === 'development') {
      errorResponse.details = error.response.data;
      errorResponse.status = error.response.status;
    }

    return res.status(error.response.status || 500).json(errorResponse);
  }

  const errorResponse = {
    error: 'Internal server error while processing request',
  };

  if (includeDetails || process.env.NODE_ENV === 'development') {
    errorResponse.message = error.message;
  }

  return res.status(500).json(errorResponse);
}

function setOptimizedHeaders(res, options = {}) {
  const {
    maxAge = 300,
    staleWhileRevalidate = 600,
    private: isPrivate = true,
  } = options;

  const cacheType = isPrivate ? 'private' : 'public';
  
  res.setHeader(
    'Cache-Control',
    `${cacheType}, max-age=${maxAge}, stale-while-revalidate=${staleWhileRevalidate}`
  );
  res.setHeader('X-Content-Type-Options', 'nosniff');
}

function addPerformanceHeaders(res, requestStart, netsuiteTime) {
  res.setHeader('X-Response-Time', `${Date.now() - requestStart}ms`);
  
  if (netsuiteTime) {
    res.setHeader('X-NetSuite-Time', `${netsuiteTime}ms`);
  }
}

function validateEnvironment() {
  const required = [
    'OAUTH1_CONSUMER_KEY',
    'OAUTH1_CONSUMER_SECRET',
    'OAUTH1_ACCESS_TOKEN',
    'OAUTH1_TOKEN_SECRET',
    'NEXT_PUBLIC_ACCOUNT_ID',
    'NEXT_PUBLIC_ACCOUNT_URL_ID',
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}`
    );
  }
}

function createConfigFromEnv() {
  validateEnvironment();

  return {
    accountId: process.env.NEXT_PUBLIC_ACCOUNT_ID,
    accountUrlId: process.env.NEXT_PUBLIC_ACCOUNT_URL_ID,
    oauth1: {
      consumerKey: process.env.OAUTH1_CONSUMER_KEY,
      consumerSecret: process.env.OAUTH1_CONSUMER_SECRET,
      accessToken: process.env.OAUTH1_ACCESS_TOKEN,
      accessTokenSecret: process.env.OAUTH1_TOKEN_SECRET,
    },
    timeout: 15000,
    debug: process.env.NODE_ENV === 'development',
  };
}

// Default client instance
let defaultClient = null;

function getDefaultClient() {
  if (!defaultClient) {
    const config = createConfigFromEnv();
    defaultClient = new NetSuiteHttpClient(config);
  }
  return defaultClient;
}

async function makeNetSuiteRequest(options) {
  const client = getDefaultClient();
  return client.request(options);
}

function buildNetSuiteUrl(script, deploy, params = {}) {
  const client = getDefaultClient();
  return client.buildUrl(script, deploy, params);
}

// CommonJS Exports
module.exports = {
  NetSuiteHttpClient,
  handleApiError,
  setOptimizedHeaders,
  addPerformanceHeaders,
  validateEnvironment,
  createConfigFromEnv,
  getDefaultClient,
  makeNetSuiteRequest,
  buildNetSuiteUrl,
};
