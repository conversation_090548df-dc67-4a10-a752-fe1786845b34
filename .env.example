# =============================================================================
# CS Event Services - Monorepo Environment Configuration
# =============================================================================
# This file contains environment variables shared across all applications
# Copy this file to .env.local and fill in the appropriate values

# =============================================================================
# NetSuite OAuth 2.0 Configuration (Shared across all apps)
# =============================================================================
OAUTH_CLIENT_ID="your_oauth_client_id"
OAUTH_CLIENT_SECRET="your_oauth_client_secret"

# =============================================================================
# NetSuite OAuth 1.0 Configuration (Legacy API access)
# =============================================================================
OAUTH1_ACCESS_TOKEN="your_oauth1_access_token"
OAUTH1_ACCESS_TOKEN_SECRET="your_oauth1_access_token_secret"
OAUTH1_CONSUMER_KEY="your_oauth1_consumer_key"
OAUTH1_CONSUMER_SECRET="your_oauth1_consumer_secret"

# =============================================================================
# NetSuite Account Configuration
# =============================================================================
NEXT_PUBLIC_ACCOUNT_ID="TSTDRV1516212"
NEXT_PUBLIC_ACCOUNT_URL_ID="tstdrv1516212"
NEXT_PUBLIC_NETSUITE_REST_DOMAIN=".restlets.api.netsuite.com/app/site/hosting/restlet.nl"

# =============================================================================
# NextAuth Configuration (Shared secret across all apps)
# =============================================================================
NEXTAUTH_SECRET="HKbyLSWTChjjGEtvxH2bFYUBZx9GWnhwtv4MYXwGcws="

# =============================================================================
# Application-Specific NextAuth URLs
# =============================================================================
# Web App (Main application)
NEXTAUTH_URL_WEB="https://yourdomain.ngrok.app/api/auth"

# Scan PWA
NEXTAUTH_URL_SCAN="https://yourdomain.ngrok.app/scan/api/auth"

# Booking Proto
NEXTAUTH_URL_BOOKING="https://yourdomain.ngrok.app/booking/api/auth"

# =============================================================================
# Development Server Domains (for zone routing)
# =============================================================================
NEXT_PUBLIC_SCAN_DOMAIN="http://localhost:4000"
NEXT_PUBLIC_BOOKING_DOMAIN="http://localhost:4001"

# =============================================================================
# Development Configuration
# =============================================================================
NODE_ENV="development"
NEXT_PUBLIC_DEBUG_MODE="true"

# =============================================================================
# Port Configuration (for reference)
# =============================================================================
# Web App: 3000
# Scan PWA: 4000  
# Booking Proto: 4001
# Storybook (booking-proto): 6006
