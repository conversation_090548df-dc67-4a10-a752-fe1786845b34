<mxfile host="app.diagrams.net" modified="2023-09-06T17:50:27.573Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/116.0.1938.69" version="21.7.2" etag="Jt743Fbt9dNmOKoYdtBD" type="github">
  <diagram name="Page-1" id="4ireVMYnxUVnr1uHJnaR">
    <mxGraphModel>
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="start" style="ellipse;aspect=fixed;strokeWidth=2;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry y="66" width="49" height="49" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Create Container" style="rounded=1;absoluteArcSize=1;arcSize=14;whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="134" y="74" width="138" height="34" as="geometry" />
        </mxCell>
        <mxCell id="4" value="Validate Context" style="rounded=1;absoluteArcSize=1;arcSize=14;whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="454" y="74" width="135" height="34" as="geometry" />
        </mxCell>
        <mxCell id="5" value="Assign Criteria" style="rounded=1;absoluteArcSize=1;arcSize=14;whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="768" y="74" width="119" height="34" as="geometry" />
        </mxCell>
        <mxCell id="6" value="Compare Criteria" style="rounded=1;absoluteArcSize=1;arcSize=14;whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1057" y="45" width="138" height="34" as="geometry" />
        </mxCell>
        <mxCell id="7" value="In Hour Bucket" style="rounded=1;absoluteArcSize=1;arcSize=14;whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1395" width="121" height="34" as="geometry" />
        </mxCell>
        <mxCell id="8" value="Out Hour Bucket" style="rounded=1;absoluteArcSize=1;arcSize=14;whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1654" y="74" width="134" height="34" as="geometry" />
        </mxCell>
        <mxCell id="9" value="stop" style="ellipse;aspect=fixed;strokeWidth=2;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="1432" y="84" width="45" height="45" as="geometry" />
        </mxCell>
        <mxCell id="10" value="Start" style="curved=1;startArrow=none;endArrow=block;exitX=1.0022853150659679;exitY=0.4981930596487863;entryX=0.0032080498294553895;entryY=0.4826899977291332;" edge="1" parent="1" source="2" target="3">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="11" value="Container Created" style="curved=1;startArrow=none;endArrow=block;exitX=1.0043592038361921;exitY=0.4826899977291332;entryX=0.0016782972547743056;entryY=0.4826899977291332;" edge="1" parent="1" source="3" target="4">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="12" value="Context Validated" style="curved=1;startArrow=none;endArrow=block;exitX=1.0043210064923322;exitY=0.4826899977291332;entryX=-0.0011379338112198004;entryY=0.4826899977291332;" edge="1" parent="1" source="4" target="5">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="13" value="Criteria Assigned" style="curved=1;startArrow=none;endArrow=block;exitX=0.9973739495798319;exitY=0.13373406678455357;entryX=-0.0028683206309442935;entryY=0.4876685423009536;" edge="1" parent="1" source="5" target="6">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="972" y="62" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="14" value="Criteria Compared" style="curved=1;startArrow=none;endArrow=block;exitX=0.9582284454853689;exitY=-0.00742951561422909;entryX=-0.003787710646952479;entryY=0.4950980579151827;" edge="1" parent="1" source="6" target="7">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1295" y="17" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="15" value="Hr In Bucket" style="curved=1;startArrow=none;endArrow=block;exitX=0.9964920548368091;exitY=0.4950980579151827;entryX=0.2684533649234264;entryY=-0.012408060186049518;" edge="1" parent="1" source="7" target="8">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1585" y="17" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="16" value="Hr Out Bucket" style="curved=1;startArrow=none;endArrow=block;exitX=0.2684533649234264;exitY=0.9777880556443158;entryX=0.7756741164575015;entryY=0.9777880556443158;" edge="1" parent="1" source="8" target="5">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1585" y="164" />
              <mxPoint x="972" y="164" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="17" value="Criteria Not Matched" style="curved=1;startArrow=none;endArrow=block;exitX=0.9582284454853689;exitY=0.9827666002161363;entryX=0.008825641208224827;entryY=0.4961805979410807;" edge="1" parent="1" source="6" target="9">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1295" y="106" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
