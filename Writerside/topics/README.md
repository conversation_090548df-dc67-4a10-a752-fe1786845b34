![event service repo img-1 (1)](https://user-images.githubusercontent.com/********/*********-56eeb27a-88d9-405c-b494-7f667ce2b078.png)

# ConventionSuite Event Services Monorepo

[![Web Build Status](https://github.com/newgen-business-solutions/cs-event-services/actions/workflows/next-build.yml/badge.svg)](https://github.com/newgen-business-solutions/cs-event-services/actions/workflows/next-build.yml)

## what's inside?

This turborepo uses [Yarn](https://classic.yarnpkg.com/lang/en/) as a package manager. It includes the following packages/apps:

- `apps/backend`: a Netsuite Account customization app
- `apps/web`: a [Next.js](https://nextjs.org) project
- `packages/config`: `eslint` configs (unused)
- `packages/tsconfig`: shared tsconfig.json
- `packages/ui`: shared ui components (unused)

## Run Locally

Clone the project

```bash
  git clone "https://github.com/newgen-business-solutions/cs-event-services"
```

Go to the project directory

```bash
  cd cs-event-services
```

Install dependencies

```bash
  yarn install
```

Start the dev server

```bash
  yarn run dev
```

### Local Testing/Linting

Before pushing, make sure you test and lint the project on your local machine:

```bash
  yarn run lint
  yarn run test
```

### Local Building

cd into the `apps/web` folder and run `yarn run staging` to create the env file

> note: The `buildEnvFile.js` script soon won't be needed because of a netsuite update that allows us to find the script urls through the script aliases.

## Deploying

Follow the instructions [On the wiki](https://github.com/newgen-business-solutions/cs-event-services/wiki/Installation-Into-an-Account)

## Committing

Please follow the commit notaion so changelogs can be properly generated for releases

```javascript
  {
    group: 'added',
    label: 'Added',
    emojis: [
      'sparkles',
      'tada',
      'white_check_mark',
      'construction_worker',
      'chart_with_upwards_trend',
      'heavy_plus_sign',
      'loud_sound',
    ],
  },
  {
    group: 'changed',
    label: 'Changed',
    emojis: [
      'art',
      'zap',
      'lipstick',
      'rotating_light',
      'arrow_down',
      'arrow_up',
      'pushpin',
      'recycle',
      'wrench',
      'rewind',
      'alien',
      'truck',
      'bento',
      'wheelchair',
      'speech_balloon',
      'card_file_box',
      'children_crossing',
      'building_construction',
      'iphone',
    ],
  },
  {
    group: 'breaking_changes',
    label: 'Breaking changes',
    emojis: [
      'boom',
    ],
  },
  {
    group: 'deprecated',
    label: 'Deprecated',
    emojis: [],
  },
  {
    group: 'removed',
    label: 'Removed',
    emojis: ['fire', 'heavy_minus_sign', 'mute'],
  },
  {
    group: 'fixed',
    label: 'Fixed',
    emojis: [
      'bug',
      'ambulance',
      'apple',
      'penguin',
      'checkered_flag',
      'robot',
      'green_apple',
      'green_heart',
      'pencil2',
    ],
  },
  {
    group: 'security',
    label: 'Security',
    emojis: ['lock'],
  },
  {
    group: 'useless',
    label: 'Useless',
    emojis: [
      'bookmark',
    ],
  },
  {
    group: 'misc',
    label: 'Miscellaneous',
    emojis: [],
  },
```

### Remote Caching (only for people in the vercel org)

Turborepo can use a technique known as [Remote Caching (Beta)](https://turborepo.org/docs/features/remote-caching) to share cache artifacts across machines, enabling you to share build caches with your team and CI/CD pipelines.

By default, Turborepo will cache locally. To enable Remote Caching (Beta) you will need an account with Vercel. If you don't have an account you can [create one](https://vercel.com/signup), then enter the following commands:

```
npx turbo login
```

This will authenticate the Turborepo CLI with your [Vercel account](https://vercel.com/docs/concepts/personal-accounts/overview).

Next, you can link your Turborepo to your Remote Cache by running the following command from the root of your turborepo:

```
npx turbo link
```

## Useful Links

Learn more about the power of Turborepo:

- [Pipelines](https://turborepo.org/docs/features/pipelines)
- [Caching](https://turborepo.org/docs/features/caching)
- [Remote Caching (Beta)](https://turborepo.org/docs/features/remote-caching)
- [Scoped Tasks](https://turborepo.org/docs/features/scopes)
- [Configuration Options](https://turborepo.org/docs/reference/configuration)
- [CLI Usage](https://turborepo.org/docs/reference/command-line-reference)
