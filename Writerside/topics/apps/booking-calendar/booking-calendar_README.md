# Getting Started with Vite + React + NetSuite

## Available Scripts

In the project directory, you can run:

### `npm run dev-spa`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `npm run server`

Runs the backend proxy express server in the development mode to be able to talk to NetSuite RESTlets.\
Open [http://localhost:9000](http://localhost:9000) to view it in the browser.

The API server will restart if you make edits.\
You will also see any lint errors in the console.

### `npm run build-spa`

Builds the app for production to the `dist` folder.\
It correctly bundles <PERSON>act in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://vitejs.dev/guide/static-deploy.html) for more information.

## Learn More

You can learn more in the [Vite documentation](https://vitejs.dev/guide/).

To learn React, check out the [React documentation](https://reactjs.org/).
