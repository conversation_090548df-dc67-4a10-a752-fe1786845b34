# Stores
These are the stores that are used in the application. They are used to manage the state of the application globally. Context is not used as it gives us too many re-renders. To reduce the tree shaking and DOM updates, we use `zustand`.

## Store Structure
Each store is a function that returns a store object. The store object contains the state and the actions that can be performed on the state. The store object is then passed to the `create` function of `zustand` to create the store.

```js
import create from 'zustand';

export const useStore = create((set) => ({
  state: 'initial state',
  action: () => set((state) => ({ state: 'new state' })),
}));
```

## Store Usage
To use the store, import the store object and call the `useStore` hook.

```js
import { useStore } from './store';

const Component = () => {
  const { state, action } = useStore();

  return (
    <div>
      <p>{state}</p>
      <button onClick={action}>Change State</button>
    </div>
  );
};

```

# Store List
- [Navigation Store](zNavigationStore.)
- [User Store](zUserStore.)
- [Settings Store](zSettingsStore.)
- [Cart Store](zustandCartStore.)
- [Event Store](eventStore.)

## Navigation Store
This store is used to manage the navigation state of the application. It contains the current routes in the navbar and later define them based on the selected event.

### Navigation Menu Default
Use this for testing the demo item pages by replacing the current `megaMenus` in the `zNavigationStore` with this.
```js
const megaMenus = [
  [
    {
      title: "Home",
      child: [
        {
          title: "Market 1",
          url: "/market-1",
        },
        {
          title: "Furniture",
          url: "/furniture-shop",
        },
        {
          title: "Grocery-v1",
          url: "/grocery1",
        },
        {
          title: "Grocery-v2",
          url: "/grocery2",
        },
        {
          title: "Grocery-v3",
          url: "/grocery3",
        },
        {
          title: "Health and Beauty",
          url: "/healthbeauty-shop",
        },
        {
          title: "Fashion",
          url: "/fashion-shop-1",
        },
        {
          title: "Gift Store",
          url: "/gift-shop",
        },
        {
          title: "Gadget",
          url: "/gadget-shop",
        },
      ],
    },
  ],
  [
    {
      title: "User Account",
      child: [
        {
          title: "Order List",
          url: "/orders",
        },
        {
          title: "Order Details",
          url: "/orders/f0ba538b-c8f3-45ce-b6c1-209cf07ba5f8",
        },
        {
          title: "View Profile",
          url: "/profile",
        },
        {
          title: "Edit Profile",
          url: "/profile/e42e28ea-528f-4bc8-81fb-97f658d67d75",
        },
        {
          title: "Address List",
          url: "/address",
        },
        {
          title: "Add Address",
          url: "/address/d27d0e28-c35e-4085-af1e-f9f1b1bd9c34",
        },
        {
          title: "All tickets",
          url: "/support-tickets",
        },
        {
          title: "Ticket details",
          url: "/support-tickets/when-will-my-product-arrive",
        },
      ],
    },
  ],
  [
    {
      title: "Vendor Account",
      child: [
        {
          title: "Dashboard",
          url: "/vendor/dashboard",
        },
        {
          title: "Profile",
          url: "/vendor/account-setting",
        },
      ],
    },
    {
      title: "Products",
      child: [
        {
          title: "All products",
          url: "/admin/products",
        },
        {
          title: "Add/Edit product",
          url: "/admin/products/create",
        },
      ],
    },
    {
      title: "Orders",
      child: [
        {
          title: "All orders",
          url: "/admin/orders",
        },
        {
          title: "Order details",
          url: "/admin/orders/f0ba538b-c8f3-45ce-b6c1-209cf07ba5f8",
        },
      ],
    },
  ],
  [
    {
      title: "Sale Page",
      child: [
        {
          title: "Sales Version 1",
          url: "/sale-page-1",
        },
        {
          title: "Sales Version 2",
          url: "/sale-page-2",
        },
      ],
    },
    {
      title: "Shop",
      child: [
        {
          title: "Search product",
          url: "/product/search/mobile phone",
        },
        {
          title: "Single product",
          url: "/product/lord-2019",
        },
        {
          title: "Cart",
          url: "/cart",
        },
        {
          title: "Checkout",
          url: "/checkout",
        },
        {
          title: "Alternative Checkout",
          url: "/checkout-alternative",
        },
        {
          title: "Order confirmation",
          url: "/order-confirmation",
        },
      ],
    },
  ],
];
```

## User Store
This store is used to manage the user state of the application. It contains the user details and the actions that can be performed on the user details.

### User Store Usage
```js
import { useUserStore } from './store';

const Component = () => {
  const { user, setUser } = useUserStore();

  return (
    <div>
      <p>{user.name}</p>
      <button onClick={() => setUser({ name: 'new name' })}>Change Name</button>
    </div>
  );
};
```
**or you can use a simpler version**
```js
import { useUser } from './store';

const Component = () => {
  const { user, setUser } = useUser();

  return (
    <div>
      <p>{user.name}</p>
      <button onClick={() => setUser({ name: 'new name' })}>Change Name</button>
    </div>
    );
};
```

## Settings Store
This store is used to manage the settings state of the application. It contains the settings details and the actions that can be performed on the settings details from the CS Settings record.

### Settings Store Usage
There are two ways to use the settings store.
You can use the `useSettingsStore` hook or the `useSettings` hook. I prefer the `useSettings` hook as it is simpler. 
Settings gets automatically updated throughout the application when the settings are updated in the CS Settings record due to SWR and the `useEffect` hook in page components.
```js
import { useSettings } from './store';

const Component = () => {
  const { settings, setSettings } = useSettings();

  return (
    <div>
      <p>{JSON.stringify(settings)}</p>
    </div>
  );
};
```


## Cart Store
This store is used to manage the cart state of the application. It contains the cart details and the actions that can be performed on the cart details.

[//]: # TODO ()
> **Note:** WIP

## Event Store
This store is used to manage the event state of the application. It contains the event details and the actions that can be performed on the event details.

> WIP
