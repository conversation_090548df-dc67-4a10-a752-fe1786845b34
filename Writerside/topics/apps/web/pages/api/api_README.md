# Next.js API Endpoints

This document provides a detailed overview of the API endpoints available in the `api` folder of this Next.js project, based on the provided JSON file structure. Each endpoint is designed to perform specific operations, offering a comprehensive backend for the web application.

## Getting Started

To use these APIs, you'll need to run the Next.js project locally or deploy it to a server. Ensure you have Node.js installed, then run the following commands:

```
yarn install
yarn dev
```

This will start the development server, making the API accessible via `http://localhost:3000/api/`.

## API Endpoints

### Authentication

- `/api/auth/[...nextauth].js`
    - **Method:** Various
    - **Description:** Handles authentication and session management using NextAuth.js.
    - **Success Response:** Depends on the action performed (login, logout, session check).
    - **Error Response:** JSON object with error message.

### Customer

#### Cart Management

- `/api/customer/cart/[ceid].js`
    - **Method:** GET, POST, DELETE
    - **Description:** Manages individual cart items for a customer.
    - **URL Parameters:**
        - `ceid` (string) - Cart item ID.
    - **Success Response:** JSON object containing cart item details or confirmation of action.
    - **Error Response:** JSON object with error message.

- `/api/customer/cart/item_check.js`
    - **Method:** POST
    - **Description:** Checks items in the cart for availability and other conditions.
    - **Success Response:** JSON object containing check results.
    - **Error Response:** JSON object with error message.

#### Data Deletion

- `/api/customer/delete/address/[id].js` and `/api/customer/delete/card/[id].js`
    - **Method:** DELETE
    - **Description:** Deletes a customer's saved address or payment card.
    - **URL Parameters:**
        - `id` (string) - Unique identifier of the address or card.
    - **Success Response:** JSON object confirming deletion.
    - **Error Response:** JSON object with error message.

#### Event Management

- `/api/customer/event/[eid].js`
    - **Method:** GET, POST
    - **Description:** Manages event-related information and registrations.
    - **URL Parameters:**
        - `eid` (string) - Event ID.
    - **Success Response:** JSON object containing event details or confirmation of registration.
    - **Error Response:** JSON object with error message.

#### Payment Processing

- `/api/customer/pay/paytracekey.js`
    - **Method:** GET
    - **Description:** Gets the PayTrace public key for encryption of payment methods.
    - **Success Response:** JSON object containing payment confirmation.
    - **Error Response:** JSON object with error message.

### Collection Management

- `/api/collection/[cid].js`
    - **Method:** GET, PUT, DELETE
    - **Description:** Manages collections, including retrieval and modification of collection items.
    - **URL Parameters:**
        - `cid` (string) - Collection ID.
    - **Success Response:** JSON object containing collection details or confirmation of action.
    - **Error Response:** JSON object with error message.

### Product Management

- `/api/product/[pid]/index.js`
    - **Method:** GET
    - **Description:** Retrieves product details by product ID.
    - **URL Parameters:**
        - `pid` (string) - Product ID.
    - **Success Response:** JSON object containing product details.
    - **Error Response:** JSON object with error message.

#### Product Search

- `/api/product/search/[slug].js`
    - **Method:** GET
    - **Description:** Performs product searches based on a slug or keyword.
    - **URL Parameters:**
        - `slug` (string) - Search keyword or slug.
    - **Success Response:** JSON object containing search results.
    - **Error Response:** JSON object with error message.

### User Management

- `/api/users/[id].js`
    - **Method:** GET, PUT, DELETE
    - **Description:** Manages user accounts, including retrieval and updating of user information.
    - **URL Parameters:**
        - `id` (string) - User ID.
    - **Success Response:** JSON object containing user details or confirmation of update/deletion.
    - **Error Response:** JSON object with error message.

## Error Handling

All API endpoints return standard HTTP status codes. In case of an error, the response will include a JSON object with an `error` key describing the nature of the error.

## Security

Please note that these APIs are intended for internal use. If exposed to the public, ensure to implement proper authentication and authorization mechanisms to protect sensitive data.

## Contributing

Contributions to the API are welcome. Please follow the project's contribution guidelines and submit a pull request for any enhancements or bug fixes.

## License

Specify the license under which your API is released, if applicable.
