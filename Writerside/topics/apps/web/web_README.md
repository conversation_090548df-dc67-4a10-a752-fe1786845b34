## Requirements

Make sure that you have the last stable [NodeJS](https://nodejs.org/en/download/) and `yarn` version.

- Do not delete the `yarn.lock file`

## Install

Navigate to the project root folder using terminal and install the dependencies.

```js
yarn || npm install;
```

## Start

After the installation is complete, you can launch dev server by running.

```js
yarn dev || npm run dev
```

This starts a local webserver at `http://localhost:3000` and auto detect file changes:

## Build

```js
yarn build || npm run build
```

## Support

Need Support? Create a ticket [HERE](https://support.ui-lib.com/help-center/tickets/new) Or Send us an email at [<EMAIL>](mailto:<EMAIL>).

### changelog

## v3.5.0

###### April 18, 2023

- Update Next.js v13
- Add lockOnWindowScroll and preventVerticalScrollOnTouch property in Carousel.tsx
- Add [icon list page](https://bazaar.ui-lib.com/docs/icons)

## v3.4.0

###### Jan 6, 2023

- Add Language Translation Feature [Next-i18next](https://github.com/i18next/next-i18next)
- Add Product Variants (Options and Type)
- Fix All Dead Links
- Improve & Rename Components/Folder Structure

## v3.3.0

###### Nov 12, 2022

- Redesign Mock Api and Data with Model
- Add Data Models for Product, Category, Shop Etc.
- Add Product Preview Image Feature with Delete Button
- Update User, Admin Dashboard all Pages With SSR
- Add useScoller Hook
- Add Currency function
- Update Documentation
- Update All Packages and Library

## v3.2.0

###### Aug 28, 2022

- Add one Market homepage
- Add two Fashion homepages
- Add SEO component
- Update layout code structure

## v3.1.0

###### Jul 03, 2022

- Add Mega menu and Fullscreen dropdown menu

## v3.0.0

###### Jun 16, 2022

- Add Admin/Vendor dashboard (25+ pages)
- Add RTL Support
- Update npm packages

## v2.3.0

###### Apr 18, 2022

- Fix Build issue (added resolutions &amp; overrides in package.json)

## v2.2.0

###### Mar 21, 2022

- Fix Eslint errors

## v2.1.0

###### Dec 28, 2021

- Add 5 new storefront variations
- Fix small UI issues

## v2.0.0

###### Oct 28, 2021

- Add JavaScript version
- Add REST API to Grocery shop
- Update to Next.js v12 and MUI v5.

## v1.2.0

###### Aug 25, 2021

- Migrate makeStyles API to v5
- Fix next/image issues
- Add a new page &quot;Shop v4&quot;

## v1.1.0

###### Aug 12, 2021

- Add a new page &quot;Shop v3&quot;

## v1.0.1

###### Aug 1, 2021

- Fix dashboard mobile navigation

## v1.0.0

###### Jul 1, 2021

Initial release

### Roadmap

1. More funcional cart, cookie
2. List of all APIs/Functions

### Doc points

. app entry poin
. page props
. component props
. SEO
. Cart
. navigation component structure and data structure
