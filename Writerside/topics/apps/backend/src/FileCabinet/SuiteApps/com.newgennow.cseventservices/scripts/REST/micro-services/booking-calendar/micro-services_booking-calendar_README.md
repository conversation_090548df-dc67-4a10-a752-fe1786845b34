# Booking Calendar Microservice
This microservice is responsible for managing the booking calendar SPA application. The SPA application is used to manage the booking calendar for the event services application. 

## Record Usage

Here is a list of records that are used in the booking calendar microservice:
- **CS Booking**: This record is used to store the booking details for the event services application. The booking details include the event details, booking status, and other details related to the booking.
  - [Record Definition](https://tstdrv1516212.app.netsuite.com/app/common/custom/custrecord.nl?id=123&e=T)
  - [Record List](https://tstdrv1516212.app.netsuite.com/app/common/custom/custrecordentrylist.nl?rectype=123)