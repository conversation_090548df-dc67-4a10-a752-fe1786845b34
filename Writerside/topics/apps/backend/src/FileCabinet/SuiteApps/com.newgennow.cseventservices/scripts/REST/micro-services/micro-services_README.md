# Micro-service folder operations
This folder contains all the micro-services that are used in the application. Each micro-service is a separate folder that contains files for each microservice.

## Folder structure
This folder can't contain any files directly. It should only contain folders for each micro-service. Each micro-service folder should contain its own files. 
Reason for this is because the AMD loader is pre-configured to load files from the micro-service folder. If we add files directly to this folder, the AMD loader will try to load those files from an incorrect path, which will cause an error. **The purpose of this is to prevent having to create an AMDConfig for each new microservice that is created**.

### Usage of custom modules from AMDConfig 
The AMDConfig had to be modified to include the micro-service folder in the paths. This is done to allow the AMD loader to load files from the micro-service folder by using `M/{custom module name}`. _"M"_ is the alias standing for micro-service. 

For example, if we have a file named `test.js` in the `booking-calendar` micro-service folder, we can load the module by using `M/settings`.

## Naming convention
The name of the micro-service folder should be in the format `micro-service-name`. The name should be in lowercase and words should be separated by hyphens.

## Current micro-services   
- [booking-calendar](micro-services_booking-calendar_README.md)