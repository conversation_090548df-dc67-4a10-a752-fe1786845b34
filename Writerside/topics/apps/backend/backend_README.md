# recon-suite-cloud
 A SuiteCloud Project reserving the RESTful communication to its React App

1. Copy all **external** urls from each deployment record into the fields after the "*`=`" sign* in the `buildEnvFile.js`

   > ## Note: Viewing Deployment Records
   >
   > ---
   >
   > When viewing each deployment record make sure under the **audience tab** that **ALL ROLES** are checked and the **Employee created** is selected. Should be: "**NewGen".**

   ---

| ENV Var Name                                                        | Deployment ID or Description                                                                                                                                                                                                                             |
|---------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **OAUTH_CLIENT_ID**                                     | *Consumer Key (**from integration record**)*                                                                                                                                                                                                             |
| **OAUTH_CLIENT_SECRET**                                             | Consumer Secret  *(**from integration record**)*                                                                                                                                                                                                         |
| **OAUTH1_ACCESS_TOKEN**                                             | Token ID *(**React Restlet Access Token Created**)*                                                                                                                                                                                                      |
| **OAUTH1_TOKEN_SECRET**                                             | Token Secret *(**React Restlet Token Created**)*                                                                                                                                                                                                         |
| **OAUTH1_CONSUMER_SECRET**                                          | **DO NOT TOUCH (Carried over from bundle)**                                                                                                                                                                                                              |
| **OAUTH1_CONSUMER_KEY**                                             | **DO NOT TOUCH (Carried over from bundle)**                                                                                                                                                                                                              |
| **NEXT_PUBLIC_ACCOUNT_ID**                                          | Ex. 7180577, TSTDRV1516212, or 7180577_SB1 for sandbox accounts. *How it displays in company info*. (**Letters all CAPS**)                                                                                                                               |
| **NEXT_PUBLIC_ACCOUNT_URL_ID**                                      | Account ID how is displays in a URL.                                                                                                                                                                                                                     |
| **NEXT_PUBLIC_SITE_URL**                                            | The url of the vercel project (hosted URL)                                                                                                                                                                                                               |
| **NEXTAUTH_URL** _Deprecated_                                       | The url of the vercel project (hosted URL). **Needs to be the** **same value as the variable above!**                                                                                                                                                    |
| **NEXT_PUBLIC_RCS_RL_EVENT_SSR_DETAILS_URL**                        | ***customdeploy_get_event_ssr_details** -* Grabs event details to render event page. **                                                                                                                                                                  |
| **NEXT_PUBLIC_RCS_RL_GET_ITEM_DETAILS_URL**                         | ***customdeploy_rcs_item_details -*** Grabs details on a single item renders product detail page.                                                                                                                                                        |
| **NEXT_PUBLIC_RCS_RL_GET_USER_RELATED_SHOWS_URL**                   | ***customdeploy_rcs_rl_user_related_shows -*** Grabs related shows based on auth'd user to render on *Event Selection* page.                                                                                                                             |
| **NEXT_PUBLIC_RCS_RL_AUTO_COMPLETE_ITEM_SEARCH_URL**                | ***customdeploy_rcs_ng_product_search -*** Interprets input to return products based on criteria entered in navbar. *Only return active items.*                                                                                                          |
| **NEXT_PUBLIC_RCS_RL_GET_CS_SETTINGS_URL**                          | ***customdeploy_rcs_ng_get_cs_settings*** - Grabs settings for current account. Public REST endpoint.                                                                                                                                                    |
| **NEXT_PUBLIC_RCS_RL_CART_ITEM_VALIDATE_URL**                       | ***customdeploy_rcs_ng_rl_cart_item_valid -*** Validates product information to retrieve correct price and child ID to create orders.                                                                                                                    |
| **NEXT_PUBLIC_RCS_RL_GET_CURRENT_USER_URL**                         | ***customdeploy_rcs_rl_get_current_user -*** Callback request to fetch current user information based off runtime module and ID of user to return a session object.                                                                                      |
| **NEXT_PUBLIC_RCS_RL_GET_EVENT_COLLECTION_ITEMS_INFINITE_LIST_URL** | **customdeploy_rcs_rl_item_inf_scroll_list *-*** Runs infinite load for items to auto paginate.                                                                                                                                                          |
| **NEXT_PUBLIC_RCS_RL_GET_EVENT_COLLECTION_ITEMS_URL**               | ***customdeploy_rcs_rl_exp_get_item_collect*** ***-*** Grabs information on a certain NS collection to render collection page. ******                                                                                                                    |
| ***NEXT_PUBLIC_RCS_RL_GET_USER_ACCOUNT_INFO_URL***                  | ***customdeploy_rcs_rl_get_account_info -***  Grabs information about user to render account page.                                                                                                                                                       |
| **NEXT_PUBLIC_RCS_RL_HANDLE_PAYMENT_ORDER_PROCESSING_URL**          | ***customdeploy_rcs_rl_order_processing -*** Multi-Functional request which handles only the processing by creating an order. *Front end only sends orders to this endpoint.*                                                                            |
| **NEXT_PUBLIC_RCS_RL_HANDLE_PAYMENT_ORDER_URL**                     | ***customdeploy_rcs_purchase_order_cart -*** Multi-Functional request which pulls pre-required information to process an order. Create cards, addresses, and orders with this endpoint. (*Orders offloaded to above deployment to increase performance)* |
| **NEXT_PUBLIC_PAYTRACE_CERT_URL**                                   | ***customdeploy_ng_sl_pt_web_handler_pubkey -*** Grabs public key for paytrace E2E encryption library to encrypt newly created cards.                                                                                                                    |

# ESLint Config Inspector

Launch the ESLint Config Inspector by running the following command:

```bash
eslint --inspect-config
```