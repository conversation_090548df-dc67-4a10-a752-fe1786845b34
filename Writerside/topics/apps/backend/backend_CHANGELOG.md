# Changelog

<a name="1.1.6"></a>
## 1.1.6 (2022-03-16)

### Added

- ✨ CSES-26 Added Sample Unit Testing [[ea8a5e8](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/ea8a5e8a5751570309bd5a0b707adcfcde5439dc)]

### Changed

- 💬 Fixed Changelog formating [[bdc62a5](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/bdc62a5eef11adb51ffe543e718f948ff5e63ab0)]
- 💬 CSES-26 Added Changelog to project [[211cc5d](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/211cc5dd65cd1afbb69c5a06a8254b0ff6b130cb)]


<a name="1.1.5"></a>
## 1.1.5 (2022-03-16)

### Fixed

-  CSES-26 Fixed Error Dataset matching name. [[b348e3c](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/b348e3cf7d3c4cc7f3f6ee494cda14e330e5a63d)]
-  CSES-26 Fixed Custom Error to paint to storefront [[d2415d5](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/d2415d5655a342315a14c0265592836c0f2b66e5)]
-  Fixed search auto complete [[29bc230](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/29bc2308b76e22d46fc5907a770655e67ce3ed4e)]
-  Fixed Collection emptiness error [[20faf60](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/20faf60444069b45e720425a994801be71532557)]
-  Added Token Role to Obj and Fixed the show date to last until its day as of midnight. [[b04b0c3](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/b04b0c334145a12d532d3368ac9591e32da1d8b6)]

### Changed

- 💬 CSES-26 Added Changelog to project [[211cc5d](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/211cc5dd65cd1afbb69c5a06a8254b0ff6b130cb)]
-  CSES-9 Restored OG script for bundle update. [[1e75dbd](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/1e75dbd6b2e62180a2d0e722e271926b7f5cbb12)]
-  CSES-12 Set Up Crypto Module import [[1a89a38](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/****************************************)]
-  CSES-3 Had to convert both values to a string as &#x60;card_id&#x60; is of type &#x60;number&#x60; [[c3c1652](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/c3c1652d097df5786e329288fec22ddc49445137)]
-  CSES-3 Need to Deduct 1 from the card count to not count &#x60;currentLine&#x60; [[b5bfce0](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/b5bfce0221c63e6eb1465d121b5b0d5b8e746843)]
-  CSES-3 Update Loop to iterate backwards to delete cards [[2606b16](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/2606b165f67ed5228b962fead6cf3506070a0c70)]
-  Updated Display name search query condition [[f207f51](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/f207f513a8534ef5e5a116fcc7b13a60c734f8c8)]
-  Update rcs_rl_user_related_company_shows.js [[6b11d2c](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/6b11d2c68979b6aeb7d6c26737b37181e5b8bb68)]

### Added
-  Added infinite load for items [[7cfe6d3](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/7cfe6d3ef6df429ccb854ed659e3e543398b0b46)]
-  CSES-26 Added Backend Payment Error Handling [[07bb395](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/07bb3952697a204ee4edf82baf8780c821130685)]
-  CSES-3 Created Card Deletion Endpoint [[f060b69](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/f060b69d4732acbbe48e167b3cf6180832d57fa7)]
-  CSES-5 Pulled in All Files Used For Payment Processing [[2f7e760](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/2f7e760bbb1273d427baad026c2004330dc65703)]
-  Added Deletion Functionality [[1862146](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/18621460d9e4a198e322f88a46eb28f2aa9ea7cb)]

### Miscellaneous

-  CSES-26 Updated Revision Version and changelog [[cfe3c3a](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/cfe3c3aad9fb4891e5a7574b9684ec9883253467)]


<a name="1.1.3"></a>
## 1.1.3 (2021-09-17)

### Miscellaneous

-  Fixed price level algo and added log to show no show dates are why no items will show. [[3f6ed35](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/3f6ed35f8b8bd58f49792bc0c87a0e92650a519d)]
-  Added Canada fields from event record to calculate tax [[6923a90](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/6923a906e7e8fcdb09dc5229765ce25c10da83e7)]
-  Added display web convenience fee bool to settings endpoint. [[d9c8414](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/d9c8414434a78db9430d825019e424b5324c9769)]
-  Added coment [[55284b0](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/55284b03bcab8454426808383bebf604885c6c40)]
-  Updated address addition logic to support international logic [[49b94bd](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/49b94bdf6dca200f9018e580a705160f4069da56)]
-  changed country PL [[e1f4418](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/e1f4418249e7fd6a630fd384523d0fbdc09cb369)]
-  Fixed cart validation [[bbbdc06](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/bbbdc06ecbf5d160fe17ba9f27923def4d6f23ea)]
-  Added linting &amp; set compare price to undefined [[0681063](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/0681063598e4329e9427c2e943500ce10e002777)]
-  Added Local Linting for proj. [[07018da](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/07018da4f37effc424950e7acf1d042a5befac95)]
-  Added Strikeout price to product page and CID page Fixed Excerpt on collection card. Fixed Query on Cart validate. [[46f8653](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/46f865381db8252f15711cccd5c0929e6d82b5f2)]
-  Added tertiary  for query of undefined args in string condition. [[5795fd3](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/5795fd326482ab2c38cc928e5d4848f621c3cdfe)]
-  Updated regex on finding &quot; &#x27; &quot;  causing invalid syntax to be generated. [[2dc1996](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/2dc199684df5a46a18886fda1ff918bc79b3a2cd)]
-  Fixed replace if arg is undefined [[154c998](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/154c9984b5c4843026cb8f1fc708bd38f3880e94)]
-  Added regex to escape &quot; &#x27; &quot; from query [[7ddd546](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/7ddd5461ada9612c19f70a5c4da6f1aab2f08089)]
-  Removed query module from order endpoint bc validation is taken place prior. [[33a62fc](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/33a62fc1998aca8ab60d907f04dcd27297dc0c6c)]
-  Fixed variable naming on list create of queries [[d7a8fc6](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/d7a8fc6e2c47ac916badba50262708ed502437f5)]
-  Fixed undefined ids [[28f9d7c](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/28f9d7c59282c9d40e834be25fa931ba54f13092)]
-  Fixed cart validation! [[99d7706](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/99d77064ff40d160ea8f61990ecf5e5f70b5d36d)]
-  Added quer checks for all variants [[ea34234](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/ea34234f98136334a1513c31d10e0acb8351947e)]
-  Fixed prices on labor items [[8f77bdf](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/8f77bdfc9d90ea889a3b2ec20bcf0bc091e9fde0)]
-  Fixed ChildId find on color mismatch [[82ae52d](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/82ae52d634f750fcb8b8b89c126aa99d93f7fafc)]
-  Price level gets set properly on items [[71c31ef](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/71c31ef16224b09bd63fe6a34da921f8bc28920c)]
-  Fixed Cart item validate. [[b7641a0](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/b7641a0bc65a35c67bb6833ee2233f69c2436f5f)]
-  Added comment to item validate [[339503c](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/339503ce9dcd6124a4e13e0681a159cc14df10a9)]
-  Updated freight &amp; item cart validation [[2c52d7d](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/2c52d7db9dc40b16483680f677240428d2b079ee)]
-  Setting fright price manually [[1f58a40](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/1f58a409d5c8c480186c766c69072c32734cabc0)]
-  Updated supervision item writing [[709c817](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/709c817b51bed11aa8c9dec77a933c116aec83bb)]
-  Labor item options have been set [[8ee7dce](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/8ee7dce785092e0eddce353de7a908b6729fd118)]
-  Added Fright Item Desc to Order when processing [[65da76f](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/65da76feaea75f42cc1f97d361224ec056d21d11)]
-  Fixed Order processing info [[0b87967](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/0b87967ad8be64b433b50d6347645db7b8ea143b)]
-  Added freight table to getItemPrice [[9328860](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/93288604a83e49fee731827f9e535798d9737e26)]
-  Added supervisor markup [[faf6c7a](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/faf6c7a30f742ef104854e0ca4d7e659259ed53e)]
-  Added labor schedule search [[47760a2](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/47760a27bcdf80faef7a2475a8e023efc1f05c7a)]
-  Copied from FileCabinet [[6e60ac4](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/6e60ac414d2d0a6fa1c7bd4823b8a6e45b20cc76)]
-  Merge branch &#x27;master&#x27; of https://github.com/newgen-business-solutions/recon-suite-cloud [[c53930d](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/c53930d91f5d5f264c03ab8e76def5d60df1c8f1)]
-  Added sale unit to collection card [[69f107c](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/69f107c4142f796c6e937102e6177744b9bb74e4)]
-  Added description of square ft item added [[bbb057c](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/bbb057cf470cc8a9663d5dfb591508bf3bc3f410)]
-  Added Labor dates to cid endpoint [[3e7ed7c](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/3e7ed7ca5b6c66c0442ea937e88286d055f65985)]
-  Removed uneeded field [[03cfe48](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/03cfe48550526308eef3c272b70b9df38330d717)]
-  Added Additional reference arrays for grabbing days calc items. [[7c48d0f](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/7c48d0ffb53b27f7bd0874880046ca3f8bc7a4e4)]
-  Removed Solupay from searches [[70d4cc0](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/70d4cc0b83584e954c9cee79b4e6e2934fc8ee27)]
-  Got rid of solupay field [[c2d2853](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/c2d28534c93abeeeeafec5ae20bb97c3ec3ccc46)]
-  Merge branch &#x27;master&#x27; of https://github.com/newgen-business-solutions/recon-suite-cloud [[960477d](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/960477d6661f7ac3f85bbde325e346be65c92a3a)]
-  Added settings EP [[a9fe81d](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/a9fe81d08373572d9ed2d4c1319643878f573cca)]
-  Update rcs_handle_payment_order.js [[4a3636d](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/4a3636d2f66d2bcbc42bc490f0b0a3438294115b)]
-  Fixed Item search to only display ones allowed from settings. [[9369694](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/93696945f277602aecf8b03b92f81b6ef7a26bc3)]
-  Merge branch &#x27;master&#x27; of https://github.com/newgen-business-solutions/recon-suite-cloud [[82a30b9](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/82a30b92fda0c5f6d60678264bb465ba8f9f9d0c)]
-  Added Product Search [[9fccf14](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/9fccf14fc067f0db92d97e430b824d312a1943fb)]
-  Got valid variants working and matrix child item ids [[ace51db](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/ace51db8eb9c4e05248ea67362a3c6ca5a91cdf9)]
-  Started Cart Check File [[3ce6f95](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/3ce6f957dbba70ee274c30a5f94a489c00402346)]
-  Order submit [[a59d46f](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/a59d46fb5c6d923d6285b4a6abd69b480de70090)]
-  Troubleshooting CSJob [[fa1fb57](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/fa1fb5766d00292f6c62102d76b41f6be875f0e3)]
-  Made settings function [[7cf4612](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/7cf461257162b4711e5c489974f31b92dc02fe60)]
-  Fixed order request [[71b90ec](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/71b90ecb76e5031edaea4b375d68f7f73cb52a62)]
-  Order all debugged up [[46912a0](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/46912a07c7941059b034efc4ea2f8d2ffae4a52a)]
-  Paytrace card record can be added [[cd174d9](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/cd174d906a3869585eb961cb513f59c7fa3fdeda)]
-  Card Types get determined via RegEx  And added successfully [[6ab2881](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/6ab288112407476ba88aef9fee5a3ccf5a03fb54)]
-  Added zipcode length cap and defaults to billing and shipping [[4e8d06d](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/4e8d06d61970a1df1ad235b220cdfd24f048ba0b)]
-  Getting Credit Cards Added [[37fff5f](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/****************************************)]
-  Payment Methods Added to Checkout [[d232cbd](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/d232cbd9ccf85f1a4f570a263343056579a7a1c1)]
-  Got payement settings added to req [[08ba790](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/08ba7908d610de24ae11bd5c2afd0a1c49269145)]
-  Got event details for order page [[bec3cb8](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/bec3cb8aae56b47ddfcf3c4e5d286c05c275781f)]
-  Got current price from price level working on collection cards and item page. [[2d7e214](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/2d7e214f14c822e0e39f0f087764ceeb4735e220)]
-  default show dates set to filter show date. [[0f02ae0](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/0f02ae01e97f5de2902245ba015dbaa6e7f82937)]
-  Building Item price Algo [[d081308](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/d081308151e02f6c5cb9a53f0a01fb32f64681dc)]
-  Fixed dumb change [[4d204b6](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/4d204b62cd66cdd0159116903230cec17ecdcced)]
-  Fixed and Added Item Details GET [[cde705a](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/cde705a1971566b2eb57a75aba7721d7627b3d43)]
-  Started Item Details [[cedf300](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/cedf3001e03f05babc64abcd026c767e39847d3e)]
-  Recent Orders Page Done [[b7e58ff](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/b7e58ffafccd91b6afea0b4b888c7d3fb2c123e3)]
-  Fix Tabel parse Endpoint [[e4437fb](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/e4437fb06f875547c7b4267f3566bd280430b4c4)]
-  Got Venue, blurbs, and info [[0d5b9c7](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/0d5b9c71abb6863dc739d585376be024f135c2f2)]
-  Updated to have two separate searches [[d92e27c](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/d92e27c926c564260190a4ae59efba3c0292a9c1)]
-  Added ssr event detail [[1bd9002](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/1bd9002b7b685b4c483b7db8ac6bb1b20c301ed9)]
-  Update rcs_rl_user_related_company_shows.js [[b883763](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/b8837631835271b8d9debc078a323cecd5d749ce)]
-  Added shows to dropdown [[ff1284b](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/ff1284bf2be4729e839d474a0d62f9843697c227)]
-  Fixed for upload [[27c6157](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/27c6157654c91c0e9e6064e043cae63a050d0df9)]
-  Made Separate Folder [[cb2227b](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/cb2227b1548e7ff57e760bed27475754ab49d937)]
-  Made Into Repo [[de3e157](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/de3e157919bb7e73a80f8449459ad58f0e1d6690)]
-  Initial commit [[cff9026](https://github.com/newgen-business-solutions/recon-suite-cloud/commit/cff90262bcf5de70634d9a03a2bc0296f8ccb520)]


