#!/usr/bin/env node

/**
 * Development Server Orchestration Script
 * 
 * This script starts all development servers in the correct order with proper
 * dependency management and helpful logging.
 */

const { spawn } = require('child_process');
const chalk = require('chalk');
const path = require('path');

// Configuration
const APPS = [
  {
    name: 'Web App',
    command: 'yarn',
    args: ['dev:web'],
    port: 3000,
    color: 'blue',
    priority: 1,
    healthCheck: 'http://localhost:3000'
  },
  {
    name: '<PERSON>an <PERSON>',
    command: 'yarn',
    args: ['dev:scan'],
    port: 4000,
    color: 'green',
    priority: 2,
    healthCheck: 'http://localhost:4000/scan'
  },
  {
    name: 'Booking Proto',
    command: 'yarn',
    args: ['dev:booking'],
    port: 4001,
    color: 'yellow',
    priority: 3,
    healthCheck: 'http://localhost:4001/booking'
  }
];

const processes = new Map();
let startupComplete = false;

// Utility functions
const log = (app, message, type = 'info') => {
  const timestamp = new Date().toLocaleTimeString();
  const colors = {
    blue: chalk.blue,
    green: chalk.green,
    yellow: chalk.yellow,
    red: chalk.red,
    cyan: chalk.cyan,
    magenta: chalk.magenta
  };
  
  const color = colors[app.color] || chalk.white;
  const prefix = color(`[${app.name}]`);
  
  switch (type) {
    case 'error':
      console.log(`${chalk.gray(timestamp)} ${prefix} ${chalk.red('ERROR:')} ${message}`);
      break;
    case 'success':
      console.log(`${chalk.gray(timestamp)} ${prefix} ${chalk.green('✓')} ${message}`);
      break;
    case 'warn':
      console.log(`${chalk.gray(timestamp)} ${prefix} ${chalk.yellow('⚠')} ${message}`);
      break;
    default:
      console.log(`${chalk.gray(timestamp)} ${prefix} ${message}`);
  }
};

const logGlobal = (message, type = 'info') => {
  const timestamp = new Date().toLocaleTimeString();
  const prefix = chalk.cyan('[DEV SERVER]');
  
  switch (type) {
    case 'error':
      console.log(`${chalk.gray(timestamp)} ${prefix} ${chalk.red('ERROR:')} ${message}`);
      break;
    case 'success':
      console.log(`${chalk.gray(timestamp)} ${prefix} ${chalk.green('✓')} ${message}`);
      break;
    case 'warn':
      console.log(`${chalk.gray(timestamp)} ${prefix} ${chalk.yellow('⚠')} ${message}`);
      break;
    default:
      console.log(`${chalk.gray(timestamp)} ${prefix} ${message}`);
  }
};

// Check if port is available
const isPortAvailable = async (port) => {
  return new Promise((resolve) => {
    const net = require('net');
    const server = net.createServer();
    
    server.listen(port, () => {
      server.once('close', () => resolve(true));
      server.close();
    });
    
    server.on('error', () => resolve(false));
  });
};

// Start a single app
const startApp = async (app) => {
  const isAvailable = await isPortAvailable(app.port);
  
  if (!isAvailable) {
    log(app, `Port ${app.port} is already in use. Please free the port and try again.`, 'error');
    return false;
  }

  log(app, `Starting on port ${app.port}...`);
  
  const process = spawn(app.command, app.args, {
    cwd: path.resolve(__dirname, '..'),
    stdio: ['pipe', 'pipe', 'pipe'],
    shell: true
  });

  processes.set(app.name, process);

  // Handle stdout
  process.stdout.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      // Filter out some noise and highlight important messages
      if (output.includes('ready') || output.includes('started') || output.includes('compiled')) {
        log(app, output, 'success');
      } else if (output.includes('error') || output.includes('failed')) {
        log(app, output, 'error');
      } else if (output.includes('warn')) {
        log(app, output, 'warn');
      } else {
        log(app, output);
      }
    }
  });

  // Handle stderr
  process.stderr.on('data', (data) => {
    const output = data.toString().trim();
    if (output && !output.includes('ExperimentalWarning')) {
      log(app, output, 'error');
    }
  });

  // Handle process exit
  process.on('exit', (code) => {
    if (code !== 0) {
      log(app, `Process exited with code ${code}`, 'error');
    } else {
      log(app, 'Process stopped', 'warn');
    }
    processes.delete(app.name);
  });

  return true;
};

// Start all apps in order
const startAllApps = async () => {
  logGlobal('🚀 Starting CS Event Services Development Environment');
  logGlobal('');
  logGlobal('Port allocation:');
  APPS.forEach(app => {
    logGlobal(`  ${app.name}: http://localhost:${app.port}`);
  });
  logGlobal('');

  // Sort apps by priority
  const sortedApps = [...APPS].sort((a, b) => a.priority - b.priority);

  for (const app of sortedApps) {
    const started = await startApp(app);
    if (!started) {
      logGlobal(`Failed to start ${app.name}. Aborting startup.`, 'error');
      process.exit(1);
    }
    
    // Add a small delay between starts to avoid overwhelming the system
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  startupComplete = true;
  logGlobal('All development servers started successfully! 🎉', 'success');
  logGlobal('');
  logGlobal('Available services:');
  APPS.forEach(app => {
    logGlobal(`  ${app.name}: ${chalk.underline(app.healthCheck)}`);
  });
  logGlobal('');
  logGlobal('Press Ctrl+C to stop all servers');
};

// Cleanup function
const cleanup = () => {
  if (processes.size > 0) {
    logGlobal('Stopping all development servers...');
    
    processes.forEach((process, name) => {
      logGlobal(`Stopping ${name}...`);
      process.kill('SIGTERM');
    });

    // Force kill after 5 seconds
    setTimeout(() => {
      processes.forEach((process, name) => {
        if (!process.killed) {
          logGlobal(`Force killing ${name}...`, 'warn');
          process.kill('SIGKILL');
        }
      });
      process.exit(0);
    }, 5000);
  } else {
    process.exit(0);
  }
};

// Handle process signals
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);
process.on('exit', cleanup);

// Start the development environment
startAllApps().catch((error) => {
  logGlobal(`Failed to start development environment: ${error.message}`, 'error');
  process.exit(1);
});
