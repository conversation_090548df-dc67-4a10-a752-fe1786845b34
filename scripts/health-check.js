#!/usr/bin/env node

/**
 * Health Check Script for CS Event Services
 * 
 * This script checks if all development servers are running and accessible.
 */

const http = require('http');
const chalk = require('chalk');

const SERVICES = [
  {
    name: 'Web App',
    url: 'http://localhost:3000',
    path: '/',
    color: 'blue'
  },
  {
    name: '<PERSON>an PWA',
    url: 'http://localhost:4000',
    path: '/scan',
    color: 'green'
  },
  {
    name: 'Booking Proto',
    url: 'http://localhost:4001',
    path: '/booking',
    color: 'yellow'
  }
];

const checkService = (service) => {
  return new Promise((resolve) => {
    const url = new URL(service.path, service.url);
    
    const req = http.get(url.toString(), { timeout: 5000 }, (res) => {
      resolve({
        ...service,
        status: res.statusCode < 400 ? 'healthy' : 'unhealthy',
        statusCode: res.statusCode
      });
    });

    req.on('error', () => {
      resolve({
        ...service,
        status: 'down',
        statusCode: null
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        ...service,
        status: 'timeout',
        statusCode: null
      });
    });
  });
};

const main = async () => {
  console.log(chalk.cyan('🏥 CS Event Services Health Check\n'));

  const results = await Promise.all(SERVICES.map(checkService));
  
  let allHealthy = true;

  results.forEach(result => {
    const colors = {
      blue: chalk.blue,
      green: chalk.green,
      yellow: chalk.yellow
    };
    
    const color = colors[result.color] || chalk.white;
    const name = color(result.name.padEnd(15));
    
    let status;
    switch (result.status) {
      case 'healthy':
        status = chalk.green('✓ Healthy');
        break;
      case 'unhealthy':
        status = chalk.yellow(`⚠ Unhealthy (${result.statusCode})`);
        allHealthy = false;
        break;
      case 'down':
        status = chalk.red('✗ Down');
        allHealthy = false;
        break;
      case 'timeout':
        status = chalk.red('✗ Timeout');
        allHealthy = false;
        break;
      default:
        status = chalk.gray('? Unknown');
        allHealthy = false;
    }
    
    console.log(`${name} ${result.url.padEnd(25)} ${status}`);
  });

  console.log();
  
  if (allHealthy) {
    console.log(chalk.green('🎉 All services are healthy!'));
    process.exit(0);
  } else {
    console.log(chalk.red('❌ Some services are not healthy. Run `yarn dev` to start them.'));
    process.exit(1);
  }
};

main().catch(error => {
  console.error(chalk.red('Health check failed:'), error.message);
  process.exit(1);
});
