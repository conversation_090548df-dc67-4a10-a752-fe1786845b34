# Changelog

<a name="12.5.81"></a>
## 12.5.81 (2025-01-13)

### Added

- ✨ Enhance Customer Query and Processing in ng_cses_sl_ordering_wizard.js [[73808b1](https://github.com/newgen-business-solutions/cs-event-services/commit/73808b1750a693744e2f27a1a1be7ea293d497ee)]
- ✨ Add Tax Item Handling and Improve Timeout Logic in ng_es_cs_orders.js [[fa43a0c](https://github.com/newgen-business-solutions/cs-event-services/commit/fa43a0cd67ac3114f378be2ebe4f896b67b2b1e4)]
- ✨ Integrate Vercel Analytics for Checkout Tracking [[ed8c60b](https://github.com/newgen-business-solutions/cs-event-services/commit/ed8c60b4833b4d8463f01e4ea44fa610df032b47)]
- ✨ Add @vercel/analytics dependency and update yarn.lock [[86f643f](https://github.com/newgen-business-solutions/cs-event-services/commit/86f643f53ba72760faf1dcf4b0e09a2f275b87cb)]
- ✨ Feat: Add Footer Rich Text Support and New Logo [[d7e9a2b](https://github.com/newgen-business-solutions/cs-event-services/commit/d7e9a2bcef163d28cba2dd25cd1ef33a297e6707)]
- ✨ Feat: Enhance Tax Code Retrieval Logic [[22efc8a](https://github.com/newgen-business-solutions/cs-event-services/commit/22efc8a2549917908b435394381a8bb434587e16)]
- ✨ Add Attachment Support To Mass Mailer [[ae1b5b4](https://github.com/newgen-business-solutions/cs-event-services/commit/ae1b5b405547f686a087281813bff704bd5ce55d)]
- ✨ Add Motion Library and Update Dependencies [[42d0640](https://github.com/newgen-business-solutions/cs-event-services/commit/42d0640033453f2106bc023ec6f491d982fcabbe)]
- ✨ Update project configuration and add Event Web Items documentation [[1e7fa3b](https://github.com/newgen-business-solutions/cs-event-services/commit/1e7fa3bf8cc1a4602342e58f472e63f4c293b53f)]
- ✨ Refactor Event Creation and Client-Side Scripts [[7a38a06](https://github.com/newgen-business-solutions/cs-event-services/commit/7a38a060affb6b16bd29c0b735087885453726f5)]
- 🔊 Enhance structured logging in price hooks [[706a235](https://github.com/newgen-business-solutions/cs-event-services/commit/706a235de4b9c4d1e30ce3c49c2a54b5e33cbb67)]
- 🔊 Enhance logging consistency in price hooks [[4191a22](https://github.com/newgen-business-solutions/cs-event-services/commit/4191a22888937fe0c009e9575d40b4f5f787618c)]
- ✨ Rename Suitelet for Event Creation Wizard and Mass Mailer [[a4d7e7a](https://github.com/newgen-business-solutions/cs-event-services/commit/a4d7e7a87b69aacab0b06457c56268b5deaf7a42)]
- ✨ Add Attachment Support To Mass Mailer [[8a5c2d5](https://github.com/newgen-business-solutions/cs-event-services/commit/8a5c2d539c91f4ecef33481af4c1b9731796f773)]
- ✅ Add test pages for API and environment variable display [[1bf0405](https://github.com/newgen-business-solutions/cs-event-services/commit/1bf0405ec7a9540b36a62de55709597792e005af)]
- ➕ Add MissingRequiredItems component to checkout flow [[6d77dc9](https://github.com/newgen-business-solutions/cs-event-services/commit/6d77dc97ebc750ed0988d7bf6b114bf4493f6916)]
- 🔊 Add logging for price output and updated item prices [[f998c59](https://github.com/newgen-business-solutions/cs-event-services/commit/f998c59abda63b620a602661968d56cad30a63ba)]
- ✨ Add Service Charge logic to RL [[92bf6b3](https://github.com/newgen-business-solutions/cs-event-services/commit/92bf6b34749a412edffb77ee531d915c6593ecfd)]
- ✨ Unset service charge desc on SO lines [[53a9614](https://github.com/newgen-business-solutions/cs-event-services/commit/53a96141a6885f927e83a8f462d633e260bdbef6)]
- ✨ Add Service Charges to Portal [[5c3f058](https://github.com/newgen-business-solutions/cs-event-services/commit/5c3f05881fa18704a631b61b299aa33fae87a531)]
- ✨ Added UE and CS for CS Service Order [[af380d8](https://github.com/newgen-business-solutions/cs-event-services/commit/af380d85c41d31e8e1db2e906f9a3bd549f0a6b7)]
- ✨ Add calculation of service charges to SOs [[744b494](https://github.com/newgen-business-solutions/cs-event-services/commit/744b494fdccc6a1dee5716b8a549bafab4c24c7a)]
- ✨ Added Gratuity Schedules To Event Wiz and SO [[29854b0](https://github.com/newgen-business-solutions/cs-event-services/commit/29854b095919e77136d616a4cb83d8afa15abedb)]
- ➕ Add PropTypes to RequiredItems and optimize useSWR call [[2f07f10](https://github.com/newgen-business-solutions/cs-event-services/commit/2f07f10bf703d2b25c1c4b5c3fd8ce566f66e9ef)]
- ➕ Add related and required items to fetchItemDetails [[b1045a0](https://github.com/newgen-business-solutions/cs-event-services/commit/b1045a01be09e0360ec9f486ce69b57528265cf0)]
- ✨ FEAT: Add support for parent product IDs and required products [[24576d3](https://github.com/newgen-business-solutions/cs-event-services/commit/24576d31a6cff65ec606abd00002933f6355be22)]

### Changed

- ⚡ Optimize: Scripted Reports top image for dynamic resize ([#206](https://github.com/newgen-business-solutions/cs-event-services/issues/206)) [[f3aa6d1](https://github.com/newgen-business-solutions/cs-event-services/commit/f3aa6d1969577e9ca2b6ddbc3542e48eacaba51f)]
- ⚡ Refactor Customer Query Logic and Enhance Event Handling in ng_cses_sl_ordering_wizard.js and ng_cses_slsc_ordering_wizard.js [[9db6cb0](https://github.com/newgen-business-solutions/cs-event-services/commit/9db6cb0b3ba958214d259eea9666625a6a9e7ca2)]
- ⚡ Refine Tax Item Setting Logic in ng_es_cs_orders.js [[5b9fd27](https://github.com/newgen-business-solutions/cs-event-services/commit/5b9fd275fb1d7fdac0e7a7c5505bea8146397b79)]
- 🎨 Update: Add missing newline in CheckoutForm2.jsx for code clarity [[18b0a69](https://github.com/newgen-business-solutions/cs-event-services/commit/18b0a694cc9a731f1e407290b02836200f36e897)]
- ⚡ Enhance Checkout Components: Add Order Fetching and Optimize Rendering [[04f7508](https://github.com/newgen-business-solutions/cs-event-services/commit/04f7508bd542d95fc4e4e3fb5267a64fa0a77136)]
- ⚡ Enhance &#x60;ng_cses_rl_item_paged_search.js&#x60;: Refactor item search logic and improve error handling [[08f36b8](https://github.com/newgen-business-solutions/cs-event-services/commit/08f36b8676a62a96bff1d784d5670d739c1ebe23)]
- 🎨 Enhance Product Search: Add Loading State and Error Handling [[01ab313](https://github.com/newgen-business-solutions/cs-event-services/commit/01ab31360f719263515b8fe11b0840f7e708aecb)]
- ⚡ Enhance ng_cses_rl_item_paged_search.js: Improve search functionality and add logging [[62e38c8](https://github.com/newgen-business-solutions/cs-event-services/commit/62e38c8079f2500c0585012c8baf11df57d17462)]
- ⚡ Enhance App Component: Integrate Analytics and Improve User Loading State [[99b473c](https://github.com/newgen-business-solutions/cs-event-services/commit/99b473cd4136694d83fc31001d7eda86ff17b0d0)]
- 🎨 Update(Web): Refactor BOL Delete [[302df0d](https://github.com/newgen-business-solutions/cs-event-services/commit/302df0d83e57103295e83ff306065b6a09932624)]
- ♻️ Refactor Material Handling Schedule ([#205](https://github.com/newgen-business-solutions/cs-event-services/issues/205)) [[397405b](https://github.com/newgen-business-solutions/cs-event-services/commit/397405bb9cf4e4a51c17149487aa33e75c1da9c7)]
- ♻️ Update Cancel BOL [[fe6c497](https://github.com/newgen-business-solutions/cs-event-services/commit/fe6c4971341d941c4e92559dd59f9d47e8d6fce3)]
- ♻️ Update BOL Delete [[fb897fe](https://github.com/newgen-business-solutions/cs-event-services/commit/fb897fe641e7fb733848a9473e394bd536e317eb)]
- ♻️ Refactor Material Handling Schedule ([#205](https://github.com/newgen-business-solutions/cs-event-services/issues/205)) [[4b89988](https://github.com/newgen-business-solutions/cs-event-services/commit/4b89988564e79d2f59b832e9bd3fe4c151a76c0e)]
- ♻️ Update Cancel BOL [[cba536e](https://github.com/newgen-business-solutions/cs-event-services/commit/cba536ef61e4627269fe617fb18599a4e9d53ec7)]
- ♻️ Update BOL Delete [[76c9195](https://github.com/newgen-business-solutions/cs-event-services/commit/76c919513ffdcaaf71f86d96eff0798ce860d091)]
- 🔧 Add initial workspace configuration for cs-event-services [[bb72c3c](https://github.com/newgen-business-solutions/cs-event-services/commit/bb72c3c1e4edb691ccccf7847eb5af983f6c901a)]
- ♻️ Change Error Banner to Info Banner [[dc0b261](https://github.com/newgen-business-solutions/cs-event-services/commit/dc0b26143e470589ae592de771b967838c07a936)]
- ♻️ Update Animation Library Imports in Components [[fd50f4c](https://github.com/newgen-business-solutions/cs-event-services/commit/fd50f4c99ed001df365a2c5303cc11b5f06638fd)]
- ♻️ Update Material Handling Schedule CS [[8f5b0f2](https://github.com/newgen-business-solutions/cs-event-services/commit/8f5b0f20849ac1ca137b1db6fd7bc054dd9405c6)]
- ♻️ Update CS Settings CS [[2fa5975](https://github.com/newgen-business-solutions/cs-event-services/commit/2fa5975ea1db5a5aa9151450bd1be818fbdc0448)]
- ♻️ Update BOL UI [[a98fd7d](https://github.com/newgen-business-solutions/cs-event-services/commit/a98fd7d762bcc618c46b419e72310ebe0581374c)]
- ♻️ Refactor Animation Imports from Framer Motion to Motion React [[18bb485](https://github.com/newgen-business-solutions/cs-event-services/commit/18bb485eebf16217f6af88e816b51642a6aac50e)]
- ⚡ Enhance Category Menu and Mega Menu Components [[cd0e0a7](https://github.com/newgen-business-solutions/cs-event-services/commit/cd0e0a71ab5e8519b0d55879a0d01c993202a32d)]
- ⚡ Enhance Template and Parent Handling in Item Collection Scripts [[839b8f6](https://github.com/newgen-business-solutions/cs-event-services/commit/839b8f63cdc4ae4361bc3630130ad3acdebf23c1)]
- ♻️ Update CS Shipment Bulk Push [[ff540a3](https://github.com/newgen-business-solutions/cs-event-services/commit/ff540a313799513f009681f604df2523392dfa94)]
- ♻️ Update CS Shipment Bulk Push [[95c288e](https://github.com/newgen-business-solutions/cs-event-services/commit/95c288e398f9a41fcc8fbe37032da98e2c873007)]
- ♻️ Update Push Shipment MR [[0570bbf](https://github.com/newgen-business-solutions/cs-event-services/commit/0570bbf1ebbc061d611789dc837dac4bc45cbaa5)]
- ♻️ Update CS Shipment Bulk Push [[b7ceb5f](https://github.com/newgen-business-solutions/cs-event-services/commit/b7ceb5f38d72a1e915f00ded60a299af51e8248c)]
- ♻️ Rename script file for consistency in naming convention [[98ac383](https://github.com/newgen-business-solutions/cs-event-services/commit/98ac383b3c97ed4239eb2ea90f8266c9d8727bf1)]
- ♻️ Cs Shipment and Cs Scan Enhancements ([#197](https://github.com/newgen-business-solutions/cs-event-services/issues/197)) [[6b5ec7b](https://github.com/newgen-business-solutions/cs-event-services/commit/6b5ec7b256a2df16ce1899d3f087547404fd893f)]
- ♻️ Update Bulk Shipment Push [[6417961](https://github.com/newgen-business-solutions/cs-event-services/commit/641796121cee8ab160f6aa9fec8967c3d51817ba)]
- ♻️ Update Event UE [[607a018](https://github.com/newgen-business-solutions/cs-event-services/commit/607a0184565f7b377dcd20687bae8c4a2bafdfe7)]
- ♻️ Refactor order deletion notification and enhance cache management [[afb5cc0](https://github.com/newgen-business-solutions/cs-event-services/commit/afb5cc093e9bfe3278879e5af73203eba55e96a7)]
- 🔧 Update test script in backend package.json [[c7cd916](https://github.com/newgen-business-solutions/cs-event-services/commit/c7cd91643f3d5db72a677a1796364eb2da0f4434)]
- ⚡ Refactor and enhance logging in price hooks and tax code retrieval [[a668489](https://github.com/newgen-business-solutions/cs-event-services/commit/a6684893df4b7d79e9d22a2d95ec5559f3534c42)]
- ♻️ Change Error Banner to Info Banner [[fe1cec9](https://github.com/newgen-business-solutions/cs-event-services/commit/fe1cec9ad80afa0aea78e4fbc955ae147a93cef7)]
- ♻️ Refactor: CS Scan ([#195](https://github.com/newgen-business-solutions/cs-event-services/issues/195)) [[56e70ad](https://github.com/newgen-business-solutions/cs-event-services/commit/56e70ad6ecdddbfc7c28e03a4c780e2aedefb6be)]
- 🔧 Remove rootDir from backend package.json [[1fc18d1](https://github.com/newgen-business-solutions/cs-event-services/commit/1fc18d1a3b1e44318a407f00d770e5b00943fc4f)]
- 🔧 Update IDE iml file [[791cf75](https://github.com/newgen-business-solutions/cs-event-services/commit/791cf75af33cb05594b71b93c1ce82b9c023820d)]
- 🔧 Update @oracle/suitecloud-unit-testing to version 1.6.0 and add new test cases [[6e6a8cd](https://github.com/newgen-business-solutions/cs-event-services/commit/6e6a8cd86b2fb8c30d1fcb638b7f2828c3344d3a)]
- ⚡ Enhance logging for server-side price calculations and event handling [[1f21c24](https://github.com/newgen-business-solutions/cs-event-services/commit/1f21c2482f59b42ff5b39b788727ecc1d3a0b7fd)]
- ⚡ Enhance logging and refactor settings handling in event wizard [[0389694](https://github.com/newgen-business-solutions/cs-event-services/commit/03896941f0990c1d19f50e5fd08f146680658098)]
- 🔧 Remove rootDir from backend package.json [[d8d6ecc](https://github.com/newgen-business-solutions/cs-event-services/commit/d8d6ecc591930f887dd9ae807dbf26b03e88138d)]
- 🔧 Update IDE iml file [[cda104c](https://github.com/newgen-business-solutions/cs-event-services/commit/cda104c4b13adaef2c626cec5e59fb8041862713)]
- 🔧 Update @oracle/suitecloud-unit-testing to version 1.6.0 and add new test cases [[9a84b2b](https://github.com/newgen-business-solutions/cs-event-services/commit/9a84b2bb4503f26531692c28f52e9aa9c54c6d6e)]
- ⚡ Enhance logging for server-side price calculations and event handling [[93355ed](https://github.com/newgen-business-solutions/cs-event-services/commit/93355edf87b6efde0f339ced300e936afc903b39)]
- ⚡ Enhance logging and refactor settings handling in event wizard [[cb2e933](https://github.com/newgen-business-solutions/cs-event-services/commit/cb2e933f9ee0e27a992a96fce7a0230698dfa1cf)]
- ⚡ Add dynamic tax code handling and UI enhancements [[e8b1ff7](https://github.com/newgen-business-solutions/cs-event-services/commit/e8b1ff761575ce73388668228f55ca7d9b159971)]
- ♻️ Refactor Item UE ([#191](https://github.com/newgen-business-solutions/cs-event-services/issues/191)) [[cb8a679](https://github.com/newgen-business-solutions/cs-event-services/commit/cb8a67943df56db4947127db6be3cf22154bf7bc)]
- ♻️ Check for surcharges from backend [[73e524f](https://github.com/newgen-business-solutions/cs-event-services/commit/73e524fe2c9b1bdd2ae5fc210b4d50c53fb67269)]
- ♻️ Update(Scan): Using shared env from middleware  ([#190](https://github.com/newgen-business-solutions/cs-event-services/issues/190)) [[504d942](https://github.com/newgen-business-solutions/cs-event-services/commit/504d942597b3cfa56cd27ad788c17b7a8caf2584)]
- ⚡ Add caching and dynamic fetching to CS Settings RESTlet [[232ba7a](https://github.com/newgen-business-solutions/cs-event-services/commit/232ba7a6fd910c491c7e5463a285ae77759ceb27)]
- ♻️ Refactor event data hook and add purgeCache param handling. [[5cc78d0](https://github.com/newgen-business-solutions/cs-event-services/commit/5cc78d0fa7cf6d4a0064fd1cf7a23edcc8091584)]
- ♻️ Refactor getServerSideProps to conform to Next.js conventions [[c4fe809](https://github.com/newgen-business-solutions/cs-event-services/commit/c4fe809e84850d98205a27ce7d38c6caa3f0613b)]
- ♻️ Refactor code style and enhance session handling [[8796788](https://github.com/newgen-business-solutions/cs-event-services/commit/8796788f55bdf9fc51637f4e9479487c87d234f3)]
- ♻️ Refactor shared env handling and add API-based fetching [[00e45b0](https://github.com/newgen-business-solutions/cs-event-services/commit/00e45b0348d408ce6c353cb0dd15968fb1afd4d3)]
- ⚡ Add &#x60;getSettings&#x60; function to fetch custom record settings [[7d6e5fa](https://github.com/newgen-business-solutions/cs-event-services/commit/7d6e5fa1d17c41e29f1cabe3e27df1a4f79d5771)]
- ♻️ Rename service charge transaction col [[24fb9eb](https://github.com/newgen-business-solutions/cs-event-services/commit/24fb9eb7a9907f4e2dc92dbd1a67719f706b98f9)]
- ⚡ Enhance error handling and logging in middleware and API. [[53e857c](https://github.com/newgen-business-solutions/cs-event-services/commit/53e857ccad40dbfae3b9ed579dd8c9ee856b923c)]
- 🎨 Update navigation, style, and API configurations [[3f6b971](https://github.com/newgen-business-solutions/cs-event-services/commit/3f6b971272e7b8d998be061fb02de55eb5e73dbf)]
- ♻️ Refactor encryption utility to use CommonJS modules [[5f243b3](https://github.com/newgen-business-solutions/cs-event-services/commit/5f243b3df29c873ca40ea152f0b7f0e1901dd144)]
- ⚡ Improve error handling and logging in getSettings function [[0ec990a](https://github.com/newgen-business-solutions/cs-event-services/commit/0ec990a63c0a040dd63abd0683178b31994f12d2)]
- ♻️ Refactor encryption functions to use top-level await. [[af5cf3d](https://github.com/newgen-business-solutions/cs-event-services/commit/af5cf3d07060861f88f4d8f385af077d1c620663)]
- ♻️ Refactor encryption import path in generate-scan-token.js [[d9a28b1](https://github.com/newgen-business-solutions/cs-event-services/commit/d9a28b128abb148d755344ccfd139aba0251135f)]
- ♻️ Refactor API base URL construction in scan token API. [[bee21d4](https://github.com/newgen-business-solutions/cs-event-services/commit/bee21d4888dd798d2ec76ff745ae6d3ce2b752b7)]
- 🔧 Add and manage EXHIBITOR_PORTAL_URL environment variable [[ad6e4dd](https://github.com/newgen-business-solutions/cs-event-services/commit/ad6e4dd46ac0d2dd56cf139752332219f613ce18)]
- 🎨 Update Scan Feedback ([#188](https://github.com/newgen-business-solutions/cs-event-services/issues/188)) [[beb2f1c](https://github.com/newgen-business-solutions/cs-event-services/commit/beb2f1c2fc9c4e93b4b28a062de8ece1f921c5dc)]
- 🏗️ Implement secure token-based environment sharing [[b53ecd9](https://github.com/newgen-business-solutions/cs-event-services/commit/b53ecd9b03d4118c3c54a4c776e90d9e33086d6f)]
- ♻️ Rename service charges to surcharges [[df6220c](https://github.com/newgen-business-solutions/cs-event-services/commit/df6220c6e3076d20d09a8a1ec79323a8a643ea6b)]
- 🎨 Refactor error handling with ProductLoadError component [[f7745d2](https://github.com/newgen-business-solutions/cs-event-services/commit/f7745d2d2353c44b97d3d09290e0376b840305bd)]
- ⚡ Return data after sending JSON response in API handler [[79788ba](https://github.com/newgen-business-solutions/cs-event-services/commit/79788ba52bc0c5de8cebbf9addc8fdfbfe0037ea)]
- ⚡ Refactor item handling functions and add limits. [[d29ec2a](https://github.com/newgen-business-solutions/cs-event-services/commit/d29ec2aed8eb73cfe9c6509eb419742d01726655)]
- ⚡ Refactor tax group retrieval and selection logic [[2d9ab8c](https://github.com/newgen-business-solutions/cs-event-services/commit/2d9ab8cde2f65c10a99cc00ca0dffbf8a3809e4d)]
- 🎨 Add error handling for product loading failures [[414c1b6](https://github.com/newgen-business-solutions/cs-event-services/commit/414c1b6efca2343400b370f8061d0aa8833cc0dd)]
- 🔧 Add ESLint configuration to project setup [[708e6ba](https://github.com/newgen-business-solutions/cs-event-services/commit/708e6ba6dd2257ffbd586043527ef3056532c6b1)]
- ♻️ Refactor price level logic for clarity and efficiency [[8112cef](https://github.com/newgen-business-solutions/cs-event-services/commit/8112cefea20f3ea64f26ea53f22082bb044a38b9)]
- ⚡ Add price retrieval and caching logic in price hooks [[707be14](https://github.com/newgen-business-solutions/cs-event-services/commit/707be14a22e5e8dc710c6b6f75db6ab04f882255)]
- ♻️ Refactor API handlers for consistent JSON response [[e7b9d0b](https://github.com/newgen-business-solutions/cs-event-services/commit/e7b9d0b07103ad103e143f6bca4e421d1a2e1b4d)]
- 🎨 Update (Backend UI): Area Fields Render ([#187](https://github.com/newgen-business-solutions/cs-event-services/issues/187)) [[866f269](https://github.com/newgen-business-solutions/cs-event-services/commit/866f26941d3d74e3b992e89a4dc097ce03e314c2)]
- ⚡ Optimize(Auto Blurb FEAT): Refresh Auto Blurb ([#186](https://github.com/newgen-business-solutions/cs-event-services/issues/186)) [[3f02198](https://github.com/newgen-business-solutions/cs-event-services/commit/3f021983e4586c1484077f8e3701cfec554fc787)]
- 🎨 UPDATE (CS Scan): UI Cleanup ([#184](https://github.com/newgen-business-solutions/cs-event-services/issues/184)) [[c78a4e9](https://github.com/newgen-business-solutions/cs-event-services/commit/c78a4e973620e3d65e3e19e9cf0199c6f2408059)]
- ♻️ Remove committed env [[6c99ab0](https://github.com/newgen-business-solutions/cs-event-services/commit/6c99ab015e6ae71013be79aa7ce258c9d59737a3)]
- ♻️ Adjust Checkout Styles [[15bf56b](https://github.com/newgen-business-solutions/cs-event-services/commit/15bf56b304cf093c46c7088d0b682b1ffc9cd2b8)]
- ♻️ Add Service Charges to TreeView [[8210d50](https://github.com/newgen-business-solutions/cs-event-services/commit/8210d5069c00c25551287992394c2bb167c88591)]
- ♻️ Refactored Calculate Service Charges [[0e81e45](https://github.com/newgen-business-solutions/cs-event-services/commit/0e81e45d7e51f7cdb1b0455bf57380c90748baa9)]
- 🎨 Simplify item name display in table rows [[d13816c](https://github.com/newgen-business-solutions/cs-event-services/commit/d13816c2f7d63694f025c9bc666c8502fbbe8185)]
- ♻️ Refactor item selection logic using Sets [[530abfc](https://github.com/newgen-business-solutions/cs-event-services/commit/530abfc1a72bdb9811c0cff7e23394d0716a4cc1)]
- ♻️ Refactor UI for item removal status indicators [[6b873ed](https://github.com/newgen-business-solutions/cs-event-services/commit/6b873ed6a79aeb5c61f185272d678149c257a88e)]
- ⚡ Add support for Alpine.js in client script [[a6574f5](https://github.com/newgen-business-solutions/cs-event-services/commit/a6574f5d27a4ee78f5a6da2350663f6d94d7aaac)]
- ⚡ Add cart and memoization to RequiredItems component [[13f71cc](https://github.com/newgen-business-solutions/cs-event-services/commit/13f71cc9fd3146e9500a189eb71f500f85ec70b6)]
- ⚡ Update cart UI to handle required items for checkout [[6c9c711](https://github.com/newgen-business-solutions/cs-event-services/commit/6c9c711f6d0d9d35955b3cc0619dc881b6be5baa)]
- 🎨 Enhance required items handling across cart and alerts [[b24a4b9](https://github.com/newgen-business-solutions/cs-event-services/commit/b24a4b999d2212a4320e7268995db516a0b0ef98)]
- ⚡ Increase Cache-Control max-age to 8 minutes [[eff3903](https://github.com/newgen-business-solutions/cs-event-services/commit/eff390342d712cebd351205a71ddea522e4f7dcf)]
- ⚡ Optimize RelatedProducts component with memoization. [[a51fba1](https://github.com/newgen-business-solutions/cs-event-services/commit/a51fba136b13e05d3d01b7e8ccacfdd2a02fc382)]
- 🎨 Enhance related products check [[bbf9ec7](https://github.com/newgen-business-solutions/cs-event-services/commit/bbf9ec771895ba4385e56631ccb2046d9c3475e9)]
- ♻️ Refactor item details retrieval logic [[298b1da](https://github.com/newgen-business-solutions/cs-event-services/commit/298b1da11653687c8d6c73db0bf8f27b40931b1c)]
- 🎨 Add extensive product details and file format support [[ad6bc3c](https://github.com/newgen-business-solutions/cs-event-services/commit/ad6bc3ca28a83d267b3d86b21d88f6d8220bbada)]
- ⚡ Optimize RelatedProducts component with memoization. [[e80b449](https://github.com/newgen-business-solutions/cs-event-services/commit/e80b449c0cfcaa49dfa4a4dfa71edddd2038516d)]
- 🎨 Enhance related products check [[57bc63e](https://github.com/newgen-business-solutions/cs-event-services/commit/57bc63e9eec608bb031ef636eac4f33ee13e917e)]
- ♻️ Refactor item details retrieval logic [[9ea29d2](https://github.com/newgen-business-solutions/cs-event-services/commit/9ea29d27bd515611d700aca2b8219101eeb26ab5)]
- 🎨 Add extensive product details and file format support [[11f6741](https://github.com/newgen-business-solutions/cs-event-services/commit/11f674100cc6b85dba0a674a9adf0b9d3f7a9531)]
- 🔧 Add example .env file to web app [[adca8da](https://github.com/newgen-business-solutions/cs-event-services/commit/adca8da3009c440eb6acd46d8c32f2bd700f328e)]

### Removed

- 🔥 Remove New Relic Analytics Script from App Component [[8946300](https://github.com/newgen-business-solutions/cs-event-services/commit/8946300b7e2c04fdcdc91d39a01af7b51c518eed)]
- 🔥 Remove unused Alpine.js import from item collection script [[0e0fd84](https://github.com/newgen-business-solutions/cs-event-services/commit/0e0fd8464d81f46c9dbab13baf45e669dede65bc)]
- 🔥 Remove unused &#x60;useItemPriceOld&#x60; function for cleanup [[c528a4f](https://github.com/newgen-business-solutions/cs-event-services/commit/c528a4fd76f73fa73f5de3ffc7a08a6f98efcb16)]

### Fixed

- 🐛 Fix: Update venue record handling for CSV import [[4d4d9c5](https://github.com/newgen-business-solutions/cs-event-services/commit/4d4d9c503b4cae53d4561d0e1281019684f9a69a)]
- 🐛 Fix: Safeguard against undefined values in user contact name rendering [[a0ff2fd](https://github.com/newgen-business-solutions/cs-event-services/commit/a0ff2fd4300a9938ca9ad34816b63bb7a9ab6094)]
- 🐛 Fix Booth Order Creation Bug [[bea12d5](https://github.com/newgen-business-solutions/cs-event-services/commit/bea12d507e66d91df1f657927eaee8804d8ce680)]
- 🐛 Remove out of date ref [[6c2f19d](https://github.com/newgen-business-solutions/cs-event-services/commit/6c2f19dbd6d807174db3f3dc8abac23b1860a370)]
- 🐛 Fix Mass Mailer on CSV Event Import [[338ac21](https://github.com/newgen-business-solutions/cs-event-services/commit/338ac21b18f021123bdb45f53219028a97d70135)]
- 🐛 Bug: Sales Order Record Changed Bug ([#202](https://github.com/newgen-business-solutions/cs-event-services/issues/202)) [[46ce248](https://github.com/newgen-business-solutions/cs-event-services/commit/46ce248c50267fd01bfce874c40d73ed429926c8)]
- 🐛 Fix Booth Order Creation Bug [[de00bec](https://github.com/newgen-business-solutions/cs-event-services/commit/de00bec9e41ecf2d66be5fbefcef4f1d2fdec755)]
- 🐛 Remove out of date ref [[04d897f](https://github.com/newgen-business-solutions/cs-event-services/commit/04d897fa8357d4a4d50c6dfcababb1c2a94a12ae)]
- 🐛 Fix Mass Mailer on CSV Event Import [[1267431](https://github.com/newgen-business-solutions/cs-event-services/commit/126743165d4d24cc8b251027a15227622ede4da4)]
- 🚑 Add dynamic item attributes and improve cart validation [[df77b68](https://github.com/newgen-business-solutions/cs-event-services/commit/df77b6839e4920c0a1cd8211c06e5f3b2d4d764c)]
- 💚 Update bundle analysis workflow for error handling and setup [[727431c](https://github.com/newgen-business-solutions/cs-event-services/commit/727431c10cfa93b7b84a3094d12513339a3a181d)]
- 🐛 Bug: CS Shipment Fixes ([#189](https://github.com/newgen-business-solutions/cs-event-services/issues/189)) [[fb260dd](https://github.com/newgen-business-solutions/cs-event-services/commit/fb260ddb3e2a00bf153e07ad0d71193b47bc6183)]
- 🐛 Fix: Add manual sort toggle for item collection updates [[b18bcbd](https://github.com/newgen-business-solutions/cs-event-services/commit/b18bcbd701e0cfb62668fe86368e6539178350eb)]
- 🐛 Fix type error in material handling schedule check [[e63dcea](https://github.com/newgen-business-solutions/cs-event-services/commit/e63dceaf288eaf8dc0638f1baa09b53bd71c1ad8)]
- 🐛 Import uuid [[5e2e391](https://github.com/newgen-business-solutions/cs-event-services/commit/5e2e391703550fdca02dc53bdc3272b6d3dd19d2)]
- 🐛 FIX: (Suitelet) Show summary report header bug ([#181](https://github.com/newgen-business-solutions/cs-event-services/issues/181)) [[f54736d](https://github.com/newgen-business-solutions/cs-event-services/commit/f54736d642cbb054abcca55992346abb2e81641b)]
- 🐛 FIX (UE): Creating Labor Schedule Records ([#180](https://github.com/newgen-business-solutions/cs-event-services/issues/180)) [[46d40c1](https://github.com/newgen-business-solutions/cs-event-services/commit/46d40c1f0cac97bcfddb4fd33bcf2c80249722f6)]
- 🐛 Fix missing return value for empty item list case [[bd035e9](https://github.com/newgen-business-solutions/cs-event-services/commit/bd035e9dc939b9b9e28efc75236c899d54c9d260)]
- 🐛 Fix missing return value for empty item list case [[dc6afbf](https://github.com/newgen-business-solutions/cs-event-services/commit/dc6afbf3ec7c6629c56db078da65e73bebca7780)]
- 🚑 Fix redirect condition from NetSuite on root page [[b837d31](https://github.com/newgen-business-solutions/cs-event-services/commit/b837d31f2c0b1b6d0fe4b33981d986e784243c4a)]
- 🐛 Add(client) quantity set to sublist on Days Calc Items ALS [[60657fb](https://github.com/newgen-business-solutions/cs-event-services/commit/60657fb1e3172b7fcb5cc53340868b58a9227e49)]

### Security

- 🔒 Refactor middleware and encryption for enhanced security. [[f01a1bb](https://github.com/newgen-business-solutions/cs-event-services/commit/f01a1bbd6b8faaa03797b23d001c14af8e6da66a)]

### Miscellaneous

-  Refactor Vercel Analytics Import in CheckoutForm2 [[4783360](https://github.com/newgen-business-solutions/cs-event-services/commit/4783360add0f70393b3dc603a0a6648db5f41bba)]
-  Merge pull request [#198](https://github.com/newgen-business-solutions/cs-event-services/issues/198) from newgen-business-solutions/CESS-639-optimize-nested-item-collections [[8f35cb8](https://github.com/newgen-business-solutions/cs-event-services/commit/8f35cb8d198f6054aa9ec84ab83fecc470d1734f)]
-  Merge branch &#x27;main&#x27; into CESS-639-optimize-nested-item-collections [[a95d2d5](https://github.com/newgen-business-solutions/cs-event-services/commit/a95d2d5d28da29d0f0eae25df6561e25ce5f6885)]
-  Merge branch &#x27;CES-660-Rework-BOL-Delete&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CES-660-Rework-BOL-Delete [[de04bc8](https://github.com/newgen-business-solutions/cs-event-services/commit/de04bc8b7a72db99670e5c0fdc75a6b124bf765f)]
-  Merge remote-tracking branch &#x27;origin/CESS-639-optimize-nested-item-collections&#x27; into CES-660-Rework-BOL-Delete [[ecd8d38](https://github.com/newgen-business-solutions/cs-event-services/commit/ecd8d38322e3747d1829d1932bb3cb0bbfe054c8)]
-  Merge branch &#x27;CESS-639-optimize-nested-item-collections&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-639-optimize-nested-item-collections [[a314d0e](https://github.com/newgen-business-solutions/cs-event-services/commit/a314d0edcb207e05f58b416315444a0d9c62bb49)]
-  Merge pull request [#193](https://github.com/newgen-business-solutions/cs-event-services/issues/193) from newgen-business-solutions/CESS-570-CSV-Import-Breaks-Mass-Mailer [[4afdf42](https://github.com/newgen-business-solutions/cs-event-services/commit/4afdf4267f9745b0145ae15bd381acc3bb3a30ce)]
-  Merge pull request [#201](https://github.com/newgen-business-solutions/cs-event-services/issues/201) from newgen-business-solutions/CESS-662-BOL-UI-Changes [[8a7669f](https://github.com/newgen-business-solutions/cs-event-services/commit/8a7669fb705d242ce85d51d232236fe9127449f5)]
-  Merge pull request [#200](https://github.com/newgen-business-solutions/cs-event-services/issues/200) from newgen-business-solutions/CESS-668-MH-Schedule-Requirements [[ff635c4](https://github.com/newgen-business-solutions/cs-event-services/commit/ff635c40f9330aa75f65f364c8825bebbb8305f5)]
-  Merge pull request [#199](https://github.com/newgen-business-solutions/cs-event-services/issues/199) from newgen-business-solutions/CESS-654-Push-To-SO-Bulk [[9652eba](https://github.com/newgen-business-solutions/cs-event-services/commit/9652ebabe9ed7ab6a9298e2575c08439933a7d76)]
-  Merge remote-tracking branch &#x27;origin/CESS-639-optimize-nested-item-collections&#x27; into CESS-654-Push-To-SO-Bulk [[09bdeaa](https://github.com/newgen-business-solutions/cs-event-services/commit/09bdeaaac01e07ab92dbcd6cb4e080d507948c0e)]
- 🍺 Implement Enhanced User Feedback and Validation in Item Collection Scripts [[57dfdd5](https://github.com/newgen-business-solutions/cs-event-services/commit/57dfdd50ac6358785bfee2a0a2be525025f4ba7d)]
- 🧪 Update Jest Configuration and Add Collection Selection Tests [[34ab541](https://github.com/newgen-business-solutions/cs-event-services/commit/34ab541ad9b6b077de372837c336b5c5f87f8967)]
- 📝 Update Event Web Items Documentation [[42ed90a](https://github.com/newgen-business-solutions/cs-event-services/commit/42ed90a3a96caa7d28b48df7eda2fdec92c11f67)]
-  Merge branch &#x27;main&#x27; into CESS-639-optimize-nested-item-collections [[0c6dbe9](https://github.com/newgen-business-solutions/cs-event-services/commit/0c6dbe91b182b93937a4f8faa5fce4707994f5fa)]
-  Merge remote-tracking branch &#x27;origin/CESS-570-CSV-Import-Breaks-Mass-Mailer&#x27; into CESS-654-Push-To-SO-Bulk [[d8bd586](https://github.com/newgen-business-solutions/cs-event-services/commit/d8bd586bd30125c7434138107bc1d5cd26908340)]
-  Formatting [[6be5430](https://github.com/newgen-business-solutions/cs-event-services/commit/6be5430015c2c623eea4876d844e5edf5edc1298)]
-  Merge branch &#x27;main&#x27; into CESS-654-Push-To-SO-Bulk [[82d7375](https://github.com/newgen-business-solutions/cs-event-services/commit/82d73757f06da7d36fb7c20ada0830d6b639620e)]
-  Merge branch &#x27;CESS-639-optimize-nested-item-collections&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-639-optimize-nested-item-collections [[f0d1f12](https://github.com/newgen-business-solutions/cs-event-services/commit/f0d1f126f391a7a3fdec1d9680a9d0b556649dbf)]
-  Fix display name property in event creation wizard script [[979bd60](https://github.com/newgen-business-solutions/cs-event-services/commit/979bd60760f3608142a9e2ee44a20d6144c512bd)]
-  Merge branch &#x27;main&#x27; into CESS-639-optimize-nested-item-collections [[1aa54c5](https://github.com/newgen-business-solutions/cs-event-services/commit/1aa54c5c44958f5c0b00b243bcc03726e9251e31)]
-  Merge remote-tracking branch &#x27;origin/main&#x27; into CESS-639-optimize-nested-item-collections [[f26ef3f](https://github.com/newgen-business-solutions/cs-event-services/commit/f26ef3fc8828291e95c48a41f2f03857a1b8c9a7)]
- 📝 Fix typo in README.md by updating &quot;changelog&quot; to &quot;Changelog&quot; for consistency. [[a38ed17](https://github.com/newgen-business-solutions/cs-event-services/commit/a38ed17e6d37814ff9278151d6777cec7c0dccae)]
-   Add surcharges functionality to event wizard [[33477cf](https://github.com/newgen-business-solutions/cs-event-services/commit/33477cf8b7fe70808bad05b0f3859a624cee7179)]
-  Merge branch &#x27;CESS-639-optimize-nested-item-collections&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-639-optimize-nested-item-collections [[ddc0b43](https://github.com/newgen-business-solutions/cs-event-services/commit/ddc0b43dd786a8513ca5eacf21c7df35750621a5)]
-  Merge branch &#x27;main&#x27; into CESS-639-optimize-nested-item-collections [[0c0ce75](https://github.com/newgen-business-solutions/cs-event-services/commit/0c0ce759d3c27b80101bdafb9939792f7e9a3b9b)]
-   Add surcharges functionality to event wizard [[264cf62](https://github.com/newgen-business-solutions/cs-event-services/commit/264cf62602d29d0d431f6062064fa0aba1fdbc16)]
-  Merge branch &#x27;CESS-639-optimize-nested-item-collections&#x27; [[90c1d41](https://github.com/newgen-business-solutions/cs-event-services/commit/90c1d4160454646ae312f82f4d809ed3405a26e9)]
-  Merge pull request [#185](https://github.com/newgen-business-solutions/cs-event-services/issues/185) from newgen-business-solutions/CESS-546-Expo-CCI-Change-Order [[fabdf50](https://github.com/newgen-business-solutions/cs-event-services/commit/fabdf50b3e412bb1bbc12c75f84cf840df2e8faa)]
-  Merge branch &#x27;CESS-546-Expo-CCI-Change-Order&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-546-Expo-CCI-Change-Order [[460b224](https://github.com/newgen-business-solutions/cs-event-services/commit/460b22451d1d1c9e10a30b440a88ad5256a4f100)]
-  Update nextjs_bundle_analysis.yml [[69620c6](https://github.com/newgen-business-solutions/cs-event-services/commit/69620c6a2c2c2c9982a17c3386198c018cb46a42)]
- 📦 Update dependencies: Upgrade @next, @emnapi, sharp packages [[0a571e7](https://github.com/newgen-business-solutions/cs-event-services/commit/0a571e7fe3a6fb5b11a324ade690dfe34539f61f)]
-  Merge branch &#x27;CESS-546-Expo-CCI-Change-Order&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-546-Expo-CCI-Change-Order [[7ea1c24](https://github.com/newgen-business-solutions/cs-event-services/commit/7ea1c240508d09ad8b03cb4052c4e23394ae83dc)]
-  Merge branch &#x27;CESS-546-Expo-CCI-Change-Order&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-546-Expo-CCI-Change-Order [[90a07ae](https://github.com/newgen-business-solutions/cs-event-services/commit/90a07ae7df2fc26417ac26a029348781040a534a)]
-  Merge branch &#x27;main&#x27; into CESS-546-Expo-CCI-Change-Order [[3fc3128](https://github.com/newgen-business-solutions/cs-event-services/commit/3fc31283f246fd31ecd26e7d3a3762d988fc10cf)]
- 📦 Update Yarn install state file [[5b7b695](https://github.com/newgen-business-solutions/cs-event-services/commit/5b7b69517329da085639f13333926c349695f0c8)]
-  Merge pull request [#175](https://github.com/newgen-business-solutions/cs-event-services/issues/175) from newgen-business-solutions/CESS-362-Gratuity-Schedules [[218030a](https://github.com/newgen-business-solutions/cs-event-services/commit/218030a325fac4927e7dce3bd9feacd50f98f688)]
-  Merge branch &#x27;CESS-546-Expo-CCI-Change-Order&#x27; into CESS-362-Gratuity-Schedules [[3207b6e](https://github.com/newgen-business-solutions/cs-event-services/commit/3207b6e364bef1dfd4bc1f93f1adc7632d27ad6d)]
-  Merge branch &#x27;main&#x27; into CESS-546-Expo-CCI-Change-Order [[2247f5d](https://github.com/newgen-business-solutions/cs-event-services/commit/2247f5d18c05ed2f6b821b0b8b586094e8917268)]
- 📦 Add @iconify/react and @iconify/types dependencies [[f8e15aa](https://github.com/newgen-business-solutions/cs-event-services/commit/f8e15aa7b9f25388bf3601e3f29d688470a6b918)]
-  Merge branch &#x27;main&#x27; into CESS-546-Expo-CCI-Change-Order [[02174de](https://github.com/newgen-business-solutions/cs-event-services/commit/02174de4ec3a26b724a36a58e243507d1e6abb53)]
- 📝 Update CHANGELOG with new features and improvements [[59ce162](https://github.com/newgen-business-solutions/cs-event-services/commit/59ce162d450d5b44d2635485e0f83bb085f2e182)]


<a name="11.0.5"></a>
## 11.0.5 (2024-11-18)

### Added

- ✨ Implement manual item sorting and handle item change notifications [[aa79c13](https://github.com/newgen-business-solutions/cs-event-services/commit/aa79c1310e97cb7ac89a18d336049f1040333d3c)]
- 🔊 Update child collections creation to include detailed logging [[e9aecec](https://github.com/newgen-business-solutions/cs-event-services/commit/e9aecec69ce51c37c8ec4787cdce9455328ae73d)]
- ✨ Feat: Show Management Work Order Event Area Detail ([#176](https://github.com/newgen-business-solutions/cs-event-services/issues/176)) [[4e3e7c5](https://github.com/newgen-business-solutions/cs-event-services/commit/4e3e7c5ec0e6e21e0f8bf497bfc1482d473ea5e7)]
- ➕ Add new Client Script for item collection handling [[bf3989f](https://github.com/newgen-business-solutions/cs-event-services/commit/bf3989f4cceb9263375652a0671da0ed561d9334)]
- ✨ Function Automation [#174](https://github.com/newgen-business-solutions/cs-event-services/issues/174) from newgen-business-solutions/CESS-461-Function-Automation [[34bdb79](https://github.com/newgen-business-solutions/cs-event-services/commit/34bdb79e64b4920e475acfbc4c429c2b74f2e793)]
- ✨ FEAT: Add nested collections &amp; scan app [#172](https://github.com/newgen-business-solutions/cs-event-services/issues/172) [[73fb0bd](https://github.com/newgen-business-solutions/cs-event-services/commit/73fb0bdca1036bf8917091e8c077daa480f0a235)]
- ✨ Function Automation [[82f44fd](https://github.com/newgen-business-solutions/cs-event-services/commit/82f44fd60e607ca00b115d22de2599548a5c6c0f)]
- 🎉 Add new loaders and update existing loader imports [[2aa7588](https://github.com/newgen-business-solutions/cs-event-services/commit/2aa758820830829d9295f117bf3ee33ac1cd8448)]
- ➕ Add parent filter and subcategory search [[e1bdd90](https://github.com/newgen-business-solutions/cs-event-services/commit/e1bdd90cc15e93405cf18199ef77a2a1148190a0)]
- ✨ Update products page with loading animations and enhance product filters [[4281e5d](https://github.com/newgen-business-solutions/cs-event-services/commit/4281e5d2fc227cf4e184d6d7a396742b74ad57e4)]
- ✨ Add Suitelet and Client Scripts for Event Services [[b52c2eb](https://github.com/newgen-business-solutions/cs-event-services/commit/b52c2eb4294b332aa936fe10681b0bb0b2ab255b)]
- ✨ Add Additional Surcharge Types ([#163](https://github.com/newgen-business-solutions/cs-event-services/issues/163)) [[42f5a31](https://github.com/newgen-business-solutions/cs-event-services/commit/42f5a318ba7c82948046850e9ab324acacfcd8ac)]
- ✨ Change print all to radio for work order add date sl [[cf9f843](https://github.com/newgen-business-solutions/cs-event-services/commit/cf9f8438d68c7432c8a1d46488e78730eae02197)]
- ➕ Add client script for additional info link validation [[5eddd93](https://github.com/newgen-business-solutions/cs-event-services/commit/5eddd93fcf573b2831308d52ab30e2b0cb8db2ca)]
- ➕ Add new middleware file and updates for formatting [[2ddcb47](https://github.com/newgen-business-solutions/cs-event-services/commit/2ddcb47d397144ff60e6b60123a9118cf595608d)]
- ✨ Add CS Scan Merge pull request [#171](https://github.com/newgen-business-solutions/cs-event-services/issues/171) from newgen-business-solutions/CESS-457-CS-Scan [[bc1e8ef](https://github.com/newgen-business-solutions/cs-event-services/commit/bc1e8efd7e602bb6ba483d9c6a2524ce96aab524)]
- 👷‍♂️ Rename Scan PWA and add GitHub Actions workflow [[e684922](https://github.com/newgen-business-solutions/cs-event-services/commit/e6849221c4b9db15f15da54318bc45c291b52d1e)]
- 🔊 Add logging to confirm existence of build output directory [[a0d2232](https://github.com/newgen-business-solutions/cs-event-services/commit/a0d22327aaeb8209d424ec542b60d7bf8145b7a9)]
- 🔊 Add directory listing to bundle analysis step [[a18be76](https://github.com/newgen-business-solutions/cs-event-services/commit/a18be767ff1acdbdf115109416e2a203ff72b21a)]
- 👷‍♂️ Update next-build workflow configuration [[31db9db](https://github.com/newgen-business-solutions/cs-event-services/commit/31db9db2867d0133ef351e567b18e5b34a74cfd5)]
- 🎉 Add &#x27;retain last show&#x27; functionality using cookies [[5a0f07c](https://github.com/newgen-business-solutions/cs-event-services/commit/5a0f07c15268fd76bb28bf79c70625c4e50cbf48)]
- ✨ Add cookies management and retain last show feature [[24470f6](https://github.com/newgen-business-solutions/cs-event-services/commit/24470f6c800b60f71baa5fcfb67f51d49612f12b)]
- ✨ Modify Exhibitor Work Order button [[0b71c9f](https://github.com/newgen-business-solutions/cs-event-services/commit/0b71c9f733cdccce37b0d46fbc8fa1fe170ba388)]
- ✨ Create Booth Order Button [[88e1289](https://github.com/newgen-business-solutions/cs-event-services/commit/88e128907066e88beae248a935007f4846f60060)]
- ➕ Add Merge pull request [#170](https://github.com/newgen-business-solutions/cs-event-services/issues/170) from CESS-457-CS-Scan [[a51587e](https://github.com/newgen-business-solutions/cs-event-services/commit/a51587e405641f1400bba8c05d6b1393ce83c630)]
- ✨ Event Order Confirmation [[82050f1](https://github.com/newgen-business-solutions/cs-event-services/commit/82050f1f03f2d14cabe81687e7cd2a46f935552c)]
- ✨ Daily Function Schedule Report [[32a2d44](https://github.com/newgen-business-solutions/cs-event-services/commit/32a2d44168863d2ec33026ec1bb0eb151807daeb)]
- ✨ Booking acknowledgment suitelet [[deafb0e](https://github.com/newgen-business-solutions/cs-event-services/commit/deafb0e4759b4ba78c5336da92f281f288f0c268)]
- ➕ Add invoice &amp; SO retrieval and payment link functionality [[5e84b5d](https://github.com/newgen-business-solutions/cs-event-services/commit/5e84b5d7616539c4bf3257f6feecb892a57ac6d0)]
- ➕ Add new &#x60;booking-enterprise&#x60; app [[3ae168b](https://github.com/newgen-business-solutions/cs-event-services/commit/3ae168bd39ffc646d85f07e8fe1b9af2a1ccfa53)]
- ➕ Optimize scan route [[5f0f667](https://github.com/newgen-business-solutions/cs-event-services/commit/5f0f6671e318a678dc1c1e4ec14403a9635078fc)]
- ➕ Add(Scan) Next config [[bfce6a5](https://github.com/newgen-business-solutions/cs-event-services/commit/bfce6a599f17b6390b827e6f4a02fa6e319946b3)]
- ➕ Add(Scan) multi zone add scan ([#168](https://github.com/newgen-business-solutions/cs-event-services/issues/168)) [[ac4a0e0](https://github.com/newgen-business-solutions/cs-event-services/commit/ac4a0e0edf149573bce1f424ebcf8e116d90023a)]
- ➕ Add(Scan) multi zone add scan [[1dbbc9e](https://github.com/newgen-business-solutions/cs-event-services/commit/1dbbc9e2258874e86f6629f0c99b5a9f853e95f4)]
- ✨ Event Dates waterfall [[e8bfc45](https://github.com/newgen-business-solutions/cs-event-services/commit/e8bfc4506c9dc27687992658d403787129bca36a)]
- ✨ Venue Ops Date Checks [[8feb1d1](https://github.com/newgen-business-solutions/cs-event-services/commit/8feb1d184e99bf73dd8b7a7248b6ce011312c9d0)]
- ✨ Add BEO Print fields [[dd0913f](https://github.com/newgen-business-solutions/cs-event-services/commit/dd0913f4b4fd1c68b3c13e465d50e2213945c29b)]
- ✨ Add space read only ue script [[9d0b17f](https://github.com/newgen-business-solutions/cs-event-services/commit/9d0b17fc832e7eec019b703bf459d2c430685442)]
- ✨ Update new event wizard [[850ca0d](https://github.com/newgen-business-solutions/cs-event-services/commit/850ca0df94fb49d96c6d3af8ca3ffe4e119d0453)]
- ✨ Update estimate ue to add fields for print [[a33d3c6](https://github.com/newgen-business-solutions/cs-event-services/commit/a33d3c6349de9a27b385b4115de184f9a9c92d2d)]
- ✨ Added auto updates for space read only field [[39e6422](https://github.com/newgen-business-solutions/cs-event-services/commit/39e64227d387e047d557d6c44e2e6f8a5fc2f7fe)]
- ✨ FEAT: Add Scan PWA pull request [#165](https://github.com/newgen-business-solutions/cs-event-services/issues/165) from newgen-business-solutions/CESS-457-CS-Scan [[2d51e52](https://github.com/newgen-business-solutions/cs-event-services/commit/2d51e52e220fe32900dd905df78f0f139d592da5)]
- ✨ Created Daily Function Schedule Report Suitelet [[21a50fd](https://github.com/newgen-business-solutions/cs-event-services/commit/21a50fd7bee3937d9d5ee505cf580eaf7340df86)]
- ✨ Auto select link to booking [[4db7f69](https://github.com/newgen-business-solutions/cs-event-services/commit/4db7f699f3d22798e49da4eb425f74ad1633ae00)]
- ✨ Auto fill for bookings [[944cd0d](https://github.com/newgen-business-solutions/cs-event-services/commit/944cd0dae9720502e3868bcde4d0701b395e1f9f)]
- ✨ Added scripting for cs event function [[6f7fce8](https://github.com/newgen-business-solutions/cs-event-services/commit/6f7fce814ba23693967e38d6d21bbf405d584494)]
- ✨ Added scripting for cs event booking [[24fef51](https://github.com/newgen-business-solutions/cs-event-services/commit/24fef5154f251b4c1d79228045ab87361db54f9b)]
- ✨ Added scripting for cs space booking rate name [[992a2dd](https://github.com/newgen-business-solutions/cs-event-services/commit/992a2ddb15c27cf2155502e50c5216bc915ecc3d)]
- ✨ Added Suitelet for generating an estimate [[5b2cfd3](https://github.com/newgen-business-solutions/cs-event-services/commit/5b2cfd3cc13f521928266aa746aada8a006af3de)]
- ✨ Add control of scripted CS Estimate Form Field [[4bab598](https://github.com/newgen-business-solutions/cs-event-services/commit/4bab59849c320209f48a39d2e41f3faa62a0448d)]
- ✨ Add control of scripted CS Event Form Field [[3442e1f](https://github.com/newgen-business-solutions/cs-event-services/commit/3442e1f0fab13fc5407ac2b901824b765bf5bb21)]
- ✨ Update bookings status off of event status [[c088da6](https://github.com/newgen-business-solutions/cs-event-services/commit/c088da6942e3dfc83365e96a844b288dcc96e01a)]
- 🔊 Log user access and password reset status [[c665d68](https://github.com/newgen-business-solutions/cs-event-services/commit/c665d688639b8abcdd4560a360cba45ab41d2977)]
- ➕ Add initial configuration and components for Scan PWA [[4fb2cf4](https://github.com/newgen-business-solutions/cs-event-services/commit/4fb2cf4e168b68507ad79ad5f1425c235e43ffb3)]

### Changed

- ⚡ Simplify CustomTreeItem closing tag [[0e9a156](https://github.com/newgen-business-solutions/cs-event-services/commit/0e9a15672ec1f7379f4c7863fc5f3838f3875547)]
- ♻️ Refactor Web Blurb UE [[cd35bfa](https://github.com/newgen-business-solutions/cs-event-services/commit/cd35bfaa85159ea7bfa1a02365514b9265d44f09)]
- ⚡ Refactor ProductFilterCard with MUI TreeView component [[a61f497](https://github.com/newgen-business-solutions/cs-event-services/commit/a61f497dae7533a771c70c14cf2cd5678ca77846)]
- ♻️ Refactor Web Blurb UE [[119aee8](https://github.com/newgen-business-solutions/cs-event-services/commit/119aee81dd62f52c896a4269298f87c43bd74516)]
- ♻️ code cleanup [[477fce2](https://github.com/newgen-business-solutions/cs-event-services/commit/477fce2d495de1fa85b9b898ea7ccd944b006b20)]
- ♻️ Refactor event handling logic for User Event script [[765a1e8](https://github.com/newgen-business-solutions/cs-event-services/commit/765a1e8d34763e4d8938458cf7103b427a9195f2)]
- ⚡ Enhance updateFieldDisplayType function with displayType parameter [[ac7e719](https://github.com/newgen-business-solutions/cs-event-services/commit/ac7e719e690211857582dbee52186a93b9bd742d)]
- ♻️ Auto Web Blurb Optimizations [[5137bf1](https://github.com/newgen-business-solutions/cs-event-services/commit/5137bf15d3c107dd944f8c68e0524cc1c1fb6f03)]
- 🔧 Add objects for new booths generation [[a96e77d](https://github.com/newgen-business-solutions/cs-event-services/commit/a96e77d6be9c27960392cf063fda097f71935dac)]
- ♿ Add aria-labels for accessibility and use theme in Topbar [[cf70159](https://github.com/newgen-business-solutions/cs-event-services/commit/cf701597c6662fc26c9fcaa3ff183ab3a37a1c64)]
- ♻️ remove comma from query [[29064f3](https://github.com/newgen-business-solutions/cs-event-services/commit/29064f34b8a16947549cb1d48a0cbdf5b9e5bb9c)]
- ♻️ Fix Generate Booths Suitelet [[6f06ed3](https://github.com/newgen-business-solutions/cs-event-services/commit/6f06ed311eb68cd86b028628620cf2dfeffce5ca)]
- ♻️ Refactor Item UE [[43a3d74](https://github.com/newgen-business-solutions/cs-event-services/commit/43a3d746cb27c2027a1d1cf89e074ac7a4653c8f)]
- ♻️ Refactor Web Blurb UE [[d00cbc4](https://github.com/newgen-business-solutions/cs-event-services/commit/d00cbc44ebb98cf074c9815f4a4c5fc12428180b)]
- ♻️ Update Event Blurbs [[fba3b33](https://github.com/newgen-business-solutions/cs-event-services/commit/fba3b330c65bbe1067ca25182d964eab7d27ce09)]
- ♻️ Update Item UE [[4ddc536](https://github.com/newgen-business-solutions/cs-event-services/commit/4ddc536d9f43c6d1582f5edb67456ba91ead5e2d)]
- ♻️ Refactor Item UE [[2c5ca02](https://github.com/newgen-business-solutions/cs-event-services/commit/2c5ca029db9865a4fad0132f7ff7cb03278d4155)]
- ⚡ Add debug mode flag and implement item sorting on create view [[6f27e91](https://github.com/newgen-business-solutions/cs-event-services/commit/6f27e91ad2a678f33fd0648513d8de5354e4ce40)]
- 🎨 Add manual sorting for item collections with UI enhancements [[4322669](https://github.com/newgen-business-solutions/cs-event-services/commit/4322669eb4a762ca4b00d9fcd6d38d05b6fe3671)]
- ⚡ Enhance event wizard to handle child item collections [[8f92263](https://github.com/newgen-business-solutions/cs-event-services/commit/8f922636cc99cfd74011f3fe100385620bc4a489)]
- ⚡ Enhance login and middleware redirects [[a80aecc](https://github.com/newgen-business-solutions/cs-event-services/commit/a80aecc9dda377af31a442ecad71cd9a2d0046da)]
- ⚡ Optimize callback URL in Login form submission [[3d2c588](https://github.com/newgen-business-solutions/cs-event-services/commit/3d2c588038f69d7aa34f84e6c58d433559f808c7)]
- ⚡ Add registration and password reset pages for standalone auth [[2bad2df](https://github.com/newgen-business-solutions/cs-event-services/commit/2bad2dfe5e717e412343a476fcc3111fc64499dc)]
- ⚡ Update auth URL paths and enhance background flexibility [[a463282](https://github.com/newgen-business-solutions/cs-event-services/commit/a463282bb7a08624c15cbb5ad5062f6869173629)]
- ⚡ Handle undefined categoryName in zNavigationOptions [[c0e2aab](https://github.com/newgen-business-solutions/cs-event-services/commit/c0e2aabe7cec5d7450b2b3d8b74dceec9c7f2632)]
- ♻️ Refactor settings handling in Show component [[bfb6e36](https://github.com/newgen-business-solutions/cs-event-services/commit/bfb6e36882fd22b507800aa4560dfd8948d4bf66)]
- ♻️ Refactor settings usage for better error handling [[dbcfc5b](https://github.com/newgen-business-solutions/cs-event-services/commit/dbcfc5b8a37ee14c277b69b6f2d0e360fef25c64)]
- 🔧 Set up server side fetch for settings [[eb4afd6](https://github.com/newgen-business-solutions/cs-event-services/commit/eb4afd6f76ef10c0a840df4ced48e5d2a1643bb3)]
- ⚡ Update subCategories check and bump version to 4.0.0 [[20622ff](https://github.com/newgen-business-solutions/cs-event-services/commit/20622ff3e79be81de7cdce78b9169b762778e392)]
- ♻️ Refactor payment order script to optimize sublist handling [[ffba3d4](https://github.com/newgen-business-solutions/cs-event-services/commit/ffba3d45357297395fdf6bfff849031fe0cc266d)]
- ♻️ Refactor animation components to use &#x60;useSafeAnimate&#x60; [[11e2fed](https://github.com/newgen-business-solutions/cs-event-services/commit/11e2fed464f66ac8f661d0a42236144eb71bd923)]
- ♻️ Refactor URL opening and add debug logs [[f755d26](https://github.com/newgen-business-solutions/cs-event-services/commit/f755d26e7bbb37e2c70ef641c3ead1f4a61e75fc)]
- ♻️ Refactor and optimize category and collection handling [[45204f8](https://github.com/newgen-business-solutions/cs-event-services/commit/45204f81432b3eb4b2bccddd84398766d4b73a22)]
- ♻️ Refactor ordering wizard scripts for improved action handling [[c835dc9](https://github.com/newgen-business-solutions/cs-event-services/commit/c835dc91a250022137f7da8224c570b092cb813c)]
- 🔧 Import new CS Event Booking UE [[f884e67](https://github.com/newgen-business-solutions/cs-event-services/commit/f884e67480ed49a0119515823a8e9dc075e53c62)]
- ♻️ Refactor customer field logic in ordering wizard [[1234b90](https://github.com/newgen-business-solutions/cs-event-services/commit/1234b908fd27782bc158f1d69e438480ed4c40f5)]
- 🎨 Fix backdrop visibility condition in CheckoutForm2 [[8d6caea](https://github.com/newgen-business-solutions/cs-event-services/commit/8d6caeac982e5f5e0ec45b4543bf9a737fbe5a96)]
- ♻️ Refactor code for readability and maintainability [[b73ee75](https://github.com/newgen-business-solutions/cs-event-services/commit/b73ee75a058bf4c0c58a21a44edfc2bb4b5bcd00)]
- ⚡ Add Alpine.js for event and booth selection enhancements [[05e4bde](https://github.com/newgen-business-solutions/cs-event-services/commit/05e4bdeca8a66f351b1df2afa43c44c27acecaac)]
- 🔧 Add new features and objects to manifest.xml [[d739d34](https://github.com/newgen-business-solutions/cs-event-services/commit/d739d34ef94a9bbf6d44356373d4b0718013610d)]
- ♻️ Refactor date handling with moment.js library [[22fb06e](https://github.com/newgen-business-solutions/cs-event-services/commit/22fb06ea9c75b26d6dec7b50c3681073ecc7f3db)]
- 🎨 Enable CSS color scheme management [[b9359bf](https://github.com/newgen-business-solutions/cs-event-services/commit/b9359bffc443aae4899ebba5adede5ff7be1591a)]
- ⚡ Refactor and optimize theme and component code [[dfd44e7](https://github.com/newgen-business-solutions/cs-event-services/commit/dfd44e7cb3b9aade4b77cf0f6f0580be253161bd)]
- 🔧 Add scan-pwa dev run configuration [[32fe848](https://github.com/newgen-business-solutions/cs-event-services/commit/32fe848893ef625a75925693cf32b782baa5b601)]
- 🔧 Update .gitignore and env configuration [[3074946](https://github.com/newgen-business-solutions/cs-event-services/commit/3074946f547522100b4d3e7c0d05e5370fe0b291)]
- 🔧 Add Jiti for dynamic imports and update env config [[647fa4b](https://github.com/newgen-business-solutions/cs-event-services/commit/647fa4b21f9983ba1afe36331a65aaa4a26bcf52)]
- 🔧 Add required_error messages to OAuth env vars [[eba9576](https://github.com/newgen-business-solutions/cs-event-services/commit/eba95767745303238e6b510c75b0f2936673ebe2)]
- 🔧 Add new env configuration for client and server [[82dfeb6](https://github.com/newgen-business-solutions/cs-event-services/commit/82dfeb66118759a458a8c4340965cb600b22ed6e)]
- 🔧 Update .gitignore for more specific environment files [[3be6f85](https://github.com/newgen-business-solutions/cs-event-services/commit/3be6f852b0ad6cd82ad36a87d3025814eb6fc100)]
- 🔧 Update .gitignore to cover more environment and build files [[94d618a](https://github.com/newgen-business-solutions/cs-event-services/commit/94d618af0339931606bc474ee44cf76d030314e2)]
- 🎨 Refactor theme component overrides for clarity [[866c2e9](https://github.com/newgen-business-solutions/cs-event-services/commit/866c2e9e971671225cf95065e2063d370b6202c0)]
- ♻️ Add color scheme support and refactor component styles [[f1ed19d](https://github.com/newgen-business-solutions/cs-event-services/commit/f1ed19ddd7e6e61fd6b08e8626acb689ac97e91e)]
- 🔧 Simplify environment variables in next.config.mjs [[1a5b4a7](https://github.com/newgen-business-solutions/cs-event-services/commit/1a5b4a769fdd476577c5fb5aab23e44da311f8a8)]
- 🔧 Expand environment variables in next.config.mjs [[b08df0f](https://github.com/newgen-business-solutions/cs-event-services/commit/b08df0fa95538a4ba35c9e984be9b251e4cc8890)]
- 🔧 Chore Refactor environment variables in next.config.mjs [[d66913a](https://github.com/newgen-business-solutions/cs-event-services/commit/d66913a617840e623c194fdedab0542608d29ea5)]
- ♻️ Refactor theme components and import statements [[1500f9b](https://github.com/newgen-business-solutions/cs-event-services/commit/1500f9b67faa8c7f957e3d24be02893bc8c4f4e6)]
- ⚡ Change file formatting [[d2859f2](https://github.com/newgen-business-solutions/cs-event-services/commit/d2859f2ce9fdc4c2b99488aa70882f3bceb2fdc7)]
- 🔧 Chore Add environment variables to next.config.mjs for web and scan-pwa [[bca9afb](https://github.com/newgen-business-solutions/cs-event-services/commit/bca9afb929af8b9a264cbab7d2acce5f07dc86af)]
- 🔧 Chore Add git-conventional-commits.yaml for commit conventions [[64f93d0](https://github.com/newgen-business-solutions/cs-event-services/commit/64f93d02ed0af3a7fca2727b500cdbfd1d6ccea2)]
- 🔧 Chore Standardize Code Formatting and Remove Unused Imports [[6b2b57e](https://github.com/newgen-business-solutions/cs-event-services/commit/6b2b57eaaf985d886c42ca3ecc08032a86f00d4b)]
- ♻️ Update auth import paths in API routes [[2008fc0](https://github.com/newgen-business-solutions/cs-event-services/commit/2008fc035870ed994926ba14472ded6b307e3a16)]
- 🔧 Add new environment variables to env.mjs files [[33912cc](https://github.com/newgen-business-solutions/cs-event-services/commit/33912ccbb62214f66d71fd1b81ee39673f8f421e)]
- 🔧 Chore Update dependencies for @t3-oss/env-core and @t3-oss/env-nextjs [[e238b01](https://github.com/newgen-business-solutions/cs-event-services/commit/e238b01cdf563fc4d4d56b003b5d2ea530fd29f9)]
- ♻️ Refactor Next.js config for improved environment handling [[06b6455](https://github.com/newgen-business-solutions/cs-event-services/commit/06b64551a10b4f022f8d2d3900658571136e4b97)]
- ⚡ Chore(vercel) Update install command in vercel.json [[18f203c](https://github.com/newgen-business-solutions/cs-event-services/commit/18f203c54c9f283fd6f5600ec231fb34b5a6627f)]
- ⚡ Fix type annotation for id.split result [[96187ab](https://github.com/newgen-business-solutions/cs-event-services/commit/96187ab8a67ad3876e62be59f55b188066f0964c)]
- 🎨 Add URL resolution for login background splash [[cb17e90](https://github.com/newgen-business-solutions/cs-event-services/commit/cb17e9096b55a9bcb39907a25dc0fbfdd78766a2)]
- 🔧 Enable JSX preservation in TypeScript compiler options [[24acd9c](https://github.com/newgen-business-solutions/cs-event-services/commit/24acd9ca4896188a9282c8a7c0bf33d4efe6ac0d)]
- ♻️ Refactor authentication and improve settings management [[0c7744e](https://github.com/newgen-business-solutions/cs-event-services/commit/0c7744e598663a0b1f313e7b3e5a2d8e54c6495e)]
- ♻️ Refactor PWA structure and update dependencies [[c8e0b34](https://github.com/newgen-business-solutions/cs-event-services/commit/c8e0b34c89bd5827517dfc50e7decd9bf517cba2)]
- 🔧 Add new authentication package with NetSuite OAuth2 integration [[ec4745c](https://github.com/newgen-business-solutions/cs-event-services/commit/ec4745cf77a5ef0fdce74019dc42292171a70c14)]
- 🔧 Add project-specific code styles and dependency validation [[3133e6b](https://github.com/newgen-business-solutions/cs-event-services/commit/3133e6b3363813bc9728bd9555bc449903d8ae52)]
- ⚡ Optimize GitHub Actions checkout step &#x60;bundle_analysis&#x60; [[29ea665](https://github.com/newgen-business-solutions/cs-event-services/commit/29ea6659f850fdfce4af36bbd460eff2ab4e92ab)]
- ♻️ Refactor work order suitelet client script [[eb75d2a](https://github.com/newgen-business-solutions/cs-event-services/commit/eb75d2a1c0bcedcedb10843529f754e6fdf1d819)]
- ♻️ Refactor Initialize work order print suitelet client script [[d80e29a](https://github.com/newgen-business-solutions/cs-event-services/commit/d80e29a3859b1e2e11d39a72569644fa092be10f)]
- ⚡ Optimize(feat) Invoice Auto Charge ([#161](https://github.com/newgen-business-solutions/cs-event-services/issues/161)) [[3a072af](https://github.com/newgen-business-solutions/cs-event-services/commit/3a072af72c811a12b01972b18a83179f87b4cb9e)]
- ♻️ Update GitHub Actions workflow for Web Next.js build. [[707de4a](https://github.com/newgen-business-solutions/cs-event-services/commit/707de4a99dbb05c7d7756dbbbbfb8d1e8ac92250)]
- ♻️ Move venue ops enums to enum cross ref [[e11a405](https://github.com/newgen-business-solutions/cs-event-services/commit/e11a405731db71158bab899836f074ee63a4e5d6)]
- ⬆️ Update actions in Next.js bundle analysis workflow [[8435a90](https://github.com/newgen-business-solutions/cs-event-services/commit/8435a901368d40d3fd96db557edbfc7a60d80f84)]
- ♻️ Refactor suitelets client script to custom module. [[3a5b6a7](https://github.com/newgen-business-solutions/cs-event-services/commit/3a5b6a74bbd1360f8aaa90f8671b546913037eba)]
- 🔧 Yarn Install [[a08d176](https://github.com/newgen-business-solutions/cs-event-services/commit/a08d176dc8f27d7bdf123d2d33ec430b1502cd6e)]
- ♻️ Refactor event handling and extend event store functionality [[169fe55](https://github.com/newgen-business-solutions/cs-event-services/commit/169fe558aa89989937e69fec83311bdd9113ae9f)]
- ♻️ Refactor cookie expiration handling [[72f4804](https://github.com/newgen-business-solutions/cs-event-services/commit/72f480417f9532dc3b13111ae9e6016214b2ba45)]
- ⚡ Add EventGuard to various page layouts and update cookie expirations [[29fb004](https://github.com/newgen-business-solutions/cs-event-services/commit/29fb004cc74012ca878842641bcd6094e17b36c5)]
- ♻️ Fix type errors [[33c58dd](https://github.com/newgen-business-solutions/cs-event-services/commit/33c58ddf65cd6f907b190e75f76913324b547080)]
- ♻️ Refactor useRouter usage [[0d46047](https://github.com/newgen-business-solutions/cs-event-services/commit/0d460473ea7695d025f171ac692788cf46039aa8)]
- ⚡ Add scan router hook for scan PWA [[c421455](https://github.com/newgen-business-solutions/cs-event-services/commit/c4214555fb9fd5cd9e1f43c2121afdbcc01e774f)]
- ♻️ Refactor api get req to json [[0fb56ab](https://github.com/newgen-business-solutions/cs-event-services/commit/0fb56abf3c86113b481bbecb97c82b21949c8b24)]
- ♻️ Refactor import statements and format icons in bottom-nav.tsx [[9db2314](https://github.com/newgen-business-solutions/cs-event-services/commit/9db23147304ce08b35bc9fbc960995f3f32b3152)]
- ♻️ Refactor api post requests [[a197065](https://github.com/newgen-business-solutions/cs-event-services/commit/a1970659f131bea9f39be6687eb4b86a6bd81103)]
- ♻️ Refactor getServerSideProps [[6a26b31](https://github.com/newgen-business-solutions/cs-event-services/commit/6a26b312224868fd40f992dcf69ef0f340d314a0)]
- ♻️ Refactor api request [[c9e2135](https://github.com/newgen-business-solutions/cs-event-services/commit/c9e2135a81f4889d1d39cdf633a85990e8b9f4bc)]
- ♻️ Update formatting [[0f68fe2](https://github.com/newgen-business-solutions/cs-event-services/commit/0f68fe2a0f9542f10363e205396cd411743c7c9a)]
- ⚡ Update yarn install command in GitHub workflow [[8b21332](https://github.com/newgen-business-solutions/cs-event-services/commit/8b21332a0c4ab52016adf62df6ffa33c5e41e573)]
- ♻️ Update Next.js build workflow [[0877a0f](https://github.com/newgen-business-solutions/cs-event-services/commit/0877a0ffaa86941292d612c4085dcc80b637efe2)]
- 🔧 Update Package Json name [[b78dd60](https://github.com/newgen-business-solutions/cs-event-services/commit/b78dd60de4960041beed93c737bb30ba65749b47)]
- ♻️ Refactor Show management Work Order [[e4a3d58](https://github.com/newgen-business-solutions/cs-event-services/commit/e4a3d58d9d7f8a59394218704b0a23f58d244ddb)]
- ♻️ Refactor AuthGuard [[d8651f5](https://github.com/newgen-business-solutions/cs-event-services/commit/d8651f5050e9f65f450bb9acdf6ce428e3e2fc25)]
- ♻️ Refactor Sales Order Client script [[7b56d37](https://github.com/newgen-business-solutions/cs-event-services/commit/7b56d37f62e30e9e67843fee1a7e5c5898cdbd37)]
- ♻️ Refactor Show Management Work Order [[824e16a](https://github.com/newgen-business-solutions/cs-event-services/commit/824e16a6ff5fbd09e524408bc7aac5f87be4cee8)]
- ♻️ Refactor order save logic and enhance logging [[6f9e523](https://github.com/newgen-business-solutions/cs-event-services/commit/6f9e523a2a90d234dac46fa1e13e3f79de4d68d0)]
- ⚡ Add new dependencies and enhance ordering wizard functions [[a6d2048](https://github.com/newgen-business-solutions/cs-event-services/commit/a6d2048488f743838c6df17307ce38b447876ec8)]
- 🔧 Chore(web) Refactor and modularize bundle size reporting [[2d5c352](https://github.com/newgen-business-solutions/cs-event-services/commit/2d5c352eb6a83bd86034d36dfb62aa432c9f73aa)]
- ⚡ Optimize Import in &#x60;components&#x60; [[826928e](https://github.com/newgen-business-solutions/cs-event-services/commit/826928e5149f9a8f20295c1c1859c4ce8cd06804)]
- ♻️ Refactor Work order generation suitelet [[b8f4e62](https://github.com/newgen-business-solutions/cs-event-services/commit/b8f4e62bdbdcd76b374882c8410d8c5f15f87caf)]
- 🔧 Chore(styles) Add post install script for importing styles [[89f2699](https://github.com/newgen-business-solutions/cs-event-services/commit/89f269949a350473b1640a84e9552b9e6da73fe0)]
- ♻️ Refactor API routes [[1708a07](https://github.com/newgen-business-solutions/cs-event-services/commit/1708a071d40c43c36ecc14849cd32effb12c6055)]
- ♻️ Refactor shouw mgmt work order suitelet [[40dd86f](https://github.com/newgen-business-solutions/cs-event-services/commit/40dd86f751097a9186670205980f5f18e64d11ee)]
- ⚡ Add iconButton for paid balance in invoices and orders [[5809a2e](https://github.com/newgen-business-solutions/cs-event-services/commit/5809a2e5136cdc92443282c807a0fbd71247a5f0)]
- ♻️ Update print URL and disable print button [[6127a35](https://github.com/newgen-business-solutions/cs-event-services/commit/6127a3552e57a173920d506b8871b14b475ecd03)]
- 🎨 Add invoice pages for user order details and list view [[9206ff6](https://github.com/newgen-business-solutions/cs-event-services/commit/9206ff64d8bb60c2bfb10774a4fd5093b2c9011c)]
- ♻️ Update scan URL redirections [[2233286](https://github.com/newgen-business-solutions/cs-event-services/commit/2233286f9223555429a1d7e19b59f24b02362ae7)]
- ♻️ Refactor Work Order Generation [[6091c1a](https://github.com/newgen-business-solutions/cs-event-services/commit/6091c1ab41ce4ff2526ee2d4554f19b8e9f2f9ec)]
- ♻️ Daily Function Report Defaults [[3c1c39b](https://github.com/newgen-business-solutions/cs-event-services/commit/3c1c39b1148c275a5c581a533cd6f78b5a8c4dc1)]
- ♻️ Update NextAuth Url [[5fe3c2d](https://github.com/newgen-business-solutions/cs-event-services/commit/5fe3c2d8440c90898e8ec94793931b193bf19f78)]
- ♻️ Remove log [[e67d0f9](https://github.com/newgen-business-solutions/cs-event-services/commit/e67d0f9d4736a466324164a185d48d612e103d2b)]
- ♻️ Refactor CS Scan [[b7d343d](https://github.com/newgen-business-solutions/cs-event-services/commit/b7d343d04a8551e004f04e556c220488c9bf1dc8)]
- ♻️ Refactor auth.ts [[cc7b389](https://github.com/newgen-business-solutions/cs-event-services/commit/cc7b389b06e4709ba803ec10a95776713607f0e1)]
- ♻️ Fix type errors [[df08a77](https://github.com/newgen-business-solutions/cs-event-services/commit/df08a77306ef71604d618abd3756fdd65f81199b)]
- ♻️ Refactor scheduler and update asset references [[ecdad15](https://github.com/newgen-business-solutions/cs-event-services/commit/ecdad15af4ca1aedf4b388974ebeebc87bdb4578)]
- ♻️ Refactor reset-password [[e684c14](https://github.com/newgen-business-solutions/cs-event-services/commit/e684c144eb3abde89fe13bce48db5d4a6e34396d)]
- ♻️ Update Reset Password [[0c18362](https://github.com/newgen-business-solutions/cs-event-services/commit/0c183626df6b2cdd65d7ea8c71bf99fb3d45a185)]
- ♻️ Refactor CS Scan [[1c20494](https://github.com/newgen-business-solutions/cs-event-services/commit/1c204941030703c4b6158404573eaa17232f6a27)]
- ♻️ Refactor Cs Scan [[49b7e66](https://github.com/newgen-business-solutions/cs-event-services/commit/49b7e669c089d74fc38cafb21a84150ae2466981)]
- ♻️ Refactor Cs Scan [[256bbcb](https://github.com/newgen-business-solutions/cs-event-services/commit/256bbcb307940715ef92a1ef797a512658148743)]
- ♻️ Generate Estimate Suitelet [[f7cf47c](https://github.com/newgen-business-solutions/cs-event-services/commit/f7cf47cd442472a678af75c544b91d631e77d4f8)]
- ♻️ Refactor CS Scan [[e564045](https://github.com/newgen-business-solutions/cs-event-services/commit/e56404528db11fed15ebc08b8caa17aa4a11a697)]
- ♻️ Refactor CS Scan [[a85d263](https://github.com/newgen-business-solutions/cs-event-services/commit/a85d263b39cd0fa27b0d0608d90098ccee9858a8)]
- ♻️ Refactor Cs Scan [[9b71959](https://github.com/newgen-business-solutions/cs-event-services/commit/9b719593f05ada60dcd80e982b0b0f9767ead78d)]
- ♻️ CS Shipment UE &amp; CS ([#162](https://github.com/newgen-business-solutions/cs-event-services/issues/162)) [[27dad94](https://github.com/newgen-business-solutions/cs-event-services/commit/27dad9490d2da29795fccde91e6a8a938fc1f951)]
- ♻️ Refactor CS Scan [[d5714dd](https://github.com/newgen-business-solutions/cs-event-services/commit/d5714dd8a969bd7bcb58bcc31c9158f7e2fe181b)]
- 🔧 CS Scan [[2826958](https://github.com/newgen-business-solutions/cs-event-services/commit/2826958fc454ddef93b56cefccde1ddce4ccc32e)]
- ♻️ Refactor booking events script for clarity and efficiency [[e719261](https://github.com/newgen-business-solutions/cs-event-services/commit/e719261a0c4a128c84bd0844b89f32fdfd18c307)]
- ♻️ Refactor Scheduler component to use SWR and Zustand [[7c5e8eb](https://github.com/newgen-business-solutions/cs-event-services/commit/7c5e8eb64ed4291a48288a878ec504c02f37bfe3)]
- ⚡ Add relationship mappings and refine event date search [[6771b63](https://github.com/newgen-business-solutions/cs-event-services/commit/6771b6379d02b70c86e4eb155f90c8c73597d6f2)]

### Removed

- 🔥 Remove console log and improve middleware logic [[59b0d00](https://github.com/newgen-business-solutions/cs-event-services/commit/59b0d00b3167370cc0888fb259633c37a6b00e3a)]
- 🔥 Remove obsolete Yarn cache files [[c00d97f](https://github.com/newgen-business-solutions/cs-event-services/commit/c00d97f8faae743d7ae77208ba2bffea011f1c56)]
- 🔥 Remove obsolete dependencies from the project [[ba83f09](https://github.com/newgen-business-solutions/cs-event-services/commit/ba83f09ccf5c0ac768510fef0f4c3c479f36bb9e)]
- 🔥 Remove assetPrefix and related reroute [[ad9e906](https://github.com/newgen-business-solutions/cs-event-services/commit/ad9e906a3c8be8c539b5a972d89640d6a99a8b10)]
- 🔥 Remove outdated scan URL rewrite and unused env variables [[c11a98f](https://github.com/newgen-business-solutions/cs-event-services/commit/c11a98fd31af95824159cd973b949fe55f05b361)]
- 🔥 Remove outdated NextConfig type comment [[d0de3f2](https://github.com/newgen-business-solutions/cs-event-services/commit/d0de3f25357c2cafcf0b45b1b5b96ba99e02691b)]
- 🔥 Remove manualClientBasePath and add sw.js rewrite [[d177016](https://github.com/newgen-business-solutions/cs-event-services/commit/d177016119724434cf05d6a528b1a06a1175efc5)]
- 🔥 Remove env.mjs and update Next.js configuration [[0e37741](https://github.com/newgen-business-solutions/cs-event-services/commit/0e37741850e99920d6397524b9405c26e58460d4)]
- 🔥 Remove globalDependencies from turbo.json [[22b0a7f](https://github.com/newgen-business-solutions/cs-event-services/commit/22b0a7f39fd9238e9ce779ebc606c02f8cb970e0)]
- 🔥 Chore Remove vercel.json configuration file at root [[6aed1fd](https://github.com/newgen-business-solutions/cs-event-services/commit/6aed1fd7d8d387f165faed05c8ea18f9c7799b12)]
- 🔥 Remove redundant outputDirectory in vercel.json [[e4df7f6](https://github.com/newgen-business-solutions/cs-event-services/commit/e4df7f649f1972f7ee180d745806f3b2f693c8d6)]
- 🔥 Remove old web scope configuration [[522c340](https://github.com/newgen-business-solutions/cs-event-services/commit/522c34040cfee632ddbe8420df27dbb216e2d937)]
- 🔥 Remove outdated package caches [[135aae8](https://github.com/newgen-business-solutions/cs-event-services/commit/135aae8759e61e163820022f0d0fe45342f36da6)]
- 🔥 Remove old JavaScript file [[0779246](https://github.com/newgen-business-solutions/cs-event-services/commit/07792468d6406a2b4f8b3fa3a270cee57ef59f84)]
- 🔥 Remove redundant CSS import. [[2c22b81](https://github.com/newgen-business-solutions/cs-event-services/commit/2c22b812a06f19b1ea5caf7c36a4eb1211c9ab7b)]

### Fixed

- 🐛 Duplicate exhibitor name on booth generation [[6ddffed](https://github.com/newgen-business-solutions/cs-event-services/commit/6ddffedb412170add51ee1b91599bacff67c13ea)]
- 🐛 Fix malformed search filter [[b042d3b](https://github.com/newgen-business-solutions/cs-event-services/commit/b042d3b61d99ba0415215fe1c105e1c971c75d9d)]
- 🐛 Fix import paths in reset-password.jsx [[b52490e](https://github.com/newgen-business-solutions/cs-event-services/commit/b52490eb05f3ac4c4b416b7b1520d3fa0cbdf773)]
- 🚑 Update return value in useSettings hook [[b8ee37b](https://github.com/newgen-business-solutions/cs-event-services/commit/b8ee37b9e08d1922deb4572a204066bf776f5822)]
- 🐛 Fix handling of empty login background splash URL [[0135c67](https://github.com/newgen-business-solutions/cs-event-services/commit/0135c67d7666fe33b28285fdec4a537b93406443)]
- 🐛 Fix: Add SWRDevTools and refactor price handling [[e902a8a](https://github.com/newgen-business-solutions/cs-event-services/commit/e902a8a25e62db77b189c0f7454fe6c8cf63f6e5)]
- ✏️ Chore Update case environment variable in vercel.json [[46977d3](https://github.com/newgen-business-solutions/cs-event-services/commit/46977d36d95c247fc6564ddf9f3e6074cbf71981)]
- 💚 Update Next.js build workflow for consistency [[46acb9c](https://github.com/newgen-business-solutions/cs-event-services/commit/46acb9c135454001ef67c4a079f5ab7ebb2f3fde)]
- 🐛 Fix venue ops not defined on fresh install [[7d62438](https://github.com/newgen-business-solutions/cs-event-services/commit/7d6243880849f8cb9128b1b3d675aedd8911863c)]
- 💚 Chore(CI) Update bundle analysis workflow paths and action version [[bc9dbd4](https://github.com/newgen-business-solutions/cs-event-services/commit/bc9dbd4d8323c5121cde645e77cd9fda2c2ecabb)]
- 💚 Update workflow to use v4 of upload-artifact action [[95309fd](https://github.com/newgen-business-solutions/cs-event-services/commit/95309fd81152d42e4b507d6ac917e638755d5ac5)]
- 💚 Update yarn install command in workflows [[d9d351b](https://github.com/newgen-business-solutions/cs-event-services/commit/d9d351bbd693ffbef2e05196de0b23872fe723e4)]
- 💚 Add SCAN_DOMAIN environment variable to build step [[0ca00c7](https://github.com/newgen-business-solutions/cs-event-services/commit/0ca00c7fcb4097dd185df50d15d215b3751c3a98)]
- 💚 Update Yarn [[3078267](https://github.com/newgen-business-solutions/cs-event-services/commit/307826780e9247bf99ce7ea976b6bee35b213d20)]
- 🐛 Forward ship info in Booth Order Button [[a17af1b](https://github.com/newgen-business-solutions/cs-event-services/commit/a17af1bfb8aef6a6440f2d333da0adba8fe7fbd4)]
- 💚 Chore(web) Add bundle size report script and update dependencies [[b2188b1](https://github.com/newgen-business-solutions/cs-event-services/commit/b2188b1b8f28386352a6f5fa2fc0328ea9d2abc2)]
- 💚 Update env Scan Domain added [[1af2e6a](https://github.com/newgen-business-solutions/cs-event-services/commit/1af2e6ab65ba8394546bb602620103a78a05ceda)]
- 💚 Update next.js bundle analysis for exhibitor portal [[18dc84f](https://github.com/newgen-business-solutions/cs-event-services/commit/18dc84f49b6bd8074c91c33a8fdb188ef8069a12)]
- 🐛 Venue Ops wrapper [[5617eca](https://github.com/newgen-business-solutions/cs-event-services/commit/5617eca56f32aa99d9fb303a9d3fe7037c6f33a3)]
- 🐛 SO bug fix for date comparison [[d020bf3](https://github.com/newgen-business-solutions/cs-event-services/commit/d020bf369447802e5eab2005690106137b8fd58e)]
- 🐛 Update next.config.js for consistent formatting [[d639d3b](https://github.com/newgen-business-solutions/cs-event-services/commit/d639d3bea673b4e81af62dc8c93e7cfc2988ed69)]
- 🐛 Fix title bug for daily fns report [[22ece23](https://github.com/newgen-business-solutions/cs-event-services/commit/22ece2377b14605763a3afdafc9bfacfa768ee9b)]
- 🐛 Refactor email comparison to be case-insensitive [[9b7a431](https://github.com/newgen-business-solutions/cs-event-services/commit/9b7a431a5148209eaa5a1fd0bdb4d97eac4ef4b2)]

### Security

- 🔒 Add authentication module and environment configuration [[da8196d](https://github.com/newgen-business-solutions/cs-event-services/commit/da8196df52bb92764cd91ed2213231b0fd7883bb)]

### Miscellaneous

- 📝 Update CHANGELOG with new features and improvements [[433e7c2](https://github.com/newgen-business-solutions/cs-event-services/commit/433e7c23295b968790a60be9ef2d9ee25ac67d41)]
-  Add Web Blurb Template Files [[efcbf6f](https://github.com/newgen-business-solutions/cs-event-services/commit/efcbf6f4345c712c61df7c2fe89caa57ea68f967)]
-  Merge branch &#x27;CESS-601-Automated-Blurb&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-601-Automated-Blurb [[952794d](https://github.com/newgen-business-solutions/cs-event-services/commit/952794dfb965c3a39ad30afbc7e4d553b1277175)]
-  Merge pull request [#178](https://github.com/newgen-business-solutions/cs-event-services/issues/178) from newgen-business-solutions/CESS-564-Generate-New-Booths-Bug-Rosemont [[70ef12f](https://github.com/newgen-business-solutions/cs-event-services/commit/70ef12fb99551d6160595cf4cb4a18091aab69b1)]
-  Merge branch &#x27;CESS-601-Automated-Blurb&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-601-Automated-Blurb [[e43b854](https://github.com/newgen-business-solutions/cs-event-services/commit/e43b85463b64169768c31019367b30bee111d814)]
- 📦 Add new package caches for @babel-runtime and @mui components [[b410317](https://github.com/newgen-business-solutions/cs-event-services/commit/b41031729845851f06d62ccb2474cb4f6b8e4899)]
-  Merge branch &#x27;main&#x27; into CESS-601-Automated-Blurb [[68e0fcb](https://github.com/newgen-business-solutions/cs-event-services/commit/68e0fcb83f6d86e48125f4222bc26cd101747907)]
-  Merge branch &#x27;main&#x27; into CESS-564-Generate-New-Booths-Bug-Rosemont [[f2aeb25](https://github.com/newgen-business-solutions/cs-event-services/commit/f2aeb25f08d0236ff62e6c9be2cfc3ea45eb7936)]
-  Merge pull request [#177](https://github.com/newgen-business-solutions/cs-event-services/issues/177) from newgen-business-solutions/CESS-601-Automated-Blurb [[ac27c52](https://github.com/newgen-business-solutions/cs-event-services/commit/ac27c52b9667a0eedcbb68b16e8492caa5e18218)]
-  Merge branch &#x27;CESS-601-Automated-Blurb&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-601-Automated-Blurb [[2120b80](https://github.com/newgen-business-solutions/cs-event-services/commit/2120b801538084a6195bf1c47825105b25e9c41e)]
-  Merge branch &#x27;main&#x27; into CESS-601-Automated-Blurb [[cde8b62](https://github.com/newgen-business-solutions/cs-event-services/commit/cde8b624c21e89d3f75f9fb14f5fb24d64b94aee)]
-  Merge branch &#x27;main&#x27; into CESS-601-Automated-Blurb [[709d49d](https://github.com/newgen-business-solutions/cs-event-services/commit/709d49d5f2f33e83f496d656aeb99c558f85269f)]
- 🩹 Update item change message and display name handling [[7242f41](https://github.com/newgen-business-solutions/cs-event-services/commit/7242f41d3090a15b693755d3445c32a1644aa3d5)]
- 📦 Add htmx.min.js to FileCabinet package [[a02505e](https://github.com/newgen-business-solutions/cs-event-services/commit/a02505effb9f1b8ceca255f2e1cabf10010f193c)]
- 🚧 Add functionality for manual item sorting. [[b49c215](https://github.com/newgen-business-solutions/cs-event-services/commit/b49c215cbf55ba4c3a0b2f4a3b4efb516adb4e34)]
-  Merge branch &#x27;main&#x27; into CESS-461-Function-Automation [[ae4794b](https://github.com/newgen-business-solutions/cs-event-services/commit/ae4794b3d35484ee7eaacd9ecb971309db231122)]
- 📦 Add @babel and related package caches to Yarn [[20e5669](https://github.com/newgen-business-solutions/cs-event-services/commit/20e56696fc61f8b265480f669bb685c70716db46)]
- 📦 Add new dependencies: swr-devtools, copy-anything, is-what, superjson [[e1cca77](https://github.com/newgen-business-solutions/cs-event-services/commit/e1cca776b79e7467e4f20d54052c993da84fc729)]
- 📦 Add custom Preact module for CS event services [[782955b](https://github.com/newgen-business-solutions/cs-event-services/commit/782955b2683ce9bef3979acdd4ff7d2b7754eb0f)]
- 📦 Add new dependencies and update various package versions [[26d712b](https://github.com/newgen-business-solutions/cs-event-services/commit/26d712b1824a67025810799d941985907c215e70)]
- 📦 Add Preact libraries to backend service [[9725ee4](https://github.com/newgen-business-solutions/cs-event-services/commit/9725ee4645d32f89a4b4afbb6028247473fa2435)]
- 📝 Add InitColorSchemeScript to _document.jsx [[ea6011e](https://github.com/newgen-business-solutions/cs-event-services/commit/ea6011e842eae62d5fb1da95ddd938031cd2d9c9)]
- 📝 Add Suitelet for fetching and rendering support docs [[8a9df25](https://github.com/newgen-business-solutions/cs-event-services/commit/8a9df2586add01f5067e4d8008908b99462ebd07)]
- 📦 Upgrade Next.js to version 14.2.10 [[b717340](https://github.com/newgen-business-solutions/cs-event-services/commit/b717340a184c6b500233bdb43b2c8b139022ef68)]
- 📦 Add jiti package to Yarn cache [[096445c](https://github.com/newgen-business-solutions/cs-event-services/commit/096445c705c280835eba32516350a00cc8da7dcc)]
- 📦 Add @t3-oss/env-core to package dependencies [[ac46c4a](https://github.com/newgen-business-solutions/cs-event-services/commit/ac46c4aa5c4cdd9ee73555114f2e0d4eec2547a3)]
- 🔨 Chore Enable scan route rewrites and simplify Vercel config [[061bbec](https://github.com/newgen-business-solutions/cs-event-services/commit/061bbec8b54631ca43646b8658276ee13770ead7)]
- 📦 Update config extension to .mjs in vercel.json [[4d6b9a7](https://github.com/newgen-business-solutions/cs-event-services/commit/4d6b9a758f76436a3ce330a352d84f692d199272)]
- 📦 Update @mui/lab to beta version [[854d7ae](https://github.com/newgen-business-solutions/cs-event-services/commit/854d7ae26c789abca6e328297d3e17ce5703af39)]
- 📦 Add @mui/material-nextjs to dependencies [[5d50fb4](https://github.com/newgen-business-solutions/cs-event-services/commit/5d50fb448ba4ba737fefd2169de9740b33e5bb90)]
-  :pacakage: Update MUI dependencies to latest versions [[10712cd](https://github.com/newgen-business-solutions/cs-event-services/commit/10712cdc194f19b09421925225f7465f2cf1cbdf)]
- 🔨 Add global dependencies, Vercel config, and dynamic redirect [[5b4fad8](https://github.com/newgen-business-solutions/cs-event-services/commit/5b4fad829e28be907fdda41eb05b1a9c1b7615f3)]
- 📦 Update dependencies in yarn.lock [[1792458](https://github.com/newgen-business-solutions/cs-event-services/commit/1792458f326b87292f3a2e801182b25e8b056d62)]
- 📦 Update project dependencies to latest versions [[6d8f477](https://github.com/newgen-business-solutions/cs-event-services/commit/6d8f477e09edc1f284e89496f877a817d781e237)]
- 📦 Update Next.js to 15.0.0-canary.178 and add new dependencies [[42caaa9](https://github.com/newgen-business-solutions/cs-event-services/commit/42caaa94440d2b7f5b3d905de44f3956c67e8a1f)]
-  Merge branch &#x27;CESS-457-CS-Scan&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-457-CS-Scan [[69c5ebe](https://github.com/newgen-business-solutions/cs-event-services/commit/69c5ebe10ae36e3698db449d8335b1f7e4586170)]
-  Add new environment variables to env.mjs files [[274454f](https://github.com/newgen-business-solutions/cs-event-services/commit/274454f1e333f13077c8ed489be67b22dac1083c)]
- 📦 Add @t3-oss/env packages to yarn cache [[d53255c](https://github.com/newgen-business-solutions/cs-event-services/commit/d53255c029471209f5c9cd6bb45c71cb8fbf188b)]
- 📦 Add new env variable and @t3-oss/env-nextjs dependency [[1188096](https://github.com/newgen-business-solutions/cs-event-services/commit/11880962d52e14b618f7843fa1dbf991172ddd28)]
- 🔨 Update scan URL paths in next.config.mjs [[4ff74b1](https://github.com/newgen-business-solutions/cs-event-services/commit/4ff74b17a26e16b0f16496bc55337651b78ebf71)]
- 🔨 Chore Add Vercel config files and streamline environment variables [[f499d27](https://github.com/newgen-business-solutions/cs-event-services/commit/f499d27c12fa6bd22863591e2d39ea4db62c9783)]
- 🔨 Chore Update ignoreCommand syntax in vercel.json [[884a447](https://github.com/newgen-business-solutions/cs-event-services/commit/884a447f9b79fddc2056e040054cc750b1b8657f)]
- 🔨 Chore Enhance rewrite configurations and add env debug endpoint [[839bb12](https://github.com/newgen-business-solutions/cs-event-services/commit/839bb12e200bd3cb89c90c6a6727c4923b0d7f9d)]
- 🔨 Chore Update ignoreCommand in vercel.json [[63793c2](https://github.com/newgen-business-solutions/cs-event-services/commit/63793c2a3210a7a6cfc98c91af99f78ccc92e26e)]
- 🔨 Update vercel.json to include new build configurations [[ba627f7](https://github.com/newgen-business-solutions/cs-event-services/commit/ba627f73e16b40e55cbc82b5aff609f679e5eb2d)]
- 🏷️ Refactor reset-password component for consistency and clarity [[54442bc](https://github.com/newgen-business-solutions/cs-event-services/commit/54442bca1ca6ec42cbaa6cd2017c6ef8ef2fa537)]
- 🔨 Add vercel.json and update turbo.json with global dependencies [[8dafb14](https://github.com/newgen-business-solutions/cs-event-services/commit/8dafb14b2f8ff9fe3e6c9a420f9c12a951dab069)]
- 📦 Add new npm package caches to the project [[1c3c2c5](https://github.com/newgen-business-solutions/cs-event-services/commit/1c3c2c564b50f92dee11e95b179da1bcf1d47304)]
- 📝 Add branch switching and troubleshooting instructions to README [[b7af246](https://github.com/newgen-business-solutions/cs-event-services/commit/b7af246f24ff339441692941df5b004c554312e4)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[66ce17c](https://github.com/newgen-business-solutions/cs-event-services/commit/66ce17c2444efcdac817d6cd16e957ddaf11de9d)]
- 🩹 Refine freight item quantity calculation logic [[d8a3e2c](https://github.com/newgen-business-solutions/cs-event-services/commit/d8a3e2c372f195d1c50d984728dce2bfb875cae0)]
-  Merge remote-tracking branch &#x27;origin/CESS-500-changing-status-price-on-event-propagation&#x27; [[38bc03e](https://github.com/newgen-business-solutions/cs-event-services/commit/38bc03e3efd040dffd8f250bdb3e42b1485b1acc)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[feaf407](https://github.com/newgen-business-solutions/cs-event-services/commit/feaf407d71b6491b76b69ca767336acbbd1639d7)]
- 🎇 Feat: Show Management Work Order Enhancements [#173](https://github.com/newgen-business-solutions/cs-event-services/issues/173) [[6f2a78a](https://github.com/newgen-business-solutions/cs-event-services/commit/6f2a78a6986a5c1ad521bd7ac8909e89cf85911d)]
-  Merge branch &#x27;CESS-504-Work-Order-Enhancements&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-504-Work-Order-Enhancements [[c6bf768](https://github.com/newgen-business-solutions/cs-event-services/commit/c6bf768e1abc6dcd921b128201ba36c4f4824638)]
-  Merge branch &#x27;main&#x27; into CESS-504-Work-Order-Enhancements [[898e1c1](https://github.com/newgen-business-solutions/cs-event-services/commit/898e1c1b1009106ac5954d8a1fa675ef81ae6bb4)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[6f6b96a](https://github.com/newgen-business-solutions/cs-event-services/commit/6f6b96a1a8929a58c03bd113e1dfe57155ee3986)]
-  Merge branch &#x27;main&#x27; into CESS-500-changing-status-price-on-event-propagation [[606602f](https://github.com/newgen-business-solutions/cs-event-services/commit/606602f9745addc63e210c757f6a0badb9c1fa1a)]
- 🩹 Set file URL in custom record field after file upload [[bd5975a](https://github.com/newgen-business-solutions/cs-event-services/commit/bd5975ace34be970763a2a2f6387cc955034a477)]
-  Merge branch &#x27;main&#x27; into CESS-504-Work-Order-Enhancements [[a24585a](https://github.com/newgen-business-solutions/cs-event-services/commit/a24585ada40a7c7eef6949508a89155e4a44691c)]
-  Merge remote-tracking branch &#x27;origin/CESS-500-changing-status-price-on-event-propagation&#x27; into CESS-500-changing-status-price-on-event-propagation [[977186f](https://github.com/newgen-business-solutions/cs-event-services/commit/977186f46ce164cc4485c39ce0254e5eba4600cf)]
-  Merge remote-tracking branch &#x27;origin/CESS-500-changing-status-price-on-event-propagation&#x27; into CESS-500-changing-status-price-on-event-propagation [[f68e720](https://github.com/newgen-business-solutions/cs-event-services/commit/f68e72028c3c88bead285b3ea8ed336d08a18aba)]
-  Merge branch &#x27;main&#x27; into CESS-500-changing-status-price-on-event-propagation [[61196b6](https://github.com/newgen-business-solutions/cs-event-services/commit/61196b6442201672730eacfef38f253f217157b3)]
- 🩹 Fix user event error message and hide CWT field conditionally [[d802484](https://github.com/newgen-business-solutions/cs-event-services/commit/d8024841a6ceccbc89ab224ed757ac5581c0838b)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[36e7b4e](https://github.com/newgen-business-solutions/cs-event-services/commit/36e7b4e1a8ad84c0da2d7ff839d7119b3dfd598d)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[afdb956](https://github.com/newgen-business-solutions/cs-event-services/commit/afdb9562fbca451ea7bb564aae59231a3cc2eb14)]
- 📦 Upgrade Next.js to version 14.2.13 [[ad222e0](https://github.com/newgen-business-solutions/cs-event-services/commit/ad222e01ac7b7223fad9bc2c60363b506d18073e)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[76ff65d](https://github.com/newgen-business-solutions/cs-event-services/commit/76ff65db49bb2b4dc6e5a50384c8c1088a44756a)]
- 📦 Add Turbo packages to Yarn cache [[57e8c6a](https://github.com/newgen-business-solutions/cs-event-services/commit/57e8c6abcf5ce8d7dd595b4776fb1670b01e1e00)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[52526a6](https://github.com/newgen-business-solutions/cs-event-services/commit/52526a65cc2e51ca8e2fb365519f40945b121616)]
- 🔨 Chore Update Turbo package and add new dev-zones script [[804fa27](https://github.com/newgen-business-solutions/cs-event-services/commit/804fa27f357ef565f671c4fa817f93509d3b5da3)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[6f9d74a](https://github.com/newgen-business-solutions/cs-event-services/commit/6f9d74abe50b740d2e5269e44e38827afb66b86c)]
-  Merge branch &#x27;main&#x27; into CESS-500-changing-status-price-on-event-propagation [[ca337a3](https://github.com/newgen-business-solutions/cs-event-services/commit/ca337a3ac203247396b8d843afd99fdc8e05e920)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[55decd6](https://github.com/newgen-business-solutions/cs-event-services/commit/55decd6e08bcef90674f89c0a9725f61ea28a634)]
-  Merge branch &#x27;main&#x27; into CESS-500-changing-status-price-on-event-propagation [[4753b27](https://github.com/newgen-business-solutions/cs-event-services/commit/4753b2758b0d77fa0da753d0ab738721dbe1b2a8)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[3014328](https://github.com/newgen-business-solutions/cs-event-services/commit/3014328891dd6abb5433776649a3341db2727dbe)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[7cdc02a](https://github.com/newgen-business-solutions/cs-event-services/commit/7cdc02a44e93ed7e46b7f2ff87dff44e2fa4afdd)]
- 📝 Add build status badge to scan README. [[af6c375](https://github.com/newgen-business-solutions/cs-event-services/commit/af6c3750b9d32862e3bae2522128e3a777406f6e)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[823ca8a](https://github.com/newgen-business-solutions/cs-event-services/commit/823ca8a431fe75f25ca3c64e4677b86073aa6420)]
- 📦 Update yarn cache and gitignore [[f692cd1](https://github.com/newgen-business-solutions/cs-event-services/commit/f692cd1bb441f163f9b15cd3b184a04c15f64900)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[84c5612](https://github.com/newgen-business-solutions/cs-event-services/commit/84c5612525491c6f25bf705b9b58f549da8b632a)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[2d909c9](https://github.com/newgen-business-solutions/cs-event-services/commit/2d909c9fd2d5684e889b5ff701c10d0294d0a7ab)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[c4a41d1](https://github.com/newgen-business-solutions/cs-event-services/commit/c4a41d185fac51ecd96d2733ac40254820c0ad50)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[02c4adc](https://github.com/newgen-business-solutions/cs-event-services/commit/02c4adcfd3282327d3df088f5010797f468bd3b0)]
- 📦 Update package version to 9.19.115 [[f4554bf](https://github.com/newgen-business-solutions/cs-event-services/commit/f4554bfe38b95241f2ae1d38efa42403ef8c44fd)]
- 🩹 Format imports consistently [[f6b4508](https://github.com/newgen-business-solutions/cs-event-services/commit/f6b45085f9ed0c14743536e236b7c1b8872933f3)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[dbdb211](https://github.com/newgen-business-solutions/cs-event-services/commit/dbdb211cf559a9ab3e3b3cbcdcfbfa3e8f336cc7)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[930a01d](https://github.com/newgen-business-solutions/cs-event-services/commit/930a01dad0111b4c11e4f1bad33051c8e95fa896)]
- 🔨 Update yarn lock [[44b708f](https://github.com/newgen-business-solutions/cs-event-services/commit/44b708fc2860d3b92efd7205a8fa0048fcdae7a6)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[460a428](https://github.com/newgen-business-solutions/cs-event-services/commit/460a428746e806c5c6efb135680943edd99d9f75)]
-  :pacakge: Update dependencies in &#x60;yarn.lock&#x60; and &#x60;package.json&#x60; [[a891042](https://github.com/newgen-business-solutions/cs-event-services/commit/a891042a6db60c0743fe8534c281e14b12240706)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[9c581ab](https://github.com/newgen-business-solutions/cs-event-services/commit/9c581abd50a896e0564fcb26ca771df40a6dfb12)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[705730c](https://github.com/newgen-business-solutions/cs-event-services/commit/705730ca53e53bc71fb924766e5e3a49f76c8e41)]
-  Update nextjs_bundle_analysis.yml for web portal scoping [[11a39c0](https://github.com/newgen-business-solutions/cs-event-services/commit/11a39c05cfd4ada9d9a76ce45b085b66135ad745)]
- 📦 update yarn lock. [[e7b61d5](https://github.com/newgen-business-solutions/cs-event-services/commit/e7b61d5b6c54a8fab962f8a2c048103c9871d1c6)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[1b406c7](https://github.com/newgen-business-solutions/cs-event-services/commit/1b406c75a71f6148c85208d137bddcb07ef09452)]
-  Update yarn.lock with new and revised package resolutions [[ee6baf4](https://github.com/newgen-business-solutions/cs-event-services/commit/ee6baf458ead97b47aad5a62ad6bdb00d83d1f9b)]
-  :robot: Add GitHub Actions for code review and Next.js bundle analysis [[758da28](https://github.com/newgen-business-solutions/cs-event-services/commit/758da28905cae5925d386d04d00aa152f713020e)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[1d65d76](https://github.com/newgen-business-solutions/cs-event-services/commit/1d65d7631a9dedb44efb51e765dcb317c2f0e643)]
-  Merge pull request [#167](https://github.com/newgen-business-solutions/cs-event-services/issues/167) from newgen-business-solutions/CESS-457-CS-Scan [[511fb8a](https://github.com/newgen-business-solutions/cs-event-services/commit/511fb8a0cb62e5fad6e079ebee4aa25666be37a9)]
-  Merge pull request [#166](https://github.com/newgen-business-solutions/cs-event-services/issues/166) from newgen-business-solutions/CESS-457-CS-Scan [[b936f93](https://github.com/newgen-business-solutions/cs-event-services/commit/b936f93f430ba6357500c6d85a16322babfc2dfc)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[24fdd4a](https://github.com/newgen-business-solutions/cs-event-services/commit/24fdd4ab59f364a277fb98b8a10bab0c79232e96)]
-  :refactor: Refactor ScheduleConfig URL and update sync config [[c261a0c](https://github.com/newgen-business-solutions/cs-event-services/commit/c261a0c7c51de89d290987a75d19d6a641b129b7)]
- 🔨 Update Booking Calandar Build [[6578a7f](https://github.com/newgen-business-solutions/cs-event-services/commit/6578a7f8b57d5a9f1d85b7800ad55e09a397bc55)]
- 📦 Update Yarn install state [[953c522](https://github.com/newgen-business-solutions/cs-event-services/commit/953c522acb2f51f1f89914b634fb7b691fe05687)]
- 📦 Update Yarn Lock [[013048a](https://github.com/newgen-business-solutions/cs-event-services/commit/013048a92315631aeeafeb5c8e42d894e6af67fc)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[1b62a8d](https://github.com/newgen-business-solutions/cs-event-services/commit/1b62a8daea48b944fabc2b75babc37deb52a8ef6)]
- 📦 update yarn.lock [[fae5d93](https://github.com/newgen-business-solutions/cs-event-services/commit/fae5d930fcbfd7dd9882111d26bc4758bb925c67)]
- 📦 Remove &#x60;package-lock.json&#x60; file for Yarn migration [[6f83966](https://github.com/newgen-business-solutions/cs-event-services/commit/6f83966ed7eb9a0924e21e9b7720546a55489744)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[b498097](https://github.com/newgen-business-solutions/cs-event-services/commit/b498097f396c2bd2513658b22a1fe7170987838b)]
- 📦 Update install-state.gz [[d509861](https://github.com/newgen-business-solutions/cs-event-services/commit/d509861e02ec45f14b0d6c02c2612b010bf78e39)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[e5834c0](https://github.com/newgen-business-solutions/cs-event-services/commit/e5834c069a29fe170704a6c4e22574f6a053638d)]
- 📦 Package json and yarn lock updates [[7dd4758](https://github.com/newgen-business-solutions/cs-event-services/commit/7dd47580d0136d9661caccfd571b7b3dafcc8db1)]
- 📦 Update Babel dependencies to latest versions [[afe17ce](https://github.com/newgen-business-solutions/cs-event-services/commit/afe17ce82bafcf24b03d13432b247be271180c2d)]
- 🔨 Build new booking calendar to CS Dev [[1ebb372](https://github.com/newgen-business-solutions/cs-event-services/commit/1ebb3726b26b5fa07c73f271632966fee2a6160f)]
-  Merge branch &#x27;main&#x27; into CESS-457-CS-Scan [[19d000d](https://github.com/newgen-business-solutions/cs-event-services/commit/19d000d106f8f1e6cee31506136ebd0e5e08697c)]
- 📦 Upgrade axios to version 1.7.2 [[5d4ae29](https://github.com/newgen-business-solutions/cs-event-services/commit/5d4ae29b0b50627f52a95b2e382a10d9238197c1)]
- 📦 Upgrade Bryntum dependencies to version 6.0.4 [[1706cca](https://github.com/newgen-business-solutions/cs-event-services/commit/1706cca86a283119f1feb2d699dc133b9151dc66)]
- 📦 Consolidate dependencies in yarn.lock [[902c7c2](https://github.com/newgen-business-solutions/cs-event-services/commit/902c7c2537cd3f01724dc8aea89b6d71ce08de50)]
- 📝 Update CHANGELOG.md for release version 9.18.75 [[de8536d](https://github.com/newgen-business-solutions/cs-event-services/commit/de8536d53982dfc551b7c67b489fd96424097178)]
- 📦 Add workbox service worker script [[9ed3adc](https://github.com/newgen-business-solutions/cs-event-services/commit/9ed3adc7d827fb3c8e188428883d63df729761e6)]


<a name="9.18.75"></a>
## 9.18.75 (2024-07-25)

### Added

- ✨ FEAT: Merge pull request [#159](https://github.com/newgen-business-solutions/cs-event-services/issues/159) from newgen-business-solutions/CESS-467-feat-material-handling-pricing [[651b474](https://github.com/newgen-business-solutions/cs-event-services/commit/651b474c6ef3efd7e19bcfdce78d8545e81d18a2)]
- ➕ Add enums to amdUserEventConfig and amdClientConfig [[739ca45](https://github.com/newgen-business-solutions/cs-event-services/commit/739ca4565147efd4108aa1e8fba42665075f9082)]
- ➕ Add new ClientScript for item validation and update item UserEvent script [[2a309e6](https://github.com/newgen-business-solutions/cs-event-services/commit/2a309e6c368e019fec8b1cfb36dc986819b36174)]
- ➕ Add new properties to ng_cses_enum_cross_ref enumerator [[07e8073](https://github.com/newgen-business-solutions/cs-event-services/commit/07e80733f3b11bd7cc66cf7c0489bcb2d867aa11)]
- ➕ Add new scripts for CS Event Services SuiteApps [[ad0efe4](https://github.com/newgen-business-solutions/cs-event-services/commit/ad0efe43474cb4cce094bdc2c636578bc74f53ec)]
- ✨ Implement material handling beta functionality [[dadad47](https://github.com/newgen-business-solutions/cs-event-services/commit/dadad47b96a4a8236473cd0c40615c74b67343c0)]
- ➕ Add labor schedule creation to event wizard [[d24daf1](https://github.com/newgen-business-solutions/cs-event-services/commit/d24daf112f2f1c2f8094de7f2068c0c7794d93ac)]
- ✨ Feat: CS Shipment To Sales Order Updates ([#156](https://github.com/newgen-business-solutions/cs-event-services/issues/156)) [[c792af0](https://github.com/newgen-business-solutions/cs-event-services/commit/c792af04ac473950303e7deb2032a7fd32ba5364)]
- ✨ Add status chip and dynamic timespan to scheduler [[42cbdda](https://github.com/newgen-business-solutions/cs-event-services/commit/42cbdda2d3eb96b5dc577744b0289fe8ccae3a0d)]
- ✅ Update Scheduler components and add new scheduler-event component [[16ba28c](https://github.com/newgen-business-solutions/cs-event-services/commit/16ba28c87bb2b10e7126e46a8d89ad772a60dc9a)]

### Changed

- 🚚 Rename CS Shipment Client script [[1ddc5a9](https://github.com/newgen-business-solutions/cs-event-services/commit/1ddc5a9440159a52c9570ee62ffbc31b9d61ae38)]
- ♻️ Refactor CS Shipment client [[a3a8bae](https://github.com/newgen-business-solutions/cs-event-services/commit/a3a8bae491d304dae490f5c2fa8e169ae2879e31)]
- ⚡ Improve labor schedule checks and logging [[7b352d3](https://github.com/newgen-business-solutions/cs-event-services/commit/7b352d32e7165e3e224ee70d254b631ab81a128f)]
- ⚡ Improve handling and display of missing labor rates and time slots [[c0fc40f](https://github.com/newgen-business-solutions/cs-event-services/commit/c0fc40f7e358bc68504255ec7dab8b8c138505bb)]
- ♻️ Refactor Material Handling [[990695b](https://github.com/newgen-business-solutions/cs-event-services/commit/990695b66f8d0a9e1e2a5f4b1568b927312ce1c8)]
- ♻️ Refactor Material Handling [[a956150](https://github.com/newgen-business-solutions/cs-event-services/commit/a956150b4a4d0e6e7250477edec95f910f1fa904)]
- ♻️ Material Handling Feature [[31f8f65](https://github.com/newgen-business-solutions/cs-event-services/commit/31f8f65d319c43b5be6bbcf1b875a58300965580)]
- ♻️ Refactor Material Handling [[735a21c](https://github.com/newgen-business-solutions/cs-event-services/commit/735a21ca5f884fdd9ed7ea2fdac77ce50f0ec6bc)]
- 🎨 Update material handling and labor options status [[7ec4606](https://github.com/newgen-business-solutions/cs-event-services/commit/7ec46069978be6de039f0c478ef4f9ff3e6607be)]
- ♻️ Refactor Material Handling CS Shipment [[d17128c](https://github.com/newgen-business-solutions/cs-event-services/commit/d17128c7775de56fc3647c31873dffa2f0e79279)]
- ♻️ Add material handling beta features to various scripts [[887e2ff](https://github.com/newgen-business-solutions/cs-event-services/commit/887e2ffc02122ea761b648cc249aea35a5a67c8c)]
- ♻️ Refactor code for error checking and null safety [[5ae1f53](https://github.com/newgen-business-solutions/cs-event-services/commit/5ae1f536a6ee31192ccae965eaae7801030b0e60)]
- ♻️ Refactor CS Shipment UE [[b03f97b](https://github.com/newgen-business-solutions/cs-event-services/commit/b03f97b2b8fe73aa97e0c36f16d816c0b096e4f1)]
- ♻️ Refactor CS Shipment UE and CS [[0b576fb](https://github.com/newgen-business-solutions/cs-event-services/commit/0b576fb837661d668b11ad829699c6a22ed37e43)]
- ♻️ Add functionality to hide Freight table sublist [[4d3c013](https://github.com/newgen-business-solutions/cs-event-services/commit/4d3c01335f685acef182a10f2125b1d5d1595f4d)]
- ♻️ Refactor Item UE [[1d183e1](https://github.com/newgen-business-solutions/cs-event-services/commit/1d183e128eeb44b3cae94737f4da785eb253ee6c)]
- ♻️ Add new enum keys in ng_cses_enum_cross_ref.js [[e85f25b](https://github.com/newgen-business-solutions/cs-event-services/commit/e85f25b704c091e5c58c26de86538f173f2c83b5)]
- ♻️ Refactor &#x60;ng_cs_clientCSSettings_v2.js&#x60; to improve readability and performance [[b4067ed](https://github.com/newgen-business-solutions/cs-event-services/commit/b4067ed5d91783f3c0d9151991d088260a5ab48b)]
- ♻️ Update handling of material handling schedule and item quantities [[3f6f452](https://github.com/newgen-business-solutions/cs-event-services/commit/3f6f4526c4b913c6c96a0aa664fe23447747ac19)]
- ♻️ Refactor material handling pricing data lookup [[333c252](https://github.com/newgen-business-solutions/cs-event-services/commit/333c252b585b36433bea347cded5aa047fc0f167)]
- 🎨 Add handling for &#x27;Per Piece&#x27; products not available for web purchasing [[01302af](https://github.com/newgen-business-solutions/cs-event-services/commit/01302af66f959e98dbf9f801f1069efbe79d2dda)]
- ♻️ Add material handling beta feature and refactor settings [[e12894f](https://github.com/newgen-business-solutions/cs-event-services/commit/e12894f4cfd324552c478e5662ea533700b45e7b)]
- ♻️ Refactor Suitelet scripts for better syntax and status code addition [[8335a8b](https://github.com/newgen-business-solutions/cs-event-services/commit/8335a8b6055b819409cff5d7a7f7a8cee8e3a2f9)]
- ♻️ Update enum cross ref and add new User Event script [[facfa05](https://github.com/newgen-business-solutions/cs-event-services/commit/facfa0506669edc606a0f28b3a8fbb8d35756fbb)]
- ♻️ Refactor Delivery By Product Report ([#158](https://github.com/newgen-business-solutions/cs-event-services/issues/158)) [[3c1b341](https://github.com/newgen-business-solutions/cs-event-services/commit/3c1b3414de5cd578bde97be5bc27bac1feb96f8e)]
- ♻️ Add &#x27;tomorrow&#x27; variable in company shows script [[8add617](https://github.com/newgen-business-solutions/cs-event-services/commit/8add617c79c2fe1ec463351195b7f000e1fc43ba)]
- ⚡ Add HTML escape function to sanitize user input [[cfb73ad](https://github.com/newgen-business-solutions/cs-event-services/commit/cfb73ad6890ebfa20b198c9a4b448ffbfcaf676f)]
- 🎨 Update transaction conditions and improve syntax in deposit scripts [[5458201](https://github.com/newgen-business-solutions/cs-event-services/commit/5458201099478bb781a037327d9824f8cc140167)]
- ♻️ Refactor code for improved logging and better syntax consistency [[2aa2218](https://github.com/newgen-business-solutions/cs-event-services/commit/2aa22186c6e7cdd61ff61633b5c46e2e17b2ee28)]
- 🎨 Update end date in calendarStore [[7a13027](https://github.com/newgen-business-solutions/cs-event-services/commit/7a130276551141a031bc39bd167514e8efd7deb4)]
- 💄 BookingCalendar component and calendar config [[14de35f](https://github.com/newgen-business-solutions/cs-event-services/commit/14de35f847cbe8270c6c96de9244c67e848ba24b)]
- ♻️ Update booking-calendar and booking-events functionality [[723dd62](https://github.com/newgen-business-solutions/cs-event-services/commit/723dd6265e35eb7d4e8a7995f37c7244a39bbe4a)]
- ♻️ Update Bryntum Scheduler and related components [[e9b26f8](https://github.com/newgen-business-solutions/cs-event-services/commit/e9b26f8ff48cc11fcfb3324245ca3f79bd2ab762)]
- 🔧 Update SuiteScript library versions in cs-event-services module [[f30b8df](https://github.com/newgen-business-solutions/cs-event-services/commit/f30b8df527f69ab04ee2ecf40837d59717777926)]
- ⬆️ Update Changelog and increment version to 9.17.38 [[ccd400d](https://github.com/newgen-business-solutions/cs-event-services/commit/ccd400db9fdc2664c1296b9f11e167fc2ca6497c)]
- ♻️ Refactor booking events and assignments handling [[e0e2469](https://github.com/newgen-business-solutions/cs-event-services/commit/e0e246914155bc22b9412b6caa94f9af7a0aa5d3)]
- ⬆️ Update Booking Calendar app with revised scripts [[d9604ae](https://github.com/newgen-business-solutions/cs-event-services/commit/d9604ae02e730398bb7d7abf18dac433419ef534)]
- ♻️ Update authStore and calendarStore settings [[f9ce3e9](https://github.com/newgen-business-solutions/cs-event-services/commit/f9ce3e92ed2721734e380766782c885bd2fcdabb)]
- 🎨 Update layout settings and refine Scheduler UI [[824eeed](https://github.com/newgen-business-solutions/cs-event-services/commit/824eeed21a8e35ab4aff804a78742861957d82db)]
- 🔧 Add watcherTasks.xml for SCSS file monitoring [[3c0e5e3](https://github.com/newgen-business-solutions/cs-event-services/commit/3c0e5e32f75741263d904b6f2dce3ca8bbd4367a)]
- ⬆️ Update booking calendar prod build [[4deea7f](https://github.com/newgen-business-solutions/cs-event-services/commit/4deea7fbd0b6b9e04ec04152f424a2145f6a7c21)]
- ♻️ Refactor booking calendar services and implement partial caching [[c54e868](https://github.com/newgen-business-solutions/cs-event-services/commit/c54e8683d23e1ad44136d8178bf962c12a79a3cd)]
- 🎨 Add styling classes to event booking system [[4880bed](https://github.com/newgen-business-solutions/cs-event-services/commit/4880bedda10ef18328609aa1ed66ce1b52d57a47)]
- ♻️ Refactor Scheduler component and remove unused CSS [[d63be77](https://github.com/newgen-business-solutions/cs-event-services/commit/d63be7719f3a619f151c4fb151a6bf875f8871e4)]
- 🎨 Update dependencies and clean up code [[aa0023c](https://github.com/newgen-business-solutions/cs-event-services/commit/aa0023cca53af2d7dd60ff27138677dcc2ca44e2)]
- ♻️ Refactor scheduler component to include data fetching (SWR) and state management [[a837028](https://github.com/newgen-business-solutions/cs-event-services/commit/a837028f10db18001e43b59b9da045d3fc3b48d8)]
- ♻️ Update console debug text and adjust bookingCalendarUrl [[ae68ce6](https://github.com/newgen-business-solutions/cs-event-services/commit/ae68ce622ab7ac59d7164094f9a0bfab71d950d0)]
- ⬆️ Update dependencies in yarn.lock file [[4abee5a](https://github.com/newgen-business-solutions/cs-event-services/commit/4abee5ad1dfd73f74442d9072c525b5fb6da9cc5)]
- 🎨 Update authentication and calendar store logic [[5cb1a97](https://github.com/newgen-business-solutions/cs-event-services/commit/5cb1a979d1b1e38846055b261b1b4005dd4eb87b)]
- ♻️ Refactored booking code to support multiple spaces [[7ef7cf1](https://github.com/newgen-business-solutions/cs-event-services/commit/7ef7cf1f8052b8f0f0872d14a123f2544c18e452)]
- 🎨 Update Scheduler component with new event editor and various enhancements [[c5ebba1](https://github.com/newgen-business-solutions/cs-event-services/commit/c5ebba172dbaa6984956effb2d1b840e12198af2)]
- ♻️ Refactored booking processing and removed Tooltip from FilterDrawer [[2c34b1e](https://github.com/newgen-business-solutions/cs-event-services/commit/2c34b1ef15e4aa7f4b63992a2fdb4611b5c43450)]
- 🎨 Update SPA in NS account [[8ba3c64](https://github.com/newgen-business-solutions/cs-event-services/commit/8ba3c646b9a15b5c1027d6af56f63c8e2e9e7e90)]
- ♻️ Refactor Scheduler and FilterDrawer components for Venue/Space selection [[e41a591](https://github.com/newgen-business-solutions/cs-event-services/commit/e41a59135e0d65f32770eadbaffcce114fdfc30c)]
- ♻️ Update item price calculation and comparison in cart validation [[50ed06a](https://github.com/newgen-business-solutions/cs-event-services/commit/50ed06a4b2ae00b8546aaaa8f11a94281417051d)]
- ⬆️ Update script code in index.html in booking calendar application [[cb65491](https://github.com/newgen-business-solutions/cs-event-services/commit/cb65491311d38aa2135785509f5511792997831d)]
- ⚡ Optimize booking event retrieval and processing [[e67e174](https://github.com/newgen-business-solutions/cs-event-services/commit/e67e1742042ea50e8ccc4352aed4f8a9f48beb7e)]
- 🎨 Update booking calendar features and improve app styling [[10d3143](https://github.com/newgen-business-solutions/cs-event-services/commit/10d3143f6a7b542836610890180f67c1de8ef244)]
- 🔧 Refactor ESLint configuration in backend app [[72e3052](https://github.com/newgen-business-solutions/cs-event-services/commit/72e3052a3abccd12540bb579d806b5111bd5454f)]
- 🎨 Update New Relic configuration in _app.jsx [[c5ab053](https://github.com/newgen-business-solutions/cs-event-services/commit/c5ab053a1c8d1305d03c6b1dcea9ff909aec8ad8)]
- ♻️ Update date handling and error checking in UE [[1bb8e98](https://github.com/newgen-business-solutions/cs-event-services/commit/1bb8e98f03cf92ca8cc11ed20757baea2214e15e)]
- 🎨 Ignore unused variable in parsePercentString function [[d4da908](https://github.com/newgen-business-solutions/cs-event-services/commit/d4da908aba82408ea884bd9d8a70838f95be8e2a)]
- ♻️ Refactor value parsing method in ng_cses_ue_event_date script [[aae37dc](https://github.com/newgen-business-solutions/cs-event-services/commit/aae37dc13f1f1510cc81f907403980a3ffb4b117)]

### Breaking changes

- 💥 Replace Syncfusion Scheduler with Bryntum Scheduler in Scheduler component [[f9e0738](https://github.com/newgen-business-solutions/cs-event-services/commit/f9e07383fcb6392816972ec4c93bbf81e32eca1f)]

### Removed

- 🔥 Remove unnecessary blank lines and update auth token [[8d52f41](https://github.com/newgen-business-solutions/cs-event-services/commit/8d52f41b186009474c7fd1dd3ebffac98a9263f3)]
- 🔥 Remove index-BGUq6ACa.css from booking-calendar assets [[3950c24](https://github.com/newgen-business-solutions/cs-event-services/commit/3950c24d83adaa1f12741f7046e3ee6b84b57020)]
- 🔥 Delete unused booking-calendar/index.html file [[21cde19](https://github.com/newgen-business-solutions/cs-event-services/commit/21cde19e10b7b5324f993ba656927f0e5c51f00f)]
- 🔥 Remove unused &#x27;material3.css&#x27; file &amp; add all bryntum scss files [[7322c79](https://github.com/newgen-business-solutions/cs-event-services/commit/7322c79134531572acc0791c800dca57337360fa)]
- 🔥 Deprecate &#x60;custrecord_ng_cs_job_type&#x60; field [[048f38d](https://github.com/newgen-business-solutions/cs-event-services/commit/048f38d23aaae42d63368c6c38e43dbca05cd7b5)]
- 🔥 Remove tsconfig.tsbuildinfo in booking-calendar app [[f5d1104](https://github.com/newgen-business-solutions/cs-event-services/commit/f5d1104517ca9b1f4eb609aa1668102e36169fc0)]

### Fixed

- 🐛 Refactor conditional logic in item field handling [[755cd12](https://github.com/newgen-business-solutions/cs-event-services/commit/755cd1249da8c63493e88609d201909ffee15a35)]
- 🐛 Update condition for user interface execution context [[de15848](https://github.com/newgen-business-solutions/cs-event-services/commit/de158482806cdaec2e3c8fa93a51635baf09f945)]
- 🐛 Normalize email to lowercase in user registration [[fa09c91](https://github.com/newgen-business-solutions/cs-event-services/commit/fa09c91ad08688811958fa208d8e0c82e90be2d1)]
- 🐛 Update and optimize scripts for client event operations [[2290664](https://github.com/newgen-business-solutions/cs-event-services/commit/22906647adf05902583835bd8e1db07bffcdd781)]

### Security

- 🔒 Update npmAuthToken in .yarnrc.yml [[b3f3369](https://github.com/newgen-business-solutions/cs-event-services/commit/b3f3369a1e975afc25e92e0a8dc25c2315316c97)]

### Miscellaneous

- 🚀 Merge pull request [#160](https://github.com/newgen-business-solutions/cs-event-services/issues/160) from newgen-business-solutions/CESS-467-feat-material-handling-pricing [[09ea5db](https://github.com/newgen-business-solutions/cs-event-services/commit/09ea5db6836b29077521a9a249d07e005ce4ae1f)]
-  Merge branch &#x27;main&#x27; into CESS-467-feat-material-handling-pricing [[1802f33](https://github.com/newgen-business-solutions/cs-event-services/commit/1802f33395dd2226c7358a99765e74380b9bf9f1)]
-  Merge branch &#x27;CESS-467-feat-material-handling-pricing&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-467-feat-material-handling-pricing [[719ed12](https://github.com/newgen-business-solutions/cs-event-services/commit/719ed129005b12c20701346c762174c70097f934)]
- 📦 Add new minified Alpine.js file [[5d6303d](https://github.com/newgen-business-solutions/cs-event-services/commit/5d6303dbf9acb6531d5c3218789c63dacb759602)]
-  Merge branch &#x27;main&#x27; into CESS-467-feat-material-handling-pricing [[82c584f](https://github.com/newgen-business-solutions/cs-event-services/commit/82c584f45c4964109d0f63b54a6de40e52214c02)]
-  Merge branch &#x27;CESS-467-feat-material-handling-pricing&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-467-feat-material-handling-pricing [[78621b5](https://github.com/newgen-business-solutions/cs-event-services/commit/78621b5911af83bc2514219a7bfe445cab332f0f)]
-  Add freight maximum and corresponding checks in product components [[242da8f](https://github.com/newgen-business-solutions/cs-event-services/commit/242da8f944135a1378dddbbca535e137c3918eab)]
-  Merge branch &#x27;CESS-467-feat-material-handling-pricing&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-467-feat-material-handling-pricing [[0f1cf21](https://github.com/newgen-business-solutions/cs-event-services/commit/0f1cf2115336939c19e4cf2c550bb7d5eefd6c28)]
- 📦 Update Bryntum packages in yarn.lock and package.json [[6764654](https://github.com/newgen-business-solutions/cs-event-services/commit/676465412c709aec8df04f8d0885474853e9b9c7)]
- 🔐 Update npmAuthToken in .yarnrc.yml [[3f3694f](https://github.com/newgen-business-solutions/cs-event-services/commit/3f3694f0df8db72f5c931cb9307fb43cd8562fa8)]
-  Merge branch &#x27;CESS-467-feat-material-handling-pricing&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-467-feat-material-handling-pricing [[c9265b9](https://github.com/newgen-business-solutions/cs-event-services/commit/c9265b99247e79182aa3a00c53ef1022b611e9bb)]
- 👔 Add material handling calculations in sales order [[2a3fcbf](https://github.com/newgen-business-solutions/cs-event-services/commit/2a3fcbf7dfa256dcee019ddae8b71a1f9d7444bc)]
-  Merge branch &#x27;CESS-467-feat-material-handling-pricing&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-467-feat-material-handling-pricing [[22b7912](https://github.com/newgen-business-solutions/cs-event-services/commit/22b79123ea7f62b2d9a6bc0ead7f6b83c134f777)]
-  Merge branch &#x27;CESS-467-feat-material-handling-pricing&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-467-feat-material-handling-pricing [[3291458](https://github.com/newgen-business-solutions/cs-event-services/commit/3291458b116dc24ce9e2bc55726a54cca0b971aa)]
- 🗑️ Deprecate ORIENTATION in ng_cses_enum_cross_ref.js [[5be1b62](https://github.com/newgen-business-solutions/cs-event-services/commit/5be1b62bbb71321d964efae3b863fc9bda727d12)]
- 👔 Map Reduce script for updating material item quantities [[05423c4](https://github.com/newgen-business-solutions/cs-event-services/commit/05423c4d551449eade58ae31314ded54b23595aa)]
- 👔 material handling schedule to item to web [[f41f985](https://github.com/newgen-business-solutions/cs-event-services/commit/f41f985df23990d9198a539254776d8c1470b603)]
- 🗑️ Move ng_cs_clientItem.js to deprecated directory [[13b6370](https://github.com/newgen-business-solutions/cs-event-services/commit/13b63708507881635ee1a5c7c8e3ca02ce12fb25)]
- 🗑️ Move &#x27;ng_cs_ueItem.js&#x27; to deprecated directory [[6107fd3](https://github.com/newgen-business-solutions/cs-event-services/commit/6107fd391d3d89691f0029e5a50a434da685b27a)]
- 🚧 Add new files and update existing ones for improved project flow [[57a1456](https://github.com/newgen-business-solutions/cs-event-services/commit/57a145663d4e4ec1f4f61f9ae3a3607aec837e3b)]
-  Merge branch &#x27;main&#x27; into CESS-168-Booking-Dev-Extreme [[1027be3](https://github.com/newgen-business-solutions/cs-event-services/commit/1027be352fecc5981d7933aa78d1dad36f77437f)]
-  Merge branch &#x27;main&#x27; into CESS-168-Booking-Dev-Extreme [[e04819c](https://github.com/newgen-business-solutions/cs-event-services/commit/e04819cedccf826c244727a91d55efdd53c0ccac)]
- 📝 Add Writerside configuration and resource files [[bbc24b6](https://github.com/newgen-business-solutions/cs-event-services/commit/bbc24b6bae768cb4d7bcd4bc3845715bedb5f922)]
-  Simplify and optimize booking process handling in micro-services [[d1e1870](https://github.com/newgen-business-solutions/cs-event-services/commit/d1e1870405024f58a39ee1c4651bd2559d1d08e3)]
- 📦 Update Bryntum dependencies and add Cheerio [[15f6bb3](https://github.com/newgen-business-solutions/cs-event-services/commit/15f6bb33c6236e1116923c75cc1a86a8f864a766)]
-  Refactor styling of booking events and improve email handling [[693882f](https://github.com/newgen-business-solutions/cs-event-services/commit/693882faa9ac38952546f1240bdb26466f37b429)]
- 📦 Update dependencies and modify authentication configuration [[35cd485](https://github.com/newgen-business-solutions/cs-event-services/commit/35cd4856d18ada18bde1790ec2c04d25e3a42a1d)]
-  Refactor bookings into spaces [[a06c0a5](https://github.com/newgen-business-solutions/cs-event-services/commit/a06c0a5d0da3764310f3011a7e545e75b2ceae70)]
- 📦 Add Bryntum npm scope in .yarnrc.yml and update packages in yarn.lock [[ccad81f](https://github.com/newgen-business-solutions/cs-event-services/commit/ccad81f68e94f732c0cc431c8343bcce54bce784)]
- 🚀 Updated Booking Calendar script elements and packages, upgraded React version [[d1eeaee](https://github.com/newgen-business-solutions/cs-event-services/commit/d1eeaeea65a885f76080e994b341939f4048fc76)]
- 🔨 Replace spawn with exec in booking calendar app [[319ea87](https://github.com/newgen-business-solutions/cs-event-services/commit/319ea8755fdcba83d56531b0df9a6a6cd8cc78e3)]
- 📦 Update package versions across multiple projects [[5bbde4a](https://github.com/newgen-business-solutions/cs-event-services/commit/5bbde4af18cab6c372714f3f6413df01fdc2731c)]
-  Updated booking url and added cache for bookings [[2beaf60](https://github.com/newgen-business-solutions/cs-event-services/commit/2beaf60a18b02fc78136ed7504153b91714a23cd)]
-  Refactor multiple components for the booking calendar [[443c0df](https://github.com/newgen-business-solutions/cs-event-services/commit/443c0df74f462c419a93f9705231013848435d7a)]
-  :folder: Update React SPA booking calendar in NS [[db4c049](https://github.com/newgen-business-solutions/cs-event-services/commit/db4c049e896b75ceda2129bbdacc258340ca73f8)]
- 🚧 Refactor Scheduler and improve data handling [[3c94cbe](https://github.com/newgen-business-solutions/cs-event-services/commit/3c94cbeb1110e58b2612a7fdab256d4f1100e7ec)]
- 📝 Update npm commands and add script reference README [[c578280](https://github.com/newgen-business-solutions/cs-event-services/commit/c578280e36f3ac3de7ea7e922b53301669004e2c)]
- 💹 Implement Partytown library in _app.jsx [[5ce85dd](https://github.com/newgen-business-solutions/cs-event-services/commit/5ce85dd4400399b8476592bb87ce7ab802298bf7)]


<a name="9.17.38"></a>
## 9.17.38 (2024-06-07)

### Changed

- ♻️ Refactor the logic for updating Adv Warehouse Address ([#154](https://github.com/newgen-business-solutions/cs-event-services/issues/154)) [[4773d3e](https://github.com/newgen-business-solutions/cs-event-services/commit/4773d3ea621b8f75c7ca61660e09a3645b43d95a)]
- ♻️ Item Attribute [[cb6a99a](https://github.com/newgen-business-solutions/cs-event-services/commit/cb6a99a83691d5fb3fdf60c7dcc7b65fccef95b8)]
- ♻️ Refactor date format [[b2f0f7e](https://github.com/newgen-business-solutions/cs-event-services/commit/b2f0f7e5317fd8054dd6020fa84bb7068a770422)]
- ♻️ Refactor Order View [[bb46617](https://github.com/newgen-business-solutions/cs-event-services/commit/bb46617d4dee71bd9e29d7cdc725f4f065b17a20)]
- 💬 Add commented parsing code to rcs_get_item_details.js [[07212b6](https://github.com/newgen-business-solutions/cs-event-services/commit/07212b680dd5ff9efea2175bdaeafdeaada6a887)]
- ♻️ Refactor rcs_get_item_details.js script for better code clarity [[a306ade](https://github.com/newgen-business-solutions/cs-event-services/commit/a306ade06ab0f60680538779292cb37c1cb761c5)]
- ♻️ Refactor Get Item Details [[8c38f31](https://github.com/newgen-business-solutions/cs-event-services/commit/8c38f3119daaac44dd4ba9fe0c9627251abc492e)]
- ♻️ Refactor Handle Payment Order [[6644a19](https://github.com/newgen-business-solutions/cs-event-services/commit/6644a190d4589e3eb125f2bb23bc506d900d456f)]
- ♻️ Refactor Labor Table views [[a6da0c0](https://github.com/newgen-business-solutions/cs-event-services/commit/a6da0c008bd73741f27c62707f03e2b89f7a672d)]
- ♻️ Refactor Product Intro [[8182e96](https://github.com/newgen-business-solutions/cs-event-services/commit/8182e96a9fa79bd3408241cbcc15d73675f09a96)]
- ♻️ Refactor Item Attributes [[242e761](https://github.com/newgen-business-solutions/cs-event-services/commit/242e761db447d5ac629bab48344a903efb633d2d)]
- ♻️ Refactor Product Card 1 [[edcc576](https://github.com/newgen-business-solutions/cs-event-services/commit/edcc5763f573e06ac2f740d0e17aab453d6c3160)]
- ♻️ Refactor Get Items Infinite Scroll [[ec19157](https://github.com/newgen-business-solutions/cs-event-services/commit/ec19157395921725687c2085945c89cd2ef1a8f4)]

### Fixed

- 🐛 Fix error on cs event save ([#151](https://github.com/newgen-business-solutions/cs-event-services/issues/151)) [[1f3cd99](https://github.com/newgen-business-solutions/cs-event-services/commit/1f3cd99d7469e0a386a34388da977d0091afb4ac)]
- 🐛 Fixed saving event with no endtime ([#150](https://github.com/newgen-business-solutions/cs-event-services/issues/150)) [[48de065](https://github.com/newgen-business-solutions/cs-event-services/commit/48de06588e76a97179115993faace5d4fe5a5003)]

### Miscellaneous

- 💥 Feat: Item Attributes  [#153](https://github.com/newgen-business-solutions/cs-event-services/issues/153) [[71534d2](https://github.com/newgen-business-solutions/cs-event-services/commit/71534d223df020d4a1299193ac084ae197020f96)]
-  Merge branch &#x27;main&#x27; into CESS-408-Item-Attributes [[68ad128](https://github.com/newgen-business-solutions/cs-event-services/commit/68ad1284c27ca2ebc76ebc354eed3f45bd7362ff)]
-  Merge branch &#x27;CESS-408-Item-Attributes&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-408-Item-Attributes [[cd40ca6](https://github.com/newgen-business-solutions/cs-event-services/commit/cd40ca6184f2dd4bf2ac8055ecc76ec139ae1e12)]
-  Delete packages/eslint-config/runConfigs/.env [[d00d6f8](https://github.com/newgen-business-solutions/cs-event-services/commit/d00d6f81c1f7886dfaf12cda3a4c860ee685b150)]
-  Merge branch &#x27;CESS-408-Item-Attributes&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CESS-408-Item-Attributes [[b9116f4](https://github.com/newgen-business-solutions/cs-event-services/commit/b9116f42a46713b604af4cd9909373a27e835a0b)]
-  Merge branch &#x27;main&#x27; into CESS-408-Item-Attributes [[0935aa6](https://github.com/newgen-business-solutions/cs-event-services/commit/0935aa6bd38b6459cfc6b51879b1f775cd33a7ed)]


<a name="9.16.35"></a>
## 9.16.35 (2024-05-24)

### Added

- ✨ FEAT(BE): Estimate Approval Link Button ([#144](https://github.com/newgen-business-solutions/cs-event-services/issues/144)) [[7b4c777](https://github.com/newgen-business-solutions/cs-event-services/commit/7b4c7771505ed5d796ea92632bd33f98521aeb83)]
- 🎉 Add Party-town lib for prod SWs [[3b3feb7](https://github.com/newgen-business-solutions/cs-event-services/commit/3b3feb70ab88cbeab7cf7a6fa4dfcde3a809d326)]
- 📈 Add(web) New Relic Updated Snippet [[baf2598](https://github.com/newgen-business-solutions/cs-event-services/commit/baf259880bc6d58e3f3c4a83280dbefed8fd78c0)]
- ➕ Add Email Template to project. [[a469cd1](https://github.com/newgen-business-solutions/cs-event-services/commit/a469cd156d429cfbbffdfa07431fb6e286435e85)]
- ➕ Add(web) forgot-password page [[d3bc603](https://github.com/newgen-business-solutions/cs-event-services/commit/d3bc603752bb6122c51b314fcf0b540aa2bfa8f3)]
- ✨ Add new on-boarding process [[201b600](https://github.com/newgen-business-solutions/cs-event-services/commit/201b60061ecc464b6239f745592549a9a51b9322)]
- ➕ Add Email Template to project. [[880f44b](https://github.com/newgen-business-solutions/cs-event-services/commit/880f44b011978096444674a812ad320a2dbd82e4)]
- ➕ Add(web) forgot-password page [[183e877](https://github.com/newgen-business-solutions/cs-event-services/commit/183e877d8c697b34eaf608a258692b142ba23326)]

### Changed

- ♻️ Refactor Exhibitor CSV Import ([#149](https://github.com/newgen-business-solutions/cs-event-services/issues/149)) [[1d4a927](https://github.com/newgen-business-solutions/cs-event-services/commit/1d4a9273c831b3e593ab60fefe813707d4bd8b32)]
- ♻️ Refactor Estimate UE ([#148](https://github.com/newgen-business-solutions/cs-event-services/issues/148)) [[90b8cc1](https://github.com/newgen-business-solutions/cs-event-services/commit/90b8cc1f8bf24b8c117fe90c170b4f2dedae920d)]
- ♻️ Refactor Show Summary Report ([#146](https://github.com/newgen-business-solutions/cs-event-services/issues/146)) [[33fb32a](https://github.com/newgen-business-solutions/cs-event-services/commit/33fb32a38c6aabf5747fcfda0c2e2836b17016d9)]
- ♻️ Refactor Invoice UE ([#147](https://github.com/newgen-business-solutions/cs-event-services/issues/147)) [[a988dac](https://github.com/newgen-business-solutions/cs-event-services/commit/a988dacd6d27fe55f44f4d2acff68faa12fae3c9)]
- ♻️ Refactor Signup &amp; Password Reset [[0680bed](https://github.com/newgen-business-solutions/cs-event-services/commit/0680bed695661083db435cbc71902b425448c319)]
- ♻️ Refactor Contact UE [[d226764](https://github.com/newgen-business-solutions/cs-event-services/commit/d22676499518c47f386f93a1065e0aced4f37227)]
- ♻️ Refactor Delivery By Product Suitelet [[5e2120a](https://github.com/newgen-business-solutions/cs-event-services/commit/5e2120a8f6765693864bb7d77f6d30932671c918)]
- ♻️ Refactor Pull List and Delivery By Product Reports [[e15be0c](https://github.com/newgen-business-solutions/cs-event-services/commit/e15be0c3bd1a07586adf35b71584d0814e5ffe2d)]
- ♻️ Refactor Sales Order Client [[950b262](https://github.com/newgen-business-solutions/cs-event-services/commit/950b262dfcf5166e98b6a301abae66d713a1735a)]
- ⚡ Add parseFloat function to supervisionMarkup in ng_cses_ue_event_date script [[d739342](https://github.com/newgen-business-solutions/cs-event-services/commit/d7393425960cfae56f944f93d79b3df0fb1efe13)]
- ⚡ Enhance date handling and logging in event scheduling. [[a02d053](https://github.com/newgen-business-solutions/cs-event-services/commit/a02d05377defd568aef5ff54bbdd2323f235e66a)]
- 🔧 Add TypeScript configuration for Vite and React [[94b26cd](https://github.com/newgen-business-solutions/cs-event-services/commit/94b26cd51ed948cb38401fedfc5a95bbcfca2094)]
- 🎨 Reorganize event date related files to new structure [[dbd6437](https://github.com/newgen-business-solutions/cs-event-services/commit/dbd6437ade5df658e6c9b2664861c7eead53bff5)]
- ♻️ Refactor code to cleanup parameters and improve readability [[9d0e240](https://github.com/newgen-business-solutions/cs-event-services/commit/9d0e240dc5bed10b6c1aee6c7d6f9739bb8b8992)]
- ⚡ Optimize(web) all imports Optimize all imports in &#x60;web&#x60; and adjust ESLint config for next.js configurations. [[c4a3142](https://github.com/newgen-business-solutions/cs-event-services/commit/c4a31424deb2e8d4da48fe6e7448c3bca6843f37)]
- 🔧 Update TypeScript configurations and dependencies [[56fe584](https://github.com/newgen-business-solutions/cs-event-services/commit/56fe584c73d805eece5e9936ee52421d422577c9)]
- ⬆️ Bump jose from 4.15.4 to 4.15.5 [[9521a9e](https://github.com/newgen-business-solutions/cs-event-services/commit/9521a9edf3e42465e3c133669ddc2d49bd902ce9)]
- ⬆️ Bump jose from 4.15.4 to 4.15.5 in /apps/web [[e308687](https://github.com/newgen-business-solutions/cs-event-services/commit/e3086878e620de5280f1bb05c0c73e568570a868)]
- ⬆️ Bump express in /services/booking-calendar-proxy [[edf68c5](https://github.com/newgen-business-solutions/cs-event-services/commit/edf68c5587b3074c8299e910f92913e468115dfb)]
- ⬆️ Bump follow-redirects in /services/booking-calendar-proxy [[b409266](https://github.com/newgen-business-solutions/cs-event-services/commit/b409266726ab2da6f6fb3d9b32178b5ccc969eed)]
- ⬆️ Bump vite from 4.5.2 to 4.5.3 in /apps/booking-calendar [[8746b2a](https://github.com/newgen-business-solutions/cs-event-services/commit/8746b2aff7876b3fc8590e697862fed3d21cc732)]
- 🔧 Chore(web) Add &#x60;partytown&#x60; to prod [[a619e64](https://github.com/newgen-business-solutions/cs-event-services/commit/a619e648e9c7beec9b67354226184871498eadca)]
- 🔧 Chore(web) add web workers [[e9a3e1d](https://github.com/newgen-business-solutions/cs-event-services/commit/e9a3e1d48423b033b88780236b4e62a9a347969b)]
- ⚡ Optimize for PW reset email lookup [[9eeeadc](https://github.com/newgen-business-solutions/cs-event-services/commit/9eeeadc1938485b1d014c95dc484fcc713d28ae5)]
- 🎨 Add(web) Mui Link to Signup [[795859c](https://github.com/newgen-business-solutions/cs-event-services/commit/795859c30e0b0e9701ad7c520afed083087dbc13)]
- ⬆️ Update version and CHANGELOG [[4be2487](https://github.com/newgen-business-solutions/cs-event-services/commit/4be24872b2227f075908d3da5a62baa82f8f423c)]
- ♻️ Refactor Handle Payment Order Restlet [[23002d4](https://github.com/newgen-business-solutions/cs-event-services/commit/23002d47a94ef80b1ce92021c9240b455c1ade3e)]
- ⚡ Optimize for PW reset email lookup [[281bbf8](https://github.com/newgen-business-solutions/cs-event-services/commit/281bbf884dacd0fa53242658462bdfb8c1b6ad57)]
- 🎨 Add(web) Mui Link to Signup [[5c36b62](https://github.com/newgen-business-solutions/cs-event-services/commit/5c36b62f513adde01fad52084b52392b5824e24f)]
- ⬆️ Update version and CHANGELOG [[ca1b2c4](https://github.com/newgen-business-solutions/cs-event-services/commit/ca1b2c4ecd8e60e3606ade5a408bd44e9e71b6a3)]
- ⬆️ Bump follow-redirects in /apps/booking-calendar [[067a07c](https://github.com/newgen-business-solutions/cs-event-services/commit/067a07c8590aceae0ed97bbe23fcac76c81ad570)]
- ⬆️ Bump follow-redirects from 1.15.4 to 1.15.6 in /apps/web [[4107149](https://github.com/newgen-business-solutions/cs-event-services/commit/41071498b8cffea00bd79a739e7621a91827cb44)]

### Removed

- 🔥 Remove(web) worker strategy [[3fa6abe](https://github.com/newgen-business-solutions/cs-event-services/commit/3fa6abef1830db0e2836baada68465c6920a27a5)]

### Fixed

- 🚑 Fix(BE) Status lookup null handling [[48a5f0a](https://github.com/newgen-business-solutions/cs-event-services/commit/48a5f0ac66e891b9dd4c490defbb25306dd64ff3)]
- 🚑 Labor Table &amp; Ordering Supervision Patch [#140](https://github.com/newgen-business-solutions/cs-event-services/issues/140) [[6a059e0](https://github.com/newgen-business-solutions/cs-event-services/commit/6a059e05dd06379ba93595e3eb5b3965e5586007)]
- 🚑 Fix(API) Method Not Allowed 405 [[205321b](https://github.com/newgen-business-solutions/cs-event-services/commit/205321bf8ab0ff5149da4936bfb02dc8a7cd145f)]
- 🐛 Fix(web) API response to return email [[7761676](https://github.com/newgen-business-solutions/cs-event-services/commit/7761676d83975470cd9bff818719313c47284bb6)]
- ✏️ Update(BE) email send with recipient [[f25c1a5](https://github.com/newgen-business-solutions/cs-event-services/commit/f25c1a5b2895495da7e0373641e9b16b332e8179)]
- 🐛 Fix(web) API response to return email [[43464e9](https://github.com/newgen-business-solutions/cs-event-services/commit/43464e9613d6e619b9f21719f8976bad07879844)]
- ✏️ Update(BE) email send with recipient [[b963426](https://github.com/newgen-business-solutions/cs-event-services/commit/b9634268be038aa4be217f2705d3c77b7f028031)]

### Miscellaneous

- 💹 Update &#x60;new-relic&#x60; analytics [[09ab89f](https://github.com/newgen-business-solutions/cs-event-services/commit/09ab89f0b0aff081705d5583ebd4c41bd45d7862)]
-  Update ng_cses_ue_event_date.js [[eac1890](https://github.com/newgen-business-solutions/cs-event-services/commit/eac18901b189b321eaef128a7ff9322c55ba7378)]
-  Update ng_cses_ue_event_date.js [[0c4f793](https://github.com/newgen-business-solutions/cs-event-services/commit/0c4f793f2843b8aba7439d80af236583e1aae09b)]
-  Delete user event/ng_cses_ue_cs_show_date.js [[db7821c](https://github.com/newgen-business-solutions/cs-event-services/commit/db7821c3ec09e276dbe12eb0c8fc797dc3c4de53)]
-  :fix: Add helper function for percentage parsing and improve event logging [[154bbe5](https://github.com/newgen-business-solutions/cs-event-services/commit/154bbe5158433ec0aee96dccd12993d818b89425)]
-  Merge branch &#x27;CSES-274-Booking-Calendar-Prototype&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-274-Booking-Calendar-Prototype [[f462ff8](https://github.com/newgen-business-solutions/cs-event-services/commit/f462ff886fc2b0af4dfc26dbfddef50b08164699)]
-  Merge pull request [#128](https://github.com/newgen-business-solutions/cs-event-services/issues/128) from newgen-business-solutions/dependabot/npm_and_yarn/apps/web/follow-redirects-1.15.6 [[ccaa9d5](https://github.com/newgen-business-solutions/cs-event-services/commit/ccaa9d537ab0381878bf7ad4946db35be229b63a)]
-  Merge branch &#x27;CSES-274-Booking-Calendar-Prototype&#x27; into dependabot/npm_and_yarn/apps/web/follow-redirects-1.15.6 [[95d89f1](https://github.com/newgen-business-solutions/cs-event-services/commit/95d89f19a89693566978b5507de9c0b63b22a6fc)]
-  Merge pull request [#129](https://github.com/newgen-business-solutions/cs-event-services/issues/129) from newgen-business-solutions/dependabot/npm_and_yarn/apps/booking-calendar/follow-redirects-1.15.6 [[a183bb7](https://github.com/newgen-business-solutions/cs-event-services/commit/a183bb79f19b849cdb28b18aa2765125f677397c)]
-  Merge branch &#x27;CSES-274-Booking-Calendar-Prototype&#x27; into dependabot/npm_and_yarn/apps/booking-calendar/follow-redirects-1.15.6 [[24a5372](https://github.com/newgen-business-solutions/cs-event-services/commit/24a5372d57b23d52ae399c49af129425dcce9405)]
-  Merge pull request [#125](https://github.com/newgen-business-solutions/cs-event-services/issues/125) from newgen-business-solutions/dependabot/npm_and_yarn/jose-4.15.5 [[f093509](https://github.com/newgen-business-solutions/cs-event-services/commit/f0935096e025e153e8642ed1269314cd2d9a1b4f)]
-  Merge branch &#x27;CSES-274-Booking-Calendar-Prototype&#x27; into dependabot/npm_and_yarn/jose-4.15.5 [[0dfedf6](https://github.com/newgen-business-solutions/cs-event-services/commit/0dfedf66e17949c98561f6737f5d0be46da72f6f)]
- 📦 Update &#x60;yarn.lock&#x60; [[f7cbe36](https://github.com/newgen-business-solutions/cs-event-services/commit/f7cbe36e4fe028c76976a3ecfce39e9b7f4d8109)]
-  Merge branch &#x27;CSES-274-Booking-Calendar-Prototype&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-274-Booking-Calendar-Prototype [[216657b](https://github.com/newgen-business-solutions/cs-event-services/commit/216657b816200433568a4962d0eec7c216f702dd)]
-  Merge branch &#x27;main&#x27; into CSES-274-Booking-Calendar-Prototype [[3bea686](https://github.com/newgen-business-solutions/cs-event-services/commit/3bea686e1832fb2b6d7c7482f516f8b996d1c753)]
-  Merge branch &#x27;main&#x27; into dependabot/npm_and_yarn/apps/web/follow-redirects-1.15.6 [[7792834](https://github.com/newgen-business-solutions/cs-event-services/commit/77928348358b4d4b88c8f925c77a7a80f8a85bd3)]
-  Merge branch &#x27;main&#x27; into dependabot/npm_and_yarn/apps/booking-calendar/follow-redirects-1.15.6 [[8921c31](https://github.com/newgen-business-solutions/cs-event-services/commit/8921c312c7d104dfbd3e0b49952c3fa1e4e26a3d)]
- 📝 Update(web) pw lang grammar fix [[5dbbf82](https://github.com/newgen-business-solutions/cs-event-services/commit/5dbbf823562ec5c166516ef0414cd2a852edae5c)]
- 📝 Update(web) with Netsuite pw lang [[a0193ac](https://github.com/newgen-business-solutions/cs-event-services/commit/a0193acdd2347e06a1dfec11777e970b73e39e22)]
- 📝 Update(BE) with customer contact association email [[3bf5f9b](https://github.com/newgen-business-solutions/cs-event-services/commit/3bf5f9b77a210f447f4f38463de3e8610f3a818f)]
- 📝 Update API README.md [[99dd5aa](https://github.com/newgen-business-solutions/cs-event-services/commit/99dd5aa389675c4092f6cc0fef1a74ef171f73a0)]
- 📝 Change(web) typo in alert type [[d018604](https://github.com/newgen-business-solutions/cs-event-services/commit/d018604343e9d9b82902665c8d6b7d2131916a41)]
- 📝 Create README for API docs. [[6a55f3d](https://github.com/newgen-business-solutions/cs-event-services/commit/6a55f3d165fa5e2bc459cd197aa4136b9b2a2df0)]
- 📝 Update(web) pw lang grammar fix [[5e94cb0](https://github.com/newgen-business-solutions/cs-event-services/commit/5e94cb0ec35b9322d4a7d94f3cebf6e2a65dd3ce)]
- 📝 Update(web) with Netsuite pw lang [[59bfa37](https://github.com/newgen-business-solutions/cs-event-services/commit/59bfa3702f302cead8acc20bba7286cab597b867)]
- 📝 Update(BE) with customer contact association email [[8e6f93e](https://github.com/newgen-business-solutions/cs-event-services/commit/8e6f93efb26ca4eb99ce504002f33bd8d2949e0d)]
-  Merge branch &#x27;CSES-274-Booking-Calendar-Prototype&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-274-Booking-Calendar-Prototype [[6c103cb](https://github.com/newgen-business-solutions/cs-event-services/commit/6c103cb19950a9de0478cf589cbe38a75e18e270)]
- 📝 Update API README.md [[974eb56](https://github.com/newgen-business-solutions/cs-event-services/commit/974eb56198c40486c019a05e02c0f6e527b83434)]


<a name="9.14.5"></a>
## 9.14.5 (2024-04-03)

### Added

- ✨ Add new on-boarding process [[11fb99d](https://github.com/newgen-business-solutions/cs-event-services/commit/11fb99d660f92787f92e8d46917bc8a265ffb950)]

### Changed

- ♻️ Refactor Handle Payment Order Restlet [[660a2c7](https://github.com/newgen-business-solutions/cs-event-services/commit/660a2c76ca7680aa3a99ef6a5a22078e68e25244)]
- ⚡ Optimize(web) Event date to have a default format [[9fa4fae](https://github.com/newgen-business-solutions/cs-event-services/commit/9fa4faeb99ba2aa4a3b2a49d75ce2e6f2733af03)]
- 🔧 Update(web) ESLint next pkg config [[48c853e](https://github.com/newgen-business-solutions/cs-event-services/commit/48c853e0a396c1598c33aa1bc86231359c1264e1)]
- ⚡ Optimize(web) Imports, warnings, and format [[41d24d3](https://github.com/newgen-business-solutions/cs-event-services/commit/41d24d3d7dc5c27699fc41ef4f76dc83b0118c06)]
- ⚡ Optimize(REST) Backend RESTlet for Demo account [[7294d7c](https://github.com/newgen-business-solutions/cs-event-services/commit/7294d7c185468de5bae2c12e877faefeec5d9966)]
- 🔧 Update(IDE) ESLint Config [[7295eef](https://github.com/newgen-business-solutions/cs-event-services/commit/7295eef7ee9925258d3e6588041cf70da218be9e)]
- ♻️ REF(web) imports and lint suggestions [[e26f64f](https://github.com/newgen-business-solutions/cs-event-services/commit/e26f64fd03d873fc6163357d5eb7223a153a9296)]
- ⚡ Optimize(web) Event date detection [[3c12b9a](https://github.com/newgen-business-solutions/cs-event-services/commit/3c12b9a8cf11b7afef3408e7435ed49bbdf4c21f)]
- ⚡ Optimize(web) Event Expiration Detection [[a9eefb4](https://github.com/newgen-business-solutions/cs-event-services/commit/a9eefb463081cd4defb510eb846d192b60d6b92e)]
- ♻️ REF(REST) Data Manager Syncfusion [[372a726](https://github.com/newgen-business-solutions/cs-event-services/commit/372a726389fd581c4ae5f9199479a610ea868bab)]
- 🔧 Update(IDE) Project config for copilot [[cb111ee](https://github.com/newgen-business-solutions/cs-event-services/commit/cb111ee30e9b0fb0c0dd82d849484370b863f140)]

### Breaking changes

- 💥 Update &#x60;@mui&#x60; to &#x60;5.15.14&#x60; [[a61e0ea](https://github.com/newgen-business-solutions/cs-event-services/commit/a61e0ea8d6f10ec99c7a2b98e05c11e8d46a37ed)]

### Fixed

- 🍎 Add(REST) Settings default date format [[4bda225](https://github.com/newgen-business-solutions/cs-event-services/commit/4bda22500e6f12b74db17952d7724bd2cb66d4e3)]
- 🍎 Fix(web) Collection Card Image [[12a32c7](https://github.com/newgen-business-solutions/cs-event-services/commit/12a32c73e4d7c1073d8aa16ea57334109cd2724f)]

### Miscellaneous

-  Merge branch &#x27;CSES-274-Booking-Calendar-Prototype&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-274-Booking-Calendar-Prototype [[b1ed468](https://github.com/newgen-business-solutions/cs-event-services/commit/b1ed468a42edcc83a219fc898d02ce7d5ce74fbc)]
- 📝 Change(web) typo in alert type [[6829e80](https://github.com/newgen-business-solutions/cs-event-services/commit/6829e803ce2aa3b2dba250a6b144fa3c57878599)]
-  Merge branch &#x27;main&#x27; into CSES-274-Booking-Calendar-Prototype [[31ce58a](https://github.com/newgen-business-solutions/cs-event-services/commit/31ce58ad2387220680467aeed78989171ab31d05)]
- 📝 Create README for API docs. [[d4817bb](https://github.com/newgen-business-solutions/cs-event-services/commit/d4817bbcae5786fcc5a6d83fb2bd1325b527d676)]
-  Merge pull request [#136](https://github.com/newgen-business-solutions/cs-event-services/issues/136) from newgen-business-solutions/CSES-274-Booking-Calendar-Prototype [[00f7f1e](https://github.com/newgen-business-solutions/cs-event-services/commit/00f7f1ecec85066bea616609cc8b554de969d353)]
-  Merge branch &#x27;main&#x27; into CSES-274-Booking-Calendar-Prototype [[9ed4e7d](https://github.com/newgen-business-solutions/cs-event-services/commit/9ed4e7de56982e03b724056a1a7e8884ee435d8f)]
- 📦 Update linked dependencies [[aedbefa](https://github.com/newgen-business-solutions/cs-event-services/commit/aedbefae7023785e73d94f0dadc822b3cb04eb77)]
- 💥 Update &#x60;mui/material&#x60; to &#x60;v5.15.14&#x60; ([#135](https://github.com/newgen-business-solutions/cs-event-services/issues/135)) [[0635356](https://github.com/newgen-business-solutions/cs-event-services/commit/0635356511658bd9e993c83e4210ac73ac56ccef)]
-  Create pr agent.yml [[b6d5a6d](https://github.com/newgen-business-solutions/cs-event-services/commit/b6d5a6d7dcd60df8183687ffc2ca4e20a839aeae)]
- 🔨 Update(service) Booking cal routes [[54ca7f5](https://github.com/newgen-business-solutions/cs-event-services/commit/54ca7f5e3ab069268edf18bc146c2c14283c281b)]
- 🔨 Update(SPA) Booking calendar build [[dbf57db](https://github.com/newgen-business-solutions/cs-event-services/commit/dbf57dbffacd5ee9e2171b19fff657fd6a3a482e)]


<a name="9.13.1"></a>
## 9.13.1 (2024-03-26)

### Changed

- ♻️ REF(REST) Data Manager Syncfusion [[372a726](https://github.com/newgen-business-solutions/cs-event-services/commit/372a726389fd581c4ae5f9199479a610ea868bab)]
- 🔧 Update(IDE) Project config for copilot [[cb111ee](https://github.com/newgen-business-solutions/cs-event-services/commit/cb111ee30e9b0fb0c0dd82d849484370b863f140)]
- 🔧 Chore(turbo) update outputs for web [[05de7da](https://github.com/newgen-business-solutions/cs-event-services/commit/05de7da8c4a58a3ff15a6ccfd52743fc3e3678e3)]
- ⚡ Optimize(web) Lint and DateAdapter [[9ca8f90](https://github.com/newgen-business-solutions/cs-event-services/commit/9ca8f901c6166fe8400d504a5244e455d2614d9a)]
- ♻️ REF(REST) Related shows query to match date [[9a3022e](https://github.com/newgen-business-solutions/cs-event-services/commit/9a3022e09e98faf0489aee8da132784fc107256e)]
- 🔧 Update ngork shell run [[a959807](https://github.com/newgen-business-solutions/cs-event-services/commit/a959807d208629a1d4fe6749d84de3feed7226e4)]
- ⚡ Optimize(web) react rules including hooks [[1660c16](https://github.com/newgen-business-solutions/cs-event-services/commit/1660c1640b646ec0667039ffa24b5dd3b351c698)]
- ♻️ REF(micro-service) Booking Calendar [[0c3e2fa](https://github.com/newgen-business-solutions/cs-event-services/commit/0c3e2fa4130c0b6b39209fea0c7a4ee1f84a9ceb)]
- 🔧 Chore(OXC) Set up mono-repo with autolint [[09cac26](https://github.com/newgen-business-solutions/cs-event-services/commit/09cac269f839d229e3e3df018ae75f55aef2317a)]
- ⚡ Optimize(UE) Event date setting [[b0d40c7](https://github.com/newgen-business-solutions/cs-event-services/commit/b0d40c72ef7d15233a64f081a5c45a6a5427e91e)]
- 🔧 Update booking calendar setup [[cc2bf2b](https://github.com/newgen-business-solutions/cs-event-services/commit/cc2bf2bb999c14db1fb8a111b482573aa1591700)]

### Breaking changes

- 💥 Merge pull request [#131](https://github.com/newgen-business-solutions/cs-event-services/issues/131) CSES-274-Booking-Calendar-Prototype [[84f94b9](https://github.com/newgen-business-solutions/cs-event-services/commit/84f94b9983fe83f0928bfaf421008a4c01edc979)]

### Miscellaneous

- 🔨 Update(service) Booking cal routes [[54ca7f5](https://github.com/newgen-business-solutions/cs-event-services/commit/54ca7f5e3ab069268edf18bc146c2c14283c281b)]
- 🔨 Update(SPA) Booking calendar build [[dbf57db](https://github.com/newgen-business-solutions/cs-event-services/commit/dbf57dbffacd5ee9e2171b19fff657fd6a3a482e)]
- 📦 Update turbo, mui-pickers, &amp; date-fns [[d6686bb](https://github.com/newgen-business-solutions/cs-event-services/commit/d6686bb44085ced1f49b2b6549b3c06cd0aef990)]
- 📝 Update Changelog [[0163ae5](https://github.com/newgen-business-solutions/cs-event-services/commit/0163ae562f61921a45bbab5c02bf0d09afcdc2b1)]


<a name="9.12.83"></a>
## 9.12.83 (2024-03-22)

### Added

- ➕ Add(SPA) Booking stores [[ff53146](https://github.com/newgen-business-solutions/cs-event-services/commit/ff531463ce0db8c46bc622f3e0c05127528934f3)]
- ➕ Add(CS EVENT) Tax Group tax detection [[5d000f7](https://github.com/newgen-business-solutions/cs-event-services/commit/5d000f7a4f9c05b664e63af3514cdb5953b4d1a3)]
- ✨ Add new pull list 2.1 [[8b87bb8](https://github.com/newgen-business-solutions/cs-event-services/commit/8b87bb8e1eb35d5267df1d9eeab16e3dbb77f96a)]

### Changed

- ⬆️ Update booking calendar build [[faa81d5](https://github.com/newgen-business-solutions/cs-event-services/commit/faa81d59145f7c9a42b54a6d080848a943ee514e)]
- ⚡ Optimize(UE) CS Event for dynamic date ranges [[b71f181](https://github.com/newgen-business-solutions/cs-event-services/commit/b71f18165956b1c413ba814885deef0e58fa252b)]
- ♻️ REF(SL) Booking calendar serve [[78b3135](https://github.com/newgen-business-solutions/cs-event-services/commit/78b31354ccf2b7544f877e1b1bbc15450b61a78e)]
- ♻️ REF(Rest) Account info to support employee [[ccc0338](https://github.com/newgen-business-solutions/cs-event-services/commit/ccc0338fcb735957b9c55a8fff8398c4b4e4e274)]
- ♻️ REF(RL) Booking calendar Micro Service [[0c57bf5](https://github.com/newgen-business-solutions/cs-event-services/commit/0c57bf5bb04e8db5edc634038d386490b7f7bb0e)]
- ⚡ Optimize(RL) Add line price query and format [[1a660a4](https://github.com/newgen-business-solutions/cs-event-services/commit/1a660a4b553b35591a4ce83734f559526c1a8dab)]
- ♻️ REF(BC) Booking Calendar to Tailwind [[f9ca9df](https://github.com/newgen-business-solutions/cs-event-services/commit/f9ca9df63f3b6b660fdf87ecede04e02ff27698d)]
- ⚡ Optimize(UE) deposit for delete [[cd17fc8](https://github.com/newgen-business-solutions/cs-event-services/commit/cd17fc86f0f0c9984e0769daee420e193d7ee62d)]
- ♻️ Update(UE) Deposit with &#x60;xedit&#x60; case [[c61a9d0](https://github.com/newgen-business-solutions/cs-event-services/commit/c61a9d0ae920b105ece4494d39dff645b8c28f42)]
- ♻️ Refactor(deposit) conv fee ref to SO [[4634ef6](https://github.com/newgen-business-solutions/cs-event-services/commit/4634ef6063d640e92138b6203be859cdeee078ff)]
- 🔧 Update(web) MUI-X License Key [[4a85cdc](https://github.com/newgen-business-solutions/cs-event-services/commit/4a85cdc7edad04dedb3cf25d758579e0d46a53c2)]
- ⚡ Optimize(page) Login &amp; Signup imports [[7b14e81](https://github.com/newgen-business-solutions/cs-event-services/commit/7b14e813e3b1a3335cd3247408d1d05a28d00493)]
- 🎨 Update(modal) PW Update Modal &amp; UX [[52eabdf](https://github.com/newgen-business-solutions/cs-event-services/commit/52eabdf1a13a2979d0a0f1bf6599c58e53f00645)]
- ⚡ Optimize(page) Login/Signup Logo imgs [[eb0e371](https://github.com/newgen-business-solutions/cs-event-services/commit/eb0e371c20c2c6342baadfd29ae8ec49443c2018)]
- 🔧 Add(IDE) Ngrok run config [[f9d3f22](https://github.com/newgen-business-solutions/cs-event-services/commit/f9d3f22c91eb3f6df76445f1c5ccbd9877c73bdd)]
- 🎨 Update(page) order confirmation image [[4a6203e](https://github.com/newgen-business-solutions/cs-event-services/commit/4a6203e4ecda4ea4c30af00f7ee87e3994438ab0)]
- ♻️ Ref(web) Change home return location [[14975dd](https://github.com/newgen-business-solutions/cs-event-services/commit/14975dd9c52d23e6be4038ef361273e65e35140f)]
- ⚡ Optimize(BE) Balance updates [[c95514b](https://github.com/newgen-business-solutions/cs-event-services/commit/c95514b524651b2e8e6d041d00461f1018961876)]
- 🎨 Change(page) order confirmation icon [[427d10c](https://github.com/newgen-business-solutions/cs-event-services/commit/427d10c26766e14431f1dbbbbd32cb0ea54e494b)]
- 🔧 Update ngrok config [[92c7dbe](https://github.com/newgen-business-solutions/cs-event-services/commit/92c7dbe63e27121b77e3f2616b626df4b8d87eb6)]
- ⚡ Optimize(MR) PW Updating via Map/reduce [[c749ef9](https://github.com/newgen-business-solutions/cs-event-services/commit/c749ef9c9737d6b02a969d37d9518a0082702401)]
- ♻️ Refactor CS Item [[fe10420](https://github.com/newgen-business-solutions/cs-event-services/commit/fe1042084e9448556cae99f3745add8b6a094712)]
- ♻️ Refactor Exhibitor Password Reset [[bd7bcf4](https://github.com/newgen-business-solutions/cs-event-services/commit/bd7bcf4d23ccba20414d9c3c1eff1c4731123821)]
- ♻️ Refactor Exhibitor Password Reset Suitelet [[ad0aa8d](https://github.com/newgen-business-solutions/cs-event-services/commit/ad0aa8d2ca097bc3f5d2c07330f0898bcc011ec6)]
- ♻️ Refactor Exhibitor Password Updater [[7482906](https://github.com/newgen-business-solutions/cs-event-services/commit/748290632c49b21c12714b76e235a70c5a883030)]
- ♻️ Refactor Exhibitor Password Updater [[9475f74](https://github.com/newgen-business-solutions/cs-event-services/commit/9475f7432aa7acdbf2383e902d0a2bb6e04eb7c5)]
- ♻️ Refactor Exhibitor Password Update [[0bce802](https://github.com/newgen-business-solutions/cs-event-services/commit/0bce802907782d5f3466dbf0b6ca603964fa2b79)]
- ⚡ Optimize(REST) Get Current User RL [[c811661](https://github.com/newgen-business-solutions/cs-event-services/commit/c811661beb9ffb5fdd04d6c19147a5e89d8d22a2)]
- ♻️ REF(SO) UE for balance and paid [[c9108fc](https://github.com/newgen-business-solutions/cs-event-services/commit/c9108fc3cebc23a6fc24a0676676a071faa35c83)]
- 🎨 Format Pull list 1.0 files [[f8c488a](https://github.com/newgen-business-solutions/cs-event-services/commit/f8c488af4ac43dfc168529dfad3e63c2223b56db)]
- 🔧 Update hooks to include all &#x60;dates&#x60; [[9942796](https://github.com/newgen-business-solutions/cs-event-services/commit/99427964dc5ba1c33458ae062261790a97ef2789)]
- ♻️ REF(API) Event  Selection Filter [[e1b6c9e](https://github.com/newgen-business-solutions/cs-event-services/commit/e1b6c9ec7487940541a4eff1578ee36fb4c4ad3f)]

### Removed

- 🔥 Remove(RL) Uneeded code [[00c474a](https://github.com/newgen-business-solutions/cs-event-services/commit/00c474ac70019bd0ee64db4eb12e4d0290998ba7)]
- 🔥 Remove(UE) git SHAs on deposit [[e3fa72f](https://github.com/newgen-business-solutions/cs-event-services/commit/e3fa72fb58c3f2090ce48d10182ecb0bdda75582)]
- 🔥 Remove old checkout page [[58395d3](https://github.com/newgen-business-solutions/cs-event-services/commit/58395d33e20315064592f9e2f1a3801a30ba2574)]
- 🔥 Removed Deprecated Fields [[5246fde](https://github.com/newgen-business-solutions/cs-event-services/commit/5246fded1288ab36033198e791157355bb243816)]
- 🔥 Remove global booth logic [[81b354d](https://github.com/newgen-business-solutions/cs-event-services/commit/81b354dce2775924ed94beaa1ae4709d1b610a3b)]
- 🔥 Remove &#x60;custrecord_ng_cs_use_global_booth_ord&#x60; [[57ddb20](https://github.com/newgen-business-solutions/cs-event-services/commit/57ddb2079adcd54eddd149821fd34ce1c2f75b55)]
- 🔥 Remove &#x60;custitem_ng_cs_web_primary_image_url&#x60; [[154cf7e](https://github.com/newgen-business-solutions/cs-event-services/commit/154cf7e048732575fc2f2c8b6edb4ece8b7132ff)]

### Fixed

- 🍎 Fix(deposit) No conv fee order type detection [[d59afd1](https://github.com/newgen-business-solutions/cs-event-services/commit/d59afd1ee523631bd7ea947b461f77e5ae801196)]
- 🍎 Fix(UE) Deposit SO ref updating [[f755c08](https://github.com/newgen-business-solutions/cs-event-services/commit/f755c080a208780498d2f239de676c2faa418398)]
- 🍎 Fix(UE) Sales order template rendering [[ae607a6](https://github.com/newgen-business-solutions/cs-event-services/commit/ae607a60d69d7038cd29188095ebebb57fe0f6c7)]
- 🍎 Fix(suitelets) Auto Charge form rendering [[c0d78a7](https://github.com/newgen-business-solutions/cs-event-services/commit/c0d78a7810a1da09cb5bd3215662ae5b64e914ff)]
- ✏️ Fix(web) tooltip in upload item [[a3739cb](https://github.com/newgen-business-solutions/cs-event-services/commit/a3739cbec38bef4c5e54021a9a2c69503bb4fe30)]

### Miscellaneous

- 📝 Update Changelog [[8920feb](https://github.com/newgen-business-solutions/cs-event-services/commit/8920febdf7754cbd1536ad0bbad0f077ac3149e7)]
-  Merge branch &#x27;main&#x27; into CSES-274-Booking-Calendar-Prototype [[8512aad](https://github.com/newgen-business-solutions/cs-event-services/commit/8512aad7d901df7c9a89e08cff9e94326c85fa13)]
- 🚧 Calendar booking data syncing [[a69fa4f](https://github.com/newgen-business-solutions/cs-event-services/commit/a69fa4fed38648995dd199108b30b107f5b490d0)]
- 📗 Add(Client) Deposit PostSource debugging [[d2441c2](https://github.com/newgen-business-solutions/cs-event-services/commit/d2441c2a305cf5ec70482ade174eaaf9c8dfc909)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[7c80f99](https://github.com/newgen-business-solutions/cs-event-services/commit/7c80f99785136e944e94effec6f16670b676eb14)]
- 💥 9.13.X Release [#124](https://github.com/newgen-business-solutions/cs-event-services/issues/124) [[fc3cd7d](https://github.com/newgen-business-solutions/cs-event-services/commit/fc3cd7de7ef419ef15df41b926c5e3cdc9d84d89)]
- ◀️ Turn on AutoCharge Form SL [[ddd0913](https://github.com/newgen-business-solutions/cs-event-services/commit/ddd0913242e7648a0313d83feb8754e9fe709792)]
- 📝 Update user info RESTlet docs [[e1b6eb8](https://github.com/newgen-business-solutions/cs-event-services/commit/e1b6eb87ae6f158e30ea95bbc531dba52724b757)]
- 📝 Update Settings Type Docs [[4dfd77c](https://github.com/newgen-business-solutions/cs-event-services/commit/4dfd77ccfd202bb0756a1768e162dd3a0be49b32)]
- 📦 Add &#x60;jspdf&#x60; [[e6f8bbf](https://github.com/newgen-business-solutions/cs-event-services/commit/e6f8bbfd285f59059d42c4a0d5107a00fbec80e2)]
- 📦 Update Install State [[2b30e74](https://github.com/newgen-business-solutions/cs-event-services/commit/2b30e746eed1d427c0a24dc3b0ca9ca7fc327b6c)]
- 📝 Add(Booking) &#x60;ts-ignore&#x60; to root [[c5fc488](https://github.com/newgen-business-solutions/cs-event-services/commit/c5fc488793998895493a129e54ed299715f2afa2)]
- 📦 Update(Booking) syncfusion to &#x60;24.2.7&#x60; [[e92afed](https://github.com/newgen-business-solutions/cs-event-services/commit/e92afede17ddc3aa2c35f4f6db96f0a5ce03aa1c)]
- 🚧 Update(reporting) Pull list with 2.1 [[e10180d](https://github.com/newgen-business-solutions/cs-event-services/commit/e10180d629af3c0941b75ebf3a89361fcf18ae82)]
- 📝 Update hook docs [[c768d66](https://github.com/newgen-business-solutions/cs-event-services/commit/c768d664243cf123388e80c617ff671116f255e3)]


<a name="9.11.70"></a>
## 9.11.70 (2024-02-13)

### Added

- ✨ Add File Upload Item Type [#120](https://github.com/newgen-business-solutions/cs-event-services/issues/120) [[a305dc8](https://github.com/newgen-business-solutions/cs-event-services/commit/a305dc8f67632c6a9b83bfcdf8ef97b644cd33ed)]
- ✨ Add Conv Fee Alias &amp; Event Alias [[2785eae](https://github.com/newgen-business-solutions/cs-event-services/commit/2785eae89787110e614d9fd080c02e3f062a6f7d)]
- ✅ Add(web) file upload to detail page [[12a21a3](https://github.com/newgen-business-solutions/cs-event-services/commit/12a21a3f69e8b5347a38e9bd27303f42ee89b3dd)]
- ✅ Add(web) file upload to store [[a0cbfff](https://github.com/newgen-business-solutions/cs-event-services/commit/a0cbfff527844daa67735646236587e7c4cdb079)]
- ✅ Add(web) File upload to collection [[c0b0549](https://github.com/newgen-business-solutions/cs-event-services/commit/c0b05491e3086a5ca6c920bb44ed9e52609e129f)]
- ✨ Add Upload Item To Collection Page [[9901f86](https://github.com/newgen-business-solutions/cs-event-services/commit/9901f868e6d479bdccd4b39d5ba6a0b984828ea5)]

### Changed

- ⬆️ Update Version to &#x60;9.11.70&#x60; [[a844fc3](https://github.com/newgen-business-solutions/cs-event-services/commit/a844fc3a0d1429e4351eaaa93940e12da6a1fc4a)]
- 🗃️ Update(BE) Settings to include company [[6ccd2b4](https://github.com/newgen-business-solutions/cs-event-services/commit/6ccd2b403032908fa601a7d8c63bc76de155da98)]
- ♻️ Refactor Handle Upload Suitelet [[b889439](https://github.com/newgen-business-solutions/cs-event-services/commit/b8894399cc656604af0c996069abebdb8faad8e4)]
- ⚡ Optimize(BE) SO Form Init [[8cd5217](https://github.com/newgen-business-solutions/cs-event-services/commit/8cd5217668c925fef1365ade86a6b4df0c398440)]
- ⚡ Optimize(BE) Order REST for multi-sub [[2371752](https://github.com/newgen-business-solutions/cs-event-services/commit/2371752a2580fce7fc5cad470e99a78b43af256b)]
- 🎨 Modify(web) Signup verbiage [[dbdbacd](https://github.com/newgen-business-solutions/cs-event-services/commit/dbdbacdd993bb484f36288c273b953455460793c)]
- 🎨 Restyle empty cart state [[534d2f4](https://github.com/newgen-business-solutions/cs-event-services/commit/534d2f428cbabca6e8b9fc6234d88e782fb0a20f)]
- 🎨 Add(WEB) File Upload indicator cart [[90da723](https://github.com/newgen-business-solutions/cs-event-services/commit/90da723b45d26541cbc90860e99c55ccc62e3f16)]
- ♻️ Refactor Handle Upload [[f6a4cdd](https://github.com/newgen-business-solutions/cs-event-services/commit/f6a4cdd878950ca540020c3b4a8fee419ceedc85)]
- ♻️ Refactor Handle Upload Suitelet [[da930da](https://github.com/newgen-business-solutions/cs-event-services/commit/da930daf69970361a5a4e84adbdd1b176cb80a0c)]
- ♻️ Refactor Handle Upload Suitelet [[af48878](https://github.com/newgen-business-solutions/cs-event-services/commit/af4887858972071cffb3cb4dfbf82cb1267e1e7e)]
- ♻️ Refactor Handle Upload Restlet [[8e8017f](https://github.com/newgen-business-solutions/cs-event-services/commit/8e8017f6fdc088aa3faa76cfca942585f168c575)]
- ♻️ Refactor Handle Upload Restlet [[3ed3e02](https://github.com/newgen-business-solutions/cs-event-services/commit/3ed3e022d9a6db91e05c64be1940ce26e9829a73)]
- ⚡ Optimize(web) Item upload [[2ec2407](https://github.com/newgen-business-solutions/cs-event-services/commit/2ec24079e56458006b96b23c3d18e883337b90f2)]
- ♻️ Refactor Handle Upload [[5a26851](https://github.com/newgen-business-solutions/cs-event-services/commit/5a26851b6f07419dad7712bcbcb1db92cb605a6a)]
- ♻️ Refactor(BE) CSV Import ([#118](https://github.com/newgen-business-solutions/cs-event-services/issues/118)) [[b58daeb](https://github.com/newgen-business-solutions/cs-event-services/commit/b58daeb78e857db9005d021a763c541b4d6c792e)]
- ♻️ Rename(BE) Web Upload Folder [[dddaaa2](https://github.com/newgen-business-solutions/cs-event-services/commit/dddaaa218757fc3ff14ff0f1464cf86c57d373f3)]
- ♻️ Refactor Handle Upload [[3c23940](https://github.com/newgen-business-solutions/cs-event-services/commit/3c23940b992d68645536422a5ac2e88ca36b7e80)]
- ♻️ REF(web) Upload API Route for proper res [[0506534](https://github.com/newgen-business-solutions/cs-event-services/commit/0506534e789a92d96629b7c43b29ca003b18b150)]
- 🎨 Format(BE) REST code for better readability [[e8ad93f](https://github.com/newgen-business-solutions/cs-event-services/commit/e8ad93f5f5017e6b2a2106b388c66b182cd295a5)]
- ⚡ Optimize(BE) SO Price Level set on DaysCalc [[625a492](https://github.com/newgen-business-solutions/cs-event-services/commit/625a49299a12e88a45a30af0ebb1ee1b83ae1bde)]
- ♻️ Refactor Handle Upload Restlet [[d840c92](https://github.com/newgen-business-solutions/cs-event-services/commit/d840c9242bdcfd6a78e35e4e49b4b090cc9028ff)]
- ♻️ Refactor Handle Upload Restlet [[0f1a916](https://github.com/newgen-business-solutions/cs-event-services/commit/0f1a91692676c53e2bb6e67ddec0d6b91dab8465)]
- ⚡ Update(web) file upload endpoint [[a50edaa](https://github.com/newgen-business-solutions/cs-event-services/commit/a50edaab9059dfa7ae95611d4dc625e01bb7c089)]
- ♻️ Refactor Upload API [[b6d5b83](https://github.com/newgen-business-solutions/cs-event-services/commit/b6d5b8372cb623648611e0c5c107d635751d3aa5)]
- ⚡ Optimize(web) Upload endpoint [[d7563e4](https://github.com/newgen-business-solutions/cs-event-services/commit/d7563e43b3285b9ea2267564a36c391851a3095e)]
- ♻️ Refactor Product Intro [[cf57aff](https://github.com/newgen-business-solutions/cs-event-services/commit/cf57aff238fd9d8b6ab2e8c2dc2b69ff961733eb)]
- ⚡ Optimize(FE) File upload for multi-file support [[4029c7b](https://github.com/newgen-business-solutions/cs-event-services/commit/4029c7b6ee1b8e01d44304c6e3591529b3c129ab)]
- ♻️ Refactor Item Upload Suitelet [[a074286](https://github.com/newgen-business-solutions/cs-event-services/commit/a0742868e5cd65730e4e3d874ddbe4d10bc9e17b)]

### Removed

- 🔥 Remove(web) formData from upload API [[4de33fa](https://github.com/newgen-business-solutions/cs-event-services/commit/4de33fae25715f5409ec169bc4ce1e102f3ec2c8)]

### Fixed

- ✏️ Update(web) typo in BOL Request Form [[bd8cfb9](https://github.com/newgen-business-solutions/cs-event-services/commit/bd8cfb9e02267f84df4a782cefc8fecf6bcfe90d)]

### Miscellaneous

-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[5365a7c](https://github.com/newgen-business-solutions/cs-event-services/commit/5365a7ccde480638a563bfed5ba4ca6f241f1fa4)]
- 🔍 Add(web) SEO Titles to pages [[f6f92fb](https://github.com/newgen-business-solutions/cs-event-services/commit/f6f92fb62a35263fe2b0ebe32c79880a9a30863f)]
-  Merge branch &#x27;main&#x27; into CSES-242-allow-customer-upload-type-item [[1a2cd2a](https://github.com/newgen-business-solutions/cs-event-services/commit/1a2cd2a6856a6d174632957716e3dbbc8a261e39)]
- 🗑️ Deprecate(BE) Unused REST Endpoints [[7d7e8a1](https://github.com/newgen-business-solutions/cs-event-services/commit/7d7e8a1b1f99f1a51b94ba3c2afd3bb6a8fc3db1)]
- 📝 Fix(web) ESLint errors [[6c516b7](https://github.com/newgen-business-solutions/cs-event-services/commit/6c516b7dac12957fc010c53d72a34541064fb9da)]
-  Merge branch &#x27;CSES-242-allow-customer-upload-type-item&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-242-allow-customer-upload-type-item [[fe9cc05](https://github.com/newgen-business-solutions/cs-event-services/commit/fe9cc05be69af7e4e3e30143779c6553b70b3c00)]
- 🚧 : Add(web) delete to API Route [[eb5e61b](https://github.com/newgen-business-solutions/cs-event-services/commit/eb5e61bbb7d36136e502cf3b66a6aa04b34eada9)]
-  Merge branch &#x27;CSES-242-allow-customer-upload-type-item&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-242-allow-customer-upload-type-item [[05ee5df](https://github.com/newgen-business-solutions/cs-event-services/commit/05ee5dfae5d72e839045bff82d03e7ecf00d2a62)]
-  Merge branch &#x27;CSES-242-allow-customer-upload-type-item&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-242-allow-customer-upload-type-item [[2b6ba08](https://github.com/newgen-business-solutions/cs-event-services/commit/2b6ba08f7139ced1e6dfa8e0fe608e7d6877020f)]
-  Merge branch &#x27;CSES-242-allow-customer-upload-type-item&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-242-allow-customer-upload-type-item [[b197b8b](https://github.com/newgen-business-solutions/cs-event-services/commit/b197b8b7db6d9e3b7fb706b47f887ad810af567f)]
-  Merge branch &#x27;CSES-242-allow-customer-upload-type-item&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-242-allow-customer-upload-type-item [[cc06408](https://github.com/newgen-business-solutions/cs-event-services/commit/cc06408221538370030a2efe879f44dc8348dc9c)]
- 📗 Reduce(web) Auth logs [[27d8697](https://github.com/newgen-business-solutions/cs-event-services/commit/27d8697ea6c1f7a0206222515763db4bed3cabe1)]
- 📦 Add &#x60;stream&#x60; v0.0.2 [[2abc0b5](https://github.com/newgen-business-solutions/cs-event-services/commit/2abc0b5f3fa5e37a8aead6512a63da303debd5de)]
- 📦 Add (Web)  form-data &amp; formidable [[7295827](https://github.com/newgen-business-solutions/cs-event-services/commit/72958279da9df7bd327bff6440e6a59e176ea6c1)]
- 📁 Update(FE) with multi-part form req [[bb8b714](https://github.com/newgen-business-solutions/cs-event-services/commit/bb8b7141c6e1cccc821ab2ec222e55ba1334e7d1)]
-  Merge branch &#x27;CSES-242-allow-customer-upload-type-item&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-242-allow-customer-upload-type-item [[7fd9ad4](https://github.com/newgen-business-solutions/cs-event-services/commit/7fd9ad45fd2e9579591be429c4933fd8fb916edd)]
- 🚧 Setup(web) File upload dropbox [[d5e748d](https://github.com/newgen-business-solutions/cs-event-services/commit/d5e748dd2b214280b3f740cdb952989e30c046b6)]


<a name="9.10.50"></a>
## 9.10.50 (2024-01-24)

### Added

- ✨ Add Max-Min Quantity Restrictions [#101](https://github.com/newgen-business-solutions/cs-event-services/issues/101) [[119d72f](https://github.com/newgen-business-solutions/cs-event-services/commit/119d72f6d3eea81700fef8bf6aa5984e3d358b59)]
- ➕ Add(BE) Client Price Hooks [[4e7e873](https://github.com/newgen-business-solutions/cs-event-services/commit/4e7e873b5c77af48257d11eeb01b5575be367d3e)]
- ✨ Create Handle Upload Restlet [[ee49528](https://github.com/newgen-business-solutions/cs-event-services/commit/ee49528048fe4da634e3d7d6412a9ee2e61035bf)]
- ✅ Add(BE) Max Quantity to detail pg [[703e5e7](https://github.com/newgen-business-solutions/cs-event-services/commit/703e5e75f8c9a77259b7e124cd698a09421bfebf)]
- ✨ Add Min/Max quantities to items ([#100](https://github.com/newgen-business-solutions/cs-event-services/issues/100)) [[73781db](https://github.com/newgen-business-solutions/cs-event-services/commit/73781dbf98ccaafeb8a59e05db837f619f724a25)]
- ✨ Add Min/Max quantities to items [[544c501](https://github.com/newgen-business-solutions/cs-event-services/commit/544c50103de8529cbca1b346d294c8da7e1f09e3)]

### Changed

- ♻️ Refactor Exhibitor and Booth Create Suitelet ([#117](https://github.com/newgen-business-solutions/cs-event-services/issues/117)) [[7580e66](https://github.com/newgen-business-solutions/cs-event-services/commit/7580e66e2ca3af24b9ffc91ddb54852ce6949a4b)]
- 🎨 Update(web) order page for Canadian Tax [[b1057b6](https://github.com/newgen-business-solutions/cs-event-services/commit/b1057b6d01f1776dbbe3f001209a0d24973a3674)]
- 🎨 Update(BE) to round supervision cost [[348f7fb](https://github.com/newgen-business-solutions/cs-event-services/commit/348f7fbedaf2acef17ab75c2e3cf67aaa3a1bf80)]
- ♻️ Refactor Exhibitor CSV Import and Suitelet ([#116](https://github.com/newgen-business-solutions/cs-event-services/issues/116)) [[e124c37](https://github.com/newgen-business-solutions/cs-event-services/commit/e124c377ac1de9e0198b5bfb8737f7c989a2020b)]
- ♻️ REF(BE) Labor item date consuming [[b7f4bda](https://github.com/newgen-business-solutions/cs-event-services/commit/b7f4bda9f5ec4f857a6c39b77f66113b22eb7d7b)]
- 🎨 Update(web) TOS on checkout [[e9e7d1c](https://github.com/newgen-business-solutions/cs-event-services/commit/e9e7d1cae38fdfcf97543621d9ae73d2dae574f3)]
- ⬆️ Update Install Cache [[f0caab9](https://github.com/newgen-business-solutions/cs-event-services/commit/f0caab9b390d1d449157043a4f96ed01510058b5)]
- ♻️ Refactor Handle Upload Restlet [[250ea4b](https://github.com/newgen-business-solutions/cs-event-services/commit/250ea4b9ce2e4ad8a9392a1f7fe796645e009cde)]
- ♻️ Refactor Exhibitor CSV Import [[1bc67c2](https://github.com/newgen-business-solutions/cs-event-services/commit/1bc67c23968b5919ecc16b4fed9e6c951a496948)]
- ♻️ Refactor Event Tax fields and Exhibitor CSV logic [#111](https://github.com/newgen-business-solutions/cs-event-services/issues/111) [[74035cb](https://github.com/newgen-business-solutions/cs-event-services/commit/74035cbb3da8ac1c94cbfcb04dba6b001289380a)]
- ♻️ Refactor Event and Exhibitor CSV Import [[b8e7e80](https://github.com/newgen-business-solutions/cs-event-services/commit/b8e7e800fb0510411a49a42da703d0665fcd0b28)]
- 🎨 Update(WEB) Cart Store Date Usage [[bf79ca9](https://github.com/newgen-business-solutions/cs-event-services/commit/bf79ca94d5a420554260377cd2ea78c10d39b879)]
- ♻️ Refactor(BE) Order Value Updates [[140329c](https://github.com/newgen-business-solutions/cs-event-services/commit/140329cdf24c91e953b12a36f3863c39c57fb7dd)]
- ♻️ Refactor(BE) Price Hooks [[3aca92b](https://github.com/newgen-business-solutions/cs-event-services/commit/3aca92bfe820dc51879ec1f15528a434eb19cd72)]
- ⚡ Optimized(BE) Price hooks [[d8afaaa](https://github.com/newgen-business-solutions/cs-event-services/commit/d8afaaadae1d3521158edf2db4c1ee5d247c72b6)]
- ⚡ Optimize(BE) Tax Code lookups [[7f21caa](https://github.com/newgen-business-solutions/cs-event-services/commit/7f21caa5bcca860c92f4f356fcbe3f5657f277e6)]
- ♻️ Refactor Handle Order Payment&quot; [[d41dca9](https://github.com/newgen-business-solutions/cs-event-services/commit/d41dca9ef6bf99faf18402716ad9f28617c6411a)]
- ♻️ Refactor Handle Order Payment [[ba73200](https://github.com/newgen-business-solutions/cs-event-services/commit/ba732009e604fffb528c4b4dbe2967bca38eedc2)]
- 🎨 Improve(BE) pricing hooks &#x60;useItemPriceLevel&#x60; [[4bfd482](https://github.com/newgen-business-solutions/cs-event-services/commit/4bfd48209a4fc70e0dbc58ffbc606920498b08d9)]
- ♻️ Refactor Event Tax Fields [[2367cbf](https://github.com/newgen-business-solutions/cs-event-services/commit/2367cbf07c3cd343aa7a14ce78500ab30c0c410d)]
- ⚡ Optimize(BE) Add-line utility with &#x60;moment&#x60;  ([#110](https://github.com/newgen-business-solutions/cs-event-services/issues/110)) [[58b26b8](https://github.com/newgen-business-solutions/cs-event-services/commit/58b26b87c99f65c83838968641e0585642ed9118)]
- ♻️ Refactor Exhibitor CSV Import [[c2ef5ef](https://github.com/newgen-business-solutions/cs-event-services/commit/c2ef5efd986e9b28d24f7f956b8b2c1e3c24949e)]
- ⬆️ Increment version to &#x60;9.9.26&#x60; [[6ce7c6c](https://github.com/newgen-business-solutions/cs-event-services/commit/6ce7c6cdcfeccd631d437f4994505111d40cb4d8)]
- ⬆️ Update Dependencies [[9f9eb93](https://github.com/newgen-business-solutions/cs-event-services/commit/9f9eb93f7f98e25304b97f5456af35b58676f812)]
- ♻️ Refactor Booth and Exhibitor create suitelet to 2.1 ([#103](https://github.com/newgen-business-solutions/cs-event-services/issues/103)) [[019e13f](https://github.com/newgen-business-solutions/cs-event-services/commit/019e13f1f6107f278c32b34155d8b85d5de1e6bb)]
- ♻️ Refactor Event Venue CA Tax Fields ([#109](https://github.com/newgen-business-solutions/cs-event-services/issues/109)) [[87d4c45](https://github.com/newgen-business-solutions/cs-event-services/commit/87d4c457e700ca819cb86ce6851cbb802c3516bf)]
- ♻️ Refactor Event and Venue Tax Calculation ([#108](https://github.com/newgen-business-solutions/cs-event-services/issues/108)) [[bfbade0](https://github.com/newgen-business-solutions/cs-event-services/commit/bfbade0e54b12061cf6a38393b51465a6ba1b37e)]
- ♻️ REF(BE) Create Event Wizard ([#102](https://github.com/newgen-business-solutions/cs-event-services/issues/102)) [[abd6569](https://github.com/newgen-business-solutions/cs-event-services/commit/abd656916378d7cee03b672a5b0b8fe6416eeb06)]
- ♻️ REF(BE) Removing &#x60;custitem_max_quantity&#x60; from project [[d18e74e](https://github.com/newgen-business-solutions/cs-event-services/commit/d18e74ecf0eb7d210c924cf672ff257b38261d71)]
- ♻️ REF(BE) event search for fatal error [[e836176](https://github.com/newgen-business-solutions/cs-event-services/commit/e8361768d076a1d84604e68a6a762a8e045610d0)]
- 🎨 Change(web) search button disabled logic [[65eb0a5](https://github.com/newgen-business-solutions/cs-event-services/commit/65eb0a567a355e53688211ac8f27dae3f901a4e3)]
- 🎨 Adjust(web) Terms line with asterisks [[0a6d867](https://github.com/newgen-business-solutions/cs-event-services/commit/0a6d867ac0f033dafc3dbb401ddabbd31f09a103)]
- ⬆️ Increment version to &#x60;9.8.19&#x60; [[71402c5](https://github.com/newgen-business-solutions/cs-event-services/commit/71402c5f9cd424ee0c43c4ee0878b85d34038ab3)]
- ♻️ REF(BE) Item search to use native qty fields [[1754791](https://github.com/newgen-business-solutions/cs-event-services/commit/1754791c805770f6629d5d752ac2c6113cbda752)]
- 🎨 Add(web) Terms &amp; Conditions display on signup [[747e28e](https://github.com/newgen-business-solutions/cs-event-services/commit/747e28e131436bbdb846e868c40cd6643d486000)]

### Breaking changes

- 💥 REF(BE) Renamed &#x60;customrecord_upload_docs&#x60; [[16e12f7](https://github.com/newgen-business-solutions/cs-event-services/commit/16e12f74572b59e017b60bc027177bfd958ef515)]

### Removed

- 🔥 Remove(BE) &#x60;custrecord_ng_cs_show_image_url&#x60; [[d59a7d2](https://github.com/newgen-business-solutions/cs-event-services/commit/d59a7d298eb6c5fb0e42dcabe202080e6578dfb8)]

### Fixed

- 🐛 Fix(BE) Price Level set timing [[c7047fb](https://github.com/newgen-business-solutions/cs-event-services/commit/c7047fb6227a0b7f3927995d73c9b6909f971b74)]
- 🐛 Fix(web) Labor preview on dates [[7c7b721](https://github.com/newgen-business-solutions/cs-event-services/commit/7c7b7215d85811470bd797cee53629927072e87b)]
- ✏️ Fix(web) linting error [[6c66875](https://github.com/newgen-business-solutions/cs-event-services/commit/6c66875f870c8034edc49ba6994f427842f827a6)]
- 🐛 Fix(WEB) SQFT + Matrix cart adding [[32e2f6f](https://github.com/newgen-business-solutions/cs-event-services/commit/32e2f6fe54990cd31f4274ed812f96e57db40712)]
- 🐛 Fix Event Logo Rendering [[e27fb5c](https://github.com/newgen-business-solutions/cs-event-services/commit/e27fb5c67cac60e4952f2f0abdcf89656e76e464)]
- 🚑 Support Canadian Transactions [#113](https://github.com/newgen-business-solutions/cs-event-services/issues/113) [[7df5d60](https://github.com/newgen-business-solutions/cs-event-services/commit/7df5d60161431ecab7aa97ce5cd243c63413649d)]
- 🚑 Fix(BE) Event Loading [[f8827c1](https://github.com/newgen-business-solutions/cs-event-services/commit/f8827c1b06d566f9a95beee69b2624226bd43a45)]
- ✏️ Change(BE) Min Qty Fallback to 1 [[d41c146](https://github.com/newgen-business-solutions/cs-event-services/commit/d41c146f5fb7e16984674b0f0e235599296e307c)]
- 🐛 Fix(BE) Matrix array output for item detail [[e2a6011](https://github.com/newgen-business-solutions/cs-event-services/commit/e2a60110be81c768d78bc05c54998dcd2215801d)]
- 🐛 Fix(BE) Single Matrix Result Options [[fbf9bf5](https://github.com/newgen-business-solutions/cs-event-services/commit/fbf9bf549f843521af4be6edca905c58301290ca)]

### Miscellaneous

-  Merge branch &#x27;FEAT-Jan-16-24-V9-10-X&#x27; of https://github.com/newgen-business-solutions/cs-event-services into FEAT-Jan-16-24-V9-10-X [[7919fc5](https://github.com/newgen-business-solutions/cs-event-services/commit/7919fc5cc36922345fc7218cceabef608c15f754)]
- 📦 Update package dependencies [[72dbb9a](https://github.com/newgen-business-solutions/cs-event-services/commit/72dbb9ac6542ed8f21a08c81992cb0a2d809c446)]
-  Merge pull request [#115](https://github.com/newgen-business-solutions/cs-event-services/issues/115) from newgen-business-solutions/dependabot/npm_and_yarn/apps/booking-calendar/vite-4.5.2 [[c949ee4](https://github.com/newgen-business-solutions/cs-event-services/commit/c949ee49126f101a1b69df3a53c46722fa99f6a5)]
-  build(deps-dev): Bump vite in /apps/booking-calendar [[e51a9e0](https://github.com/newgen-business-solutions/cs-event-services/commit/e51a9e0af2b41a6d5f3d38ed9af3c344e0adb62c)]
-  Merge branch &#x27;CSES-311-Minimum-Quantity-and-Maximum&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-311-Minimum-Quantity-and-Maximum [[2356f46](https://github.com/newgen-business-solutions/cs-event-services/commit/2356f460c4f27a9906245faa4e41731a61390fef)]
- 👔 Update(BE) Event Logo Logic [[03e7ccb](https://github.com/newgen-business-solutions/cs-event-services/commit/03e7ccb48702a5c84a99a220e9ab143bc01591b3)]
-  Merge branch &#x27;CSES-311-Minimum-Quantity-and-Maximum&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-311-Minimum-Quantity-and-Maximum [[0dbd6c1](https://github.com/newgen-business-solutions/cs-event-services/commit/0dbd6c17c5fa3691a9232e2625890d89cb3be5be)]
- 🚧 Item Upload File Upload Set Up [[8842daa](https://github.com/newgen-business-solutions/cs-event-services/commit/8842daa97961ba759beae2877ef7bdc926d785db)]
-  Merge branch &#x27;CSES-311-Minimum-Quantity-and-Maximum&#x27; into CSES-353-tax-rate-fields [[fd02b80](https://github.com/newgen-business-solutions/cs-event-services/commit/fd02b8038a04f534ae79eae0d3933a80ea892c8d)]
- 🪲 Fix CA Transaction Processing ([#83](https://github.com/newgen-business-solutions/cs-event-services/issues/83)) [[0f09d5a](https://github.com/newgen-business-solutions/cs-event-services/commit/0f09d5a4755d5b5ea8d2796a05321e426ab118f4)]
-  Merge branch &#x27;CSES-370-Exhibitor-CSV-Import-ID-null&#x27; into CSES-353-tax-rate-fields [[693656d](https://github.com/newgen-business-solutions/cs-event-services/commit/693656d29cb9aee78b1c6bb9bc318f9af8e17d15)]
-  Merge branch &#x27;CSES-311-Minimum-Quantity-and-Maximum&#x27; into CSES-370-Exhibitor-CSV-Import-ID-null [[3fcb93b](https://github.com/newgen-business-solutions/cs-event-services/commit/3fcb93b6e2c653497b07f3c2b3c0ce7173c00c07)]
-  Merge branch &#x27;CSES-311-Minimum-Quantity-and-Maximum&#x27; into CSES-353-tax-rate-fields [[9b88c41](https://github.com/newgen-business-solutions/cs-event-services/commit/9b88c41ded9b727ddbe2cc12aa9a773554de19ac)]
- 📝 Update Changelog [[b1a5313](https://github.com/newgen-business-solutions/cs-event-services/commit/b1a5313a3c5da984ee00d8c76562bd7b48da1fcc)]
-  build(deps): Bump follow-redirects in /apps/booking-calendar/api ([#104](https://github.com/newgen-business-solutions/cs-event-services/issues/104)) [[c992a59](https://github.com/newgen-business-solutions/cs-event-services/commit/c992a598d8470404204f0afce59c60b7e4f695e9)]
-  build(deps): Bump follow-redirects from 1.15.2 to 1.15.4 in /apps/web ([#105](https://github.com/newgen-business-solutions/cs-event-services/issues/105)) [[0ba4954](https://github.com/newgen-business-solutions/cs-event-services/commit/0ba495400abe41d70b617cef952bef2461875b05)]
-  build(deps): Bump follow-redirects from 1.15.3 to 1.15.4 ([#106](https://github.com/newgen-business-solutions/cs-event-services/issues/106)) [[8e739cf](https://github.com/newgen-business-solutions/cs-event-services/commit/8e739cfe90f9ea37305d7d12bfb53e24dd4b4a78)]
-  build(deps): Bump follow-redirects in /apps/booking-calendar ([#107](https://github.com/newgen-business-solutions/cs-event-services/issues/107)) [[9173cbb](https://github.com/newgen-business-solutions/cs-event-services/commit/9173cbb63c7e48bbafe7c08ac95538b8d34325c3)]
-  Merge branch &#x27;main&#x27; into CSES-311-Minimum-Quantity-and-Maximum [[4a62c9f](https://github.com/newgen-business-solutions/cs-event-services/commit/4a62c9f288954bfb000c85163907d3f08e33ca06)]


<a name="9.7.17"></a>
## 9.7.17 (2023-12-19)

### Added

- ✨ FEAT: Update 9.7.X ([#97](https://github.com/newgen-business-solutions/cs-event-services/issues/97)) [[b757f4b](https://github.com/newgen-business-solutions/cs-event-services/commit/b757f4bd749f91d920c1b1c300a51e59227973ed)]

### Changed

- ♻️ REF(Web) CC Memo Format [[1d7b981](https://github.com/newgen-business-solutions/cs-event-services/commit/1d7b9810d83780373bc2eef13caf122352ff3224)]
- ♻️ REF(web) Change &#x60;memo&#x60; to &#x60;ccmemo&#x60; [[e3f3a38](https://github.com/newgen-business-solutions/cs-event-services/commit/e3f3a38561e19c2596ccc21f3837a0aab4fe515b)]
- ♻️ REF(web) Format of memo card creation [[26b6f6f](https://github.com/newgen-business-solutions/cs-event-services/commit/26b6f6f24b238badc28da14cc7972289fb82bbba)]
- ♻️ REF(web) Remove all usages of &#x60;session.*&#x60; [[14ecfdc](https://github.com/newgen-business-solutions/cs-event-services/commit/14ecfdc586b1726e8ae841df1e140d73298782f3)]
- ♻️ : REF(web) Show rendering for users [[02e9d0f](https://github.com/newgen-business-solutions/cs-event-services/commit/02e9d0f1520147be33593a3500f6e1fea74e969c)]
- ⚡ Optimize(web) Encrypt Card Number Funciton [[0ba9e24](https://github.com/newgen-business-solutions/cs-event-services/commit/0ba9e24c2b362d189b9ac4a89784cc963ad1e842)]
- ♻️ REF(web) API Routes to use &#x60;getServerSession&#x60; [[bb2a46a](https://github.com/newgen-business-solutions/cs-event-services/commit/bb2a46af8d8eebaf5fbb82febc931b2d7a7e05ee)]

### Fixed

- ✏️ REF(web) Adjust formatting on ccmemo [[a194dbd](https://github.com/newgen-business-solutions/cs-event-services/commit/a194dbdd76c588bc541de870f611ce40b1139acf)]
- 🚑 Fix(BE) Card API Write [[4c9c77d](https://github.com/newgen-business-solutions/cs-event-services/commit/4c9c77de31f74d3971eaea2885bb4ea367928904)]
- 🚑 Fix(BE) Card API Content-Type JSON [[e70f68e](https://github.com/newgen-business-solutions/cs-event-services/commit/e70f68e0792050a2ce171f6b5450bc4e28fa4dff)]
- 🚑 Fix(web) card addition API [[43c9f70](https://github.com/newgen-business-solutions/cs-event-services/commit/43c9f70b39a018aff1320de134182f7edb01d142)]
- 🚑 Fix(web) Query params in request [[e78aa70](https://github.com/newgen-business-solutions/cs-event-services/commit/e78aa706880c3f58839ebe45d126962681525d21)]
- 🚑 Fix(web) chrome Gpay autofill card encrypt [[2dd597c](https://github.com/newgen-business-solutions/cs-event-services/commit/2dd597cf6d8af5ef356f256a4047fce8111e8430)]
- 🚑 Fix contact param missing user API route [[bd2774c](https://github.com/newgen-business-solutions/cs-event-services/commit/bd2774c1f17426614615851b7bb92930be0dcbcb)]

### Miscellaneous

-  Create pull_request_template.md [[07a32cf](https://github.com/newgen-business-solutions/cs-event-services/commit/07a32cfb03c85579a7a4dd5ad3954e5148ffaf8f)]
- 📗 Add(web) Logging &#x60;query&#x60; destructure [[271db08](https://github.com/newgen-business-solutions/cs-event-services/commit/271db089e0715fe6a9707d8f511c3fdc9c6a7975)]


<a name="9.6.11"></a>
## 9.6.11 (2023-12-14)

### Added

- 🎉 FEAT: Avery Shipping Labels ([#92](https://github.com/newgen-business-solutions/cs-event-services/issues/92)) [[892c4ae](https://github.com/newgen-business-solutions/cs-event-services/commit/892c4ae12724559d01fdc3a728accb913758a5db)]
- ➕ Add(web) inline Quantity Input Field [[f186ecc](https://github.com/newgen-business-solutions/cs-event-services/commit/f186eccc90e46f19cf893a160f11294529b5fc10)]
- ✅ Add(BE) Settings Auto Job Field [[fc50f2c](https://github.com/newgen-business-solutions/cs-event-services/commit/fc50f2c19ef3b29bfe5718061959ec293e76ab99)]
- ➕ Add(BE) New Event Wizard SL [[7c61d68](https://github.com/newgen-business-solutions/cs-event-services/commit/7c61d681cd34cb1e05962440a6222d46ad0f2858)]
- ➕ Add Preview NS config option [[a518f86](https://github.com/newgen-business-solutions/cs-event-services/commit/a518f867389da222179b75b4446cc04a49b8a73a)]
- ➕ Add Ngrok config to project [[e7b98aa](https://github.com/newgen-business-solutions/cs-event-services/commit/e7b98aadbd7f046cdc24531777a4349bd83ada46)]
- ✅ Add(web) payment default card selected [[8069f2e](https://github.com/newgen-business-solutions/cs-event-services/commit/8069f2ecb319360d567d92df56ce00c38dbd1d50)]

### Changed

- 📌 Refactor(web) Dependency usage and cache [[75b5c07](https://github.com/newgen-business-solutions/cs-event-services/commit/75b5c07b0eccb09de57c959d11f5ee05e0fed86a)]
- ⬆️ Bump vite from 4.4.0 to 4.4.12 in /apps/booking-calendar ([#86](https://github.com/newgen-business-solutions/cs-event-services/issues/86)) [[5fae73f](https://github.com/newgen-business-solutions/cs-event-services/commit/5fae73f2bf47264854228f0b3f23c21013e78f16)]
- ⬆️ Update &#x60;vite&#x60; to &#x60;4.4.12&#x60; [[fccefa8](https://github.com/newgen-business-solutions/cs-event-services/commit/fccefa8412c840c0764d156da96f47fd3a2ed639)]
- ⬆️ Update Deps [[8c3f54b](https://github.com/newgen-business-solutions/cs-event-services/commit/8c3f54bc8fc9611514d00386927237750b30b26c)]
- ⬆️ Update Dependencies [[93aeef5](https://github.com/newgen-business-solutions/cs-event-services/commit/93aeef5475e60cf9d8f1d7d81ba983a34ae5c318)]
- ⬆️ Bump browserify-sign from 4.2.1 to 4.2.2 [[918c878](https://github.com/newgen-business-solutions/cs-event-services/commit/918c8780c2aee31363f142ec6ef6868e04d8f186)]
- ⬆️ Bump @babel/traverse from 7.23.0 to 7.23.6 [[079a486](https://github.com/newgen-business-solutions/cs-event-services/commit/079a4866ce4c5afe40d9583fea6c42176691626d)]
- ♻️ REF(web) Card Encryption Sequence [[164eced](https://github.com/newgen-business-solutions/cs-event-services/commit/164eced427c6851ea4bc35b904f6912807bb4374)]
- ♻️ REF(web) Cart Checkout route change [[37fb880](https://github.com/newgen-business-solutions/cs-event-services/commit/37fb8801bef9d12be55c96f47a8c49c291d2eb14)]
- ♻️ REF(BE) Tax sync with venue on event [[ad51a9a](https://github.com/newgen-business-solutions/cs-event-services/commit/ad51a9a6f43e266e2b520ec6061ae7cd932f38ae)]
- ♻️ REF(BE) Have Job inheritance tied to settings [[f1732ac](https://github.com/newgen-business-solutions/cs-event-services/commit/f1732acedc1ec07411e85ee684b0d844543fd8ca)]
- 🎨 Adjust(web) Checkout UI [[9484ec6](https://github.com/newgen-business-solutions/cs-event-services/commit/9484ec6917cf45f8a7ceac845a3693f0490f02fa)]
- 🎨 Add(web) Input Quantity &amp; Sale Unit - Add and adjust sale unit rendering on cards CSES-336 - Optimize image sizing on cart page CSES-340 - Add input quantity to all applicable cards as an inline edit CSES-339 - Adjust product title wrapping CSES-337 - Fix labor supervision logic CSES-341 - Add address modal dismiss icon [[b43231b](https://github.com/newgen-business-solutions/cs-event-services/commit/b43231b2519d33407e90de3bec4fdd8de6ef322f)]
- ♻️ REF(web) Page name removing &quot;alternative&quot; [[8a3e5c0](https://github.com/newgen-business-solutions/cs-event-services/commit/8a3e5c00c9de223701cfd377e34de4c7a5c37e5c)]
- 🎨 Add(web) Tax calculated at checkout [[3828942](https://github.com/newgen-business-solutions/cs-event-services/commit/38289422d08e67d9de40518a9dafb5dbaf4279c6)]
- ♻️ REF(BE) Venue RE-Evaluation Fot Tax % [[aa12b5d](https://github.com/newgen-business-solutions/cs-event-services/commit/aa12b5d5693dc81f133f4526740ccb9c61a2bd32)]
- ⚡ Optimize(BE) Tax to re-evaluate on load [[43e7741](https://github.com/newgen-business-solutions/cs-event-services/commit/43e7741d091d07d776542bddb702a015bf53c036)]
- ♻️ REF(BE) Mass Mailer (Event Wizard) [[2d9ac45](https://github.com/newgen-business-solutions/cs-event-services/commit/2d9ac4573e1ce564f0ca2a871e6042864817a1b9)]
- ⚡ Optimize(BE) SO UE for CA Tax [[ca2fd63](https://github.com/newgen-business-solutions/cs-event-services/commit/ca2fd63dd73d65e9ad76cb6aa9afc14f5feb1876)]
- ⬆️ Bump axios from 1.3.5 to 1.6.0 in /apps/web ([#80](https://github.com/newgen-business-solutions/cs-event-services/issues/80)) [[31e8bdd](https://github.com/newgen-business-solutions/cs-event-services/commit/31e8bdda35b485fff09b28294ad2d586ced9e9f3)]
- 🔧 Update AMD Config for &#x60;moment&#x60; [[36a200e](https://github.com/newgen-business-solutions/cs-event-services/commit/36a200e7a1057c4fa22e34800adc0cfc6cbabe98)]
- 🎨 Change Address Step [[bc6a016](https://github.com/newgen-business-solutions/cs-event-services/commit/bc6a016e09bf6bdf5c6ff3930bc6d31ebfab41af)]
- ♻️ REF(BE) NewGen Lib File [[17d4459](https://github.com/newgen-business-solutions/cs-event-services/commit/17d445901e8625a004546ffbd03a6ddf188f5daa)]
- ⬆️ Bump axios from 1.4.0 to 1.6.0 in /apps/booking-calendar/api ([#81](https://github.com/newgen-business-solutions/cs-event-services/issues/81)) [[4e6c2bc](https://github.com/newgen-business-solutions/cs-event-services/commit/4e6c2bc6f74ba984deb58fc1b0468b574d00fd3a)]
- ⬆️ Bump axios from 1.5.1 to 1.6.0 ([#82](https://github.com/newgen-business-solutions/cs-event-services/issues/82)) [[5d8d857](https://github.com/newgen-business-solutions/cs-event-services/commit/5d8d8577fd0a54cacbc8516813567402720590f2)]
- 🎨 Fix topbar contact button [[0ed62e9](https://github.com/newgen-business-solutions/cs-event-services/commit/0ed62e9502382ff31e32b10a062585a038013cea)]
- 🔧 Update(web) Auth routes needing cookies [[61ef33c](https://github.com/newgen-business-solutions/cs-event-services/commit/61ef33c3eaf1d9fa355c77d46b33c24c0c66715c)]
- 🎨 Update(web) max width for login logo [[c31c6d7](https://github.com/newgen-business-solutions/cs-event-services/commit/c31c6d789dd1753481da9b941e0700ebbaf7c255)]
- 🎨 Fix footer link color [[2d6a19c](https://github.com/newgen-business-solutions/cs-event-services/commit/2d6a19c7374e519f87070a79169f310fb34d8e2e)]

### Removed

- 🔥 Remove BOL Fields [[69b77c6](https://github.com/newgen-business-solutions/cs-event-services/commit/69b77c6226987dd8641480c126d3f3c101a36476)]
- 🔥 Remove(BE) Deposit Received Field Ref [[a065e40](https://github.com/newgen-business-solutions/cs-event-services/commit/a065e40c838db81f88a2c838e6f8d5320bf18912)]

### Fixed

- ✏️ Format ESLint Fixes [[8260f06](https://github.com/newgen-business-solutions/cs-event-services/commit/8260f062778157e9432430564f075b1752286095)]
- 🚑 Fix(BE) Card delete of PT Enc Card [[acf3229](https://github.com/newgen-business-solutions/cs-event-services/commit/acf3229d82489d321cd5ac41b8a092c6f2455e73)]
- 🐛 Fix(BE) Ordering Wizard Country Entry [[53f01f1](https://github.com/newgen-business-solutions/cs-event-services/commit/53f01f124425fcebcbde61f4db6dc1ad43000977)]
- 🚑 Fix mobile nav signout [[99a0306](https://github.com/newgen-business-solutions/cs-event-services/commit/99a03060e7a20f7f47a3eb3616fc78fa779a94e8)]
- 🚑 Max height Set on login logo [[da104ef](https://github.com/newgen-business-solutions/cs-event-services/commit/da104ef0762fafba67ea71e95409c2e8c8835cc7)]
- 🚑 Fix styles for rich text [[38d5311](https://github.com/newgen-business-solutions/cs-event-services/commit/38d53114d67ad6cc89a742a6931f3a390a5bf373)]

### Miscellaneous

-  Merge branch &#x27;CSES-347-Payment-Patch&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-347-Payment-Patch [[6338af9](https://github.com/newgen-business-solutions/cs-event-services/commit/6338af9b2b041ffbede0d97424a9bf80fefd7b61)]
- 🗑️ Deprecate old portal [[47aaae5](https://github.com/newgen-business-solutions/cs-event-services/commit/47aaae597d2242fa5bc23363f0120c5878d493a8)]
-  Merge branch &#x27;CSES-347-Payment-Patch&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-347-Payment-Patch [[bac619e](https://github.com/newgen-business-solutions/cs-event-services/commit/bac619e6f65490d0005e9b0e05ebdbb7b4dc3adf)]
- 📦 Update yarn install state [[80ea1e2](https://github.com/newgen-business-solutions/cs-event-services/commit/80ea1e207573f0393123717c7cf1982e4717f2e2)]
- 📦 Update Dependencies [[b687f3b](https://github.com/newgen-business-solutions/cs-event-services/commit/b687f3bdab24368d13a8b7228c0eed1473d68a87)]
-  Merge branch &#x27;main&#x27; into CSES-305-ca-sale-support [[d64624d](https://github.com/newgen-business-solutions/cs-event-services/commit/d64624df575514c420ae070822a95e638209c70a)]
- 🦺 Patch 9.5.X  ([#88](https://github.com/newgen-business-solutions/cs-event-services/issues/88)) [[000ea59](https://github.com/newgen-business-solutions/cs-event-services/commit/000ea59e39cdedac6cded956acf8f88e8c0f1e61)]
-  Update next-build.yml node version [[730b18f](https://github.com/newgen-business-solutions/cs-event-services/commit/730b18f4aefef069f7d6d03ffeaaed0e32786778)]
- 📦 Update &#x60;turbo&#x60; to &#x60;1.11.0&#x60; [[a112ded](https://github.com/newgen-business-solutions/cs-event-services/commit/a112ded1ee7a052c5570543f44c751b4c2df60a1)]
- 📦 Update Turbo [[ee4eba1](https://github.com/newgen-business-solutions/cs-event-services/commit/ee4eba185725b1c052b6621faf14bf8deced8340)]
- 📦 Update yarn install [[6cfe9b9](https://github.com/newgen-business-solutions/cs-event-services/commit/6cfe9b9676695660cd7b0262c3457a893cb8d8b2)]
-  Merge remote-tracking branch &#x27;origin/dependabot/npm_and_yarn/browserify-sign-4.2.2&#x27; into CSES-305-ca-sale-support [[9f5c66b](https://github.com/newgen-business-solutions/cs-event-services/commit/9f5c66b81e9077756ceb83d7cc73526d694329eb)]
-  Merge remote-tracking branch &#x27;origin/dependabot/npm_and_yarn/babel/traverse-7.23.3&#x27; into CSES-305-ca-sale-support [[9cf9d1f](https://github.com/newgen-business-solutions/cs-event-services/commit/9cf9d1fe0eca6c0269694c7a10a2977d70191c03)]
-  build(deps): Bump next from 13.2.3 to 14.0.1 in /apps/web ([#75](https://github.com/newgen-business-solutions/cs-event-services/issues/75)) [[e1f9ad3](https://github.com/newgen-business-solutions/cs-event-services/commit/e1f9ad3df250ed177a00b5dc7b1e297b6d16b49f)]
-  Squashed commit of the following: [[8c9e841](https://github.com/newgen-business-solutions/cs-event-services/commit/8c9e841217be41e792ffca4c922b852d0672500f)]
- 📦 Update yarn lock and install state [[77dc440](https://github.com/newgen-business-solutions/cs-event-services/commit/77dc4405ab6a3da3655857dd4d88ef3ad59916f6)]
- 📦 Update next to &#x60;14.0.1&#x60; [[0e8b7df](https://github.com/newgen-business-solutions/cs-event-services/commit/0e8b7df5aca44de14db6175ba8ad66eea37f671d)]
-  Squashed commit of the following: [[35a4020](https://github.com/newgen-business-solutions/cs-event-services/commit/35a4020b9bbd0953a15da6f26d985250429dbc36)]
-  Merge branch &#x27;CSES-305-ca-sale-support&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-305-ca-sale-support [[d25a3d7](https://github.com/newgen-business-solutions/cs-event-services/commit/d25a3d74a80556dbe9faf0dbf49ad09726185dc9)]
- 🚧 Troubleshooting CA Tax on order RL [[887f8c1](https://github.com/newgen-business-solutions/cs-event-services/commit/887f8c16b1e2f9db7c8e54489d7576ea0fcc375c)]
-  build(deps-dev): Bump postcss from 8.4.14 to 8.4.31 in /apps/web ([#74](https://github.com/newgen-business-solutions/cs-event-services/issues/74)) [[b5eefa6](https://github.com/newgen-business-solutions/cs-event-services/commit/b5eefa62d1e2e4cdac8f831ef8d9035cfda6f9c8)]
-  build(deps): Bump word-wrap from 1.2.3 to 1.2.5 in /apps/web ([#76](https://github.com/newgen-business-solutions/cs-event-services/issues/76)) [[56b4c93](https://github.com/newgen-business-solutions/cs-event-services/commit/56b4c93d139cf86312a26b5ad1fa3852942d5496)]
-  Merge branch &#x27;CSES-305-ca-sale-support&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-305-ca-sale-support [[04bae6f](https://github.com/newgen-business-solutions/cs-event-services/commit/04bae6fab6853ab7f5c24b3ebc7adc934774668e)]
-  Squashed commit of the following: [[bbb5b7f](https://github.com/newgen-business-solutions/cs-event-services/commit/bbb5b7f218790d906ff06a99a18cbeaa08494539)]
-  Squashed commit of the following: [[3e8d1d0](https://github.com/newgen-business-solutions/cs-event-services/commit/3e8d1d00e47cfbd78af0d22b0cc22a88f6030742)]
-  Squashed commit of the following: [[db08d76](https://github.com/newgen-business-solutions/cs-event-services/commit/db08d76da2c84dc1828e13febea8c2b7fffc3adc)]
-  Squashed commit of the following: [[b4ebeb5](https://github.com/newgen-business-solutions/cs-event-services/commit/b4ebeb5fe6fc901d9cb01cb08de771282512979d)]
- 📦 Update install state &#x60;yarn&#x60; [[34e81fd](https://github.com/newgen-business-solutions/cs-event-services/commit/34e81fd65cb90fa62986b0970d1bbdfe6df9c1e8)]
- 📦 Update package deps [[4a44bfb](https://github.com/newgen-business-solutions/cs-event-services/commit/4a44bfbb00f261f1717b1c45f6cf3bc37343e662)]
-  Merge remote-tracking branch &#x27;origin/dependabot/npm_and_yarn/apps/booking-calendar/babel/traverse-7.23.2&#x27; into CSES-305-ca-sale-support [[ec1e834](https://github.com/newgen-business-solutions/cs-event-services/commit/ec1e834e019bf0123302e007ac2e7574f0187d00)]
-  build(deps): Bump @babel/traverse from 7.21.4 to 7.23.2 in /apps/backend ([#63](https://github.com/newgen-business-solutions/cs-event-services/issues/63)) [[0f2b096](https://github.com/newgen-business-solutions/cs-event-services/commit/0f2b096c2848633d76d704a54e17a4e42e0e057c)]
-  build(deps-dev): Bump postcss in /apps/booking-calendar ([#59](https://github.com/newgen-business-solutions/cs-event-services/issues/59)) [[f1fe2e1](https://github.com/newgen-business-solutions/cs-event-services/commit/f1fe2e14d509c837deb006107d1c834be339a929)]
-  Squashed commit of the following: [[3f492a0](https://github.com/newgen-business-solutions/cs-event-services/commit/3f492a093719f3d9955beb1d36b4819dcb526592)]
-  build(deps): Bump semver in /apps/booking-calendar ([#51](https://github.com/newgen-business-solutions/cs-event-services/issues/51)) [[af6a58c](https://github.com/newgen-business-solutions/cs-event-services/commit/af6a58ca608cca0dcf93a5dcc7e4af3a805382c2)]
-  Merge branch &#x27;CSES-305-ca-sale-support&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-305-ca-sale-support [[b1fabf1](https://github.com/newgen-business-solutions/cs-event-services/commit/b1fabf13e3a985827c6b8c5504f76723bdeddc46)]
- 📦 Package updates of deps [[64c6afe](https://github.com/newgen-business-solutions/cs-event-services/commit/64c6afe26318ae6d20a62bc341f6f133b018fc7a)]
-  build(deps): Bump @babel/traverse from 7.21.4 to 7.23.3 in /apps/web ([#77](https://github.com/newgen-business-solutions/cs-event-services/issues/77)) [[ee6c75c](https://github.com/newgen-business-solutions/cs-event-services/commit/ee6c75cfa026831da3770174eb572b754d0e7e2a)]
-  build(deps): Bump qs and express in /apps/booking-calendar/api ([#50](https://github.com/newgen-business-solutions/cs-event-services/issues/50)) [[9106cde](https://github.com/newgen-business-solutions/cs-event-services/commit/9106cdedec8457584d1b7a4edc946512bbe64864)]
- 📝 Fix ESLint Error [[c32793d](https://github.com/newgen-business-solutions/cs-event-services/commit/c32793d17d832f5e03a85c6e7b2eed894d0c7995)]
-  build(deps): Bump browserify-sign from 4.2.1 to 4.2.2 [[2537930](https://github.com/newgen-business-solutions/cs-event-services/commit/25379306a6b41cc908607c64612ca11e2d1c9055)]
-  build(deps): Bump @babel/traverse from 7.23.0 to 7.23.3 [[32a93e6](https://github.com/newgen-business-solutions/cs-event-services/commit/32a93e62483204be4fbfcb46f3c32db6971c3181)]
-  build(deps-dev): Bump crypto-js from 4.1.1 to 4.2.0 [[85be94a](https://github.com/newgen-business-solutions/cs-event-services/commit/85be94a298d225bb99dde0a093aca545e834b8e7)]
-  build(deps): Bump @babel/traverse in /apps/booking-calendar [[adb1967](https://github.com/newgen-business-solutions/cs-event-services/commit/adb1967e289ff1d7b10579d32901930e6c09b889)]


<a name="9.4.4"></a>
## 9.4.4 (2023-11-09)

### Added

- 🎉 FEAT: CS Bill Of Lading ([#57](https://github.com/newgen-business-solutions/cs-event-services/issues/57)) [[97494e3](https://github.com/newgen-business-solutions/cs-event-services/commit/97494e3b3de072cae4dc55a7f906749735b286ca)]
- 🎉 Add New Relic Analytics to browser session [[5f4b4c2](https://github.com/newgen-business-solutions/cs-event-services/commit/5f4b4c2580e423b3a5876348874beed3a2c69466)]
- ✅ Add express proxy for local REST [[a2aa754](https://github.com/newgen-business-solutions/cs-event-services/commit/a2aa754f29edcd740cf237877a7875896eae9958)]
- ➕ Created Restlet BOL creator [[2d2afb9](https://github.com/newgen-business-solutions/cs-event-services/commit/2d2afb939b3e383607c1fedea188d4345647e70e)]

### Changed

- ⬆️ Update SuiteApp Manifest Version 9.4.4 [[1e0dec2](https://github.com/newgen-business-solutions/cs-event-services/commit/1e0dec2518f32814408223f04fbfb444df65156a)]
- ⬆️ Update version number [[b07a20b](https://github.com/newgen-business-solutions/cs-event-services/commit/b07a20bab758b18ee13cb0f1790d7c9da22ff75f)]
- 🔧 Fix(BE) Web handler for Non PT users [[5aac4fb](https://github.com/newgen-business-solutions/cs-event-services/commit/5aac4fb803845acf04e7c613f4ed91337c6d0054)]
- 🔧 Change(web) tracking optional chaining [[e270a38](https://github.com/newgen-business-solutions/cs-event-services/commit/e270a386b11a1d79a02c62eb49da32ef4c44b40c)]
- ⚡ Optimize(web) tracking info purge [[d345b08](https://github.com/newgen-business-solutions/cs-event-services/commit/d345b082d7859562c50d36331d9f0c56e7986c98)]
- ⚡ Optimize(web) tracking requests [[a8fe421](https://github.com/newgen-business-solutions/cs-event-services/commit/a8fe42107287723cdc957c55312b0368b2aae292)]
- 🔧 Change(web) &#x60;post/order&#x60; timeout&#x60;&#x60; [[afdb82b](https://github.com/newgen-business-solutions/cs-event-services/commit/afdb82bb6eb2016f071426304a33aa660ce10967)]
- 🔧 Update(web) card defaulting endpoint [[7f6cc47](https://github.com/newgen-business-solutions/cs-event-services/commit/7f6cc4714beae8ea2624339ab13ccf062e78920f)]
- 🔧 Set config in function scoped [[4b54d12](https://github.com/newgen-business-solutions/cs-event-services/commit/4b54d124d588450eb4485af1367b4f0a9bc6a586)]
- 🔧 Update(web) timeout for just the order POST [[9d66a92](https://github.com/newgen-business-solutions/cs-event-services/commit/9d66a925801e63b815906aa7ba1320f6ee1f067d)]
- 🔧 Fix timeout config path [[c3e93cf](https://github.com/newgen-business-solutions/cs-event-services/commit/c3e93cf39b2972c97a4aa388571e442c02cb4581)]
- ♻️ REF(web) out of &#x60;prevState&#x60; for days calc [[f6d6d50](https://github.com/newgen-business-solutions/cs-event-services/commit/f6d6d5040a30f58c9dc7764b6063412fdd1c9ecd)]
- 🎨 Change(web) nav logo loader [[c90ad8e](https://github.com/newgen-business-solutions/cs-event-services/commit/c90ad8e39cb2621ca47113d7726cc26efcef93bb)]
- ⚡ Optimize(web) settings paint server side [[746e6bf](https://github.com/newgen-business-solutions/cs-event-services/commit/746e6bfd9ac9bce4f2ff1796b4769bdb8c144a84)]
- ⚡ Optimize(web) URL for settings fetch [[3c7c809](https://github.com/newgen-business-solutions/cs-event-services/commit/3c7c809ae521f3ea9bc00647befc0eacdf9fb2be)]
- ⚡ Optimize(web) settings delivery by server [[f2dcec8](https://github.com/newgen-business-solutions/cs-event-services/commit/f2dcec889ef6cf2ae67ec4e898429ef82aadc687)]
- ⚡ Optimize(web) order execution timeout [[cfb04d3](https://github.com/newgen-business-solutions/cs-event-services/commit/cfb04d3c4df5b8e07950abbcd25286524b7c9db5)]
- 🔧 Change: TENT On Event Dates ([#56](https://github.com/newgen-business-solutions/cs-event-services/issues/56)) [[f662473](https://github.com/newgen-business-solutions/cs-event-services/commit/f662473dc397a37bae8c6d4620d26850dba9c421)]
- ⚡ Optimize Labor Algorithm ([#55](https://github.com/newgen-business-solutions/cs-event-services/issues/55)) [[6d47a0a](https://github.com/newgen-business-solutions/cs-event-services/commit/6d47a0a486cd15136f97ee8bf78cf8e4af2d81c9)]
- 🔧 Change(booking) local startup for SPA [[3e4a16b](https://github.com/newgen-business-solutions/cs-event-services/commit/3e4a16bf999176380830cc63ef7d98d8ea52c932)]
- 🔧 Config changes to Ngrok path [[4aa228e](https://github.com/newgen-business-solutions/cs-event-services/commit/4aa228ed24f29868a5f27aec8e6572c94fa79c5e)]
- 🎨 Change PID event path for header [[152002f](https://github.com/newgen-business-solutions/cs-event-services/commit/152002f2c74955c108ed11e55617bbde0cfa5616)]
- 🔧 Change(BE) Master Reset Rendering [[fccc759](https://github.com/newgen-business-solutions/cs-event-services/commit/fccc759eeda691eba327d71bb9c2f99659c2a534)]
- ⬆️ Bump decode-uri-component from 0.2.0 to 0.2.2 ([#19](https://github.com/newgen-business-solutions/cs-event-services/issues/19)) [[a709586](https://github.com/newgen-business-solutions/cs-event-services/commit/a709586060d196688880067d2587d7e665e7c2b9)]
- ⬆️ Bump semver from 6.3.0 to 6.3.1 ([#39](https://github.com/newgen-business-solutions/cs-event-services/issues/39)) [[a5f2fd4](https://github.com/newgen-business-solutions/cs-event-services/commit/a5f2fd41e5ef43b43f6b5d379d6047ab4524dee7)]
- ⬆️ Bump tough-cookie from 4.1.2 to 4.1.3 in /apps/backend ([#38](https://github.com/newgen-business-solutions/cs-event-services/issues/38)) [[a2662bd](https://github.com/newgen-business-solutions/cs-event-services/commit/a2662bd3957930454d949d77fad8e8bd2e94919a)]
- ⬆️ Bump word-wrap from 1.2.3 to 1.2.4 in /apps/backend ([#41](https://github.com/newgen-business-solutions/cs-event-services/issues/41)) [[6b6ced9](https://github.com/newgen-business-solutions/cs-event-services/commit/6b6ced992aa7ffc12efb5b2cc7a43a54c9968f2e)]
- 🔧 Sync changes from &#x60;main&#x60; [[c7a9ead](https://github.com/newgen-business-solutions/cs-event-services/commit/c7a9eade23f720a03aca0ad49a55d818e9d50f9e)]
- 🔧 Chore(booking) Add HMR to API server [[165912b](https://github.com/newgen-business-solutions/cs-event-services/commit/165912be6817830c4166935c1f1bd9ea7a374f73)]

### Removed

- 🔥 Remove(web) &#x60;functions&#x60; config [[92999f6](https://github.com/newgen-business-solutions/cs-event-services/commit/92999f616c1de56c262cea96bef0b14bca5c56eb)]
- 🔥 Remove(web) Pinnacle ENVs for builds [[a7108e0](https://github.com/newgen-business-solutions/cs-event-services/commit/a7108e07c147e7808bd876f7925d549aacbd14ca)]
- 🔥 Remove(web) &#x60;useStyles&#x60; from input mask [[7f2c179](https://github.com/newgen-business-solutions/cs-event-services/commit/7f2c1794396f67f1c6e145952db1263cf286fdbf)]

### Fixed

- 🚑 Fix(web) No PT checkout [[254e00c](https://github.com/newgen-business-solutions/cs-event-services/commit/254e00c0103704d59443536f15c0d360199e9637)]
- 🚑 Fix(web) vercel timeout to 90 seconds [[27b71ca](https://github.com/newgen-business-solutions/cs-event-services/commit/27b71cafe14aea4c65e8cf85913f6aeef6a939eb)]
- 🚑 Fix(web) default import on BOL Pickup [[b5c2d45](https://github.com/newgen-business-solutions/cs-event-services/commit/b5c2d45c3977fb1e835e7e2a58798ee6078e2cca)]
- 🚑 Fix(web) address modal [[a4573d3](https://github.com/newgen-business-solutions/cs-event-services/commit/a4573d3d4935463909af312acc502780a67ec3e5)]
- 🚑 Fix(BE) my account center [[6c3eb1f](https://github.com/newgen-business-solutions/cs-event-services/commit/6c3eb1f19a4642d9ddc71280c6536db0835ff1fc)]
- 🐛 Fix Deposit Amount Retrieval  ([#53](https://github.com/newgen-business-solutions/cs-event-services/issues/53)) [[e0080d7](https://github.com/newgen-business-solutions/cs-event-services/commit/e0080d7c485f4975980460da328203ce152a9f10)]
- 🐛 Fix Tax Group Application On Orders ([#52](https://github.com/newgen-business-solutions/cs-event-services/issues/52)) [[73cf4bd](https://github.com/newgen-business-solutions/cs-event-services/commit/73cf4bd8b5690cf9fc6d02bea1018f172aee9500)]
- ✏️ Update version and manifest to &#x60;8.13.171&#x60; [[e82ac6d](https://github.com/newgen-business-solutions/cs-event-services/commit/e82ac6d644dd8a511feefab3b58ff0c0235c4263)]

### Security

- 🔒 Add(web) Guard to all Protected Pages [[d3d5a95](https://github.com/newgen-business-solutions/cs-event-services/commit/d3d5a95b00be1065971eead2d4dc6efbbb672fb3)]

### Miscellaneous

- 💥 Major Release 9.0 ([#73](https://github.com/newgen-business-solutions/cs-event-services/issues/73)) [[0e1ee15](https://github.com/newgen-business-solutions/cs-event-services/commit/0e1ee15ecf211309d1311417154006a63d40a1a1)]
- 🔨 Chore(web) Update PRX Creds [[fb8d9c0](https://github.com/newgen-business-solutions/cs-event-services/commit/fb8d9c0f822ca95677a9faaf8fe0231ef4f6497c)]
- 🔨 Chore: Update next-build.yml ([#62](https://github.com/newgen-business-solutions/cs-event-services/issues/62)) [[0222bdb](https://github.com/newgen-business-solutions/cs-event-services/commit/0222bdbfbb80f555f5995f47a5784df023864efb)]
- 📦 Update lock with cypress version &#x60;13.3.0&#x60; [[e40507a](https://github.com/newgen-business-solutions/cs-event-services/commit/e40507aa829b3fd86b5e05f922ff4f0c037fe5ac)]
- 📗 Update(web) cypress tests [[17c7a3d](https://github.com/newgen-business-solutions/cs-event-services/commit/17c7a3de145dbe0e8206e18ccef8a98421ac4759)]
- 📝 Fix(web) default importing BOL text mask [[892c199](https://github.com/newgen-business-solutions/cs-event-services/commit/892c19947defb96c4a4fa0d50c27c68cd8e299a8)]
- 📝 Fix(web) ESLint import arrangement [[3fe8ad8](https://github.com/newgen-business-solutions/cs-event-services/commit/3fe8ad84036e0f9b95947c50144bd0fd2af2d0c4)]
-  Added labor-sequence-diagram.drawio [[3f40fec](https://github.com/newgen-business-solutions/cs-event-services/commit/3f40fecaa29618e80918c7e484f83ae31e7b3467)]
-  Merge pull request [#49](https://github.com/newgen-business-solutions/cs-event-services/issues/49) from Bol-and-label-feature [[579f98d](https://github.com/newgen-business-solutions/cs-event-services/commit/579f98d6fce2765508764fe959a32a129ea6c489)]
-  Merge branch &#x27;Bol-and-label-feature&#x27; of https://github.com/newgen-business-solutions/cs-event-services into Bol-and-label-feature [[75a1113](https://github.com/newgen-business-solutions/cs-event-services/commit/75a11132d998bca696984725fa0677fb75ce95ca)]
-  Merge branch &#x27;CSES-274-Booking-Calendar-Prototype&#x27; into Bol-and-label-feature [[801ec5e](https://github.com/newgen-business-solutions/cs-event-services/commit/801ec5e3834fd9d149ef38beac81344c07bc2225)]
- 📎 Update(booking) API spa docs [[a980a18](https://github.com/newgen-business-solutions/cs-event-services/commit/a980a1851c918f785c5210b93086ec2f7da8e7cc)]
- 🚧 Booking calendar initial set up [[41f6d29](https://github.com/newgen-business-solutions/cs-event-services/commit/41f6d29fc851c304923803dbc295a29cb1f2c1b5)]
-  BOL UE Script [[e46765a](https://github.com/newgen-business-solutions/cs-event-services/commit/e46765a9bd94592fb2b629c1833b89a1357c4763)]


<a name="8.13.171"></a>
## 8.13.171 (2023-08-28)

### Added

- ✨ Feat: Start and End Dates on CS Event ([#48](https://github.com/newgen-business-solutions/cs-event-services/issues/48)) [[a7dbf23](https://github.com/newgen-business-solutions/cs-event-services/commit/a7dbf238a94847f304fe55ad43b7dd765d2245ab)]
- ✅ Add express proxy for local REST [[a2aa754](https://github.com/newgen-business-solutions/cs-event-services/commit/a2aa754f29edcd740cf237877a7875896eae9958)]
- ➕ Add(backend) EUA settings fields to REST [[b5acc81](https://github.com/newgen-business-solutions/cs-event-services/commit/b5acc814a7b04128badfc874e5d84840790d0fbb)]
- ✨ Add(WEB) Support for SPFT + Matrix ([#44](https://github.com/newgen-business-solutions/cs-event-services/issues/44)) [[f35c330](https://github.com/newgen-business-solutions/cs-event-services/commit/f35c330ee873d8601c125f8f0c086a7e96d2d76b)]
- ✅ Added new user structure [[31ff8dd](https://github.com/newgen-business-solutions/cs-event-services/commit/31ff8dd245adbe5bebdbc2bb295544d728c38f92)]
- ➕ Added react-buddy dev tools [[ab956b0](https://github.com/newgen-business-solutions/cs-event-services/commit/ab956b081d5316720ff2165030f80aca7c7aeeed)]
- 🎉 FEAT: International ordering support [[9ba4243](https://github.com/newgen-business-solutions/cs-event-services/commit/9ba4243c4cb7648710ffd0ffd99f7b2258a78f27)]
- ➕ Created Restlet BOL creator [[2d2afb9](https://github.com/newgen-business-solutions/cs-event-services/commit/2d2afb939b3e383607c1fedea188d4345647e70e)]
- 🎉 Added auto tax on venue import [[b35361f](https://github.com/newgen-business-solutions/cs-event-services/commit/b35361fe124f349591a4ee576cdb18ddd6fb7d21)]
- ✅ Done refactoring CS Event UE [[d5c2226](https://github.com/newgen-business-solutions/cs-event-services/commit/d5c22262695c9cbcbc16daf0ca0e9378390332c5)]
- 🎉 FEAT Finished International Ordering Wizard [[05f436e](https://github.com/newgen-business-solutions/cs-event-services/commit/05f436e997697f7f3034e5d3e031d23cfc10524e)]
- ✨ FT Added print button to recent orders [[8c09b3d](https://github.com/newgen-business-solutions/cs-event-services/commit/8c09b3d8803bfe6b5dc4928c5571150e991c6ac5)]
- ✅ Added client msg for default form [[5318b69](https://github.com/newgen-business-solutions/cs-event-services/commit/5318b69646f9d8a71a35e063cb2831b3fa4f14a4)]
- 🎉 Added Customer Refund Support for SOs PDF [[a104568](https://github.com/newgen-business-solutions/cs-event-services/commit/a1045684293430c865fe0bfe684004dc08497a79)]

### Changed

- ⬆️ Bump decode-uri-component from 0.2.0 to 0.2.2 ([#19](https://github.com/newgen-business-solutions/cs-event-services/issues/19)) [[a709586](https://github.com/newgen-business-solutions/cs-event-services/commit/a709586060d196688880067d2587d7e665e7c2b9)]
- ⬆️ Bump semver from 6.3.0 to 6.3.1 ([#39](https://github.com/newgen-business-solutions/cs-event-services/issues/39)) [[a5f2fd4](https://github.com/newgen-business-solutions/cs-event-services/commit/a5f2fd41e5ef43b43f6b5d379d6047ab4524dee7)]
- ⬆️ Bump tough-cookie from 4.1.2 to 4.1.3 in /apps/backend ([#38](https://github.com/newgen-business-solutions/cs-event-services/issues/38)) [[a2662bd](https://github.com/newgen-business-solutions/cs-event-services/commit/a2662bd3957930454d949d77fad8e8bd2e94919a)]
- ⬆️ Bump word-wrap from 1.2.3 to 1.2.4 in /apps/backend ([#41](https://github.com/newgen-business-solutions/cs-event-services/issues/41)) [[6b6ced9](https://github.com/newgen-business-solutions/cs-event-services/commit/6b6ced992aa7ffc12efb5b2cc7a43a54c9968f2e)]
- 🔧 Sync changes from &#x60;main&#x60; [[c7a9ead](https://github.com/newgen-business-solutions/cs-event-services/commit/c7a9eade23f720a03aca0ad49a55d818e9d50f9e)]
- 🔧 Chore(booking) Add HMR to API server [[165912b](https://github.com/newgen-business-solutions/cs-event-services/commit/165912be6817830c4166935c1f1bd9ea7a374f73)]
- 🎨 Change(web) Media query for payment modal [[6a6bf42](https://github.com/newgen-business-solutions/cs-event-services/commit/6a6bf420f99cbce95186427f7c33901f695ae355)]
- 🎨 Change(web) view port media query [[581df74](https://github.com/newgen-business-solutions/cs-event-services/commit/581df7467a867dfd371a38490082f088db20b537)]
- 🎨 Change(web) Event Select link [[2037f9b](https://github.com/newgen-business-solutions/cs-event-services/commit/2037f9b05cd6392127e79790990511f060ae9f2d)]
- 🎨 Change(web) Avatar render without session [[4b52eb6](https://github.com/newgen-business-solutions/cs-event-services/commit/4b52eb6f242863ca8e082e5f90aa9fb692368a4f)]
- 🔧 Fix(web) add router [[418588b](https://github.com/newgen-business-solutions/cs-event-services/commit/418588bb744960e5b4bbd7053e3bafe90633f00e)]
- 🔧 Change(web) callback for sign in [[4b9fa9e](https://github.com/newgen-business-solutions/cs-event-services/commit/4b9fa9e2b46ba1f3ecd23d0786cc2257c5239895)]
- 🎨 Change(web) font fix for userbox [[7b78dfd](https://github.com/newgen-business-solutions/cs-event-services/commit/7b78dfde114a1a9b34d2e9a33b495c30050dc4f9)]
- 🎨 Change(web) auth routes to use array [[4a2354b](https://github.com/newgen-business-solutions/cs-event-services/commit/4a2354bfe08d71cc3a69002f5db8a6874adf9248)]
- 🎨 Change(web) move sign out button [[2cd3714](https://github.com/newgen-business-solutions/cs-event-services/commit/2cd3714d81c20567fe2767807314c85e164fdab9)]
- 🔧 Change Auth page to exclude root from guard [[9947612](https://github.com/newgen-business-solutions/cs-event-services/commit/9947612cf76ee041b429871f9def4cf108d18bd1)]
- 🎨 Change(web) Event selection href on Nav [[aa78679](https://github.com/newgen-business-solutions/cs-event-services/commit/aa786793012998d6ed0e3edee186ad49f38c8362)]
- ♻️ REF(web) API Routes now use aliases [[f987e12](https://github.com/newgen-business-solutions/cs-event-services/commit/f987e12e2a4c2a980fce3ff9da89c41fd36acffd)]
- 🔧 Fixed(web) Removal of cart context [[6954bab](https://github.com/newgen-business-solutions/cs-event-services/commit/6954bab5faaa4309423b09ba83154032a2bff817)]
- 🔧 Fix(web) Auth callback with REST domain [[9abb405](https://github.com/newgen-business-solutions/cs-event-services/commit/9abb405fcd4c01bbe4bffdb375c1bafd9f84db07)]
- ♻️ REF(web) ENV Client ID exposure [[c3a47c8](https://github.com/newgen-business-solutions/cs-event-services/commit/c3a47c80a04697b47c6f1190cef3d4ca4ebfaffe)]
- ⚡ Optimize(web) Component Imports [[4252029](https://github.com/newgen-business-solutions/cs-event-services/commit/42520290af3d3b98ba78cb1ec7e80017380fe100)]
- ⚡ Optimize(web) Increase Page Speed [[a412282](https://github.com/newgen-business-solutions/cs-event-services/commit/a412282f2a1176d554bee8c4be353b06c71eac1d)]
- ♻️ REF(web) To &#x60;next/legacy/image&#x60; [[a797dd3](https://github.com/newgen-business-solutions/cs-event-services/commit/a797dd3e5036c6e3a481316bb666d4ef2389afa6)]
- ♻️ Refactor(web) Out &#x60;@next/font&#x60; to &#x60;next/font&#x60; [[6935abf](https://github.com/newgen-business-solutions/cs-event-services/commit/6935abfbae283dde86c424f50196389a7f9ae589)]
- 🔧 Refactor(backend) Opp &amp; Price level SLs [[12a62e0](https://github.com/newgen-business-solutions/cs-event-services/commit/12a62e0ce762ebcc5b1eb24d776b207a186ea2d0)]
- ♻️ Refactor(backend): CSV Import tool [[d2bf8af](https://github.com/newgen-business-solutions/cs-event-services/commit/d2bf8afdf6c3662dbe09c6dc33a02fd9d001ce90)]
- 🔧 Refactor(backend): Changed project type [[0f1b940](https://github.com/newgen-business-solutions/cs-event-services/commit/0f1b940cfc36578a9d8fad6ddb4b15363ba81b8c)]
- 🔧 Fix(backend) ALS Settings Grab [[44a450e](https://github.com/newgen-business-solutions/cs-event-services/commit/44a450ea28e857dba8b0f094bcd77cff45c97304)]
- 🔧 Fix(web) Modal State for checkout [[e16fe63](https://github.com/newgen-business-solutions/cs-event-services/commit/e16fe63d5317cadb5bcb80878520d1bef2c8390b)]
- 🎨 Change(web) conditionally render EUA for checkout [[7ef667c](https://github.com/newgen-business-solutions/cs-event-services/commit/7ef667cef85693ba8c8bfe822cae140646171311)]
- 🔧 Fix(web) ESLint Formatting [[b09ade1](https://github.com/newgen-business-solutions/cs-event-services/commit/b09ade18462c25da2c6c7cf3d4c745647d7d2f39)]
- ♻️ Refactor(web) Settings to use Zustand [[68ee078](https://github.com/newgen-business-solutions/cs-event-services/commit/68ee078952725838fb0f861d9645db39673fd92d)]
- 🔧 Fix(backend) Mailer Tab Btn Render [[b58544c](https://github.com/newgen-business-solutions/cs-event-services/commit/b58544c20a80f78f6eb35229f9ac2cacc6527b16)]
- 🔧 Modified(backend) to not use prev dep % [[3eaccb0](https://github.com/newgen-business-solutions/cs-event-services/commit/3eaccb098782484d28403212f7f0324370c6cf59)]
- 🔧 Add(backend) Mass update for LVE [[ce01cc6](https://github.com/newgen-business-solutions/cs-event-services/commit/ce01cc6d7263bcbe618fa64b1836e105131b1843)]
- ♻️ Refactor(backend) - Deposit percent enforcement according to order type. ([#42](https://github.com/newgen-business-solutions/cs-event-services/issues/42)) [[cc5968c](https://github.com/newgen-business-solutions/cs-event-services/commit/cc5968c30b083416a8c1f774373894468bf08ee2)]
- ♻️ Refactor(backend) replace old order type [[f7dd350](https://github.com/newgen-business-solutions/cs-event-services/commit/f7dd350eadbc1f71ea7bc08f48ec8e7602fae66c)]
- 🔧 Optimize(backend) ALS scripting for LVE [[1020b9b](https://github.com/newgen-business-solutions/cs-event-services/commit/1020b9b8103bf8c86f9cd970932226ee2c1455eb)]
- 🔧 Fix(ABDN Cart) Client Country Sourcing [[d07fdcb](https://github.com/newgen-business-solutions/cs-event-services/commit/d07fdcba7aa6254cc4bff392629b7b1488511790)]
- 🔧 Fix(SO UE) Country lookup to use address key [[9eb2ca6](https://github.com/newgen-business-solutions/cs-event-services/commit/9eb2ca6857616909e2adbe4578775ee8157f9f38)]
- 🔧 Update(SO UE) balance and paid [[6553e7f](https://github.com/newgen-business-solutions/cs-event-services/commit/6553e7f565c5a02df13899bf6ef7bce8bf0056db)]
- 🔧 Update(Dep UE) for new session storage [[7e05d53](https://github.com/newgen-business-solutions/cs-event-services/commit/7e05d53d6b8df3c1e355f9148fb87a8343429c36)]
- 🔧 Fix(CD Client) Conv Fee order type check [[31e33a9](https://github.com/newgen-business-solutions/cs-event-services/commit/31e33a9ac5017b1916b6190bdd027e0ce58284de)]
- ⚡ Optimize(Deposit CS) Increase UX Speed [[f4da4d4](https://github.com/newgen-business-solutions/cs-event-services/commit/f4da4d42fc374d80613c19ce2b4a9ec48e39d668)]
- ♻️ Refactor logic for convenience fee [[a44b1ab](https://github.com/newgen-business-solutions/cs-event-services/commit/a44b1ab5c464d255c3486f7bb8b6efe6341870e6)]
- ♻️ Update to account objects &amp; scripts [[6c5dfb2](https://github.com/newgen-business-solutions/cs-event-services/commit/6c5dfb21c56193255b0dc1f543f6d69aa06e0e16)]
- 🔧 Chore(buildEnv) update tracking url in dev [[30b93dc](https://github.com/newgen-business-solutions/cs-event-services/commit/30b93dc9de93fd638ac1cc644cbb6468054cbf2e)]
- 🎨 Update README with automation stuff [[923378e](https://github.com/newgen-business-solutions/cs-event-services/commit/923378e85a9f402f4e06aa93b0c96a03a6c70411)]
- 🎨 update backend readme [[cbb52fb](https://github.com/newgen-business-solutions/cs-event-services/commit/cbb52fbfc2231630ef9fc9944cd2464f7a66a4c6)]
- 🎨 Update README [[feed871](https://github.com/newgen-business-solutions/cs-event-services/commit/feed871709a1b3db3c107020a60e79075d0672ca)]
- 🎨 Refactor: Booth banner text color inherits parent [[133d6bf](https://github.com/newgen-business-solutions/cs-event-services/commit/133d6bfb5a15666c5a7a9491690ec0e1ba96a0e1)]
- ⚡ Optimized cart order conversion [[f5efa38](https://github.com/newgen-business-solutions/cs-event-services/commit/f5efa3893e23608ec83a4139728d9e9346c8553d)]
- 🎨 Fixed extra padding on banner entrance [[e40bd0a](https://github.com/newgen-business-solutions/cs-event-services/commit/e40bd0afb8ec8a3059871e693a3d619c0a3eabc8)]
- ⚡ Optimized imports for components [[cc20ee5](https://github.com/newgen-business-solutions/cs-event-services/commit/cc20ee5504f6cd31eb1883191df9cb9e57b52aec)]
- 🎨 UI: design for event details [[40f397f](https://github.com/newgen-business-solutions/cs-event-services/commit/40f397f9db45a9fb8fd16e520717196518b16528)]
- ♻️ Refactored CS Event wizard taxation [[706be08](https://github.com/newgen-business-solutions/cs-event-services/commit/706be08504fdd2f2749a8c2a3bbc85f1f17b315a)]
- 🎨 UI: Redesigned account list and hero [[9562e7b](https://github.com/newgen-business-solutions/cs-event-services/commit/9562e7b20d92c466ff361b571ca191789a4e737b)]
- 🎨 Added loader button to event submission [[909248b](https://github.com/newgen-business-solutions/cs-event-services/commit/909248bd4293f1fd70bf0c51c4f316194aa4fd14)]
- 🎨 Added Event Banner Component [[9babe12](https://github.com/newgen-business-solutions/cs-event-services/commit/9babe12665515e314cdc2cd60f5a9072930a61d7)]
- ⚡ Optimized all component imports [[85fc76a](https://github.com/newgen-business-solutions/cs-event-services/commit/85fc76a6901065ce9b97893df47e90d426c28889)]
- 🔧 Fixed ESLinting on BOL File [[a549986](https://github.com/newgen-business-solutions/cs-event-services/commit/a549986865e2513282857dd20b83bfc987f69f52)]
- 🎨 Refactored design of search header [[8eee1f1](https://github.com/newgen-business-solutions/cs-event-services/commit/8eee1f16ec3d4a7c035ace912ccc1c77008af4ea)]
- ♻️ Fixed Paytrace error capturing. [[76ede8d](https://github.com/newgen-business-solutions/cs-event-services/commit/76ede8d8fb182337909368af08e6e41acb43954d)]
- 🎨 Corrected event pg mobile responsiveness [[4c3804f](https://github.com/newgen-business-solutions/cs-event-services/commit/4c3804f754432a42631bd8aac607b3eda6238f2d)]
- 🎨 Reworked UI for event page [[0471bff](https://github.com/newgen-business-solutions/cs-event-services/commit/0471bff1f5b533d0851f9fa934b244dd074d87b9)]
- ♻️ Merge PR [#32](https://github.com/newgen-business-solutions/cs-event-services/issues/32) from CSES-168-major-release-4 [[a5ec8ac](https://github.com/newgen-business-solutions/cs-event-services/commit/a5ec8ac8fdf449298536f07f059ccdfea920b25f)]
- 💬 Added comments for wizard [[d76a74a](https://github.com/newgen-business-solutions/cs-event-services/commit/d76a74a9d89c2962fecde8343d83213849e9f164)]
- ♻️ Refactored New Order Wizard [[c82bedc](https://github.com/newgen-business-solutions/cs-event-services/commit/c82bedc9bfd38c78aeda29c098d142c3f2ffb4bf)]
- ⚡ Optimized checkout page order posting [[617415f](https://github.com/newgen-business-solutions/cs-event-services/commit/617415f7dc250fa3e2b9d1bd475d939f0ac87dbc)]
- ♻️ Refactor CS Event Creation Wizard [[8fa6af9](https://github.com/newgen-business-solutions/cs-event-services/commit/8fa6af96d9f82b3c2048c2731a5cb32a724b7e74)]
- ♻️ Refactor portal order processing [[9846ffc](https://github.com/newgen-business-solutions/cs-event-services/commit/9846ffcbc6a1def60d01e810b7f7502213278924)]
- 🔧 Refactor Changed image file AWL to dialog [[1b6f1bd](https://github.com/newgen-business-solutions/cs-event-services/commit/1b6f1bdfe1151479904a8f8161a33c64222cc6f5)]
- ⚡ Completed CS Event Client Interaction [[ee06219](https://github.com/newgen-business-solutions/cs-event-services/commit/ee06219621de3fcfcd89a340b6e5ed03b10b4a8d)]
- 🔧 Added in tasking for deletion of CS Event [[94683ad](https://github.com/newgen-business-solutions/cs-event-services/commit/94683ad842f75297e9aa3de7c95468b56134ac09)]
- 🔧 Changes made to &#x60;Settings&#x60; type hook [[c4e6605](https://github.com/newgen-business-solutions/cs-event-services/commit/c4e66050df39329ad8b7c330944f49f52437a7e9)]
- ♻️ Refactored &#x60;useTaxCode&#x60; hook [[1c4058c](https://github.com/newgen-business-solutions/cs-event-services/commit/1c4058c053c5dc41e44f0f07e579a0565a4a8aa7)]
- ⚡ Finished CS Event UE Optimization [[08eca39](https://github.com/newgen-business-solutions/cs-event-services/commit/08eca39b732657203ae1a7e7fd10f28eb73c190e)]
- 🔧 Refactor: Changed prettier config [[eb48b27](https://github.com/newgen-business-solutions/cs-event-services/commit/eb48b2779062858c4fe5865283614ee4d00d9bf4)]
- ⬆️ Bump xml2js and @oracle/suitecloud-unit-testing [[a7e23cc](https://github.com/newgen-business-solutions/cs-event-services/commit/a7e23cc93665bdf0c230ee9624b8146520753471)]
- 🎨 Refactored label of &quot;Position&quot; to &quot;Variant&quot; [[3b5e8ec](https://github.com/newgen-business-solutions/cs-event-services/commit/3b5e8ecad45dc9bcc3a83709ae3ce09e23c39675)]
- ♻️ Refactored rendering of tax fields CS Venue [[b88787f](https://github.com/newgen-business-solutions/cs-event-services/commit/b88787f4dd5b2c30268327aa6a477c32253c3615)]
- ⚡ Refactor - Optimized Sales order UE [[ea4c8f3](https://github.com/newgen-business-solutions/cs-event-services/commit/ea4c8f371e1d742c3e99452b4da678f9fd33ddd4)]
- ♻️ Refactored event ordering process [[ff11aa8](https://github.com/newgen-business-solutions/cs-event-services/commit/ff11aa8365e35a8c10e7b750af84eb631bca73e1)]
- 🎨 RF Made scrollbar appear on mouse enter [[461952b](https://github.com/newgen-business-solutions/cs-event-services/commit/461952bb1317fd7b46089ddb08f37ca0c57249dc)]
- ♻️ RF Paytrace upgrade for new SB URLS [[f4fe7d2](https://github.com/newgen-business-solutions/cs-event-services/commit/f4fe7d25f6f30c99561285b85231d8e6a36a832b)]
- 🔧 Updated event summary reporting 2.1 [[b16a3a7](https://github.com/newgen-business-solutions/cs-event-services/commit/b16a3a7311f5f32399fb013613e9690e77b4d82d)]
- 🔧 Changed API EP Account to return 406 [[2f01271](https://github.com/newgen-business-solutions/cs-event-services/commit/2f0127107239047e6c8e54b56fb2894a50a926dc)]
- ⬆️ Bump webpack from 5.74.0 to 5.76.0 [[9bb78a0](https://github.com/newgen-business-solutions/cs-event-services/commit/9bb78a0ae10f063c24df55963705c900e78d27d6)]
- ⬆️ Bump next-auth from 4.19.2 to 4.20.1 [[3dc30c9](https://github.com/newgen-business-solutions/cs-event-services/commit/3dc30c984717b75565c6325ffe2416fa9411fadb)]
- ⚡ Optimized tracking to be off loaded [[aadb1f5](https://github.com/newgen-business-solutions/cs-event-services/commit/aadb1f5da61f22ef53c13a25a207ecbba6114a60)]
- ⬇️ Reduced Order POST timeout to 40s [[5ad10a3](https://github.com/newgen-business-solutions/cs-event-services/commit/5ad10a31ba4515f9e183ec0f7aabc8d125e2b704)]
- ⬇️ Reduced timeout for invocation order POST [[8f6ca16](https://github.com/newgen-business-solutions/cs-event-services/commit/8f6ca16b51c64e4172096470c83f3ac98421f57a)]
- ⚡ Optimized checkout retry for order fetching [[47b3388](https://github.com/newgen-business-solutions/cs-event-services/commit/47b3388a70530ab60cb3161f096315374afbfe55)]
- 🔧 Changed order handler to use N/hooks [[3c94df7](https://github.com/newgen-business-solutions/cs-event-services/commit/3c94df774774eb60971e19209d8a8e53516ca4ff)]
- 🔧 Added **NEXTAUTH_SECRECT** ENV to readmen [[c1fe45a](https://github.com/newgen-business-solutions/cs-event-services/commit/c1fe45adc108305a2d5580c3e8e2e42eee56dc70)]
- 🎨 Changed matrix variant selection UX [[efe6169](https://github.com/newgen-business-solutions/cs-event-services/commit/efe6169bd52059e726d5a0500033d70c59b99dd3)]
- 🔧 Configure IDE [[38d65b7](https://github.com/newgen-business-solutions/cs-event-services/commit/38d65b7011572f49982425efe2235a4bb0726c5d)]
- 🔧 Disabled quantity change on freight items [[4f43af8](https://github.com/newgen-business-solutions/cs-event-services/commit/4f43af867c4152e8a5288cd86a4ad50f20b1b883)]
- ⚡ Optimized nextauth provider [[04c4a96](https://github.com/newgen-business-solutions/cs-event-services/commit/04c4a966c997975acabfc54c3b97605d290ea565)]
- 🔧 Changed login form verbiage [[c1613b4](https://github.com/newgen-business-solutions/cs-event-services/commit/c1613b4984fa59dff6c65842466a3485a4ddc688)]
- 🎨 Changed loader dismissal locations [[d6d26f1](https://github.com/newgen-business-solutions/cs-event-services/commit/d6d26f156a113d97e9a77cbf27f8df659d814d2e)]
- ⚡ Optimized header setting on order POST [[b53a5b0](https://github.com/newgen-business-solutions/cs-event-services/commit/b53a5b09e4523754d0b4e8960d900b99dee66703)]
- ♻️ Refactored checkout errors for new upstream handler [[2e8b1ee](https://github.com/newgen-business-solutions/cs-event-services/commit/2e8b1ee336ab3eb1246b63dc001c7ba9cbcc71dd)]
- 🎨 Improved layout of buttons on event details [[37b511e](https://github.com/newgen-business-solutions/cs-event-services/commit/37b511effd1526bbb8a5c0b07c902a12a0abe626)]

### Breaking changes

- 💥 Add(web) minimum hundred weight by show ([#47](https://github.com/newgen-business-solutions/cs-event-services/issues/47)) [[f392875](https://github.com/newgen-business-solutions/cs-event-services/commit/f392875713cb1331b4155e5085f9bbd31d81281d)]
- 💥 Refactor(backend): Complete project restructure [[81f0c0d](https://github.com/newgen-business-solutions/cs-event-services/commit/81f0c0d5d5f4c272f822e5d115c593bacfd148f5)]
- 💥 Refactored out CA tax from venue [[038268b](https://github.com/newgen-business-solutions/cs-event-services/commit/038268b4188873f18ca310f4de678a5aa66756fd)]

### Removed

- 🔥 Remove .yarn dir [[216ceba](https://github.com/newgen-business-solutions/cs-event-services/commit/216cebacb8eda83aaee3734d32318d1a0127adbb)]
- 🔥 Remove .yarn from repo [[f488804](https://github.com/newgen-business-solutions/cs-event-services/commit/f488804da49b7e9f8e1a793123412fd4431f34b6)]
- 🔥 Remove(web) unused state for modals on checkout [[6dd93b8](https://github.com/newgen-business-solutions/cs-event-services/commit/6dd93b8ad04ffd2013b8b966f0d50498c73ecc81)]
- 🔥 Removed all file attribute folders [[248ad46](https://github.com/newgen-business-solutions/cs-event-services/commit/248ad46615915ac62fbf81438b8974172894444d)]
- 🔥 Removed location being set on deposit [[ffad27a](https://github.com/newgen-business-solutions/cs-event-services/commit/ffad27a12704837065b89dfb633fa8ea9ee33fd5)]
- 🔥 Removed &#x60;custcol_ng_cs_is_taxable&#x60; from reports [[50235cf](https://github.com/newgen-business-solutions/cs-event-services/commit/50235cf813a8ed3e36a06a5dcd4dbb39d4fb896e)]
- 🔥 Removed unused login file [[aad45e3](https://github.com/newgen-business-solutions/cs-event-services/commit/aad45e3e6c0af1be551a00feb711436a2665435f)]
- 🔥 Removed CA tax setting from SO UE [[3069159](https://github.com/newgen-business-solutions/cs-event-services/commit/3069159ca76caa36b4012fec8ab0e933c80ee282)]

### Fixed

- 🚑 Fix(PayGen) Card encryption GET [[305d0b6](https://github.com/newgen-business-solutions/cs-event-services/commit/305d0b6faf0155bd17db899bc31316a74d9129e0)]
- 🐛 Fix(backend) Rounding discrepancy on SOs [[dc401f9](https://github.com/newgen-business-solutions/cs-event-services/commit/dc401f911d3f46748f52d4d157ae328ead17d734)]
- ✏️ Fix(web) ESLinting of types [[d9faba7](https://github.com/newgen-business-solutions/cs-event-services/commit/d9faba70d46b5270307c82194d1a15317ea49c32)]
- 🚑 Fix refactor of cart context [[5dccf4b](https://github.com/newgen-business-solutions/cs-event-services/commit/5dccf4bb34d1f3e0cbdb6f26e4f8fc56b8345135)]
- ✏️ Fix(web) ESLint Formatting [[999bca5](https://github.com/newgen-business-solutions/cs-event-services/commit/999bca5fe388a2e8e5dfbf08d401cec52af8b080)]
- ✏️ Lint: Removed duplicate import on palette [[12c528d](https://github.com/newgen-business-solutions/cs-event-services/commit/12c528d38dee30e323f65cec148c32cc0484dea5)]
- 🚑 Lint: Fixed &#x60;react-buddy&#x60; previews [[9fec05a](https://github.com/newgen-business-solutions/cs-event-services/commit/9fec05ab246197c38d6b4e54fa6d06c8970da397)]
- 🐛 Fixed SO Record changed error [[97081c9](https://github.com/newgen-business-solutions/cs-event-services/commit/97081c901a229b20be7f3ae236aa6a3451f59d0b)]
- 🐛 Fixed SO &quot;record has changed&quot; error [[fedd99f](https://github.com/newgen-business-solutions/cs-event-services/commit/fedd99f81805402d286fe1cc71da462c85d9ce85)]
- 🐛 Fixed lookup on venue with CS Event wizard [[d180cf9](https://github.com/newgen-business-solutions/cs-event-services/commit/d180cf91584ca382e901d510e894a48cd849a7ea)]
- 🐛 Fixed error on field change for wizard [[cdf85fb](https://github.com/newgen-business-solutions/cs-event-services/commit/cdf85fba38171414cbf0af56bd533cdae95c7cb9)]
- 🚑 Fixed taxation up creating events [[1b59ec5](https://github.com/newgen-business-solutions/cs-event-services/commit/1b59ec5f15e3e060ff6b88bad3184d626bad55ab)]
- ✏️ Fixed search value error S.O. Client [[9e5e44a](https://github.com/newgen-business-solutions/cs-event-services/commit/9e5e44aaaeb1422b42691f439c0414c0740166db)]
- 🍎 Refactor address country ID for S.O. [[5cb1290](https://github.com/newgen-business-solutions/cs-event-services/commit/5cb129037b70c5e4a06a8cd172e12ce594dea9f7)]
- 🏁 Fixed Linting for NS Post [[38f101a](https://github.com/newgen-business-solutions/cs-event-services/commit/38f101a6a78a91d223c303ccedcc7c1daa543a7e)]
- 🐛 Fixed display of booth orders for printing [[f9a4d0e](https://github.com/newgen-business-solutions/cs-event-services/commit/f9a4d0e6eb8bca8c825f319edda53bd49a3ec389)]
- 🐛 Fixed &#x60;id&#x60; error on ordering wizard [[cadc13c](https://github.com/newgen-business-solutions/cs-event-services/commit/cadc13c5946defe302f89e7da492d5b47953db0f)]
- 🍎 Finalized CS Venue with prop tax grouping [[4481014](https://github.com/newgen-business-solutions/cs-event-services/commit/4481014f555bdbc4a45b88af97648f2feb2d8d87)]
- 🚑 Fixed card entry UX on CD [[71de74c](https://github.com/newgen-business-solutions/cs-event-services/commit/71de74c7deb6db317cab4bdd4c8d5c92193da7cf)]
- 🚑 HF Pull request [#29](https://github.com/newgen-business-solutions/cs-event-services/issues/29) merge CSES-168-major-release-4 Patch 3.5.3 [[ee42315](https://github.com/newgen-business-solutions/cs-event-services/commit/ee42315919b5590f9d1e22f60106b8b9636ac6df)]
- 🚑 Fixed checkout sales web funnel [[83bd15b](https://github.com/newgen-business-solutions/cs-event-services/commit/83bd15b990c7a3f0f79446a493529f7107eb79a6)]
- 🍎 Fixed invalid card field change [[d90176e](https://github.com/newgen-business-solutions/cs-event-services/commit/d90176ea474915ef6587fc1b2a71588ca6abc719)]
- 🚑 FIX Merge pull request [#28](https://github.com/newgen-business-solutions/cs-event-services/issues/28) from newgen-business-solutions/CS_Scripted_Reports_Updates [[3acf3b5](https://github.com/newgen-business-solutions/cs-event-services/commit/3acf3b5c2d98770cff6b505450dc6e04bb6693b3)]
- 🚑 Fixed &#x60;null&#x60; item price on detail [[e85342b](https://github.com/newgen-business-solutions/cs-event-services/commit/e85342bbe74788a70a1834d1860b4ab73ca43272)]
- 🚑 Fixed null item price passthrough [[5ad1b06](https://github.com/newgen-business-solutions/cs-event-services/commit/5ad1b06d6732b40acddd5583c8116b49b14d92ca)]
- ✏️ Fixed collection link clicking UX CSES-182 [[491c5b5](https://github.com/newgen-business-solutions/cs-event-services/commit/491c5b58621520f9ddc8db7b2c8d7c22a6986aec)]
- 🐛 Fixed item price hooks for dates [[4a081fd](https://github.com/newgen-business-solutions/cs-event-services/commit/4a081fdc7424c5ee8c319855e29a7e390fa1f67a)]
- 🐛 Fixed date display on labor modal [[a499948](https://github.com/newgen-business-solutions/cs-event-services/commit/a4999482f9817d58d6ddf9d062d4574d65098250)]
- 🐛 Fixed upstream timeout to catch request CSES-169 [[2e5944f](https://github.com/newgen-business-solutions/cs-event-services/commit/2e5944f19ec211c6d7954e6c0535afa9ccdc8642)]
- 🐛 Fixed prices to become shared CSES-170 [[2d35995](https://github.com/newgen-business-solutions/cs-event-services/commit/2d35995b86eaa3bc8d8057a076a3bc426a034ea3)]
- 🐛 Fixed date display for event shows [[11c1d39](https://github.com/newgen-business-solutions/cs-event-services/commit/11c1d391875b7a3d82b0d4da0d6775b194cdd9d3)]

### Security

- 🔒 Update(web) auth security with guards [[55c9c1d](https://github.com/newgen-business-solutions/cs-event-services/commit/55c9c1dff3950d9a1bd5b1ac852b0aecd6f388a1)]
- 🔒 Updated to NextAuth 4.19.2 [[f82f1bf](https://github.com/newgen-business-solutions/cs-event-services/commit/f82f1bf40e96307a6c870b3d83186931412377b2)]

### Miscellaneous

-  Merge branch &#x27;Bol-and-label-feature&#x27; of https://github.com/newgen-business-solutions/cs-event-services into Bol-and-label-feature [[75a1113](https://github.com/newgen-business-solutions/cs-event-services/commit/75a11132d998bca696984725fa0677fb75ce95ca)]
-  Merge branch &#x27;CSES-274-Booking-Calendar-Prototype&#x27; into Bol-and-label-feature [[801ec5e](https://github.com/newgen-business-solutions/cs-event-services/commit/801ec5e3834fd9d149ef38beac81344c07bc2225)]
- 📎 Update(booking) API spa docs [[a980a18](https://github.com/newgen-business-solutions/cs-event-services/commit/a980a1851c918f785c5210b93086ec2f7da8e7cc)]
- 🚧 Booking calendar initial set up [[41f6d29](https://github.com/newgen-business-solutions/cs-event-services/commit/41f6d29fc851c304923803dbc295a29cb1f2c1b5)]
- 🚧 Got bookings calendar scaffolded [[5e3d644](https://github.com/newgen-business-solutions/cs-event-services/commit/5e3d6440e94e79c68caee99be5229023e4d9f856)]
- 📦 Add &#x60;synfusion&#x60; package [[37869f5](https://github.com/newgen-business-solutions/cs-event-services/commit/37869f5336018a5c5606b183471ab9f02e53c860)]
- 🚧 Scaffold(SPA) Bookings calendar [[f535b5c](https://github.com/newgen-business-solutions/cs-event-services/commit/f535b5c7a3f0181cc8f4ad020ec5dbea07d3091c)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[359d357](https://github.com/newgen-business-solutions/cs-event-services/commit/359d357c44a0d6959b68fc82403b4b90d35d4e2b)]
- 🔨 Chore Update next-build.yml [[96fb030](https://github.com/newgen-business-solutions/cs-event-services/commit/96fb0306fb68ab5932f1f48959a782c722e14e79)]
- 📝 Chore(web) Fix ESLint [[075d532](https://github.com/newgen-business-solutions/cs-event-services/commit/075d5320099b2037b5a2424011f60ddf256fc33a)]
- 🔨 Fix the yarn version upgrade [[e9c9b82](https://github.com/newgen-business-solutions/cs-event-services/commit/e9c9b828a58ebb25d2de1c7b4d30319dbd31d923)]
- 🔨 Fix git for new yarn version [[09b279c](https://github.com/newgen-business-solutions/cs-event-services/commit/09b279cb1cc25056fd5d9d0f42c0216023a6e3dc)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[8052440](https://github.com/newgen-business-solutions/cs-event-services/commit/8052440f448ff1330b9396ea04602192aa63bda8)]
-  Update .gitignore [[65bf807](https://github.com/newgen-business-solutions/cs-event-services/commit/65bf807fd1299d95e7555271507e3cac3df3a8c7)]
- 🔨 Chore: Update yarn and SuiteApp config [[ec5549a](https://github.com/newgen-business-solutions/cs-event-services/commit/ec5549a434546f608719eae36a73bf7ad0216ff7)]
- 📦 Update(web) Nextjs 13.4.16 [[39b0b14](https://github.com/newgen-business-solutions/cs-event-services/commit/39b0b141d6b3aee9c4db12b2920dfa377348b5cc)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[51f06a0](https://github.com/newgen-business-solutions/cs-event-services/commit/51f06a0d71fd770534ab245e854ea5ac47ac87dd)]
-  CSES-246 order checklist bug ([#37](https://github.com/newgen-business-solutions/cs-event-services/issues/37)) [[463214d](https://github.com/newgen-business-solutions/cs-event-services/commit/463214da829a9a8b08b4ae8ec6319c6647c7cb87)]
-  Merge pull request [#45](https://github.com/newgen-business-solutions/cs-event-services/issues/45) from newgen-business-solutions/CSES-221-acknowledgeable-terms-and-conditions-before-checkout [[fe29d26](https://github.com/newgen-business-solutions/cs-event-services/commit/fe29d2602448d191c6ec4a5be8695122be75d55b)]
- 🚧 Started EUA and State refactor [[b6dfef3](https://github.com/newgen-business-solutions/cs-event-services/commit/b6dfef3e3518ccee88533db5ca79023d26dbf40b)]
- 🚀 alliance prod env vars part deux [[71b0b44](https://github.com/newgen-business-solutions/cs-event-services/commit/71b0b446fcbb7864c2a86756f2c31f6e37934ac0)]
- 🚀 refreshed alliance prod creds [[a422af3](https://github.com/newgen-business-solutions/cs-event-services/commit/a422af36c14af72d0b7f8c7921f1dbea70a48d14)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[a022c75](https://github.com/newgen-business-solutions/cs-event-services/commit/a022c752562f4e819493b8dce0ca1292f9857659)]
- 📰 Reformat(backend) old scripts [[b52b0c4](https://github.com/newgen-business-solutions/cs-event-services/commit/b52b0c408441055d4dfc063c8a8df8dbcee153d1)]
- 🚀 redeploying all after vercel issue [[b1db8e6](https://github.com/newgen-business-solutions/cs-event-services/commit/b1db8e6167802c459402e9aeabdbdd0a01c59974)]
- 🚀 village of rosemont prod vars [[b984ad7](https://github.com/newgen-business-solutions/cs-event-services/commit/b984ad70582d2d01c26645cf50f4bea649d2fa4d)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[009bc76](https://github.com/newgen-business-solutions/cs-event-services/commit/009bc76261ddcadac499f44b77a86976541c5a87)]
-  Merge pull request [#36](https://github.com/newgen-business-solutions/cs-event-services/issues/36) from newgen-business-solutions/documentation [[15c7435](https://github.com/newgen-business-solutions/cs-event-services/commit/15c7435f19a02f1a704f6ba0dd003be0d2a85fd3)]
-  Merge branch &#x27;main&#x27; into documentation [[8af14f3](https://github.com/newgen-business-solutions/cs-event-services/commit/8af14f3a606974c7588fa9e23dfc21314d675bf5)]
-  user id -&gt; script aliases [[206d153](https://github.com/newgen-business-solutions/cs-event-services/commit/206d153614d45a7b267a7cd5fba9ac341593b3d0)]
-  Merge pull request [#30](https://github.com/newgen-business-solutions/cs-event-services/issues/30) from newgen-business-solutions/dependabot/npm_and_yarn/apps/backend/xml2js-and-oracle/suitecloud-unit-testing-0.5.0 [[17a016c](https://github.com/newgen-business-solutions/cs-event-services/commit/17a016c227d439c105bc62b90db806ce03ca0e2a)]
-  Merge pull request [#26](https://github.com/newgen-business-solutions/cs-event-services/issues/26) from newgen-business-solutions/dependabot/npm_and_yarn/next-auth-4.20.1 [[0ae9025](https://github.com/newgen-business-solutions/cs-event-services/commit/0ae9025550a156811b391f0e593726525eb20472)]
-  Merge pull request [#27](https://github.com/newgen-business-solutions/cs-event-services/issues/27) from newgen-business-solutions/dependabot/npm_and_yarn/webpack-5.76.0 [[949cca3](https://github.com/newgen-business-solutions/cs-event-services/commit/949cca3923d84091e3ee7d38d88dd734e7678ac1)]
-  Merge pull request [#35](https://github.com/newgen-business-solutions/cs-event-services/issues/35) from newgen-business-solutions/documentation [[b426bf0](https://github.com/newgen-business-solutions/cs-event-services/commit/b426bf0d9090d878337c48dfd157b022177bd93f)]
- 🚧 Add file attributes [[d71ef52](https://github.com/newgen-business-solutions/cs-event-services/commit/d71ef5299bd043e2ef575b90fbf44b8fb9935465)]
- 🚀 added lve prod env vars [[e0186c6](https://github.com/newgen-business-solutions/cs-event-services/commit/e0186c60b6853610ba8252ffb48237a530e8bbdb)]
- 🚀 art craft display env vars [[5972ba6](https://github.com/newgen-business-solutions/cs-event-services/commit/5972ba6cd297284b1ffd253bf681234c6c60cc54)]
- 🚀 updated lve sb1 envs [[be5f043](https://github.com/newgen-business-solutions/cs-event-services/commit/be5f0437e7d05fa879b5247423ff5742d8c0ee89)]
-  Update README.md [[3fba423](https://github.com/newgen-business-solutions/cs-event-services/commit/3fba423bd8df23223f5d42b2a351503edd86b44d)]
-  :chore: added env vars for alliance sb1 [[998df70](https://github.com/newgen-business-solutions/cs-event-services/commit/998df70820cf28db046e783af719b215da6a7ea9)]
- 🔨 Lint: Fixed linting for build [[216e6af](https://github.com/newgen-business-solutions/cs-event-services/commit/216e6af1108e81aee4e4d7c8b4827d6b04652902)]
- 🚧 Added BOL templating for render [[9027fa2](https://github.com/newgen-business-solutions/cs-event-services/commit/9027fa244ee4d22fdc7172786ae88f9962dc9f4b)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[6c5b79b](https://github.com/newgen-business-solutions/cs-event-services/commit/6c5b79be67eddb9f099549a543ba014c69dcca5a)]
-  Merge pull request [#34](https://github.com/newgen-business-solutions/cs-event-services/issues/34) from newgen-business-solutions/CSES-168-major-release-4 [[dfee70e](https://github.com/newgen-business-solutions/cs-event-services/commit/dfee70e8d9450afd22b366ddf9f135550b5c312b)]
- 📝 Fixed linting shorthand on mapbox [[35e1c58](https://github.com/newgen-business-solutions/cs-event-services/commit/35e1c58a1e4817ed63622209f89415c697813859)]
- 🔁 Refactor: Reordered shipping country [[03d328d](https://github.com/newgen-business-solutions/cs-event-services/commit/03d328dc82d17140a12f8062c6812106300234d7)]
-  BOL UE Script [[e46765a](https://github.com/newgen-business-solutions/cs-event-services/commit/e46765a9bd94592fb2b629c1833b89a1357c4763)]
-  Merge pull request [#33](https://github.com/newgen-business-solutions/cs-event-services/issues/33) from newgen-business-solutions/CSES-168-major-release-4 [[26c916b](https://github.com/newgen-business-solutions/cs-event-services/commit/26c916b769171de2bd6b071a8f7e35bca476d5bb)]
- 📰 Format: Reformatted SO Client Module [[4cb19ae](https://github.com/newgen-business-solutions/cs-event-services/commit/4cb19ae0e9e87fd3c2b225e14949a1c958197df0)]
-  Merge pull request [#31](https://github.com/newgen-business-solutions/cs-event-services/issues/31) from newgen-business-solutions/CSES-168-major-release-4 [[8f98262](https://github.com/newgen-business-solutions/cs-event-services/commit/8f98262b65d90eaf5bd75f8b29265c0350e3c955)]
- 📖 Format: Added comments and formatting [[637e5e7](https://github.com/newgen-business-solutions/cs-event-services/commit/637e5e745ae84aab5fc63b4a4db162c9247533c1)]
-  Merge branch &#x27;main&#x27; into CSES-168-major-release-4 [[875fc5f](https://github.com/newgen-business-solutions/cs-event-services/commit/875fc5f139713337326863886e5c01da05cdf504)]
- 📎 Formatted booth work order print SL [[22f32a5](https://github.com/newgen-business-solutions/cs-event-services/commit/22f32a51fb2bad1d48890602a49a9c93a1d44441)]
- 🔌 Attached Backend project [[f7c776f](https://github.com/newgen-business-solutions/cs-event-services/commit/f7c776f0104c88ff963fc7893a2c5a4fb90d72e6)]
- 📎 FT Attached project for improved DX [[f4055f0](https://github.com/newgen-business-solutions/cs-event-services/commit/f4055f0f128afafff8ad50de66d68380e93cb106)]
- 😁 Formatted SO UE &amp; Client Settings [[0800940](https://github.com/newgen-business-solutions/cs-event-services/commit/08009401b1e17b575c5383bb9b700666bc80ddbf)]
- 🚧 Chore updated AMD Client config [[000cce6](https://github.com/newgen-business-solutions/cs-event-services/commit/000cce6704dbd88460e6bc362b29412a8a20c474)]
- 📦 Added packages [[fc5df58](https://github.com/newgen-business-solutions/cs-event-services/commit/fc5df58530bcead829bcebe0b5a450c7ba33f583)]
- 🚧 Chore added typescript config [[084fc5c](https://github.com/newgen-business-solutions/cs-event-services/commit/084fc5ceeeecb74bea9c31bc1abab6c09a7ddf7c)]
- 🔨 Chore - updated prime elec post build envs [[f437643](https://github.com/newgen-business-solutions/cs-event-services/commit/f4376432934b186ea60d1349c277a7eba19ea8d1)]
- 📦 Added JS-Cookie v3.0.1 [[26e6eba](https://github.com/newgen-business-solutions/cs-event-services/commit/26e6eba56c3aa3d6cbdd19b90a4cddf9db90753b)]
- 📗 Reformatted &amp; Added Logging [[a607ecc](https://github.com/newgen-business-solutions/cs-event-services/commit/a607ecceb85d62be5ddf3109fb02a4312a4c1ab5)]
-  Scripted Report Fixes [[5eb8f78](https://github.com/newgen-business-solutions/cs-event-services/commit/5eb8f787f75bdd8331609f80576418220ded71df)]
-  Merge branch &#x27;main&#x27; into CS_Scripted_Reports_Updates [[4df8ac6](https://github.com/newgen-business-solutions/cs-event-services/commit/4df8ac6677b5074856e6e11b0c0a20ee9f39e002)]
- 📦 Added CryptoJs package [[eaef29c](https://github.com/newgen-business-solutions/cs-event-services/commit/eaef29cfd79892f280e219d8562a0f8afc860475)]
- 📦 Added moment.js package [[277dbb7](https://github.com/newgen-business-solutions/cs-event-services/commit/277dbb7ddb97c2f487dda7712c33e488cc388080)]
- 📓 Added indication for form logic skip [[9f506a8](https://github.com/newgen-business-solutions/cs-event-services/commit/9f506a82171fbc7b50cdaf8e1a728bc87685b617)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[4c9d3e4](https://github.com/newgen-business-solutions/cs-event-services/commit/4c9d3e44a9c84239e55ac7a42253d943ffffdf56)]
-  corrected access token for show strategy [[bec2d01](https://github.com/newgen-business-solutions/cs-event-services/commit/bec2d013b8bd0997b0125be712fde50183b5eab8)]
-  trigger rebuild for show strategy deployment [[806b609](https://github.com/newgen-business-solutions/cs-event-services/commit/806b609659d4bbd802be252ece6c9e6487232d92)]
-  update for show strategy envVars [[c68cffb](https://github.com/newgen-business-solutions/cs-event-services/commit/c68cffb8c19d79f563dcc7e71a5faeed85a3a488)]
-  Updated Alliance buildenv [[0385932](https://github.com/newgen-business-solutions/cs-event-services/commit/03859328d3f5da08da457cdc397af4498fa65438)]
- 📰 Updated ENV instructions [[df99f97](https://github.com/newgen-business-solutions/cs-event-services/commit/df99f976145278cd4f1a9316443b0255c85f471c)]
-  update buildenv for alliance [[a749439](https://github.com/newgen-business-solutions/cs-event-services/commit/a749439dc569b623ac20f9d63cdf5df81aac1fc0)]
-  updated buildenv case stmt for alliance [[e3dd78d](https://github.com/newgen-business-solutions/cs-event-services/commit/e3dd78d95d6db7c3c3cdcf73def8d8bce628ba16)]
-  Updated the spelling on my name [[fddbcc3](https://github.com/newgen-business-solutions/cs-event-services/commit/fddbcc315efe9f8e11f1e7e36a0a84269430e030)]
-  Update build env for Alliance project [[a6f1497](https://github.com/newgen-business-solutions/cs-event-services/commit/a6f14973016bfdb9234c11cd1a545f25e5d79605)]
-  Merge pull request [#25](https://github.com/newgen-business-solutions/cs-event-services/issues/25) from newgen-business-solutions/sandbox-build [[6dc2f77](https://github.com/newgen-business-solutions/cs-event-services/commit/6dc2f77bbf8cb620945ae57b7cf1920f0720e5c2)]
- 🆙 Updated version [[ca52a8d](https://github.com/newgen-business-solutions/cs-event-services/commit/ca52a8d8a9e7e6a52ea8a8e7bfb0b9c163487fe6)]


<a name="3.5.9"></a>
## 3.5.9 (2023-01-24)

### Added

- ✨ Added dynamic theme generation [[2ad1669](https://github.com/newgen-business-solutions/cs-event-services/commit/2ad16695e0b08058978a4c67b66cf0f14e224ff5)]
- 🎉 Finished Abandon Cart Beta FT [[8a2c781](https://github.com/newgen-business-solutions/cs-event-services/commit/8a2c78186e47afe1e8ee854e7f5e13b18da8cc51)]
- ➕ Added UUID function helper [[0fe45e2](https://github.com/newgen-business-solutions/cs-event-services/commit/0fe45e2d19b86377473da54f95b5e3d188e64b15)]
- 🎉 Order recovery completed! [[f7c2e37](https://github.com/newgen-business-solutions/cs-event-services/commit/f7c2e375c36061093383ee16f088b23da8318d4a)]
- ✨ Added useWithin hook [[ee43a77](https://github.com/newgen-business-solutions/cs-event-services/commit/ee43a776e6e2915bb56810db7d2827fbb6fd4f0b)]
- ➕ Added middleware edge API [[f1d4d13](https://github.com/newgen-business-solutions/cs-event-services/commit/f1d4d13a9bc23646d07be862173ea65e95e12fa9)]
- ✅ Abandon cart checkout complete [[9e7d2fe](https://github.com/newgen-business-solutions/cs-event-services/commit/9e7d2fed6afdcb9bd09bbd54fadb08fc424639dd)]
- ✨ Added checkout tracking for analytics [[3a8f158](https://github.com/newgen-business-solutions/cs-event-services/commit/3a8f1588ac1b1c705edecb5416eb98fed69d6efa)]
- ✨ Added fast context example for reference [[492a97d](https://github.com/newgen-business-solutions/cs-event-services/commit/492a97d5b0321af2c74988b32a687d954db40e90)]
- ✨ Free items are now available to purchase CSES-108 [[96bd452](https://github.com/newgen-business-solutions/cs-event-services/commit/96bd452c60930a87994de323023b13ce80e38a49)]
- ✨ Added dialog to notify user of order deletion [[ec5848b](https://github.com/newgen-business-solutions/cs-event-services/commit/ec5848b912ccc0dbcf6c4273726f99d6bdd81267)]
- ✨ Added SPA Tester [[5ce2091](https://github.com/newgen-business-solutions/cs-event-services/commit/5ce2091379551b5cd08d61d76d20740eaf3c4dfe)]
- ✨ Added Payment Processing Profile Config [[985b004](https://github.com/newgen-business-solutions/cs-event-services/commit/985b0041bc43f925ade80ab6341243ac1431eac8)]
- ✨ Added Non PT payment handling [[5dd78cf](https://github.com/newgen-business-solutions/cs-event-services/commit/5dd78cf6e02ceb53d13e283c9b89b47fd54b1b4b)]

### Changed

- 🎨 Changed navigation layout [[52710cc](https://github.com/newgen-business-solutions/cs-event-services/commit/52710cc6a832f578a429b6e8c88c173c7c7a0f8e)]
- ♻️ Refactored ListItem to new version [[2047762](https://github.com/newgen-business-solutions/cs-event-services/commit/2047762d9f218481078e46702f470aabc83c36ed)]
- ♻️ Refactored balance to be &quot;visible&quot; CSES-163 [[ab108bf](https://github.com/newgen-business-solutions/cs-event-services/commit/ab108bfac1d164c9c2fcbb5fc4b401877e65a60f)]
- ⬆️ Updated oauth helper for auth token signatures [[04fd354](https://github.com/newgen-business-solutions/cs-event-services/commit/04fd3547d1ff18696cd301fc7fb3d81857283973)]
- ♻️ Refactored lazy loading ecom card [[2812025](https://github.com/newgen-business-solutions/cs-event-services/commit/2812025f66230c5a4638e04bb2522e9ce8ff3b13)]
- ⚡ Optimized cart reset for tracking [[c3415bb](https://github.com/newgen-business-solutions/cs-event-services/commit/c3415bb52be74ee1fd7166f52481887da9937e34)]
- ⚡ Optimized ecom card formatting [[ed72edf](https://github.com/newgen-business-solutions/cs-event-services/commit/ed72edf3119d5efd296a070e6055fe3e595832b6)]
- ⚡ Optimized settings in cache + more [[74cd72d](https://github.com/newgen-business-solutions/cs-event-services/commit/74cd72df8b24dd46e1fede3cf75629895c7c6939)]
- ⚡ Optimized tab title display for cart items [[e71bd1d](https://github.com/newgen-business-solutions/cs-event-services/commit/e71bd1da9b828f61cbc7c2afaf8e4482d7206f9e)]
- 👽 Formatted files [[80ee9ae](https://github.com/newgen-business-solutions/cs-event-services/commit/80ee9ae4ddb86dc55c247083bc60ffe0cb6bdca5)]
- 🎨 Changed duration find upon event availability [[6e684a5](https://github.com/newgen-business-solutions/cs-event-services/commit/6e684a5daaf731a6b58143fff2c46cd5cc617b8c)]
- ⬆️ Changed web site end hours to reflect midnight CSES-160 [[ffe41dc](https://github.com/newgen-business-solutions/cs-event-services/commit/ffe41dc87018df9a458411f8ccd39f0592250833)]
- ⚡ Optimized fonts using @next/fonts [[3531f23](https://github.com/newgen-business-solutions/cs-event-services/commit/3531f23d6e6d2012e085dd7376d874fe0779cd78)]
- ⚡ Optimized collection links into a memo [[cb20113](https://github.com/newgen-business-solutions/cs-event-services/commit/cb20113b61fb0095bcf771ae8a184db0f3d6fd75)]
- 🎨 Fixed disabled styling with event selection [[12991b4](https://github.com/newgen-business-solutions/cs-event-services/commit/12991b47ff9b6b8f33f544e5913843097c8e2a67)]
- 🎨 Changed verbiage on PDF template CSES-55 [[3c65b47](https://github.com/newgen-business-solutions/cs-event-services/commit/3c65b47a66cd5f1250325db1e96c1a97e04a5de0)]
- ⚡ Optimized ecom card reducing rerenders [[03d885a](https://github.com/newgen-business-solutions/cs-event-services/commit/03d885a83dacca8a01510eed2e94fddca90777f5)]
- ⚡ Optimized collection rendering of prices [[91ce42e](https://github.com/newgen-business-solutions/cs-event-services/commit/91ce42e1e7707ddad3ec501c23619f76c82824a6)]
- ♻️ Refactored PID comps for new event context [[f229971](https://github.com/newgen-business-solutions/cs-event-services/commit/f229971449055a0c0ab344f76129c650d46e22c1)]
- ⚡ Optimized re-rendering with Ecom card [[4c7b394](https://github.com/newgen-business-solutions/cs-event-services/commit/4c7b3946ca3e3df2e7590bb8c6d9f9c54a076b3d)]
- ♻️ Refactored event ID Context for checkout [[26fce9a](https://github.com/newgen-business-solutions/cs-event-services/commit/26fce9a70a958ec80d447dbe5e32b5b3416cbd1d)]
- 🎨 Updated Account tab change transitions [[cc356f2](https://github.com/newgen-business-solutions/cs-event-services/commit/cc356f26795fc023db6e547592df44022e525cf0)]
- ⬆️ Updated Changelog [[915be79](https://github.com/newgen-business-solutions/cs-event-services/commit/915be79fd7b5c1069fb57f144a51c102065d8f85)]
- ♻️ Refactored UE for Non Paygen Integrations [[cee399d](https://github.com/newgen-business-solutions/cs-event-services/commit/cee399d846125486071c0fc8b1e530d817235f12)]
- ♻️ Refactored order of SO consolidation [[e165f22](https://github.com/newgen-business-solutions/cs-event-services/commit/e165f22bd09b573a0367445c7016827bdb96e6b3)]
- ♻️ Refactored conv fee from settings lib [[bfdc5c9](https://github.com/newgen-business-solutions/cs-event-services/commit/bfdc5c96414e3858a4a189d7e6b5589c8fcd8ef2)]
- ⬆️ Updated version [[f62fc54](https://github.com/newgen-business-solutions/cs-event-services/commit/f62fc54b4eabd2b4cf571fbaf648bf3de1a655da)]

### Removed

- 🔥 Removed url params function [[e231295](https://github.com/newgen-business-solutions/cs-event-services/commit/e23129576cec9204436e872689cc83b7e1dede40)]
- 🔥 Removed fast context reference [[67dd1f1](https://github.com/newgen-business-solutions/cs-event-services/commit/67dd1f1ba5107403c7a5f808b644c12ef39c0d02)]
- 🔥 Removed New Exhibitor button on SO CSES-156 [[b6dd738](https://github.com/newgen-business-solutions/cs-event-services/commit/b6dd7387cc2fbf4714ab72cb31c64cc2d848397c)]
- 🔥 Removed locked payment card for non-PT users [[23e43e4](https://github.com/newgen-business-solutions/cs-event-services/commit/23e43e4ae69f75f0448f0bf1fd5b67c2c28d78bc)]
- 🔥 Removed setting lib to detect item options [[71f1e02](https://github.com/newgen-business-solutions/cs-event-services/commit/71f1e021bd6215ce26d8b90da3660b74ddcf4c0b)]

### Fixed

- 🍎 Fixed font across all pages [[3c9dd02](https://github.com/newgen-business-solutions/cs-event-services/commit/3c9dd02616ef8d86b184b7a0c0203c8dadc66430)]
- 🍎 Fixed explicit font calls [[3d7dd00](https://github.com/newgen-business-solutions/cs-event-services/commit/3d7dd00e9590f9f395353deefc48bd398c419211)]
- 🍎 Fixed self hosted fonts [[60d0b86](https://github.com/newgen-business-solutions/cs-event-services/commit/60d0b86e9bc39751b99bc58466c9c6d4e1a39bcb)]
- 🍎 Fixed font class usage [[c45259c](https://github.com/newgen-business-solutions/cs-event-services/commit/c45259ccdafcbb97f024ffc775beb49ef16c1398)]
- 🐛 Fixed balance field updates CSES-163 [[5a1e105](https://github.com/newgen-business-solutions/cs-event-services/commit/5a1e105b87a53853a6f6eb61165de574d95d38f1)]
- 🚑 Fixed first order ID placemnt [[7038e8c](https://github.com/newgen-business-solutions/cs-event-services/commit/7038e8c57d52f6ce79f4dc4cf125dd11426bf766)]
- 🐛 Fixed &#x60;format&#x60; module faliure for balances CSES-163 [[38732c1](https://github.com/newgen-business-solutions/cs-event-services/commit/38732c1eaac92dca59aca300ec81711d3c60ea4e)]
- 🐛 Fixed SM Order default order type set CSES-161 [[39123f5](https://github.com/newgen-business-solutions/cs-event-services/commit/39123f5631e287d81932440b98f260bc2527c9e0)]
- 🐛 Fixed freight item price displays CSES-162 [[f84ce2a](https://github.com/newgen-business-solutions/cs-event-services/commit/f84ce2adc9f4112877dd9f039d2939119a699e49)]
- 🐛 Fixed checkout to use context with booth [[a01136f](https://github.com/newgen-business-solutions/cs-event-services/commit/a01136fb6f235481b66ab780e0d61bd912c5090e)]
- 🚑 Fixed effect with default payment [[0a7524d](https://github.com/newgen-business-solutions/cs-event-services/commit/0a7524d5175ca2bdfb4539baa2db3d0e32392329)]
- 🍎 Fixed conv fee tax lines and preferred processing profile [[dcbc805](https://github.com/newgen-business-solutions/cs-event-services/commit/dcbc80543079f2ec6b28ba1a4a95576d778b5ae6)]
- 🐛 Fixed Paytrace cache loaders [[601fbeb](https://github.com/newgen-business-solutions/cs-event-services/commit/601fbeb279f9632bbd02572c3ddd12f6c9e2d64e)]
- 🐛 Fixed rec mode and no desc items CSES-155 [[9358eec](https://github.com/newgen-business-solutions/cs-event-services/commit/9358eecf8f7b060a1825ee9f3ac7a2a8c74975d3)]

### Miscellaneous

- 🔨 Fixed build errors [[391e2e2](https://github.com/newgen-business-solutions/cs-event-services/commit/391e2e2ae7362a0b57863cae3f396bbc80a0e174)]
- 🚧 Fixed build issue [[bf21d17](https://github.com/newgen-business-solutions/cs-event-services/commit/bf21d17386d4b09d082b4ddeb7f918e8fab7c658)]
- 📦 Updated to Next 13.1.1 [[f9ce5a5](https://github.com/newgen-business-solutions/cs-event-services/commit/f9ce5a50c06e90577e9a837aec9c6b3bad8dbace)]
- 📘 Reduced log level on form id gather [[73e68de](https://github.com/newgen-business-solutions/cs-event-services/commit/73e68de5a75aa17249a2d4d5c27e688f2d095e2a)]
- 🚛 Refactored PID page with new event context [[4c7b356](https://github.com/newgen-business-solutions/cs-event-services/commit/4c7b356046f223b041f71b7334c75fc5703ecef0)]
-  Merge branch &#x27;sandbox-build&#x27; [[b9b7244](https://github.com/newgen-business-solutions/cs-event-services/commit/b9b7244d0a84947683e7e77dc6f20587f0d3a3d2)]


<a name="3.5.0"></a>
## 3.5.0 (2022-12-23)

### Added

- ✨ Added Progression Loader to ALS CSES-75 [[5685408](https://github.com/newgen-business-solutions/cs-event-services/commit/568540895fd6c6b4f8cc64bf63a2859b12334fe5)]
- 🎉 Added enhanced ALS with loader [[213fa19](https://github.com/newgen-business-solutions/cs-event-services/commit/213fa196f1a1c7aa791be74232457ece7ec705d8)]
- ✨ Completed Project Detail Page CSES-115 [[f88d293](https://github.com/newgen-business-solutions/cs-event-services/commit/f88d2936a0f718f63b075f21ba28397ddd1c696f)]
- ✨ Added product detail files [[0e268f6](https://github.com/newgen-business-solutions/cs-event-services/commit/0e268f6706d54f6177318bd8750ce971919ba694)]

### Changed

- ♻️ -optimize-checkout-processes [[11803bc](https://github.com/newgen-business-solutions/cs-event-services/commit/11803bccd93a09bfb9888ef554c776925a9e75c2)]
- 📱 Optimized Mobile search options [[a96f3e6](https://github.com/newgen-business-solutions/cs-event-services/commit/a96f3e630ff574d6d6300e9a4cc96abeb6815bf8)]
- ⚡ Optimized PID page to include Collection [[5db195f](https://github.com/newgen-business-solutions/cs-event-services/commit/5db195f83cb1260004af9a5c9243fd7ad3c5e52b)]
- 🎨 Changed Color of ALS Progress Bar CSES-75 [[f7e9eec](https://github.com/newgen-business-solutions/cs-event-services/commit/f7e9eec4753806a17d0a543a2898bac76f29c5dd)]
- ⚡ Optimized Client Module For Sales Order CSES-153 [[77f59bd](https://github.com/newgen-business-solutions/cs-event-services/commit/77f59bd04aa9bc9ed96529f6b7049fcd1e92ca6b)]
- 💄 Fixed grammar on event selection &quot;space&quot; CSES-152 [[8696e08](https://github.com/newgen-business-solutions/cs-event-services/commit/8696e08f6600c76b6bda8f475ccc9e472a5605ec)]
- ♻️ -optimize-checkout-processes [[43d6ad1](https://github.com/newgen-business-solutions/cs-event-services/commit/43d6ad198aa1d828955eb91fd471812d29e796dd)]
- ♻️ -optimize-checkout-processes [[b4904e0](https://github.com/newgen-business-solutions/cs-event-services/commit/b4904e010fa4eacdadaa6177e27dc0d53c5bae91)]
- ⬆️ Updated Version [[d19d607](https://github.com/newgen-business-solutions/cs-event-services/commit/d19d607aea54e35c4f624ea68e5db0da82533203)]

### Miscellaneous

-  Merge pull request [#24](https://github.com/newgen-business-solutions/cs-event-services/issues/24) from newgen-business-solutions/CSES-115-Item-detail-page [[ef43eae](https://github.com/newgen-business-solutions/cs-event-services/commit/ef43eaefaf669156cda8bb3a1baa97c25ee81bf2)]
- 🐭 Added Collection tracking cookie [[af58a0c](https://github.com/newgen-business-solutions/cs-event-services/commit/af58a0c2e2edd2000853e8a0036601ed4f66ae16)]
-  Merge branch &#x27;main&#x27; into CSES-115-Item-detail-page [[a632c94](https://github.com/newgen-business-solutions/cs-event-services/commit/a632c94cbad7f2e020739514c99ab63594addf7e)]
- 🐊 Removed empty JSX [[17969d9](https://github.com/newgen-business-solutions/cs-event-services/commit/17969d984166a65bd55586dc8f4bd22c8a15fd4a)]
- 🚧 Product info rendering CSES-115 [[58864b1](https://github.com/newgen-business-solutions/cs-event-services/commit/58864b1c722781dd7d13a19d19fbf37bdfc7a35a)]
- 📓 Added JSDocs to price change handler [[ee01042](https://github.com/newgen-business-solutions/cs-event-services/commit/ee01042ced1e3b6157cf2ad0cc2b7150a1b7f27e)]


<a name="3.2.1"></a>
## 3.2.1 (2022-12-14)

### Added

- ✨ Added enhanced ALS CSES-140 [[30571cc](https://github.com/newgen-business-solutions/cs-event-services/commit/30571cc8b0a8ec3ce4e5df8eeceee801a803dda2)]
- ✨ Added card expiration handler [[fce4578](https://github.com/newgen-business-solutions/cs-event-services/commit/fce4578cfe64364fea9f715b34222e01d11cc04c)]
- ➕ Added ship dates and venue ID to event hook [[e4a9f71](https://github.com/newgen-business-solutions/cs-event-services/commit/e4a9f717cf1fde22949e9506e9c66a00ae198d55)]
- ✨ Payments working with error msgs [[64a6cfb](https://github.com/newgen-business-solutions/cs-event-services/commit/64a6cfbb9d8911d4e36f68a0d31393f44ba77a50)]
- ✨ Added Paytrace Error displays [[0a5479b](https://github.com/newgen-business-solutions/cs-event-services/commit/0a5479b6273cadbc1d2534fd8e75c51b44da3af0)]
- ➕ Added countries hook pkg [[468ddb8](https://github.com/newgen-business-solutions/cs-event-services/commit/468ddb809022dcc64f119f7da0b97bc74dce3ffe)]
- ✅ Added Formik [[58b00b4](https://github.com/newgen-business-solutions/cs-event-services/commit/58b00b4b4794d11097c2c940d687488421a686bb)]
- ✅ Added tailwind css [[d2eb000](https://github.com/newgen-business-solutions/cs-event-services/commit/d2eb000dca584993b629432238c33a86a231c094)]
- ✅ Added customer error page. [[6428da6](https://github.com/newgen-business-solutions/cs-event-services/commit/6428da6d549ea77694f03ce382c6e0cb56e1cac2)]
- ✅ Added build status badge [[fa5d7fc](https://github.com/newgen-business-solutions/cs-event-services/commit/fa5d7fc68bf95dfcf99aaa5a41c62269071af69c)]
- ✅ Added web build check [[77aad32](https://github.com/newgen-business-solutions/cs-event-services/commit/77aad328ce4ee0d2e8eedfd25af42fd87ea66d28)]
- ➕ Added montserrat to rest of nav [[0a2cc4b](https://github.com/newgen-business-solutions/cs-event-services/commit/0a2cc4bedbbf9a9d599d2a20846f651fa15179a4)]
- ✅ Added monserrat to css globally [[3236a75](https://github.com/newgen-business-solutions/cs-event-services/commit/3236a751a67bff6e2b4f5dfdb59e9beb70060e7b)]
- ✅ Added remove cart function to context [[bf3e241](https://github.com/newgen-business-solutions/cs-event-services/commit/bf3e2410c2e2b36007e227f66610b1edebb2cf58)]
- ✅ Added Table Row comp for cart UX [[634d8e0](https://github.com/newgen-business-solutions/cs-event-services/commit/634d8e00ca7a42d70618e48bd42729e9fc7569b2)]

### Changed

- ♻️ -optimize-checkout-processes [[4e6a26c](https://github.com/newgen-business-solutions/cs-event-services/commit/4e6a26c1035557eac5f534a629f4f24715af9e33)]
- 🚚 Shipping address changes per event change [[9e0a0fa](https://github.com/newgen-business-solutions/cs-event-services/commit/9e0a0fab9d50409972c42deb122a3cc2511d00e6)]
- ♻️ -optimize-checkout-processes [[bd9cc85](https://github.com/newgen-business-solutions/cs-event-services/commit/bd9cc85d2e585156c4e6dbeb6a4f25c12795f1eb)]
- ♻️ -optimize-checkout-processes [[08bbd69](https://github.com/newgen-business-solutions/cs-event-services/commit/08bbd695a2090195b2bef5c6d8bf4c3ae7bdd23f)]
- 🎨 Added &#x60;fin_show&#x60; to eventData [[9460530](https://github.com/newgen-business-solutions/cs-event-services/commit/94605308f0223d868d1a68054069b937930f485f)]
- ⚡ Increased performance of single booth order FT CSES-141 [[f9ada18](https://github.com/newgen-business-solutions/cs-event-services/commit/f9ada18f4ea63f12f2e5ca9e7026a92699573c95)]
- 🎨 Changed key reference for review item [[915ee04](https://github.com/newgen-business-solutions/cs-event-services/commit/915ee047d265f563597865cf4582aa862c1eec92)]
- 💄 Polish Ecommerce card button layout [[74d1ec9](https://github.com/newgen-business-solutions/cs-event-services/commit/74d1ec9447d2cc81359c8466db1e9474998b23e8)]
- 💄 Optimized UI for mobile on event details [[1572df8](https://github.com/newgen-business-solutions/cs-event-services/commit/1572df80d37fd569aa2490a2a3c6a611683dd97a)]
- 🔧 : Errors will not fatally break page. [[badc2ee](https://github.com/newgen-business-solutions/cs-event-services/commit/badc2eea66643966a2ab77a11a2c1fe181aa43f7)]
- ⚡ Changed web order handler [[392c465](https://github.com/newgen-business-solutions/cs-event-services/commit/392c465dcbfd9dae714bff1c50520e0bc22a8e5b)]
- 🎨 Fixed img on readme [[0945c3a](https://github.com/newgen-business-solutions/cs-event-services/commit/0945c3ad13d6bf6d22877bc290d739b05a15f62b)]
- 🎨 Updated Readme image [[0e314f3](https://github.com/newgen-business-solutions/cs-event-services/commit/0e314f34e144947c2dd10b6c70b630abe01c80bb)]
- 🎨 Fixed up readme [[6c5619a](https://github.com/newgen-business-solutions/cs-event-services/commit/6c5619ad3fb4f1bab6eabbd735a1cff3a4b17e15)]
- ⬆️ Updated Paytrace lib CSES-126 [[fb38ed2](https://github.com/newgen-business-solutions/cs-event-services/commit/fb38ed23301dd0c772b70c593ea153a87f8594b9)]
- 🔧 Fixed for linter to complie [[8991d1d](https://github.com/newgen-business-solutions/cs-event-services/commit/8991d1de4f02dcd5d09bb178875e9211a0ab7033)]
- 🎨 Fixed login bg coverage [[a2db034](https://github.com/newgen-business-solutions/cs-event-services/commit/a2db0342141b2b37a730514def0a2911e06e0d67)]
- 🎨 Fixed up layout on login page for mobile [[04ae68c](https://github.com/newgen-business-solutions/cs-event-services/commit/04ae68ced53ef9cf05af05e84bc9253d3f8d21b6)]
- 🎨 Fluid on ecom card layout fixed [[abf8567](https://github.com/newgen-business-solutions/cs-event-services/commit/abf856786f0430c6905573802385e42e1521d845)]
- 🎨 Fixed width to respond to cid cards [[eda18ce](https://github.com/newgen-business-solutions/cs-event-services/commit/eda18ce251896b2d0b18ce2308b2e591ad4acb3f)]
- 📱 Made search responsive for mobile [[a4cc15e](https://github.com/newgen-business-solutions/cs-event-services/commit/a4cc15ea6464304ea18beec6e705f7a63525e83b)]
- 📱 Fixed mobile responsiveness during checkout [[5a53b23](https://github.com/newgen-business-solutions/cs-event-services/commit/5a53b23dcc9eed52973aee56885e6c4fc9acacbb)]
- ⚡ Changed logout callbacks [[d847657](https://github.com/newgen-business-solutions/cs-event-services/commit/d847657522e433ac90ab4ede6be9d076a113c45c)]
- 🎨 Added drawer auth protected items [[e294c7d](https://github.com/newgen-business-solutions/cs-event-services/commit/e294c7d12c2d0fdb7f1f9bc3e2403113c7ba76ab)]
- 🎨 Fixed event logo rendering [[44d72c4](https://github.com/newgen-business-solutions/cs-event-services/commit/44d72c46729f3f0ebcf158fac9d32152eeea825d)]
- ⚡ Refactored pages for proper css paths [[8d5f24c](https://github.com/newgen-business-solutions/cs-event-services/commit/8d5f24c446bd17051129768660a612c66bcd5d0e)]
- ⚡ Changed redirect to replace history not push [[d7e3757](https://github.com/newgen-business-solutions/cs-event-services/commit/d7e3757e3fa3319aa5da64c282e1e999d6f898af)]
- ⚡ Optimized CID page [[4fa3cb4](https://github.com/newgen-business-solutions/cs-event-services/commit/4fa3cb41c964611467362f38fde003c3a56db154)]
- 🎨 Finished SID optimization [[ff1a1b7](https://github.com/newgen-business-solutions/cs-event-services/commit/ff1a1b7090e1a359db364f19592befcb162f31e9)]
- 📱 Increased logging on web suitelet [[f462d78](https://github.com/newgen-business-solutions/cs-event-services/commit/f462d781318967cb40cc655cfdb4dc5dd6f2042c)]
- 🎨 Changed labor preview subtitle display [[bf60fdd](https://github.com/newgen-business-solutions/cs-event-services/commit/bf60fddbf006fc1b748f1590e7be7883d34d0910)]
- 🎨 Changed Nav to be fixed on auth pages. [[b9f28f5](https://github.com/newgen-business-solutions/cs-event-services/commit/b9f28f5644d28b7978c86606a314ef405c2fd35a)]
- ⚡ Updated changelog [[03e62aa](https://github.com/newgen-business-solutions/cs-event-services/commit/03e62aa1c6f74055b694ab46cbeadf81254734a9)]
- 🎨 Changed responsive design to fit ipads [[3172bdd](https://github.com/newgen-business-solutions/cs-event-services/commit/3172bdd85068c209c51f0bfede7609843dc9306b)]
- 🎨 Polished Profile Page [[6d19cc2](https://github.com/newgen-business-solutions/cs-event-services/commit/6d19cc2949f301241addf6a015692b6c82d694f7)]
- 🎨 Finished order summary page after checkout [[a4d13aa](https://github.com/newgen-business-solutions/cs-event-services/commit/a4d13aabf8397d33f64add4403d83d691be5db6f)]
- 🎨 Checkout pages completed [[6b0f86f](https://github.com/newgen-business-solutions/cs-event-services/commit/6b0f86fc1e98d630b9178b42981becffd6bbee68)]
- 🎨 Cleaned up Drawer [[63f7988](https://github.com/newgen-business-solutions/cs-event-services/commit/63f7988bdecdff00cf5d3f74a6be99dd516966c3)]
- 🎨 Polished Ecom Card for Labor items [[7bc855b](https://github.com/newgen-business-solutions/cs-event-services/commit/7bc855b19912023ec91b5049ed5c7d716e2bdaf8)]
- ⚡ Extended timeout for session on cid page [[3119b68](https://github.com/newgen-business-solutions/cs-event-services/commit/3119b6807e3d183219e3dc43b6b0424a152860a9)]
- 🎨 Changed blur amount on event banner [[5794c55](https://github.com/newgen-business-solutions/cs-event-services/commit/5794c557aeca2065e6f6520fe9da96a98cc14c97)]
- 🎨 Added tooltip to cart icon [[42b5ea3](https://github.com/newgen-business-solutions/cs-event-services/commit/42b5ea378cd261488b81676b5a932df806679a48)]
- 🎨 Fixed up event details page [[7a907dd](https://github.com/newgen-business-solutions/cs-event-services/commit/7a907dd722208afdf57be3d5cdc08fbdd6e3fcd1)]
- 🎨 Fixed up Navbar and event banner [[20bdf52](https://github.com/newgen-business-solutions/cs-event-services/commit/20bdf5209f1363845647998292fb169281df30c1)]
- ⚡ Update static generation timeout [[cc5a873](https://github.com/newgen-business-solutions/cs-event-services/commit/cc5a873c46c46326b70f290ee0441c6bd42bc2b9)]
- 🎨 🐛 Fixed Signin forms [[2680fde](https://github.com/newgen-business-solutions/cs-event-services/commit/2680fde86e531648bb4cd2469f218a981bbb96fa)]
- ⚡ Optimized all imports and fixed root login [[c929b75](https://github.com/newgen-business-solutions/cs-event-services/commit/c929b7512d99cbb19402fad3ecfe6d955825ff53)]
- 🎨 Auth pages redone [[bc136fb](https://github.com/newgen-business-solutions/cs-event-services/commit/bc136fb005afb4d9fd64965bd851b02c4076fd61)]
- 🎨 Fixed remaining UI bugs [[21f220e](https://github.com/newgen-business-solutions/cs-event-services/commit/21f220e47525da43f82912266a6b3d3013f687d8)]
- 🎨 Redressed Map box [[d43ff51](https://github.com/newgen-business-solutions/cs-event-services/commit/d43ff519b9daa7adc015234c0f7793df7ea0eda0)]
- 🎨 Changed Profile Userbox to slate [[d96b2ed](https://github.com/newgen-business-solutions/cs-event-services/commit/d96b2ed0a1f1d2c3b519d04d875072778b51a6cd)]
- 🎨 Fixed code formatting on payment RESTlet [[e100b30](https://github.com/newgen-business-solutions/cs-event-services/commit/e100b30eef733cd03cbe16212fda239b16c81ed9)]
- ⚡ Updated PT library file [[ab852cf](https://github.com/newgen-business-solutions/cs-event-services/commit/ab852cf53be5a13d1af6abdb289b0f1ae8dcae51)]

### Breaking changes

- 💥 Updated paytrace lib CSES-141 [[ccf831f](https://github.com/newgen-business-solutions/cs-event-services/commit/ccf831f86d196c02c1df66d25c360a49aec2453a)]
- 💥 Completely Removed MDB [[c8025d4](https://github.com/newgen-business-solutions/cs-event-services/commit/c8025d43e6045a8e19d94cbc989adb9e10991cfe)]
- 💥 Fixed search auto complete for MUI 5 [[8584acf](https://github.com/newgen-business-solutions/cs-event-services/commit/8584acfb61e7df63c8f249990385573e7494743c)]

### Removed

- 🔥 Removed UPC propType on order review item [[f55b6b6](https://github.com/newgen-business-solutions/cs-event-services/commit/f55b6b694a58c500eb817de5a6fc1413dc810cf2)]
- 🔥 Removed effect causing fatal error on checkout [[102b6e8](https://github.com/newgen-business-solutions/cs-event-services/commit/102b6e8f0d49c82589e84d025af125e6bb144f1a)]
- 🔥 Removed product link TEMP [[2f85ca1](https://github.com/newgen-business-solutions/cs-event-services/commit/2f85ca11f3253b837bf9742fe068b9f9655c37c7)]
- 🔥 Removed card encryption on second step [[55011fd](https://github.com/newgen-business-solutions/cs-event-services/commit/55011fd494fc77bf52753a5d75ba1245c2e212fe)]
- 🔥 Removed layout base references [[8462cfe](https://github.com/newgen-business-solutions/cs-event-services/commit/8462cfe7a38973d24f8ad16b2f9ff50aa3c840c9)]
- 🔥 Removed rest of MDB pertaining references [[975c07d](https://github.com/newgen-business-solutions/cs-event-services/commit/975c07d2df32c2bdc3939d7b64c31959b17d393a)]
- 🔥 Removed and renamed pages containing MDB [[1833eb1](https://github.com/newgen-business-solutions/cs-event-services/commit/1833eb10d1519b2c944ce8df540ee80efad0b7f1)]
- 🔥 Removed old MDB components [[2bf412e](https://github.com/newgen-business-solutions/cs-event-services/commit/2bf412e03c8d844ab5cf07070ccfa5e5b27b051b)]
- 🔥 Removed Cart context on event page [[4e67b88](https://github.com/newgen-business-solutions/cs-event-services/commit/4e67b88187326acab1c2d33c50981a7f6f544fd8)]
- 🔥 Removed MDB [[6b47f65](https://github.com/newgen-business-solutions/cs-event-services/commit/6b47f65fdddf89e3951275d7dae474fb04c27241)]

### Fixed

- 🐛 Fixed zipcode entry leading with 0s [[6eb6415](https://github.com/newgen-business-solutions/cs-event-services/commit/6eb64155327654217d0747d7788e4ea361c705fa)]
- 🐛 Fixed tax from clearing on entity change CSES-150 [[b0aa507](https://github.com/newgen-business-solutions/cs-event-services/commit/b0aa507eb70786067513463a80fd37e0f8553c79)]
- 🐛 Fixed tax setting on SO CSES-137 [[43b0d22](https://github.com/newgen-business-solutions/cs-event-services/commit/43b0d22758dc7d47b0b477b907fb9bec5518b96a)]
- 🐛 Fixed deposit amount clearing CSES-137 [[4dbc1c4](https://github.com/newgen-business-solutions/cs-event-services/commit/4dbc1c41bc6adcd9f505c28d3d64ad992520aedd)]
- 🐛 Fixed deposit value to source from balance CSES-137 [[71cfa87](https://github.com/newgen-business-solutions/cs-event-services/commit/71cfa8701c8b670f421340bbcfecfa00cb7766fb)]
- 🐛 Fixed amount totaling with &#x60;format&#x60; CSES-147 [[6b5db78](https://github.com/newgen-business-solutions/cs-event-services/commit/6b5db7883351e061a281cf01e3e3d97f4d1cb640)]
- 🐛 Fixed default order type on load CSES-149 [[05c1e68](https://github.com/newgen-business-solutions/cs-event-services/commit/05c1e68948d2460f75f950042ce902f52d2419a4)]
- 🚑 Fixed Current form init for create types [[5e19a92](https://github.com/newgen-business-solutions/cs-event-services/commit/5e19a92eb72d1c749aae8089ba095435380de0f4)]
- 🐛 Fixed booth order consolidation application [[581b4fd](https://github.com/newgen-business-solutions/cs-event-services/commit/581b4fde51e417d37e2f5b72c6102e199a50e12a)]
- 🐛 Fixed special character in customer name [[4a56a48](https://github.com/newgen-business-solutions/cs-event-services/commit/4a56a48d88a9db2ca810e457f8188756ba673708)]
- 🐛 Fixed CWT Items for submission in portal [[bc8da36](https://github.com/newgen-business-solutions/cs-event-services/commit/bc8da36f17d4653103c0553a18f2832b4d59bc0e)]
- 🐛 Fixed Payment Addition for autofills [[f0b3317](https://github.com/newgen-business-solutions/cs-event-services/commit/f0b3317fbcb94bdf747d16d1a9137bee3dfe24b5)]
- 🐛 Fixed sales order shipping address [[cbf7950](https://github.com/newgen-business-solutions/cs-event-services/commit/cbf7950f36f6be00068ba78f81f42f249233ee09)]
- 🚑 Hotfix for CSES-132 Nexus tax sets [[f5e2f45](https://github.com/newgen-business-solutions/cs-event-services/commit/f5e2f4546633d252aaee30b47adf69990b54a595)]
- 🚑 Issued hot fix for payment captures CSES-134 [[801cedf](https://github.com/newgen-business-solutions/cs-event-services/commit/801cedfe8e5ba5301bd809ece9d718a496a3c7b1)]
- 🚑 Fixed show management work order CSES-133 [[bfcc85d](https://github.com/newgen-business-solutions/cs-event-services/commit/bfcc85d4f2dd8f55640c695bcc006bfa0fd1180d)]
- 🚑 Fixed casing in import [[1e068ff](https://github.com/newgen-business-solutions/cs-event-services/commit/1e068ff5e86aa6982dc57c71df79bcdfaf3ede9d)]
- 🍎 Fixed drawer external links removed target [[fbd8f89](https://github.com/newgen-business-solutions/cs-event-services/commit/fbd8f899ff4ff87fb20978ddc5374bbff4581fec)]
- 🍎 Fixed external nav links [[9f002d8](https://github.com/newgen-business-solutions/cs-event-services/commit/9f002d8b1f2d1a11f5ee7e226ef60e721d204b68)]
- 🍎 Fixed tailwind config [[f955893](https://github.com/newgen-business-solutions/cs-event-services/commit/f955893a7f309fcdd278f5c2aa0cd208b6a0ae75)]
- 🚑 Fixed address modal [[57dc0f3](https://github.com/newgen-business-solutions/cs-event-services/commit/57dc0f331212a823185deda858a1edc30c18c312)]
- 🚑 Fixed custom hook refactor on profile [[0f112d2](https://github.com/newgen-business-solutions/cs-event-services/commit/0f112d2278df32fc2d79d657ef156d64fcf15c97)]
- 🚑 Converted data types to Sets [[fef7c4f](https://github.com/newgen-business-solutions/cs-event-services/commit/fef7c4fd8b408b1d297cf25421665c72eec90957)]
- 🚑 Added job set assurance on other roles CSES-123 [[05632d7](https://github.com/newgen-business-solutions/cs-event-services/commit/05632d7840b4a78129649921fda7bb1980b8135c)]
- 🚑 Fixed csJob set on payment CSES-123 [[40b007f](https://github.com/newgen-business-solutions/cs-event-services/commit/40b007f02855dc63813cb5789bb297f1396ed470)]
- 🍎 Fixed Search autocomplete CSEC-18 [[bffd630](https://github.com/newgen-business-solutions/cs-event-services/commit/bffd630ce478a3113c26e2f664316b16f18b5f37)]
- 🚑 Fixed Location Api and country gather. CSES-16 [[a8e4153](https://github.com/newgen-business-solutions/cs-event-services/commit/a8e415355a7cf93085604867d2beddb9f91b2f69)]
- 🐛 Fixed card entry expiration CSES-122 [[f81b6f1](https://github.com/newgen-business-solutions/cs-event-services/commit/f81b6f10eb1a564f502a3028bebd6b63c9ce8e96)]
- 🍎 Fixed freight item price displays CSES-121 [[74c098c](https://github.com/newgen-business-solutions/cs-event-services/commit/74c098cca03b60346b09b6939c554b2f55bd64cb)]
- 🐛 Fixed CSJob not setting on SO via user interface CSES-117 [[065dacf](https://github.com/newgen-business-solutions/cs-event-services/commit/065dacfb035283aa1382deb1050896ebf8e1ff36)]
- 🐛 Fixed cart quantity on change [[c0c1ebc](https://github.com/newgen-business-solutions/cs-event-services/commit/c0c1ebc69e0942b7505d4a022a5c0eef763d811d)]
- 🐛 Fixed comp render of contact of status M [[d82b880](https://github.com/newgen-business-solutions/cs-event-services/commit/d82b88071afa7fe06984c13c65e881354592cde8)]
- 🍎 Fixed render on input change to quantity - ecom card [[b92908c](https://github.com/newgen-business-solutions/cs-event-services/commit/b92908c17529a96d6072d035d9b9459d0ec2cbd2)]
- 🐛 Fixed cart count in nav [[d9a3536](https://github.com/newgen-business-solutions/cs-event-services/commit/d9a353656d4554239a257fa22266f570079194fb)]
- 🐛 Fixed layout on collection, sid, &amp; nav [[23ca51a](https://github.com/newgen-business-solutions/cs-event-services/commit/23ca51a57c4cfa9d6a118fad3797382fd2c2373c)]
- 🐛 Fixed collection link rendering [[b72834f](https://github.com/newgen-business-solutions/cs-event-services/commit/b72834feb44ff96fa2a7ef6e7c95e56ffc94192e)]
- 🐛 Fixed contact us button color detection [[356286e](https://github.com/newgen-business-solutions/cs-event-services/commit/356286eb9219ae1433c09495e3ed052e28a495b3)]
- 🐛 Fixed button using wrong color [[662f470](https://github.com/newgen-business-solutions/cs-event-services/commit/662f470b637df1c7a606eced826bf7a6a9ec5e7b)]
- 🐛 Fixed event page accordion banner color text [[ab3936d](https://github.com/newgen-business-solutions/cs-event-services/commit/ab3936dcf9f1537de0e5fb5caa6f7fa7118012ba)]
- 🐛 Fixed current show banner text bg detection [[d9e46c2](https://github.com/newgen-business-solutions/cs-event-services/commit/d9e46c273134a0d073835f690dcd4ca6e941ac06)]
- 🐛 Fixed Nav background color detection [[b0eaed0](https://github.com/newgen-business-solutions/cs-event-services/commit/b0eaed0d434c7a4dca40f2d33c134b5ebee2a650)]
- 🍎 No more linting issues [[2f7b348](https://github.com/newgen-business-solutions/cs-event-services/commit/2f7b348c47c21227d72011d5b7c36579da17e797)]
- 🐛 Fixed linting [[b599918](https://github.com/newgen-business-solutions/cs-event-services/commit/b599918f3c23878d0619ba2370fb12923b98f130)]
- 🏁 Fixed Linting errors [[3641ebe](https://github.com/newgen-business-solutions/cs-event-services/commit/3641ebe1e0c028b84477c9f5ff5a4858ba4864e9)]
- 🐛 Fixed cart context on event selection [[8d8a6d9](https://github.com/newgen-business-solutions/cs-event-services/commit/8d8a6d98ee07ba7de178b42acddb6955e6dd190c)]
- 🐛 Fixed cards rendering dependency graph [[9314ab3](https://github.com/newgen-business-solutions/cs-event-services/commit/9314ab326bcf44b11e2e348522405cb4aab00b86)]
- 🐛 Fixed Collection link render [[a56e7bd](https://github.com/newgen-business-solutions/cs-event-services/commit/a56e7bd8cc477319cf252a54dfdae748346929cd)]
- 🐛 Fixed collection item card desc render [[bfabe5b](https://github.com/newgen-business-solutions/cs-event-services/commit/bfabe5b1d7d9610c1425501df2dec97d52997df0)]
- 🏁 Updated version v1.2.0 [[031f36d](https://github.com/newgen-business-solutions/cs-event-services/commit/031f36d9d41d4b7c3dc04094853675ceb5c1ab31)]

### Security

- 🔒 Fixed secure signout redirection [[d8fd64a](https://github.com/newgen-business-solutions/cs-event-services/commit/d8fd64a60cb8edebc3e0a8364ce4c05d3c59779a)]

### Miscellaneous

- 🕚 Fixed race condition upon tax group setting [[530d113](https://github.com/newgen-business-solutions/cs-event-services/commit/530d1131395378405ac78b43ac32e579519511fe)]
- 🎱 Race condition beat for tax group setting CSES-150 [[e012b04](https://github.com/newgen-business-solutions/cs-event-services/commit/e012b049c4be64ce0b8d083c3ffcfc4abdb6b599)]
- 📓 Added Function signature to order types CSES-149 [[5d0e52b](https://github.com/newgen-business-solutions/cs-event-services/commit/5d0e52b7239420d75fbe47e7d0f38450e84b4f43)]
-  Update issue templates [[f9581a2](https://github.com/newgen-business-solutions/cs-event-services/commit/f9581a20a5f29206e824029c5feb9dc40eb45910)]
-  Update issue templates [[7b62673](https://github.com/newgen-business-solutions/cs-event-services/commit/7b626733c6d7b7cb68bed895033fb67f26e49775)]
- 📦 Updated Tailwind 3.2.4 [[a0f599e](https://github.com/newgen-business-solutions/cs-event-services/commit/a0f599ec99bd0c5d65baaf319a46ae4766500556)]
- 💳 Payment Reference addition completed CSES-143 [[8f81c6d](https://github.com/newgen-business-solutions/cs-event-services/commit/8f81c6d9834b678a111d1429ef451236b454dbc5)]
- 📚 Changed library reference CSES-140 [[f3bbc1e](https://github.com/newgen-business-solutions/cs-event-services/commit/f3bbc1ef61e653ba39c60629f2cf09495b6a754e)]
- ❇️ Sales order UE completed CSES-141 [[eddf7e2](https://github.com/newgen-business-solutions/cs-event-services/commit/eddf7e26ec812b6a7ba1d2f1f50c7f4e37cd201c)]
- 📗 Added logging for CS Job on restlet CSES-140 [[1300673](https://github.com/newgen-business-solutions/cs-event-services/commit/13006738fe16e44f1eedd1ba2eb0a046e226518b)]
- 🚧 Working in Payment capture for Paytrace [[d8c80b1](https://github.com/newgen-business-solutions/cs-event-services/commit/d8c80b1f4c9743fdc82bb2e0b51ffebaec318271)]
- 📗 Added logging for inactive forms CSES-140 [[8f9efa1](https://github.com/newgen-business-solutions/cs-event-services/commit/8f9efa13b9fb460f88eb211a32929da451373d8e)]
- 🚧 Finished SS load processing CSES-140 [[7af8087](https://github.com/newgen-business-solutions/cs-event-services/commit/7af80874c04add23dbeda6a49faa172652b4f82c)]
-  Merge pull request [#17](https://github.com/newgen-business-solutions/cs-event-services/issues/17) from newgen-business-solutions/pinnacle-live-setup [[6ca1728](https://github.com/newgen-business-solutions/cs-event-services/commit/6ca1728bd51054ad025ca680e45921ed625d22f1)]
-  Merge branch &#x27;main&#x27; into pinnacle-live-setup [[14d10c8](https://github.com/newgen-business-solutions/cs-event-services/commit/14d10c833a851aa82ae77876fce06c74e2fd0996)]
-  Master Billing [[acc7e8f](https://github.com/newgen-business-solutions/cs-event-services/commit/acc7e8fb33f2c290a55307e5e74bbb682e463710)]
-  Pinnacle live setup - Add Master billing functionality CSES-133 ([#16](https://github.com/newgen-business-solutions/cs-event-services/issues/16)) [[8d7378b](https://github.com/newgen-business-solutions/cs-event-services/commit/8d7378becc6d22c733b6d7005383219af07fe4d5)]
-  Merge branch &#x27;main&#x27; into pinnacle-live-setup [[dc3b364](https://github.com/newgen-business-solutions/cs-event-services/commit/dc3b3643bf489465321987a56d22941df2dfc332)]
-  Merge branch &#x27;CSES-51-lve-e-2-e-bug-cleanup&#x27; [[3f019a7](https://github.com/newgen-business-solutions/cs-event-services/commit/3f019a7f65f1ab48f0b5a14c42e6298787919c2e)]
- 🐝 Fixed custom hook path import [[48c3572](https://github.com/newgen-business-solutions/cs-event-services/commit/48c35725406e445622d2e7eece2674f424341a92)]
- 🚗 Increased context performance using refs [[be1f58a](https://github.com/newgen-business-solutions/cs-event-services/commit/be1f58ab7ab1679f8d60ee6b8db5e929291d6484)]
-  Update AddressAdditionModal.js [[5acb6b4](https://github.com/newgen-business-solutions/cs-event-services/commit/5acb6b4463d6ea4f3ae2150fdf840c1b4c21eb4a)]
- ⭐ Added selected times &amp; date to preview on labor CSES-120 [[41be932](https://github.com/newgen-business-solutions/cs-event-services/commit/41be9328434acd3ac3640eb6b0313429c4110cac)]
-  Merge branch &#x27;main&#x27; into pinnacle-live-setup [[4fdde07](https://github.com/newgen-business-solutions/cs-event-services/commit/4fdde07044b569034e95f0ed6688d7ae39f8bf74)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[133607b](https://github.com/newgen-business-solutions/cs-event-services/commit/133607b2615780bfe9d2a9cc3bbfb0f98b1e6779)]
-  Downgraded node version [[7fe5f0e](https://github.com/newgen-business-solutions/cs-event-services/commit/7fe5f0ef57f2c5e793a95077c6af204239d10ccd)]
-  Changed action name [[3ed4026](https://github.com/newgen-business-solutions/cs-event-services/commit/3ed4026f3ed9d61ebcd4a24cbafd760764311b05)]
-  Master BIlling [[6737853](https://github.com/newgen-business-solutions/cs-event-services/commit/67378535851ebacb1d10015ef4a482b41cb9a010)]
- ☂️ Fixed Linting after checkout polish [[e6a4a5e](https://github.com/newgen-business-solutions/cs-event-services/commit/e6a4a5e359794460968caed685d72837c79c0885)]
- 🚧 Decreased timeout to redirect on product detail page [[16557b2](https://github.com/newgen-business-solutions/cs-event-services/commit/16557b2853b9f499d4615204c396e8a4bcb5c0ae)]
-  :+1:  Fixed Linter bitterness [[f569f4d](https://github.com/newgen-business-solutions/cs-event-services/commit/f569f4d6714d3fd4980fc588eec7ad56d12df2ce)]
- 🕚 Dialed in static build timeout [[6dff124](https://github.com/newgen-business-solutions/cs-event-services/commit/6dff124cee06356efeea80be67bc9de693d2f0df)]
- 🔦 Removed tests from linting scope [[bab9adf](https://github.com/newgen-business-solutions/cs-event-services/commit/bab9adfb4bee716b3f9ba1fe7a05c4398ee021e8)]
- 💯 Not sure how this is wrong 😄 [[5318d55](https://github.com/newgen-business-solutions/cs-event-services/commit/5318d5595fcfbb435247532f53906b5ce81046c1)]
- 🔨 Building product page off branch [[b8a55c6](https://github.com/newgen-business-solutions/cs-event-services/commit/b8a55c6b25063e94aa90c5eac8fd3e60e0fecf1e)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[f0f2208](https://github.com/newgen-business-solutions/cs-event-services/commit/f0f2208db7acd4d3321b0618cd302c8037c3d29c)]


<a name="1.2.0"></a>
## 1.2.0 (2022-10-07)

### Added

- ✅ Added Rental Inventory Client Script [[a57f0fe](https://github.com/newgen-business-solutions/cs-event-services/commit/a57f0fe12cf09bcef8b4203254629f2ee415774f)]
- ✅ Added commit notation to docs [[c18af74](https://github.com/newgen-business-solutions/cs-event-services/commit/c18af74ad7efe68620344102150a6e40126500ec)]

### Changed

- ⚡ Optimized rest of pages and Linted [[5cc7bfd](https://github.com/newgen-business-solutions/cs-event-services/commit/5cc7bfd65a019be418e5546be8a56c5fcc2b5e73)]
- ⚡ Updated PL SB env variables on Vercel deployment [[33abed7](https://github.com/newgen-business-solutions/cs-event-services/commit/33abed71d28f96ff3e2b6656ab646488f4c27ba7)]
- ⚡ updating buildEnv for PL SB Portal [[b833d3b](https://github.com/newgen-business-solutions/cs-event-services/commit/b833d3bb101aeebaf31c28ec835a86c7851e82c0)]
- ⚡ Update pinnacle deployment URLs [[eeaf51a](https://github.com/newgen-business-solutions/cs-event-services/commit/eeaf51a8d30f0f8773747fa6b1ad12e35c863882)]
- ⚡ Changed Location API token to fire before DOM. [[2bdf771](https://github.com/newgen-business-solutions/cs-event-services/commit/2bdf771150c3936f1900f2213650f10b9ad0b947)]
- ⚡ Updated balance &amp; total paid searches [[c3cf33d](https://github.com/newgen-business-solutions/cs-event-services/commit/c3cf33d5cb054d99e87f876058519841ea4280e4)]
- ⚡ Changed up pay amounts to single numbers list CSES-96 [[39128fc](https://github.com/newgen-business-solutions/cs-event-services/commit/39128fca9e5ac7f619db97c37eaed540c5744f4c)]

### Breaking changes

- 💥 Refactored for MUI v5 [[9345670](https://github.com/newgen-business-solutions/cs-event-services/commit/9345670e52c90f93d7105de510ec0e1499a64652)]
- 💥 Updated Packages [[9ebee7b](https://github.com/newgen-business-solutions/cs-event-services/commit/9ebee7be8bea83ef6223d77c9607bec615778412)]

### Fixed

- 🏁 Added styles import to Recent Orders Table [[ad094a4](https://github.com/newgen-business-solutions/cs-event-services/commit/ad094a46dbcad2c3b209bc4d112a0e86f06d5ffb)]
- 🐛 Fixed Card expiry month decrement [[55236d5](https://github.com/newgen-business-solutions/cs-event-services/commit/55236d57189e0e1ca464c027791d7c02ac537ccc)]
- 🐛 Fixed address deletion [[3002c99](https://github.com/newgen-business-solutions/cs-event-services/commit/3002c99da37f5ad3981c6fdc36c120c666626125)]
- 🐛 Fixed Address Addition to allow non-autocomplete [[ad66da0](https://github.com/newgen-business-solutions/cs-event-services/commit/ad66da02b7a9d5d28971f0c99091e1778925a7fa)]
- 🚑 Fixed invoice template html to only set on view [[07a12b9](https://github.com/newgen-business-solutions/cs-event-services/commit/07a12b9ced52be6fce8d6ad886a104bed59bb51a)]
- 🚑 Fixed record save upon item change CSES-101 [[8c30ba5](https://github.com/newgen-business-solutions/cs-event-services/commit/8c30ba5a99b588d09155ad443c1a9b418c05834f)]
- 🚑 Fixed location error on checkout [[78e9f7e](https://github.com/newgen-business-solutions/cs-event-services/commit/78e9f7ee55b905cca13168c0b794c7cc09fef4a5)]
- 🚑 Fixed order checklist suitelet CSES-94 [[d3ce039](https://github.com/newgen-business-solutions/cs-event-services/commit/d3ce039c517b18c6458c2659cebe813ebfb10604)]
- 🚑 Fixed Sub and tax being set incorrectly CSES-99 [[e2e23cf](https://github.com/newgen-business-solutions/cs-event-services/commit/e2e23cf7e074956b8ce9523e419f379286c3b280)]
- 🍎 Added Show Management Cancellation [[c6d20d6](https://github.com/newgen-business-solutions/cs-event-services/commit/c6d20d616b642a24dcc4489f000d537a36473dcb)]
- 🚑 Fixed Show management item deletion CSES-97 [[dc4105b](https://github.com/newgen-business-solutions/cs-event-services/commit/dc4105b85258df3e6d86e1bddad037e384041d6e)]
- 🚑 Fixed balance and total paid fields CSES-96 [[5c74963](https://github.com/newgen-business-solutions/cs-event-services/commit/5c749634140143e4ec7d356139fe7d22bd3d085d)]
- 🚑 Fixed Deposit autofill for event info CSES-95 [[b920db8](https://github.com/newgen-business-solutions/cs-event-services/commit/b920db8c5a375468b712fa96f2382d9482eef058)]
- 🚑 Fixed labor modal on client UI CSES-98 [[86d044d](https://github.com/newgen-business-solutions/cs-event-services/commit/86d044db45f67d172fd151ab9ceedcca91de4dde)]
- 🍎 Fixed submission without venue selected CSES-93 [[2a56c33](https://github.com/newgen-business-solutions/cs-event-services/commit/2a56c333e7c72fef521270b7a3e88ad1cf425976)]
- 🐧 Fixed tax resetting on entity field change CSES-90 [[20d29cf](https://github.com/newgen-business-solutions/cs-event-services/commit/20d29cfeec9ffa760b38dff7884623392d84d498)]
- 🐛 Fixed csJob saveRecord bug CSES-92 [[bf4fd98](https://github.com/newgen-business-solutions/cs-event-services/commit/bf4fd98398604ca0e273e55aab105d01037cd949)]

### Miscellaneous

- 🔨 Fixed rest of Linting [[5ec5d88](https://github.com/newgen-business-solutions/cs-event-services/commit/5ec5d88afcf58ba67089fa3d00e9bd71ac127192)]
- 🔨 Fixed inconsistent return on mapping [[405c6a7](https://github.com/newgen-business-solutions/cs-event-services/commit/405c6a7cab618b93027118ede79658cdb3528273)]
- ☂️ Fixed all Linting issues [[eba14ef](https://github.com/newgen-business-solutions/cs-event-services/commit/eba14ef28c61cabaf6e61103dbd79cac7891de09)]
-  Merge branch &#x27;main&#x27; into sandbox-build [[1b2f1d7](https://github.com/newgen-business-solutions/cs-event-services/commit/1b2f1d717dab0524938baee307ec8e5efe6b6a36)]
-  Merge branch &#x27;main&#x27; into sandbox-build [[b99770d](https://github.com/newgen-business-solutions/cs-event-services/commit/b99770d91de4553b38ee316241b714c802781a70)]
-  Merge branch &#x27;main&#x27; into sandbox-build [[0bfdbe0](https://github.com/newgen-business-solutions/cs-event-services/commit/0bfdbe098546b943db16abd2ce25a707ee12c115)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[64c6264](https://github.com/newgen-business-solutions/cs-event-services/commit/64c62641762bc1f55cadbb3c790e192dea5818bc)]
- 🎈 Added comment to access token endpoint [[3ea144c](https://github.com/newgen-business-solutions/cs-event-services/commit/3ea144cfa5a9ae83f2fc65a983e7f5d2b2aafdac)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[01a7ef0](https://github.com/newgen-business-solutions/cs-event-services/commit/01a7ef0a9fd8a75d79bfee21309e82b6cd093707)]
-  Merge branch &#x27;main&#x27; into sandbox-build [[402e503](https://github.com/newgen-business-solutions/cs-event-services/commit/402e503ccd7cf1d6a9c867a53014ebc7053f15a8)]
-  Merge branch &#x27;CSES-51-lve-e-2-e-bug-cleanup&#x27; into sandbox-build [[ce606e4](https://github.com/newgen-business-solutions/cs-event-services/commit/ce606e43799d61d94f902b809227257e18f194b7)]
-  Merge branch &#x27;CSES-51-lve-e-2-e-bug-cleanup&#x27; into sandbox-build [[a7633c8](https://github.com/newgen-business-solutions/cs-event-services/commit/a7633c84869dbd3d463ea6d0d3570c02aeb971fa)]
-  Merge branch &#x27;CSES-51-lve-e-2-e-bug-cleanup&#x27; into sandbox-build [[61186b6](https://github.com/newgen-business-solutions/cs-event-services/commit/61186b6b580cc137755e2edfebe65fd5d77910d9)]
-  Merge pull request [#9](https://github.com/newgen-business-solutions/cs-event-services/issues/9) from newgen-business-solutions/CSES-51-lve-e-2-e-bug-cleanup [[3f78ced](https://github.com/newgen-business-solutions/cs-event-services/commit/3f78ced0a0646b9a17647510a7c2e9584c4f54e8)]


<a name="0.2.13"></a>
## 0.2.13 (2022-06-20)

### Added

- ✅ Added New Payment Suitelet and Async ops [[ad3c4bd](https://github.com/newgen-business-solutions/cs-event-services/commit/ad3c4bd3cc74b839fb3eacd31a6f2e5d3b110452)]
- ✅ Added Las Vegas Prod to staging script [[928c59d](https://github.com/newgen-business-solutions/cs-event-services/commit/928c59d8ee8d976bb356f38687669deaa6287751)]
- ✅ Added subsidiary check and set on SMOs &amp; BOs CSES-86 [[eb250c9](https://github.com/newgen-business-solutions/cs-event-services/commit/eb250c9b0608a54be16f628e060ec920de1ff9d2)]

### Changed

- 🎨 Fixed code formatting on payment RESTlet [[d4fecef](https://github.com/newgen-business-solutions/cs-event-services/commit/d4fecef26b855c799966d935f12868dd79f561f0)]
- ⚡ Updated PT library file [[7238ad3](https://github.com/newgen-business-solutions/cs-event-services/commit/7238ad3fc0e06a5ed7d3669b025aa7f124d7016f)]
- 🔧 Added root &#x60;try&#x60;s to all lifecycle methods in SO UE [[74c41ae](https://github.com/newgen-business-solutions/cs-event-services/commit/74c41ae67c454984523388861a6ec17dca2a4744)]
- ⚡ Moved CSJob to be set after entity [[be8c581](https://github.com/newgen-business-solutions/cs-event-services/commit/be8c581291ee38a58e1211e23f52bb563e764336)]
- ⚡ Fixed callback on sign in page [[de1d520](https://github.com/newgen-business-solutions/cs-event-services/commit/de1d52042098650739e19e410c5a0f2ab7f48c94)]
- ⚡ Changed SO UE to utilize server methods [[a34a19a](https://github.com/newgen-business-solutions/cs-event-services/commit/a34a19a870e603983e5641a0cb4a25cb5c36e289)]
- ⬆️ Bump next-auth from 4.3.1 to 4.3.3 [[7dd283c](https://github.com/newgen-business-solutions/cs-event-services/commit/7dd283c82046524dd54cb8d0fe9cd72e2205944b)]

### Fixed

- 🚑 Fixed Online order placement with deposit set on init [[da12d9c](https://github.com/newgen-business-solutions/cs-event-services/commit/da12d9cbe4fa6d853ea04ef2d2babe5baf0fea16)]
- 🐛 Fixed Multiple subsidiary check &amp; set [[d8ce79a](https://github.com/newgen-business-solutions/cs-event-services/commit/d8ce79a3c22bbee6c49384a02d70ddcc6942972a)]
- 🐛 Fixed style cleanup on confirm CSES-87 [[a91fb94](https://github.com/newgen-business-solutions/cs-event-services/commit/a91fb94b2b9f51632cb7436dd36c16135fae7cd2)]
- 🐛 Fixed Callback to event selection using window [[fbd6a4c](https://github.com/newgen-business-solutions/cs-event-services/commit/fbd6a4cae344dfbb7cc1fdfda69426f46eb4a9c7)]
- 🐛 Fixed tailwind styles serve to sales order CSES-87 [[d3f670d](https://github.com/newgen-business-solutions/cs-event-services/commit/d3f670d778c9a06aafcfe59e98ef86328dea30ee)]
- 🐛 Fixed Tailwind styles for client script on SO CSES-87 [[84f85ac](https://github.com/newgen-business-solutions/cs-event-services/commit/84f85ac3c7306c2df991a791789146445199acba)]
- 🐛 Fixed Preview URL set up on ENVs [[a5c5f37](https://github.com/newgen-business-solutions/cs-event-services/commit/a5c5f3707c1d2e36d5a87d45934f4afb85dd1846)]
- 🐛 Fixed tailwind config on init [[63c2b40](https://github.com/newgen-business-solutions/cs-event-services/commit/63c2b40336c1dc1308437b228bd609ccc8a91c45)]
- 🐛 Fixed payment Suitelet from firing async [[11c22ba](https://github.com/newgen-business-solutions/cs-event-services/commit/11c22ba9bc51e525543c247d72792e255ba44fd4)]

### Miscellaneous

-  dumb and dangerous committ to  prod via github.dev to update envfile [[15fdcc0](https://github.com/newgen-business-solutions/cs-event-services/commit/15fdcc03a7b6392e90d5fadbc560c1a8109b0d2e)]
- 🚀 LVE fixes finalized [[3c7792d](https://github.com/newgen-business-solutions/cs-event-services/commit/3c7792d31952ef9923911173e7d2df3ead4c97f4)]
-  Create local_histor_so_ue.patch [[3da6562](https://github.com/newgen-business-solutions/cs-event-services/commit/3da6562b6d01221ec2ae3a8bfe38b4ebe183491f)]
-  :fixed: Subsidiary check on web order placement [[db326b6](https://github.com/newgen-business-solutions/cs-event-services/commit/db326b64357d33809da858f8d0d0f1bab903cb4f)]
-  Merge branch &#x27;CSES-51-lve-e-2-e-bug-cleanup&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-51-lve-e-2-e-bug-cleanup [[3b83256](https://github.com/newgen-business-solutions/cs-event-services/commit/3b832563bafafc8de92642a6c865a922a0587fc4)]
-  Merge pull request [#7](https://github.com/newgen-business-solutions/cs-event-services/issues/7) from newgen-business-solutions/dependabot/npm_and_yarn/next-auth-4.3.3 [[7449e1e](https://github.com/newgen-business-solutions/cs-event-services/commit/7449e1e8c2f6c8b780b09abf12a021cd0d92610a)]
-  Merge branch &#x27;CSES-51-lve-e-2-e-bug-cleanup&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-51-lve-e-2-e-bug-cleanup [[d58080b](https://github.com/newgen-business-solutions/cs-event-services/commit/d58080b1b7c402578a30f3514f88313e6cd8b8c7)]
-  Added Deprecation of ENV var [[7fa6896](https://github.com/newgen-business-solutions/cs-event-services/commit/7fa6896a8b78886235ea72cfad466c03a67f64fe)]
-  :white-check-mark: Added Install into account instructions [[890abcb](https://github.com/newgen-business-solutions/cs-event-services/commit/890abcbcf1f3b6d67debb351a3b5d2bfa007b525)]
- 🚀 Various LVE and general cleanup items CSES-51 [[fe80f28](https://github.com/newgen-business-solutions/cs-event-services/commit/fe80f2816191d889b94d26cd5773b30200098db5)]


<a name="0.2.13-prev"></a>
## 0.2.13-prev (2022-06-03)

### Added

- ➕ Added support or Freight on web CSES-61 [[7d1f051](https://github.com/newgen-business-solutions/cs-event-services/commit/7d1f051aab1afd560928b3c11d4a9d4f4e172c3a)]
- ➕ Added Order Type Handler to SM orders CSES-81 [[c289f33](https://github.com/newgen-business-solutions/cs-event-services/commit/c289f33a6818722e558eac1baf130fbe2f6ed913)]
- ➕ Added Social Login Tests [[d3134ae](https://github.com/newgen-business-solutions/cs-event-services/commit/d3134aeadde14d470a0487fa8da889dcb6e9a2b6)]
- ➕ Added Deposit Percentage Reset CSES-78 [[1f83fd6](https://github.com/newgen-business-solutions/cs-event-services/commit/1f83fd6ed1a7066d015ca516d6e1647b7c922f02)]
- ➕ Added Invoice Upgraded Templating CSES-76 [[85a581c](https://github.com/newgen-business-solutions/cs-event-services/commit/85a581ceb33033ee266296730895b5fb8522c6ad)]
- ➕ Added Update to SO of template render field CSES-76 [[22531b6](https://github.com/newgen-business-solutions/cs-event-services/commit/22531b63b26b0ae4897e7c8900322ddbe50db620)]
- ➕ Add support for variant IDs on Matrix item [[ca840ba](https://github.com/newgen-business-solutions/cs-event-services/commit/ca840ba20681e5b4de92b0d71c1c318a9b27535b)]
- ➕ Added Support for freight + Matrix items CSES-61 [[f08dc17](https://github.com/newgen-business-solutions/cs-event-services/commit/f08dc17b21a2ea7b093e91cc80f144ea40c959be)]
- ➕ All Special Item Types Upgraded Processing [[f94f93c](https://github.com/newgen-business-solutions/cs-event-services/commit/f94f93cf561d43792dd0993df5496633bab4bbc9)]
- ➕ All Special Item Types Done [[e20692b](https://github.com/newgen-business-solutions/cs-event-services/commit/e20692be59220714697f550d43c3563f9eb80393)]
- ➕ Added Modal For Days Calc Item Scripting [[598e37e](https://github.com/newgen-business-solutions/cs-event-services/commit/598e37ef30787a9fb4975f641ba640f39746bd38)]
- ➕ Added Item Add Init functions CSES-68 [[e1d25b1](https://github.com/newgen-business-solutions/cs-event-services/commit/e1d25b1d8d014f7df68ad32392ba01b5bd641911)]
- ✅ Added save record ops for BO scripting CSES-64 [[7a45c79](https://github.com/newgen-business-solutions/cs-event-services/commit/7a45c7980b219e623d2c6f1432d5e7791d3f2998)]
- ✅ Added save record ops for BO scripting [[a41fa7d](https://github.com/newgen-business-solutions/cs-event-services/commit/a41fa7d6374c7d5e1cf2cc4f0c0976d373982164)]
- ✅ Added sublist change for items CSES-64 [[51191d8](https://github.com/newgen-business-solutions/cs-event-services/commit/51191d8f5bc6ff299d071edf3c5c72889f5871af)]
- ✅ Cancellation Charges apply successfully CSES-69 [[b09d33a](https://github.com/newgen-business-solutions/cs-event-services/commit/b09d33a13521c01752aa5f6430ad166078147216)]
- ✅ Added Price Level handler function [[00b9049](https://github.com/newgen-business-solutions/cs-event-services/commit/00b9049383fd69c3ee26ca22ebcfcea7013d7ac0)]
- ✅ Added Booth change logic [[c5a233d](https://github.com/newgen-business-solutions/cs-event-services/commit/c5a233decacb8c2c45680ec3a2c3cb13d750edbf)]
- ➕ Added event change hander to new Client script for SOs [[af61c48](https://github.com/newgen-business-solutions/cs-event-services/commit/af61c48d95f03beadc5b7ef9c9d8e0bb7b6ed6e4)]
- ➕ Added SQL Tool to account [[d64049c](https://github.com/newgen-business-solutions/cs-event-services/commit/d64049c10ecbd8f215474999bd282fb48f84a1bb)]
- ➕ Added Pinnacle to staging script PIN-48 [[045d90a](https://github.com/newgen-business-solutions/cs-event-services/commit/045d90ad66dc3e20aa3e4d5b1118d08753b79cb6)]
- ➕ Added Pinnacle to staging script [[93c1611](https://github.com/newgen-business-solutions/cs-event-services/commit/93c1611b89104fad164fbb024962aa864c078c1f)]

### Changed

- 🔧 Added new tokens to CS Test staging script [[b3f6672](https://github.com/newgen-business-solutions/cs-event-services/commit/b3f6672c9c834088545b1d00bbeb7ffa6f80b444)]
- ⚡ Adjusted UEs and Payment Suitelet to allow for deposit percentage adjustment [[30b67dc](https://github.com/newgen-business-solutions/cs-event-services/commit/30b67dc636ed5c553238ed36af9860285bd57789)]
- 🎨 Changed Template amount print on deposits [[b5000f1](https://github.com/newgen-business-solutions/cs-event-services/commit/b5000f1b82dce5dd51ecd6db257dbf1803f920ff)]
- 🎨 Changed Labor modal styles &amp; Fixed time save CSES-68 [[91bcbf5](https://github.com/newgen-business-solutions/cs-event-services/commit/91bcbf5c455088e60dcc11fce95598a9c536d547)]
- ⚡ Fixed Freight Price return to iterate through freight table [[8b3d223](https://github.com/newgen-business-solutions/cs-event-services/commit/8b3d2236ab49dd5e7b1e096b1d26f28cee193a3f)]
- ⚡ Upped Delay on Item price level setting [[64191b1](https://github.com/newgen-business-solutions/cs-event-services/commit/64191b1e243e804d72d4a0fe8e5cde8292b961b8)]
- 🎨 Created labor Modal [[b7aa235](https://github.com/newgen-business-solutions/cs-event-services/commit/b7aa235033701c7f868cb67bf387327f301209b8)]
- ⚡ Added Days Calc Modal Cleanup [[8befbdd](https://github.com/newgen-business-solutions/cs-event-services/commit/8befbdd626380510a6c1508d8aa50c55bc9a1e02)]
- ⚡ Changed booth order lookup to understand if current order record [[8d443c1](https://github.com/newgen-business-solutions/cs-event-services/commit/8d443c18c9e185b39d313d559128cbdf4e1b9346)]
- ⚡ Fixed up Booth Order Save CSES-64 [[f431cd3](https://github.com/newgen-business-solutions/cs-event-services/commit/f431cd3fb507cf8dbaa1bcb2dffa308024ddf749)]
- ⚡ Fixed old client script to match syntax correctly [[f6e2d6b](https://github.com/newgen-business-solutions/cs-event-services/commit/f6e2d6b2033297ef6d6e9fe811a22e6922d44e3c)]
- ⚡ Fixed multiple firing of collection lists [[d6b935e](https://github.com/newgen-business-solutions/cs-event-services/commit/d6b935e9a3b378738b069d0ed04c3cb5b2aab8a6)]
- 🎨 Changed verbiage on Client script alert for filling out options [[14c9fce](https://github.com/newgen-business-solutions/cs-event-services/commit/14c9fcef052c7f194c0ed32f50fba76e201c2005)]
- 🎨 Added fallback for web item name missing CSES-48 [[ed30ac0](https://github.com/newgen-business-solutions/cs-event-services/commit/ed30ac056ed97ad568ec019af68f200fd99edb25)]
- 🎨 Handled sales order status additions CSES-48 [[c2fe7f6](https://github.com/newgen-business-solutions/cs-event-services/commit/c2fe7f66323e2d3b34f5999f6997f35cac28beaf)]
- 🎨 Adjusted unit of measure check  to allow render CSES-47 [[b70c8e8](https://github.com/newgen-business-solutions/cs-event-services/commit/b70c8e853b9828599cbb296defa39d2f5caaface)]
- 🎨 CSES-46 Fixed logo size in height of SubHeader [[2aadffa](https://github.com/newgen-business-solutions/cs-event-services/commit/2aadffaa386e98fa90a0dae0576b37f3b8b39b67)]
- 🎨 Refactored Orientation on &#x60;[pid]&#x60; page to use &quot;variant&quot; instead [[0f9c3c3](https://github.com/newgen-business-solutions/cs-event-services/commit/0f9c3c3cacb55304a8adf8c510677a943bf0bb78)]
- 🎨 Changed Field matrix names from Orientation to Variant [[ef7f5d3](https://github.com/newgen-business-solutions/cs-event-services/commit/ef7f5d3fce39e6ead0c8dc8d7727ea7ef7dcb910)]
- ⚡ Changed Oauth Token to Restlet User instead of CS [[ead7bca](https://github.com/newgen-business-solutions/cs-event-services/commit/ead7bcafa0d5653df6b6b13363f91b63c21c4883)]
- ⚡ Modified manifest [[e910383](https://github.com/newgen-business-solutions/cs-event-services/commit/e910383396ff8fe5353570386bc28da87f16ddb4)]

### Removed

- 🔥 Removed ScreenSize from web [[5e6ab21](https://github.com/newgen-business-solutions/cs-event-services/commit/5e6ab218cf8e47b419482cc681dbe338f7079017)]
- 🔥 Removed retain last category CSES-68 [[aceea49](https://github.com/newgen-business-solutions/cs-event-services/commit/aceea49838f0af53fa330ec9d75c492738f5d701)]
- 🔥 Revoked CS token as it wasn&#x27;t needed [[1561d2d](https://github.com/newgen-business-solutions/cs-event-services/commit/1561d2d952994986c94742151814a9228574806f)]

### Fixed

- 🐛 Fixed SO event tax reset on web order [[a83d86d](https://github.com/newgen-business-solutions/cs-event-services/commit/a83d86db7ea32b3c37adc4f63a1f41a15e4151d2)]
- 🐛 Fixed Matrix child support to send child id to cart object [[19fb672](https://github.com/newgen-business-solutions/cs-event-services/commit/19fb672f76e69c8cc945afa241f2b55533c2a618)]
- 🐛 Fixed Show Management Deposit Set On Init [[b9dfb32](https://github.com/newgen-business-solutions/cs-event-services/commit/b9dfb3297a899f9abaeec35ae3f1867c900d0c1c)]
- 🐛 Fixed Deposit Duplication Appearance CSES-80 [[3b865d2](https://github.com/newgen-business-solutions/cs-event-services/commit/3b865d2b5aa396ca07bb152ffca216aa4d9c590c)]
- 🐛 Fixed console undefined error on SO UE CSES-79 [[2d39252](https://github.com/newgen-business-solutions/cs-event-services/commit/2d3925239d1391fca1d4768c8ff3970036986a83)]
- 🐛 Fixed Screen size on collection cards [[82c1212](https://github.com/newgen-business-solutions/cs-event-services/commit/82c1212bae3b6155a7cfd19aca46bde6ae19448b)]
- 🐛 Fixed Related Payments Showing Up On SO Templates CSES-76 [[41df9a5](https://github.com/newgen-business-solutions/cs-event-services/commit/41df9a5b6b0fca68b857ea61773818b9aff8f855)]
- 🐛 Deprecated Screen Size Field [[bcf5643](https://github.com/newgen-business-solutions/cs-event-services/commit/bcf56430d5406f16872810185d773a24bf3d3774)]
- 🐛 Allowed to save show management orders CSES-65 [[306d1fb](https://github.com/newgen-business-solutions/cs-event-services/commit/306d1fb38c129d4f0765f256661bd4d97ec7da5c)]
- 🐛 Fixed delete error - cannot load a record that is deleted CCES-72 [[943bdaa](https://github.com/newgen-business-solutions/cs-event-services/commit/943bdaae02daa5f524f831f4499f809556daea13)]
- 🐛 Fixed tax reset issue on create CSES-40 [[1b4b812](https://github.com/newgen-business-solutions/cs-event-services/commit/1b4b812ac099bf4301149329c1ced3302fe42acd)]
- 🐛 Fixed cancellation item add [[9126268](https://github.com/newgen-business-solutions/cs-event-services/commit/91262681b8c91cf2a3ea07d96a329c0a14ccbc22)]
- 🐛 PT web error display handling to site [[95026b8](https://github.com/newgen-business-solutions/cs-event-services/commit/95026b8132891455ba70c49e544b241e3adf707c)]
- 🐛 Fixed defaulting when PT is enabled CSES-54 [[caf60cf](https://github.com/newgen-business-solutions/cs-event-services/commit/caf60cf5d5a41637e05afe13deb4c4dc7b35c494)]
- 🐛 Fixed SO balance field updating after result count refactor CSES-44 [[142b4f3](https://github.com/newgen-business-solutions/cs-event-services/commit/142b4f3197bb18420e7d810a722d47adb9e80c97)]
- 🐛 Fixed Web processing erroring CSES-45 [[a5c1a75](https://github.com/newgen-business-solutions/cs-event-services/commit/a5c1a758dab2b4745da8670456d12fc3583744e2)]
- 🐛 Fixed no price levels to not break page on render [[e902d87](https://github.com/newgen-business-solutions/cs-event-services/commit/e902d879e9f5e4744e1aadccb798433215252093)]
- 🐛 Handled item baseprice undefined breaking page CSES-48 [[c2399db](https://github.com/newgen-business-solutions/cs-event-services/commit/c2399db24bde4ad3fc9ef9aa37ae7055da4720e7)]

### Miscellaneous

-  Merge branch &#x27;CSES-51-lve-e-2-e-bug-cleanup&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-51-lve-e-2-e-bug-cleanup [[0525b99](https://github.com/newgen-business-solutions/cs-event-services/commit/0525b99427f54666592a42142e2f2920fe0339cc)]
- 📓 Updated changelog [[ed73f59](https://github.com/newgen-business-solutions/cs-event-services/commit/ed73f59511761c4221c817f3bd1de30a5b9f1378)]
-  Merge remote-tracking branch &#x27;origin/CSES-51-lve-e-2-e-bug-cleanup&#x27; into CSES-51-lve-e-2-e-bug-cleanup [[01144a3](https://github.com/newgen-business-solutions/cs-event-services/commit/01144a31dc794cc794f6f7f84d690c8f3aa5c77d)]
- ✔️ Dialog box now working with item line removal to cancel [[ebed6b5](https://github.com/newgen-business-solutions/cs-event-services/commit/ebed6b542beb7d751fcbf38b67c8c65b0e5623a2)]
-  Pinnacle portal successfully added to staging script [[4759ede](https://github.com/newgen-business-solutions/cs-event-services/commit/4759ede366f8923903b9e72e1e562beb7d18c6d2)]
-  Merge branch &#x27;PIN-48-set-up-exhibitor-ordering-portal&#x27; of https://github.com/newgen-business-solutions/cs-event-services into PIN-48-set-up-exhibitor-ordering-portal [[f811df5](https://github.com/newgen-business-solutions/cs-event-services/commit/f811df5415fb17d15606c3eb169ba130b850190f)]


<a name="0.2.12"></a>
## 0.2.12 - Backend 5.1.2 (2022-04-15) 

### Added

- ➕ Added cypress login testing [[d052a21](https://github.com/newgen-business-solutions/cs-event-services/commit/d052a2175cb0ab6533ee4c131ca8d7ea5bf4796e)]
- ✅ Added Versapay compatibility for check out [[00336fa](https://github.com/newgen-business-solutions/cs-event-services/commit/00336fa76d0fe477a1ba407ce34d786440f94e5c)]
- ➕ Added Card Default change on my account center. [[89f436d](https://github.com/newgen-business-solutions/cs-event-services/commit/89f436d3035522978d8beaaafcde8739f7de781c)]
- ➕ CSES-38 Added Card default switching to Non PT CS checkout [[ce9029a](https://github.com/newgen-business-solutions/cs-event-services/commit/ce9029ac5d2a5fdcaff9bc3a94d1bbf511b4b809)]
- ➕ Added Card Default change on my account center. [[a8e95a9](https://github.com/newgen-business-solutions/cs-event-services/commit/a8e95a9523f868cae7b02f0a41fbbc3732e86021)]
- ➕ CSES-37 Added Card default switching to Non PT CS checkout [[f9cba8f](https://github.com/newgen-business-solutions/cs-event-services/commit/f9cba8f871defdf1eaba118de0ee77a5f2158494)]
- 🔊 Added JS Doc tags to identify variables &amp; types [[8fec8a7](https://github.com/newgen-business-solutions/cs-event-services/commit/8fec8a77bc535b4fcf0f457aafc6d3672ba72a27)]
- ✅ CSES-31 Added new UE Sales order script for enhanced processing [[a40f363](https://github.com/newgen-business-solutions/cs-event-services/commit/a40f363fb32e086b952b53faf6cf41cf7f8210c8)]
- ➕ Added Cypress E2E testing lib [[8e03a62](https://github.com/newgen-business-solutions/cs-event-services/commit/8e03a62863ae6ce719153c815a20fbdf6976dae7)]
- 👷‍♂️ Added functionality to check for paytrace integration upon card additions [[fe124fc](https://github.com/newgen-business-solutions/cs-event-services/commit/fe124fcc6cf66ca54bd018b5e5dd0fa6b02a27a0)]

### Changed

- ⚡ Changed how item descriptions get set fixing tax reset issue [[efb7253](https://github.com/newgen-business-solutions/cs-event-services/commit/efb7253eb7669195c5de63ee5384d9fa813a5cb3)]
- ⚡ Rearranged Global Run for context types in switch [[37cfeb8](https://github.com/newgen-business-solutions/cs-event-services/commit/37cfeb8887e8fd74ca4a3d581939c77ba1fc6edc)]
- ⚡ Enhanced logic for CS Booth to remember initial state of booth values [[5732c1c](https://github.com/newgen-business-solutions/cs-event-services/commit/5732c1c9315ea6b23c15f2a86c1fbe14873c1463)]
- ♿ CSES-37 Fixed order consolidation and all Paytrace order calculations for deposits [[08e6885](https://github.com/newgen-business-solutions/cs-event-services/commit/08e6885ddda4b16a276fa17096ff9a5fba997374)]
- ⚡ Fixed all of solupay payment processing [[c196cb7](https://github.com/newgen-business-solutions/cs-event-services/commit/c196cb7230e43911e82dd9b31c8d09d357e2d337)]
- 🔧 : Fixed the rest of the file paths importing the PT library [[6fac580](https://github.com/newgen-business-solutions/cs-event-services/commit/6fac580055fdbec891239a75bbe24da3a6649ca3)]
- ⚡ Updated PayTrace lib for 2.1 scripting and rearranged Object files. [[7a1d0d5](https://github.com/newgen-business-solutions/cs-event-services/commit/7a1d0d56126933876256f0e362310a7c5250f65d)]
- ⚡ CSES-37 Fixed file path of paytrace lib [[d857fec](https://github.com/newgen-business-solutions/cs-event-services/commit/d857fec00012e327adb5710a16244a7ec9373e19)]
- 🔧 CSES-37 Converted ALL newgen libs to 2.1 [[9ffe14f](https://github.com/newgen-business-solutions/cs-event-services/commit/9ffe14fd15f67c707e8edef2d6b8b4a0e5507882)]
- 🎨 Changed Order summary to calculate conv fee as RCS field is now absent. [[8173855](https://github.com/newgen-business-solutions/cs-event-services/commit/81738558c5fa710f4b9d2d23e0ff354fb602e91c)]
- ⚡ CSES-37 Got sales order to create deposits successfully [[55ba67f](https://github.com/newgen-business-solutions/cs-event-services/commit/55ba67fffcb3d3ae85f7f3496f5f871a07f6fc74)]
- 📌 CSES-37 Got Authorization for Credit Cards Working [[1f8370e](https://github.com/newgen-business-solutions/cs-event-services/commit/1f8370e7b49d7032ed5e8fb6322923ef2e0092e1)]
- 🎨 Changed force signout to redirect back to the login page. [[9f3ce8c](https://github.com/newgen-business-solutions/cs-event-services/commit/9f3ce8c4ebb5fa9277362e012ca9093445136e2c)]
- ⚡ CSES-37 Enhanced Files to have better logging and conditional checks [[3cc94df](https://github.com/newgen-business-solutions/cs-event-services/commit/3cc94dff54f1ca1a89bc41f49d3da7ec1f9917f0)]
- ⚡ CSES-31 Optimized conditionals for obsolete use of &#x60;isEmpty()&#x60; [[ca85e16](https://github.com/newgen-business-solutions/cs-event-services/commit/ca85e16f9938cb11495c3c5954fa672b588104eb)]
- ⚡ Optimized conditionals for obsolete use of &#x60;isEmpty()&#x60; [[9cf8417](https://github.com/newgen-business-solutions/cs-event-services/commit/9cf84171efbe146fb484d267a9ffd3792f807335)]
- ⚡ Changed check upon POST&#x27;ing new card to allow for 3rd party payments [[3870ba2](https://github.com/newgen-business-solutions/cs-event-services/commit/3870ba2f334815deb4da745e540a50cb8c8a9c84)]
- ⚡ Acknowledge paytrace integration capture key conditionally on account page [[f2655dc](https://github.com/newgen-business-solutions/cs-event-services/commit/f2655dc66b4de99f5eb006f57f671122810952b1)]
- ⚡ Changed &#x60;encryptCard()&#x60; to acknowledge paytrace integration setting [[590f306](https://github.com/newgen-business-solutions/cs-event-services/commit/590f30625f9a49dd9c216626a7becce615ff4bfb)]
- ⚡ Check settings for paytrace before getting pem key [[d2f5f07](https://github.com/newgen-business-solutions/cs-event-services/commit/d2f5f075a6396d4f65fb4b45a6226d0f5d828e87)]
- ⚡ Enhanced some general functions [[a43ccc6](https://github.com/newgen-business-solutions/cs-event-services/commit/a43ccc6e086b8187e2a27f6cf84f097803ed68c4)]
- ⚡ Changed &#x60;encryptCard()&#x60; to check paytrace integration [[373dd4f](https://github.com/newgen-business-solutions/cs-event-services/commit/373dd4fa00e157766d533f9a75b5c01422c38cc3)]
- ⚡ Changed clent script reference locations on suitelets [[069ae61](https://github.com/newgen-business-solutions/cs-event-services/commit/069ae61fc44f65fc41e929dd1137b73e396e31f8)]
- ⚡ Only run public key capture if paytrace is enabled [[a53ab8a](https://github.com/newgen-business-solutions/cs-event-services/commit/a53ab8a93d93bdbc0b7fc80a8ba534352d0c0700)]
- 🎨 Only run card encryption when paytrace integration is active [[c506805](https://github.com/newgen-business-solutions/cs-event-services/commit/c506805546bf098754ef6f2335c8bc30864e3908)]

### Removed

- 🔥 Got rid of Mikes code for checking context mode as it is not defined on &#x60;saveRecord&#x60; [[6f0f877](https://github.com/newgen-business-solutions/cs-event-services/commit/6f0f8773d867188ba2a97c19faa21ea172f15fe2)]
- 🔥 Remove badCalc from UE afterSubmit REST Non-Paytrace [[dd6cd31](https://github.com/newgen-business-solutions/cs-event-services/commit/dd6cd31fda124e6164e0356a03979fe2551e34c8)]
- 🔥 Removed badCalc from afterSubmit [[b54bf2d](https://github.com/newgen-business-solutions/cs-event-services/commit/b54bf2d2d994217b6ee1031f388911e9319d4206)]
- 🔥 Removed badCalc from CS UE [[3b8209d](https://github.com/newgen-business-solutions/cs-event-services/commit/3b8209d9abec781349d4fe8f64263217690c6241)]
- 🔥 Remove badCalc from UE afterSubmit REST Non-Paytrace [[fdd7b99](https://github.com/newgen-business-solutions/cs-event-services/commit/fdd7b997038aa0de18b14df571968f2d58f9beb2)]
- 🔥 Removed badCalc from afterSubmit [[f6da4e9](https://github.com/newgen-business-solutions/cs-event-services/commit/f6da4e971c816bd9306d0feebb5e5a0763e8e31a)]
- 🔥 Removed badCalc from CS UE [[7ae41ba](https://github.com/newgen-business-solutions/cs-event-services/commit/7ae41bab37ad6c0ab1faa023b29e22d9f4f695ee)]

### Fixed

- 🐛 Eliminated tax forcing reset on SO CSES-40 [[0bea9c5](https://github.com/newgen-business-solutions/cs-event-services/commit/0bea9c5ed22fe005cc193beaa7646eec0d7e3b95)]
- 🐛 Fixed show management SO calcs CSES-28 [[775b6ef](https://github.com/newgen-business-solutions/cs-event-services/commit/775b6efb55f087a8d4666f8ef1c12bafb2027fc8)]
- 🐛 Fixed Annoying Validate Field Function for booth number duplicates [[33aa459](https://github.com/newgen-business-solutions/cs-event-services/commit/33aa4592fc46fb3a88d0c69ecf53a956a9bc48ae)]
- 🐛 Fixed Tax override on SO when in edit mode allowing modification [[2c50f94](https://github.com/newgen-business-solutions/cs-event-services/commit/2c50f94b360297874b38c6141a297f3bcbdf93d9)]
- 🐛 Fixed Exhibitor Sales order checklist item description render [[98b5b88](https://github.com/newgen-business-solutions/cs-event-services/commit/98b5b886367a39de9e9662a44866768cd5cab7d2)]
- 🐛 Fixed invalid field check on SO disabling fields incorrectly [[9f8bb7c](https://github.com/newgen-business-solutions/cs-event-services/commit/9f8bb7c4395e44da42789f356b631765e66c3704)]
- 🐛 Fixed field disable on SO form with correct param [[d9dab4c](https://github.com/newgen-business-solutions/cs-event-services/commit/d9dab4c9658fdf187520fffad62b8ca3bfce040f)]
- 🐛 CSES-37 Fixed fatal error on uncheck of preventing booth orders [[0559ae3](https://github.com/newgen-business-solutions/cs-event-services/commit/0559ae3b0b756c62ab3f84089e9b416241599716)]
- 🐛 Fixed Web session redirect on checkout to go to the cart page [[43e7fa1](https://github.com/newgen-business-solutions/cs-event-services/commit/43e7fa18c110db3dc2a30e0dde2769c40f3304a4)]
- 🐛 Fixed invalid session check on checkout [[eabb5f1](https://github.com/newgen-business-solutions/cs-event-services/commit/eabb5f1c4a42362323658e172bfc4b0f95ecd00a)]
- 🐛 Fixed display of convenience fee on checkout and order completion summary [[28c559b](https://github.com/newgen-business-solutions/cs-event-services/commit/28c559b9f309dd36e4eccb27e86fdb22ad037b33)]
- 🐛 Fixed paytrace object key lookup [[40f025e](https://github.com/newgen-business-solutions/cs-event-services/commit/40f025e5c7e298b6edc481aa6b0b610c9ef748d2)]
- 🐛 Fixed post on card creation to acknowledge paytrace integration [[1ba0889](https://github.com/newgen-business-solutions/cs-event-services/commit/1ba08891ee5e42134d7a0969ec64e72e7f91c8fa)]
- 🐛 Fixed script typo in search look up on &#x60;eventID&#x60; [[85fd536](https://github.com/newgen-business-solutions/cs-event-services/commit/85fd536f06e1511828a82536f2ded28570c5643e)]
- 🐛 Fixed CS library imports and cs-client imports [[7ee171a](https://github.com/newgen-business-solutions/cs-event-services/commit/7ee171aaeaed0aaec5eda3062b8ab197f0784d16)]

### Miscellaneous

-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[4a555df](https://github.com/newgen-business-solutions/cs-event-services/commit/4a555df491ec97770993846b902f6d356941343b)]
- 🚀 Prepped For SuiteApp Project on some directoires [[fab52cb](https://github.com/newgen-business-solutions/cs-event-services/commit/fab52cbec9e051e589b34a2ba3abd77d95e852e3)]
-  Update README.md [[befd9c2](https://github.com/newgen-business-solutions/cs-event-services/commit/befd9c2a8e8eedebededa253a68abf4819c77d90)]
-  Update README.md [[3760be3](https://github.com/newgen-business-solutions/cs-event-services/commit/3760be3a8998db19b12b56e1d2244a3212d65755)]
-  Merge branch &#x27;CSES-31-versapay-support-for-checkout-so-ue&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-31-versapay-support-for-checkout-so-ue [[8e7f4dd](https://github.com/newgen-business-solutions/cs-event-services/commit/8e7f4dd333f1a9ad4b600747a9b04925def958f3)]
-  Merge branch &#x27;CSES-31-versapay-support-for-checkout-so-ue&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-31-versapay-support-for-checkout-so-ue [[57ecff5](https://github.com/newgen-business-solutions/cs-event-services/commit/57ecff5058343ff81fcd5dec33056b7f0fded7ef)]
- 🔉 CSES-37 Added JSDocs to price level algorithm [[6143d6d](https://github.com/newgen-business-solutions/cs-event-services/commit/6143d6d4dd838c9347a88c1b8acc567c33c57c7e)]
- 🚧 Updated Changelog [[340841d](https://github.com/newgen-business-solutions/cs-event-services/commit/340841de67f33f85f1390e1a25d68036d4f3638a)]
-  Merge branch &#x27;CSES-31-versapay-support-for-checkout-so-ue&#x27; of https://github.com/newgen-business-solutions/cs-event-services into CSES-31-versapay-support-for-checkout-so-ue [[771d1bd](https://github.com/newgen-business-solutions/cs-event-services/commit/771d1bdfb269e43226c0131e299c648a171c7a48)]
-  Merge branch &#x27;main&#x27; of https://github.com/newgen-business-solutions/cs-event-services [[4734d13](https://github.com/newgen-business-solutions/cs-event-services/commit/4734d13eafa1502e3fdec5ab73bd8d049eb62152)]
-  :robot: Updated All NS Objects [[c2f3b07](https://github.com/newgen-business-solutions/cs-event-services/commit/c2f3b07591f3a7c4252bf44321876ffcbaa72786)]
-  Update README.md [[0319ded](https://github.com/newgen-business-solutions/cs-event-services/commit/0319dedee687d349572506d5122f0485eb5e315d)]


<a name="0.1.12"></a>
## 0.1.12 (2022-03-23)

### Added

- 👷‍♂️ Added Changelog [[44496b0](https://github.com/newgen-business-solutions/cs-event-services/commit/44496b03248aedb84f2d72d83630f1b747e2647a)]
- 👷‍♂️ Added all files from SDF conversion [[f05ea1e](https://github.com/newgen-business-solutions/cs-event-services/commit/f05ea1e316e2fb6380c2b9674c7d8ff1b0be8a41)]
- ✨ Set up initial monorepo set up [[d38ba14](https://github.com/newgen-business-solutions/cs-event-services/commit/d38ba14f21cc1e9c9258b06791eb3f9f23debd7a)]

### Changed

- ⚡ Changed version to avg semantically with both apps [[408f36d](https://github.com/newgen-business-solutions/cs-event-services/commit/408f36dfcdfce6660582008f6c11f673a0064516)]
- ⚡ Only fetch certificate of public key for paytrace only when enabled [[4105c63](https://github.com/newgen-business-solutions/cs-event-services/commit/4105c6369cc50363d1a0ae81740c8ea0fcd8755d)]

### Removed

- 🔥 Removed Docs dummy Next.js app [[3ca7d4b](https://github.com/newgen-business-solutions/cs-event-services/commit/3ca7d4b60364504a9d3d697327c59aaffaf8812a)]

### Miscellaneous

- 🚧 Massive change to file structure in NS Account [[7eeac7a](https://github.com/newgen-business-solutions/cs-event-services/commit/7eeac7ad2f2e70c85eb2de2ffb276686c4f3a69b)]
-  Initial commit from Create Turborepo [[b846b34](https://github.com/newgen-business-solutions/cs-event-services/commit/b846b34694a7fd4dc3c4d951a5db12d2b05ab7a0)]


